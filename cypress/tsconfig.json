{"extends": "../tsconfig.json", "compilerOptions": {"target": "es5", "lib": ["es5", "dom"], "types": ["cypress", "node"], "sourceMap": true, "declaration": false, "outDir": "./dist", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "resolveJsonModule": true, "esModuleInterop": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "paths": {"@/*": ["../frontend/src/*"], "@/components/*": ["../frontend/src/components/*"], "@/lib/*": ["../frontend/src/lib/*"], "@/pages/*": ["../frontend/src/pages/*"], "@/stores/*": ["../frontend/src/stores/*"], "@/types/*": ["../frontend/src/types/*"], "@/hooks/*": ["../frontend/src/hooks/*"], "@/utils/*": ["../frontend/src/lib/utils/*"]}}, "include": ["**/*.ts", "**/*.tsx", "../cypress.d.ts"], "exclude": ["../node_modules", "../dist", "../build"]}