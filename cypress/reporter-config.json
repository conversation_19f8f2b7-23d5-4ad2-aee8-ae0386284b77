{"reporterEnabled": "spec, json, mochawesome", "specReporterOptions": {"displayStacktrace": "all", "displaySuccessfulSpec": true, "displayFailedSpec": true, "displayPendingSpec": false, "displayDuration": true, "colors": {"success": "green", "failure": "red", "pending": "cyan"}}, "jsonReporterOptions": {"output": "cypress/reports/results.json"}, "mochawesomeReporterOptions": {"reportDir": "cypress/reports/mochawesome", "overwrite": true, "html": true, "json": true, "timestamp": true, "reportFilename": "report", "reportTitle": "Personal Finance Manager - E2E Test Results", "reportPageTitle": "Cypress Test Report", "embeddedScreenshots": true, "inlineAssets": true, "saveAllAttempts": false, "ignoreVideos": false, "videoOnFailOnly": false, "quiet": false, "debug": false, "charts": true, "code": true, "autoOpen": false, "showPassed": true, "showFailed": true, "showPending": true, "showSkipped": false, "showHooks": "failed", "saveJson": true, "saveHtml": true, "dev": false, "assetsDir": "cypress/reports/mochawesome/assets", "jsonFile": "cypress/reports/mochawesome/report.json", "htmlFile": "cypress/reports/mochawesome/report.html"}}