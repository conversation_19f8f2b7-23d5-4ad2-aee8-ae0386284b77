import { LoginPage } from '../../support/page-objects/LoginPage'
import { DashboardPage } from '../../support/page-objects/DashboardPage'

describe('Authentication - Login', () => {
  let loginPage: LoginPage
  let dashboardPage: DashboardPage
  let testUser: any

  beforeEach(() => {
    loginPage = new LoginPage()
    dashboardPage = new DashboardPage()
    
    // Load test user data
    cy.fixture('users').then((users) => {
      testUser = users.validUser
    })
    
    // Clean database and seed with test data
    cy.cleanDatabase()
    cy.seedDatabase()
    
    // Intercept API calls
    loginPage.interceptLoginRequest()
    dashboardPage.interceptDashboardData()
  })

  describe('Login Page', () => {
    it('should display login form correctly', () => {
      loginPage
        .visit()
        .shouldBeOnLoginPage()
        .shouldHaveValidForm()
    })

    it('should show validation errors for empty fields', () => {
      loginPage
        .visit()
        .clickLogin()
        .shouldShowEmailError('Email is required')
        .shouldShowPasswordError('Password is required')
    })

    it('should show validation error for invalid email format', () => {
      loginPage
        .visit()
        .fillEmail('invalid-email')
        .fillPassword('password123')
        .clickLogin()
        .shouldShowEmailError('Please enter a valid email address')
    })

    it('should show error for non-existent user', () => {
      cy.fixture('users').then((users) => {
        const nonExistentUser = users.invalidUsers.nonExistentUser
        
        loginPage
          .visit()
          .login(nonExistentUser.email, nonExistentUser.password)
          .waitForLoginRequest()
          .shouldShowErrorMessage('Invalid email or password')
      })
    })

    it('should show error for wrong password', () => {
      loginPage
        .visit()
        .login(testUser.email, 'wrongpassword')
        .waitForLoginRequest()
        .shouldShowErrorMessage('Invalid email or password')
    })
  })

  describe('Successful Login', () => {
    it('should login successfully with valid credentials', () => {
      loginPage
        .visit()
        .login(testUser.email, testUser.password)
        .waitForLoginRequest()
        .shouldRedirectToDashboard()

      dashboardPage
        .shouldBeOnDashboard()
        .shouldShowWelcomeMessage(testUser.name)
    })

    it('should login via API and access protected pages', () => {
      cy.loginViaAPI(testUser.email, testUser.password)
      
      dashboardPage
        .visit()
        .shouldBeOnDashboard()
        .shouldShowWelcomeMessage(testUser.name)
        .shouldShowBalanceCard()
        .shouldShowQuickActions()
    })

    it('should remember user session after page refresh', () => {
      cy.loginViaAPI(testUser.email, testUser.password)
      
      dashboardPage
        .visit()
        .shouldBeOnDashboard()
        .refreshDashboard()
        .shouldBeOnDashboard()
        .shouldShowWelcomeMessage(testUser.name)
    })

    it('should maintain authentication across different pages', () => {
      cy.loginViaAPI(testUser.email, testUser.password)
      
      dashboardPage
        .visit()
        .shouldBeOnDashboard()
        .navigateToTransactions()
      
      cy.url().should('include', '/transactions')
      
      dashboardPage
        .navigateToAccounts()
      
      cy.url().should('include', '/accounts')
    })
  })

  describe('Login Form Interactions', () => {
    it('should toggle password visibility', () => {
      loginPage
        .visit()
        .fillPassword('password123')
      
      // Check if password is hidden by default
      cy.get('[data-cy=password-input]').should('have.attr', 'type', 'password')
      
      // Toggle password visibility
      cy.get('[data-cy=toggle-password]').click()
      cy.get('[data-cy=password-input]').should('have.attr', 'type', 'text')
      
      // Toggle back to hidden
      cy.get('[data-cy=toggle-password]').click()
      cy.get('[data-cy=password-input]').should('have.attr', 'type', 'password')
    })

    it('should handle remember me functionality', () => {
      loginPage
        .visit()
        .login(testUser.email, testUser.password, true)
        .waitForLoginRequest()
        .shouldRedirectToDashboard()
      
      // Verify remember me token is stored
      cy.window().its('localStorage').invoke('getItem', 'remember-token').should('exist')
    })

    it('should clear form when cancel is clicked', () => {
      loginPage
        .visit()
        .fillEmail('<EMAIL>')
        .fillPassword('password123')
        .clearForm()
      
      cy.get('[data-cy=email-input]').should('have.value', '')
      cy.get('[data-cy=password-input]').should('have.value', '')
    })
  })

  describe('Navigation', () => {
    it('should navigate to register page', () => {
      loginPage
        .visit()
        .clickRegister()
      
      cy.url().should('include', '/register')
    })

    it('should navigate to forgot password page', () => {
      loginPage
        .visit()
        .clickForgotPassword()
      
      cy.url().should('include', '/forgot-password')
    })

    it('should redirect to login when accessing protected route without authentication', () => {
      cy.visit('/dashboard')
      cy.url().should('include', '/login')
    })
  })

  describe('Loading States', () => {
    it('should show loading spinner during login', () => {
      loginPage
        .visit()
        .fillEmail(testUser.email)
        .fillPassword(testUser.password)
        .clickLogin()
        .shouldShowLoadingSpinner()
        .waitForLoginRequest()
        .shouldHideLoadingSpinner()
    })

    it('should disable login button during submission', () => {
      loginPage
        .visit()
        .fillEmail(testUser.email)
        .fillPassword(testUser.password)
        .clickLogin()
      
      cy.get('[data-cy=login-button]').should('be.disabled')
      
      loginPage
        .waitForLoginRequest()
      
      cy.get('[data-cy=login-button]').should('not.be.disabled')
    })
  })

  describe('Accessibility', () => {
    it('should be accessible via keyboard navigation', () => {
      loginPage.visit()
      
      // Tab through form elements
      cy.get('body').tab()
      cy.focused().should('have.attr', 'data-cy', 'email-input')
      
      cy.focused().tab()
      cy.focused().should('have.attr', 'data-cy', 'password-input')
      
      cy.focused().tab()
      cy.focused().should('have.attr', 'data-cy', 'login-button')
    })

    it('should have proper ARIA labels and roles', () => {
      loginPage.visit()
      
      cy.get('[data-cy=login-form]').should('have.attr', 'role', 'form')
      cy.get('[data-cy=email-input]').should('have.attr', 'aria-label')
      cy.get('[data-cy=password-input]').should('have.attr', 'aria-label')
      cy.get('[data-cy=login-button]').should('have.attr', 'aria-label')
    })
  })
})
