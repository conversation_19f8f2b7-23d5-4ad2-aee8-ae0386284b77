// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Import code coverage support
import '@cypress/code-coverage/support'

// Import cypress-real-events for better event simulation
import 'cypress-real-events'

// Global configuration
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents <PERSON><PERSON> from failing the test
  // on uncaught exceptions. Useful for third-party code.
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false
  }
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false
  }
  return true
})

// Global before hook
beforeEach(() => {
  // Clear local storage and session storage before each test
  cy.clearLocalStorage()
  cy.clearCookies()
  
  // Set viewport to ensure consistent testing
  cy.viewport(1280, 720)
  
  // Intercept common API calls
  cy.intercept('GET', '/api/health', { statusCode: 200, body: { status: 'ok' } }).as('healthCheck')
})

// Global after hook
afterEach(() => {
  // Clean up after each test
  cy.clearLocalStorage()
  cy.clearCookies()
})

// Custom assertions
declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to check if element is visible and contains text
       * @example cy.shouldBeVisibleAndContain('[data-cy=button]', 'Click me')
       */
      shouldBeVisibleAndContain(selector: string, text: string): Chainable<Element>
      
      /**
       * Custom command to wait for loading to finish
       * @example cy.waitForLoading()
       */
      waitForLoading(): Chainable<Element>
      
      /**
       * Custom command to check if page is fully loaded
       * @example cy.waitForPageLoad()
       */
      waitForPageLoad(): Chainable<Element>
    }
  }
}

// Implement custom commands
Cypress.Commands.add('shouldBeVisibleAndContain', (selector: string, text: string) => {
  cy.get(selector).should('be.visible').and('contain.text', text)
})

Cypress.Commands.add('waitForLoading', () => {
  // Wait for loading spinners to disappear
  cy.get('[data-cy=loading]', { timeout: 10000 }).should('not.exist')
  cy.get('[data-cy=spinner]', { timeout: 10000 }).should('not.exist')
  cy.get('.loading', { timeout: 10000 }).should('not.exist')
})

Cypress.Commands.add('waitForPageLoad', () => {
  // Wait for page to be fully loaded
  cy.document().should('have.property', 'readyState', 'complete')
  cy.waitForLoading()
})
