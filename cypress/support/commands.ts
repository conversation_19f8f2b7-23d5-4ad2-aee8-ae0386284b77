// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

/// <reference types="cypress" />

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to login with JWT token
       * @example cy.loginWithJWT('<EMAIL>', 'password')
       */
      loginWithJWT(email: string, password: string): Chainable<Element>
      
      /**
       * Custom command to login via API and set token
       * @example cy.loginViaAPI('<EMAIL>', 'password')
       */
      loginViaAPI(email: string, password: string): Chainable<Element>
      
      /**
       * Custom command to logout
       * @example cy.logout()
       */
      logout(): Chainable<Element>
      
      /**
       * Custom command to create a test user
       * @example cy.createTestUser({ email: '<EMAIL>', password: 'password' })
       */
      createTestUser(userData: { email: string; password: string; name?: string }): Chainable<Element>
      
      /**
       * Custom command to seed database with test data
       * @example cy.seedDatabase()
       */
      seedDatabase(): Chainable<Element>
      
      /**
       * Custom command to clean database
       * @example cy.cleanDatabase()
       */
      cleanDatabase(): Chainable<Element>
      
      /**
       * Custom command to create test transaction
       * @example cy.createTestTransaction({ amount: 100, description: 'Test transaction' })
       */
      createTestTransaction(transactionData: any): Chainable<Element>
      
      /**
       * Custom command to navigate to page and wait for load
       * @example cy.visitAndWait('/dashboard')
       */
      visitAndWait(url: string): Chainable<Element>
      
      /**
       * Custom command to check API response
       * @example cy.checkAPIResponse('@apiCall', 200)
       */
      checkAPIResponse(alias: string, expectedStatus: number): Chainable<Element>
      
      /**
       * Custom command to fill form with data
       * @example cy.fillForm({ '[data-cy=email]': '<EMAIL>' })
       */
      fillForm(formData: Record<string, string>): Chainable<Element>
    }
  }
}

// Login with JWT token
Cypress.Commands.add('loginWithJWT', (email: string, password: string) => {
  cy.visit('/login')
  cy.get('[data-cy=email-input]').type(email)
  cy.get('[data-cy=password-input]').type(password)
  cy.get('[data-cy=login-button]').click()
  
  // Wait for successful login and token storage
  cy.window().its('localStorage').invoke('getItem', 'auth-token').should('exist')
  cy.url().should('not.include', '/login')
})

// Login via API and set token in localStorage
Cypress.Commands.add('loginViaAPI', (email: string, password: string) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiUrl')}/auth/login`,
    body: {
      email,
      password
    }
  }).then((response) => {
    expect(response.status).to.eq(200)
    expect(response.body).to.have.property('token')
    
    // Store token in localStorage
    window.localStorage.setItem('auth-token', response.body.token)
    window.localStorage.setItem('user', JSON.stringify(response.body.user))
  })
})

// Logout command
Cypress.Commands.add('logout', () => {
  cy.get('[data-cy=user-menu]').click()
  cy.get('[data-cy=logout-button]').click()
  
  // Verify logout
  cy.window().its('localStorage').invoke('getItem', 'auth-token').should('not.exist')
  cy.url().should('include', '/login')
})

// Create test user
Cypress.Commands.add('createTestUser', (userData) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiUrl')}/auth/register`,
    body: {
      email: userData.email,
      password: userData.password,
      name: userData.name || 'Test User'
    }
  }).then((response) => {
    expect(response.status).to.eq(201)
    return cy.wrap(response.body)
  })
})

// Seed database with test data
Cypress.Commands.add('seedDatabase', () => {
  cy.task('db:seed')
})

// Clean database
Cypress.Commands.add('cleanDatabase', () => {
  cy.task('db:clean')
})

// Create test transaction
Cypress.Commands.add('createTestTransaction', (transactionData) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiUrl')}/transactions`,
    headers: {
      'Authorization': `Bearer ${window.localStorage.getItem('auth-token')}`
    },
    body: transactionData
  }).then((response) => {
    expect(response.status).to.eq(201)
    return cy.wrap(response.body)
  })
})

// Visit page and wait for load
Cypress.Commands.add('visitAndWait', (url: string) => {
  cy.visit(url)
  cy.waitForPageLoad()
})

// Check API response
Cypress.Commands.add('checkAPIResponse', (alias: string, expectedStatus: number) => {
  cy.wait(alias).then((interception) => {
    expect(interception.response?.statusCode).to.eq(expectedStatus)
  })
})

// Fill form with data
Cypress.Commands.add('fillForm', (formData: Record<string, string>) => {
  Object.entries(formData).forEach(([selector, value]) => {
    cy.get(selector).clear().type(value)
  })
})
