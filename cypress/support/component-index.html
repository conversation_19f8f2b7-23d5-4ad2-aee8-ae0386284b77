<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Personal Finance Manager - Component Tests</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Configure Tailwind for deepblue theme -->
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              deepblue: {
                50: '#eff6ff',
                100: '#dbeafe',
                200: '#bfdbfe',
                300: '#93c5fd',
                400: '#60a5fa',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a',
                950: '#172554'
              }
            }
          }
        },
        darkMode: 'class'
      }
    </script>
    
    <!-- Custom CSS for component testing -->
    <style>
      body {
        margin: 0;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #0f172a;
        color: #f8fafc;
      }
      
      #root {
        min-height: 100vh;
      }
      
      /* Ensure components are visible during testing */
      .cypress-component-wrapper {
        padding: 16px;
        background-color: #1e293b;
        border-radius: 8px;
        min-height: 200px;
      }
      
      /* Custom scrollbar for dark theme */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #334155;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #64748b;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }
    </style>
  </head>
  
  <body class="dark">
    <div id="root" class="cypress-component-wrapper"></div>
  </body>
</html>
