export class DashboardPage {
  // Selectors
  private selectors = {
    // Header and navigation
    header: '[data-cy=dashboard-header]',
    userMenu: '[data-cy=user-menu]',
    logoutButton: '[data-cy=logout-button]',
    settingsButton: '[data-cy=settings-button]',
    
    // Sidebar navigation
    sidebar: '[data-cy=sidebar]',
    dashboardLink: '[data-cy=nav-dashboard]',
    transactionsLink: '[data-cy=nav-transactions]',
    accountsLink: '[data-cy=nav-accounts]',
    budgetLink: '[data-cy=nav-budget]',
    reportsLink: '[data-cy=nav-reports]',
    settingsLink: '[data-cy=nav-settings]',
    
    // Dashboard content
    welcomeMessage: '[data-cy=welcome-message]',
    balanceCard: '[data-cy=balance-card]',
    totalBalance: '[data-cy=total-balance]',
    monthlyIncome: '[data-cy=monthly-income]',
    monthlyExpenses: '[data-cy=monthly-expenses]',
    
    // Quick actions
    quickActions: '[data-cy=quick-actions]',
    addTransactionButton: '[data-cy=add-transaction-btn]',
    addAccountButton: '[data-cy=add-account-btn]',
    
    // Recent transactions
    recentTransactions: '[data-cy=recent-transactions]',
    transactionItem: '[data-cy=transaction-item]',
    viewAllTransactionsLink: '[data-cy=view-all-transactions]',
    
    // Charts and analytics
    expenseChart: '[data-cy=expense-chart]',
    incomeChart: '[data-cy=income-chart]',
    categoryChart: '[data-cy=category-chart]',
    
    // Loading states
    loadingSpinner: '[data-cy=loading-spinner]',
    skeletonLoader: '[data-cy=skeleton-loader]',
    
    // Error states
    errorMessage: '[data-cy=error-message]',
    retryButton: '[data-cy=retry-button]',
    
    // Filters and date range
    dateRangeSelector: '[data-cy=date-range-selector]',
    filterButton: '[data-cy=filter-button]',
    
    // Mobile menu
    mobileMenuButton: '[data-cy=mobile-menu-button]',
    mobileMenu: '[data-cy=mobile-menu]'
  }

  // Navigation
  visit() {
    cy.visit('/dashboard')
    cy.waitForPageLoad()
    return this
  }

  // Header interactions
  openUserMenu() {
    cy.get(this.selectors.userMenu).click()
    return this
  }

  logout() {
    this.openUserMenu()
    cy.get(this.selectors.logoutButton).click()
    return this
  }

  goToSettings() {
    this.openUserMenu()
    cy.get(this.selectors.settingsButton).click()
    return this
  }

  // Sidebar navigation
  navigateToTransactions() {
    cy.get(this.selectors.transactionsLink).click()
    return this
  }

  navigateToAccounts() {
    cy.get(this.selectors.accountsLink).click()
    return this
  }

  navigateToBudget() {
    cy.get(this.selectors.budgetLink).click()
    return this
  }

  navigateToReports() {
    cy.get(this.selectors.reportsLink).click()
    return this
  }

  navigateToSettingsFromSidebar() {
    cy.get(this.selectors.settingsLink).click()
    return this
  }

  // Quick actions
  addTransaction() {
    cy.get(this.selectors.addTransactionButton).click()
    return this
  }

  addAccount() {
    cy.get(this.selectors.addAccountButton).click()
    return this
  }

  viewAllTransactions() {
    cy.get(this.selectors.viewAllTransactionsLink).click()
    return this
  }

  // Mobile interactions
  openMobileMenu() {
    cy.get(this.selectors.mobileMenuButton).click()
    return this
  }

  closeMobileMenu() {
    cy.get(this.selectors.mobileMenu).should('be.visible')
    cy.get('body').click(0, 0) // Click outside to close
    return this
  }

  // Validations and assertions
  shouldBeOnDashboard() {
    cy.url().should('include', '/dashboard')
    cy.get(this.selectors.header).should('be.visible')
    return this
  }

  shouldShowWelcomeMessage(userName?: string) {
    cy.get(this.selectors.welcomeMessage).should('be.visible')
    if (userName) {
      cy.get(this.selectors.welcomeMessage).should('contain.text', userName)
    }
    return this
  }

  shouldShowBalanceCard() {
    cy.get(this.selectors.balanceCard).should('be.visible')
    cy.get(this.selectors.totalBalance).should('be.visible')
    return this
  }

  shouldShowFinancialSummary() {
    cy.get(this.selectors.monthlyIncome).should('be.visible')
    cy.get(this.selectors.monthlyExpenses).should('be.visible')
    return this
  }

  shouldShowRecentTransactions() {
    cy.get(this.selectors.recentTransactions).should('be.visible')
    return this
  }

  shouldHaveTransactionItems() {
    cy.get(this.selectors.transactionItem).should('have.length.greaterThan', 0)
    return this
  }

  shouldShowCharts() {
    cy.get(this.selectors.expenseChart).should('be.visible')
    cy.get(this.selectors.incomeChart).should('be.visible')
    return this
  }

  shouldShowQuickActions() {
    cy.get(this.selectors.quickActions).should('be.visible')
    cy.get(this.selectors.addTransactionButton).should('be.visible')
    cy.get(this.selectors.addAccountButton).should('be.visible')
    return this
  }

  shouldShowLoadingState() {
    cy.get(this.selectors.loadingSpinner).should('be.visible')
    return this
  }

  shouldHideLoadingState() {
    cy.get(this.selectors.loadingSpinner).should('not.exist')
    cy.get(this.selectors.skeletonLoader).should('not.exist')
    return this
  }

  shouldShowErrorMessage(message?: string) {
    cy.get(this.selectors.errorMessage).should('be.visible')
    if (message) {
      cy.get(this.selectors.errorMessage).should('contain.text', message)
    }
    return this
  }

  // Data verification
  verifyBalance(expectedBalance: string) {
    cy.get(this.selectors.totalBalance).should('contain.text', expectedBalance)
    return this
  }

  verifyMonthlyIncome(expectedIncome: string) {
    cy.get(this.selectors.monthlyIncome).should('contain.text', expectedIncome)
    return this
  }

  verifyMonthlyExpenses(expectedExpenses: string) {
    cy.get(this.selectors.monthlyExpenses).should('contain.text', expectedExpenses)
    return this
  }

  // API interactions
  interceptDashboardData() {
    cy.intercept('GET', '**/dashboard/summary').as('dashboardSummary')
    cy.intercept('GET', '**/transactions/recent').as('recentTransactions')
    cy.intercept('GET', '**/accounts/balance').as('accountBalance')
    return this
  }

  waitForDashboardData() {
    cy.wait('@dashboardSummary')
    cy.wait('@recentTransactions')
    cy.wait('@accountBalance')
    return this
  }

  // Utility methods
  waitForChartsToLoad() {
    cy.get(this.selectors.expenseChart).should('be.visible')
    cy.get(this.selectors.incomeChart).should('be.visible')
    // Wait for charts to render
    cy.wait(1000)
    return this
  }

  refreshDashboard() {
    cy.reload()
    cy.waitForPageLoad()
    return this
  }
}
