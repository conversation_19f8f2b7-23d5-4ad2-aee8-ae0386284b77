export class LoginPage {
  // Selectors
  private selectors = {
    emailInput: '[data-cy=email-input]',
    passwordInput: '[data-cy=password-input]',
    loginButton: '[data-cy=login-button]',
    registerLink: '[data-cy=register-link]',
    forgotPasswordLink: '[data-cy=forgot-password-link]',
    errorMessage: '[data-cy=error-message]',
    successMessage: '[data-cy=success-message]',
    loadingSpinner: '[data-cy=loading-spinner]',
    
    // Form validation
    emailError: '[data-cy=email-error]',
    passwordError: '[data-cy=password-error]',
    
    // Social login (if implemented)
    googleLoginButton: '[data-cy=google-login]',
    
    // Remember me
    rememberMeCheckbox: '[data-cy=remember-me]',
    
    // Page elements
    pageTitle: '[data-cy=page-title]',
    loginForm: '[data-cy=login-form]'
  }

  // Navigation
  visit() {
    cy.visit('/login')
    cy.waitForPageLoad()
    return this
  }

  // Form interactions
  fillEmail(email: string) {
    cy.get(this.selectors.emailInput).clear().type(email)
    return this
  }

  fillPassword(password: string) {
    cy.get(this.selectors.passwordInput).clear().type(password)
    return this
  }

  clickLogin() {
    cy.get(this.selectors.loginButton).click()
    return this
  }

  clickRegister() {
    cy.get(this.selectors.registerLink).click()
    return this
  }

  clickForgotPassword() {
    cy.get(this.selectors.forgotPasswordLink).click()
    return this
  }

  toggleRememberMe() {
    cy.get(this.selectors.rememberMeCheckbox).click()
    return this
  }

  // Complete login flow
  login(email: string, password: string, rememberMe = false) {
    this.fillEmail(email)
    this.fillPassword(password)
    
    if (rememberMe) {
      this.toggleRememberMe()
    }
    
    this.clickLogin()
    return this
  }

  // Validations and assertions
  shouldShowEmailError(message?: string) {
    cy.get(this.selectors.emailError).should('be.visible')
    if (message) {
      cy.get(this.selectors.emailError).should('contain.text', message)
    }
    return this
  }

  shouldShowPasswordError(message?: string) {
    cy.get(this.selectors.passwordError).should('be.visible')
    if (message) {
      cy.get(this.selectors.passwordError).should('contain.text', message)
    }
    return this
  }

  shouldShowErrorMessage(message?: string) {
    cy.get(this.selectors.errorMessage).should('be.visible')
    if (message) {
      cy.get(this.selectors.errorMessage).should('contain.text', message)
    }
    return this
  }

  shouldShowSuccessMessage(message?: string) {
    cy.get(this.selectors.successMessage).should('be.visible')
    if (message) {
      cy.get(this.selectors.successMessage).should('contain.text', message)
    }
    return this
  }

  shouldShowLoadingSpinner() {
    cy.get(this.selectors.loadingSpinner).should('be.visible')
    return this
  }

  shouldHideLoadingSpinner() {
    cy.get(this.selectors.loadingSpinner).should('not.exist')
    return this
  }

  shouldBeOnLoginPage() {
    cy.url().should('include', '/login')
    cy.get(this.selectors.pageTitle).should('be.visible')
    cy.get(this.selectors.loginForm).should('be.visible')
    return this
  }

  shouldRedirectToDashboard() {
    cy.url().should('not.include', '/login')
    cy.url().should('include', '/dashboard')
    return this
  }

  shouldHaveValidForm() {
    cy.get(this.selectors.emailInput).should('be.visible')
    cy.get(this.selectors.passwordInput).should('be.visible')
    cy.get(this.selectors.loginButton).should('be.visible')
    return this
  }

  // Utility methods
  waitForFormSubmission() {
    cy.get(this.selectors.loginButton).should('not.be.disabled')
    return this
  }

  clearForm() {
    cy.get(this.selectors.emailInput).clear()
    cy.get(this.selectors.passwordInput).clear()
    return this
  }

  // API interactions
  interceptLoginRequest() {
    cy.intercept('POST', '**/auth/login').as('loginRequest')
    return this
  }

  waitForLoginRequest() {
    cy.wait('@loginRequest')
    return this
  }

  verifyLoginRequestPayload(email: string, password: string) {
    cy.wait('@loginRequest').then((interception) => {
      expect(interception.request.body).to.deep.include({
        email,
        password
      })
    })
    return this
  }
}
