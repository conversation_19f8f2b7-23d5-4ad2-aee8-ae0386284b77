export class TransactionsPage {
  // Selectors
  private selectors = {
    // Page elements
    pageTitle: '[data-cy=transactions-title]',
    transactionsList: '[data-cy=transactions-list]',
    transactionItem: '[data-cy=transaction-item]',
    emptyState: '[data-cy=empty-transactions]',
    
    // Add transaction
    addTransactionButton: '[data-cy=add-transaction-btn]',
    transactionModal: '[data-cy=transaction-modal]',
    transactionForm: '[data-cy=transaction-form]',
    
    // Form fields
    amountInput: '[data-cy=amount-input]',
    descriptionInput: '[data-cy=description-input]',
    categorySelect: '[data-cy=category-select]',
    typeSelect: '[data-cy=type-select]',
    dateInput: '[data-cy=date-input]',
    accountSelect: '[data-cy=account-select]',
    
    // Form buttons
    saveButton: '[data-cy=save-transaction]',
    cancelButton: '[data-cy=cancel-transaction]',
    deleteButton: '[data-cy=delete-transaction]',
    
    // Filters and search
    searchInput: '[data-cy=search-transactions]',
    filterButton: '[data-cy=filter-button]',
    filterModal: '[data-cy=filter-modal]',
    dateRangeFilter: '[data-cy=date-range-filter]',
    categoryFilter: '[data-cy=category-filter]',
    typeFilter: '[data-cy=type-filter]',
    amountRangeFilter: '[data-cy=amount-range-filter]',
    applyFiltersButton: '[data-cy=apply-filters]',
    clearFiltersButton: '[data-cy=clear-filters]',
    
    // Sorting
    sortButton: '[data-cy=sort-button]',
    sortByDate: '[data-cy=sort-by-date]',
    sortByAmount: '[data-cy=sort-by-amount]',
    sortByCategory: '[data-cy=sort-by-category]',
    
    // Pagination
    pagination: '[data-cy=pagination]',
    nextPageButton: '[data-cy=next-page]',
    prevPageButton: '[data-cy=prev-page]',
    pageInfo: '[data-cy=page-info]',
    
    // Transaction actions
    editTransactionButton: '[data-cy=edit-transaction]',
    deleteTransactionButton: '[data-cy=delete-transaction]',
    duplicateTransactionButton: '[data-cy=duplicate-transaction]',
    
    // Bulk actions
    selectAllCheckbox: '[data-cy=select-all-transactions]',
    transactionCheckbox: '[data-cy=transaction-checkbox]',
    bulkActionsBar: '[data-cy=bulk-actions-bar]',
    bulkDeleteButton: '[data-cy=bulk-delete]',
    bulkCategoryButton: '[data-cy=bulk-category]',
    
    // Loading and error states
    loadingSpinner: '[data-cy=loading-spinner]',
    errorMessage: '[data-cy=error-message]',
    successMessage: '[data-cy=success-message]',
    
    // Export/Import
    exportButton: '[data-cy=export-transactions]',
    importButton: '[data-cy=import-transactions]',
    
    // Summary
    totalAmount: '[data-cy=total-amount]',
    transactionCount: '[data-cy=transaction-count]'
  }

  // Navigation
  visit() {
    cy.visit('/transactions')
    cy.waitForPageLoad()
    return this
  }

  // Add transaction flow
  openAddTransactionModal() {
    cy.get(this.selectors.addTransactionButton).click()
    cy.get(this.selectors.transactionModal).should('be.visible')
    return this
  }

  fillTransactionForm(transaction: {
    amount: string
    description: string
    category?: string
    type?: 'income' | 'expense'
    date?: string
    account?: string
  }) {
    cy.get(this.selectors.amountInput).clear().type(transaction.amount)
    cy.get(this.selectors.descriptionInput).clear().type(transaction.description)
    
    if (transaction.category) {
      cy.get(this.selectors.categorySelect).select(transaction.category)
    }
    
    if (transaction.type) {
      cy.get(this.selectors.typeSelect).select(transaction.type)
    }
    
    if (transaction.date) {
      cy.get(this.selectors.dateInput).clear().type(transaction.date)
    }
    
    if (transaction.account) {
      cy.get(this.selectors.accountSelect).select(transaction.account)
    }
    
    return this
  }

  saveTransaction() {
    cy.get(this.selectors.saveButton).click()
    return this
  }

  cancelTransaction() {
    cy.get(this.selectors.cancelButton).click()
    return this
  }

  addTransaction(transaction: {
    amount: string
    description: string
    category?: string
    type?: 'income' | 'expense'
    date?: string
    account?: string
  }) {
    this.openAddTransactionModal()
    this.fillTransactionForm(transaction)
    this.saveTransaction()
    return this
  }

  // Edit transaction
  editTransaction(index: number, updates: Partial<{
    amount: string
    description: string
    category: string
    type: 'income' | 'expense'
    date: string
    account: string
  }>) {
    cy.get(this.selectors.transactionItem).eq(index).within(() => {
      cy.get(this.selectors.editTransactionButton).click()
    })
    
    cy.get(this.selectors.transactionModal).should('be.visible')
    
    Object.entries(updates).forEach(([field, value]) => {
      if (field === 'amount') {
        cy.get(this.selectors.amountInput).clear().type(value)
      } else if (field === 'description') {
        cy.get(this.selectors.descriptionInput).clear().type(value)
      } else if (field === 'category') {
        cy.get(this.selectors.categorySelect).select(value)
      } else if (field === 'type') {
        cy.get(this.selectors.typeSelect).select(value)
      } else if (field === 'date') {
        cy.get(this.selectors.dateInput).clear().type(value)
      } else if (field === 'account') {
        cy.get(this.selectors.accountSelect).select(value)
      }
    })
    
    this.saveTransaction()
    return this
  }

  // Delete transaction
  deleteTransaction(index: number) {
    cy.get(this.selectors.transactionItem).eq(index).within(() => {
      cy.get(this.selectors.deleteTransactionButton).click()
    })
    
    // Confirm deletion if there's a confirmation dialog
    cy.get('body').then(($body) => {
      if ($body.find('[data-cy=confirm-delete]').length > 0) {
        cy.get('[data-cy=confirm-delete]').click()
      }
    })
    
    return this
  }

  // Search and filter
  searchTransactions(query: string) {
    cy.get(this.selectors.searchInput).clear().type(query)
    return this
  }

  openFilterModal() {
    cy.get(this.selectors.filterButton).click()
    cy.get(this.selectors.filterModal).should('be.visible')
    return this
  }

  applyFilters(filters: {
    dateRange?: { from: string; to: string }
    category?: string
    type?: 'income' | 'expense'
    amountRange?: { min: string; max: string }
  }) {
    this.openFilterModal()
    
    if (filters.dateRange) {
      cy.get(this.selectors.dateRangeFilter).within(() => {
        cy.get('[data-cy=date-from]').clear().type(filters.dateRange!.from)
        cy.get('[data-cy=date-to]').clear().type(filters.dateRange!.to)
      })
    }
    
    if (filters.category) {
      cy.get(this.selectors.categoryFilter).select(filters.category)
    }
    
    if (filters.type) {
      cy.get(this.selectors.typeFilter).select(filters.type)
    }
    
    if (filters.amountRange) {
      cy.get(this.selectors.amountRangeFilter).within(() => {
        cy.get('[data-cy=amount-min]').clear().type(filters.amountRange!.min)
        cy.get('[data-cy=amount-max]').clear().type(filters.amountRange!.max)
      })
    }
    
    cy.get(this.selectors.applyFiltersButton).click()
    return this
  }

  clearFilters() {
    cy.get(this.selectors.clearFiltersButton).click()
    return this
  }

  // Sorting
  sortByDate(order: 'asc' | 'desc' = 'desc') {
    cy.get(this.selectors.sortButton).click()
    cy.get(this.selectors.sortByDate).click()
    
    if (order === 'asc') {
      cy.get(this.selectors.sortByDate).click() // Click again for ascending
    }
    
    return this
  }

  sortByAmount(order: 'asc' | 'desc' = 'desc') {
    cy.get(this.selectors.sortButton).click()
    cy.get(this.selectors.sortByAmount).click()
    
    if (order === 'asc') {
      cy.get(this.selectors.sortByAmount).click()
    }
    
    return this
  }

  // Bulk actions
  selectAllTransactions() {
    cy.get(this.selectors.selectAllCheckbox).click()
    return this
  }

  selectTransaction(index: number) {
    cy.get(this.selectors.transactionItem).eq(index).within(() => {
      cy.get(this.selectors.transactionCheckbox).click()
    })
    return this
  }

  bulkDeleteSelected() {
    cy.get(this.selectors.bulkDeleteButton).click()
    
    // Confirm deletion
    cy.get('body').then(($body) => {
      if ($body.find('[data-cy=confirm-bulk-delete]').length > 0) {
        cy.get('[data-cy=confirm-bulk-delete]').click()
      }
    })
    
    return this
  }

  // Validations
  shouldShowTransactions() {
    cy.get(this.selectors.transactionsList).should('be.visible')
    cy.get(this.selectors.transactionItem).should('have.length.greaterThan', 0)
    return this
  }

  shouldShowEmptyState() {
    cy.get(this.selectors.emptyState).should('be.visible')
    return this
  }

  shouldHaveTransactionCount(count: number) {
    cy.get(this.selectors.transactionItem).should('have.length', count)
    return this
  }

  shouldShowSuccessMessage(message?: string) {
    cy.get(this.selectors.successMessage).should('be.visible')
    if (message) {
      cy.get(this.selectors.successMessage).should('contain.text', message)
    }
    return this
  }

  shouldShowErrorMessage(message?: string) {
    cy.get(this.selectors.errorMessage).should('be.visible')
    if (message) {
      cy.get(this.selectors.errorMessage).should('contain.text', message)
    }
    return this
  }

  // API interactions
  interceptTransactionsAPI() {
    cy.intercept('GET', '**/transactions').as('getTransactions')
    cy.intercept('POST', '**/transactions').as('createTransaction')
    cy.intercept('PUT', '**/transactions/*').as('updateTransaction')
    cy.intercept('DELETE', '**/transactions/*').as('deleteTransaction')
    return this
  }

  waitForTransactionsLoad() {
    cy.wait('@getTransactions')
    return this
  }

  waitForTransactionCreate() {
    cy.wait('@createTransaction')
    return this
  }

  waitForTransactionUpdate() {
    cy.wait('@updateTransaction')
    return this
  }

  waitForTransactionDelete() {
    cy.wait('@deleteTransaction')
    return this
  }
}
