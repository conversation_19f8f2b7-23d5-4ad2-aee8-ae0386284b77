// ***********************************************************
// This example support/component.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Import code coverage support
import '@cypress/code-coverage/support'

// Import React and mount function
import { mount } from 'cypress/react'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'

// Import global styles
import '../../frontend/src/index.css'

// Create a new QueryClient for each test
const createQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
})

// Custom mount command that wraps components with necessary providers
Cypress.Commands.add('mount', (component, options = {}) => {
  const { routerProps = { initialEntries: ['/'] }, queryClient = createQueryClient(), ...mountOptions } = options

  const wrapped = (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter {...routerProps}>
        {component}
        <Toaster position="top-right" />
      </BrowserRouter>
    </QueryClientProvider>
  )

  return mount(wrapped, mountOptions)
})

// Global configuration for component testing
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents Cypress from failing the test
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false
  }
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false
  }
  return true
})

// Global before hook for component tests
beforeEach(() => {
  // Mock window.matchMedia for responsive components
  cy.window().then((win) => {
    Object.defineProperty(win, 'matchMedia', {
      writable: true,
      value: cy.stub().returns({
        matches: false,
        media: '',
        onchange: null,
        addListener: cy.stub(),
        removeListener: cy.stub(),
        addEventListener: cy.stub(),
        removeEventListener: cy.stub(),
        dispatchEvent: cy.stub(),
      }),
    })
  })
})

// Extend Cypress types for component testing
declare global {
  namespace Cypress {
    interface Chainable {
      mount: typeof mount
    }
  }
}
