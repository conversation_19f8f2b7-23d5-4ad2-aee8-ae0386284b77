{"sampleTransactions": [{"id": 1, "amount": 1500.0, "description": "Salary", "category": "Income", "type": "income", "date": "2024-01-01", "account": "Checking Account", "tags": ["salary", "work"], "recurring": false}, {"id": 2, "amount": -50.0, "description": "Grocery Shopping", "category": "Food & Dining", "type": "expense", "date": "2024-01-02", "account": "Credit Card", "tags": ["groceries", "food"], "recurring": false}, {"id": 3, "amount": -800.0, "description": "Rent Payment", "category": "Housing", "type": "expense", "date": "2024-01-01", "account": "Checking Account", "tags": ["rent", "housing"], "recurring": true, "recurringPattern": "monthly"}, {"id": 4, "amount": -25.99, "description": "Netflix Subscription", "category": "Entertainment", "type": "expense", "date": "2024-01-05", "account": "Credit Card", "tags": ["subscription", "entertainment"], "recurring": true, "recurringPattern": "monthly"}, {"id": 5, "amount": 200.0, "description": "Freelance Work", "category": "Income", "type": "income", "date": "2024-01-10", "account": "Checking Account", "tags": ["freelance", "work"], "recurring": false}], "newTransaction": {"amount": 75.5, "description": "Gas Station", "category": "Transportation", "type": "expense", "date": "2024-01-15", "account": "Credit Card", "tags": ["gas", "transportation"]}, "incomeTransaction": {"amount": 500.0, "description": "Bonus Payment", "category": "Income", "type": "income", "date": "2024-01-20", "account": "Checking Account", "tags": ["bonus", "work"]}, "expenseTransaction": {"amount": -120.0, "description": "Utility Bill", "category": "Bills & Utilities", "type": "expense", "date": "2024-01-25", "account": "Checking Account", "tags": ["utilities", "bills"]}, "invalidTransactions": {"negativeIncome": {"amount": -100.0, "description": "Invalid Income", "category": "Income", "type": "income", "date": "2024-01-01", "account": "Checking Account"}, "positiveExpense": {"amount": 100.0, "description": "Invalid Expense", "category": "Food & Dining", "type": "expense", "date": "2024-01-01", "account": "Checking Account"}, "emptyDescription": {"amount": 50.0, "description": "", "category": "Miscellaneous", "type": "expense", "date": "2024-01-01", "account": "Checking Account"}, "invalidDate": {"amount": 50.0, "description": "Invalid Date Transaction", "category": "Miscellaneous", "type": "expense", "date": "invalid-date", "account": "Checking Account"}}, "bulkTransactions": [{"amount": -30.0, "description": "Coffee Shop", "category": "Food & Dining", "type": "expense", "date": "2024-01-01", "account": "Credit Card"}, {"amount": -15.0, "description": "Parking Fee", "category": "Transportation", "type": "expense", "date": "2024-01-01", "account": "Credit Card"}, {"amount": -45.0, "description": "Lunch", "category": "Food & Dining", "type": "expense", "date": "2024-01-01", "account": "Credit Card"}], "categories": ["Income", "Food & Dining", "Transportation", "Housing", "Entertainment", "Bills & Utilities", "Healthcare", "Shopping", "Education", "Travel", "Miscellaneous"], "accounts": ["Checking Account", "Savings Account", "Credit Card", "Cash"], "transactionTypes": ["income", "expense"]}