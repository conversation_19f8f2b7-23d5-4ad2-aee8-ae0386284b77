# Cypress E2E Testing Framework

Este diretório contém a configuração e testes E2E (End-to-End) para o Personal Finance Manager usando <PERSON>.

## 📁 Estrutura de Arquivos

```
cypress/
├── e2e/                    # Testes E2E
│   ├── auth/              # Testes de autenticação
│   ├── transactions/      # Testes de transações
│   ├── dashboard/         # Testes do dashboard
│   └── settings/          # Testes de configurações
├── support/               # Arquivos de suporte
│   ├── page-objects/      # Page Object Models
│   ├── commands.ts        # Comandos customizados
│   ├── e2e.ts            # Configuração E2E
│   ├── component.ts       # Configuração de componentes
│   └── component-index.html
├── fixtures/              # Dados de teste
│   ├── users.json        # Dados de usuários
│   └── transactions.json # Dados de transações
├── downloads/             # Downloads dos testes
├── screenshots/           # Screenshots dos testes
└── videos/               # Vídeos dos testes
```

## 🚀 Como Executar os Testes

### Pré-requisitos

1. Certifique-se de que o backend está rodando na porta 3001
2. Certifique-se de que o frontend está rodando na porta 5173
3. Banco de dados PostgreSQL configurado

### Comandos Disponíveis

```bash
# Executar todos os testes E2E (headless)
npm run test:e2e

# Abrir interface do Cypress
npm run test:e2e:open

# Executar testes com interface gráfica
npm run test:e2e:headed

# Executar testes no Chrome
npm run test:e2e:chrome

# Executar testes no Firefox
npm run test:e2e:firefox

# Executar testes de componentes
npm run test:component

# Abrir interface de testes de componentes
npm run test:component:open

# Executar todos os tipos de teste
npm run test:all
```

### Executar Testes Específicos

```bash
# Executar apenas testes de autenticação
npx cypress run --spec "cypress/e2e/auth/**/*"

# Executar um teste específico
npx cypress run --spec "cypress/e2e/auth/login.cy.ts"

# Executar testes com tag específica
npx cypress run --env grepTags="@smoke"
```

## 🧪 Tipos de Teste

### 1. Testes E2E (End-to-End)
- Testam fluxos completos da aplicação
- Simulam interações reais do usuário
- Verificam integração entre frontend e backend

### 2. Testes de Componente
- Testam componentes React isoladamente
- Mais rápidos que testes E2E
- Focam na funcionalidade específica de componentes

## 📋 Page Object Models

Os Page Object Models estão organizados em `cypress/support/page-objects/`:

- **LoginPage**: Interações com a página de login
- **DashboardPage**: Interações com o dashboard
- **TransactionsPage**: Interações com transações
- **SettingsPage**: Interações com configurações

### Exemplo de Uso

```typescript
import { LoginPage } from '../support/page-objects/LoginPage'
import { DashboardPage } from '../support/page-objects/DashboardPage'

describe('Login Flow', () => {
  it('should login successfully', () => {
    const loginPage = new LoginPage()
    const dashboardPage = new DashboardPage()
    
    loginPage
      .visit()
      .login('<EMAIL>', 'password')
      .shouldRedirectToDashboard()
    
    dashboardPage
      .shouldBeOnDashboard()
      .shouldShowWelcomeMessage()
  })
})
```

## 🔧 Comandos Customizados

### Autenticação
```typescript
// Login via interface
cy.loginWithJWT('<EMAIL>', 'password')

// Login via API (mais rápido)
cy.loginViaAPI('<EMAIL>', 'password')

// Logout
cy.logout()
```

### Dados de Teste
```typescript
// Limpar banco de dados
cy.cleanDatabase()

// Popular banco com dados de teste
cy.seedDatabase()

// Criar usuário de teste
cy.createTestUser({ email: '<EMAIL>', password: 'password' })

// Criar transação de teste
cy.createTestTransaction({ amount: 100, description: 'Test' })
```

### Navegação e Espera
```typescript
// Visitar página e aguardar carregamento
cy.visitAndWait('/dashboard')

// Aguardar carregamento completo
cy.waitForPageLoad()

// Aguardar loading desaparecer
cy.waitForLoading()
```

## 📊 Relatórios

Os relatórios são gerados automaticamente em:

- **Mochawesome**: `cypress/reports/mochawesome/report.html`
- **JSON**: `cypress/reports/results.json`
- **Screenshots**: `cypress/screenshots/`
- **Vídeos**: `cypress/videos/`

## 🔍 Debugging

### Modo Debug
```bash
# Executar com debug habilitado
npx cypress run --env DEBUG=true

# Executar com logs detalhados
npx cypress run --env LOG_LEVEL=debug
```

### Screenshots e Vídeos
- Screenshots são capturados automaticamente em falhas
- Vídeos são gravados para todos os testes
- Configurável em `cypress.config.ts`

## 🌐 Ambientes

### Desenvolvimento (Padrão)
- Frontend: http://localhost:5173
- Backend: http://localhost:3001

### Staging
```bash
npx cypress run --env environment=staging
```

### Produção
```bash
npx cypress run --env environment=production
```

## 📝 Convenções de Nomenclatura

### Arquivos de Teste
- Use `.cy.ts` para testes E2E
- Use `.component.cy.ts` para testes de componente
- Organize por funcionalidade em pastas

### Seletores
- Use `data-cy` attributes para seletores
- Evite seletores baseados em classes CSS
- Seja específico e descritivo

```html
<!-- ✅ Bom -->
<button data-cy="login-button">Login</button>

<!-- ❌ Evitar -->
<button class="btn btn-primary">Login</button>
```

### Descrições de Teste
- Use descrições claras e específicas
- Comece com "should" para assertions
- Agrupe testes relacionados com `describe`

```typescript
describe('Login Page', () => {
  it('should display validation errors for empty fields', () => {
    // teste aqui
  })
  
  it('should login successfully with valid credentials', () => {
    // teste aqui
  })
})
```

## 🔒 Boas Práticas

1. **Isolamento**: Cada teste deve ser independente
2. **Limpeza**: Limpe dados entre testes
3. **Espera**: Use comandos de espera apropriados
4. **Seletores**: Use data-cy attributes
5. **Page Objects**: Mantenha lógica de página separada
6. **Fixtures**: Use dados de teste consistentes
7. **API**: Prefira login via API para setup
8. **Assertions**: Seja específico nas verificações

## 🐛 Troubleshooting

### Problemas Comuns

1. **Timeout**: Aumente timeouts em `cypress.config.ts`
2. **Elementos não encontrados**: Verifique seletores data-cy
3. **Testes flaky**: Adicione esperas apropriadas
4. **Banco de dados**: Verifique se está limpo entre testes

### Logs e Debug
```typescript
// Adicionar logs customizados
cy.task('log', 'Debug message')

// Pausar execução para debug
cy.pause()

// Debug de elementos
cy.get('[data-cy=element]').debug()
```

## 📚 Recursos Adicionais

- [Documentação Oficial do Cypress](https://docs.cypress.io/)
- [Best Practices](https://docs.cypress.io/guides/references/best-practices)
- [API Reference](https://docs.cypress.io/api/table-of-contents)
- [Cypress Real World App](https://github.com/cypress-io/cypress-realworld-app)
