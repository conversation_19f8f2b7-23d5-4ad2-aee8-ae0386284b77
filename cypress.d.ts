/// <reference types="cypress" />

declare namespace Cypress {
  interface Chainable {
    /**
     * Custom command to login with JW<PERSON> token
     * @example cy.loginWithJWT('<EMAIL>', 'password')
     */
    loginWithJWT(email: string, password: string): Chainable<Element>
    
    /**
     * Custom command to login via API and set token
     * @example cy.loginViaAPI('<EMAIL>', 'password')
     */
    loginViaAPI(email: string, password: string): Chainable<Element>
    
    /**
     * Custom command to logout
     * @example cy.logout()
     */
    logout(): Chainable<Element>
    
    /**
     * Custom command to create a test user
     * @example cy.createTestUser({ email: '<EMAIL>', password: 'password' })
     */
    createTestUser(userData: { email: string; password: string; name?: string }): Chainable<Element>
    
    /**
     * Custom command to seed database with test data
     * @example cy.seedDatabase()
     */
    seedDatabase(): Chainable<Element>
    
    /**
     * Custom command to clean database
     * @example cy.cleanDatabase()
     */
    cleanDatabase(): Chainable<Element>
    
    /**
     * Custom command to create test transaction
     * @example cy.createTestTransaction({ amount: 100, description: 'Test transaction' })
     */
    createTestTransaction(transactionData: any): Chainable<Element>
    
    /**
     * Custom command to navigate to page and wait for load
     * @example cy.visitAndWait('/dashboard')
     */
    visitAndWait(url: string): Chainable<Element>
    
    /**
     * Custom command to check API response
     * @example cy.checkAPIResponse('@apiCall', 200)
     */
    checkAPIResponse(alias: string, expectedStatus: number): Chainable<Element>
    
    /**
     * Custom command to fill form with data
     * @example cy.fillForm({ '[data-cy=email]': '<EMAIL>' })
     */
    fillForm(formData: Record<string, string>): Chainable<Element>
    
    /**
     * Custom command to check if element is visible and contains text
     * @example cy.shouldBeVisibleAndContain('[data-cy=button]', 'Click me')
     */
    shouldBeVisibleAndContain(selector: string, text: string): Chainable<Element>
    
    /**
     * Custom command to wait for loading to finish
     * @example cy.waitForLoading()
     */
    waitForLoading(): Chainable<Element>
    
    /**
     * Custom command to check if page is fully loaded
     * @example cy.waitForPageLoad()
     */
    waitForPageLoad(): Chainable<Element>
    
    /**
     * Custom command to tab to next element (for accessibility testing)
     * @example cy.tab()
     */
    tab(): Chainable<Element>
  }
  
  interface ApplicationWindow {
    store?: any
    localStorage: Storage
    sessionStorage: Storage
  }
  
  interface ResolvedConfigOptions {
    env: {
      apiUrl: string
      frontendUrl: string
      backendUrl: string
      testUser: {
        email: string
        password: string
        name: string
      }
      jwtSecret: string
      tokenExpiry: string
      environment?: 'development' | 'staging' | 'production'
    }
  }
}

// Global type definitions for test data
declare global {
  interface Window {
    Cypress: any
    cy: any
  }
}

export {}
