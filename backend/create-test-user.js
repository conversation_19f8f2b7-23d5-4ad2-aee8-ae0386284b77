const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('✅ Test user already exists');
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('test123', 10);

    // Create test user
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User',
        isActive: true
      }
    });

    console.log('✅ Test user created:', user.email);

    // Create a test goal
    const goal = await prisma.goal.create({
      data: {
        name: 'Meta de Teste',
        targetAmount: 10000,
        currentAmount: 2500,
        targetDate: new Date('2024-12-31')
      }
    });

    console.log('✅ Test goal created:', goal.name);

    // Create some progress history entries
    await prisma.goalProgressHistory.createMany({
      data: [
        {
          goalId: goal.id,
          previousAmount: 0,
          newAmount: 1000,
          amountChanged: 1000,
          operation: 'add',
          description: 'Primeiro depósito'
        },
        {
          goalId: goal.id,
          previousAmount: 1000,
          newAmount: 2500,
          amountChanged: 1500,
          operation: 'add',
          description: 'Segundo depósito'
        }
      ]
    });

    console.log('✅ Test progress history created');

  } catch (error) {
    console.error('❌ Error creating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
