# Sistema de Sugestão Automática de Categorias

## Visão Geral

O Sistema de Sugestão Automática de Categorias é um módulo inteligente que analisa transações financeiras e sugere categorias apropriadas baseado em dados históricos e algoritmos de similaridade. O sistema utiliza múltiplos critérios para fornecer sugestões precisas e relevantes.

## Arquitetura

### Componentes Principais

1. **Text Similarity Utils** (`utils/text-similarity.utils.ts`)
   - Algoritmos de similaridade de texto (Levenshtein, Jaccard, Cosine)
   - Normalização e tokenização de texto
   - Extração de palavras-chave

2. **Historical Analysis Service** (`services/historical-analysis.service.ts`)
   - Análise de dados históricos de transações
   - Cache de resultados para performance
   - Extração de padrões e frequências

3. **Category Suggestion Service** (`services/category-suggestion.service.ts`)
   - Algoritmos de sugestão multi-critério
   - Combinação de pontuações de diferentes algoritmos
   - Configurações personalizáveis de pesos

4. **Category Suggestion Controller** (`controllers/category-suggestion.controller.ts`)
   - Endpoints REST para sugestões
   - Validação de entrada e tratamento de erros
   - Suporte a processamento em lote

5. **Schemas de Validação** (`schemas/category-suggestion.schemas.ts`)
   - Validação Zod para requests e responses
   - Tipagem TypeScript forte
   - Configurações de algoritmos

## Algoritmos de Sugestão

### 1. Similaridade de Descrição
- **Algoritmo**: Combinação de Levenshtein, Jaccard e Cosine
- **Peso padrão**: 40%
- **Funcionamento**: Compara a descrição da nova transação com padrões históricos

### 2. Análise de Valor
- **Algoritmo**: Proximidade com faixas de valores típicas
- **Peso padrão**: 20%
- **Funcionamento**: Verifica se o valor está dentro das faixas típicas de cada categoria

### 3. Frequência de Uso
- **Algoritmo**: Popularidade das categorias
- **Peso padrão**: 20%
- **Funcionamento**: Prioriza categorias mais utilizadas pelo usuário

### 4. Correspondência de Palavras-chave
- **Algoritmo**: Matching de keywords extraídas
- **Peso padrão**: 20%
- **Funcionamento**: Identifica palavras-chave comuns entre transações

## API Endpoints

### POST /api/v1/category-suggestions
Obter sugestões para uma transação individual.

**Request Body:**
```json
{
  "description": "Compra no Supermercado Extra",
  "amount": 150.50,
  "type": "EXPENSE",
  "maxSuggestions": 5,
  "minConfidence": 0.3
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "suggestions": [
      {
        "categoryId": "cat1",
        "categoryName": "Alimentação",
        "confidence": 0.85,
        "reason": "Descrição similar a transações anteriores",
        "matchType": "combined"
      }
    ],
    "metadata": {
      "totalSuggestions": 1,
      "hasHistoricalData": true,
      "processingTimeMs": 45
    }
  }
}
```

### POST /api/v1/category-suggestions/expense
Sugestões otimizadas para despesas (maior peso na descrição).

### POST /api/v1/category-suggestions/income
Sugestões otimizadas para receitas (maior peso na descrição).

### POST /api/v1/category-suggestions/bulk
Processamento em lote para múltiplas transações.

**Request Body:**
```json
{
  "transactions": [
    {
      "id": "trans1",
      "description": "Compra supermercado",
      "amount": 150,
      "type": "EXPENSE"
    }
  ],
  "maxSuggestionsPerTransaction": 3,
  "minConfidence": 0.3
}
```

### POST /api/v1/category-suggestions/custom
Sugestões com configuração personalizada de algoritmos.

**Request Body:**
```json
{
  "transaction": {
    "description": "Compra no mercado",
    "amount": 100
  },
  "config": {
    "descriptionWeight": 0.6,
    "amountWeight": 0.2,
    "frequencyWeight": 0.1,
    "keywordWeight": 0.1
  }
}
```

### POST /api/v1/category-suggestions/feedback
Enviar feedback sobre a qualidade das sugestões.

## Configuração de Algoritmos

### Pesos Padrão
```typescript
{
  descriptionWeight: 0.4,  // 40%
  amountWeight: 0.2,       // 20%
  frequencyWeight: 0.2,    // 20%
  keywordWeight: 0.2,      // 20%
  minConfidence: 0.3,      // 30%
  maxSuggestions: 5
}
```

### Pesos para Despesas
```typescript
{
  descriptionWeight: 0.5,  // 50%
  amountWeight: 0.3,       // 30%
  frequencyWeight: 0.1,    // 10%
  keywordWeight: 0.1       // 10%
}
```

### Pesos para Receitas
```typescript
{
  descriptionWeight: 0.6,  // 60%
  amountWeight: 0.2,       // 20%
  frequencyWeight: 0.1,    // 10%
  keywordWeight: 0.1       // 10%
}
```

## Performance e Cache

### Sistema de Cache
- **Duração**: 5 minutos
- **Escopo**: Por usuário
- **Dados cachados**: Frequências, padrões, faixas de valores
- **Invalidação**: Automática por tempo ou manual

### Otimizações
- Limite de 1000 transações para análise histórica
- Limite de 500 transações para padrões
- Processamento assíncrono para lotes
- Cache de resultados de análise

## Tratamento de Erros

### Fallback Strategies
1. **Sem dados históricos**: Retorna categorias padrão
2. **Erro de serviço**: Retorna sugestões básicas
3. **Dados malformados**: Filtragem e sanitização
4. **Timeout**: Resposta com dados parciais

### Categorias Padrão
```typescript
[
  { categoryName: 'Alimentação', confidence: 0.5 },
  { categoryName: 'Transporte', confidence: 0.4 },
  { categoryName: 'Utilidades', confidence: 0.3 }
]
```

## Monitoramento e Métricas

### Logs de Sistema
- Tempo de processamento
- Número de sugestões geradas
- Taxa de confiança média
- Erros e exceções

### Métricas de Qualidade
- Taxa de aceitação de sugestões
- Confiança média das sugestões
- Tempo de resposta médio
- Taxa de erro

## Segurança

### Autenticação
- Todos os endpoints requerem token JWT
- Validação de usuário ativo
- Rate limiting aplicado

### Validação de Dados
- Schemas Zod para todas as entradas
- Sanitização de strings
- Validação de tipos e ranges

## Exemplos de Uso

### Transação Brasileira Típica
```javascript
// Input
{
  "description": "Compra Supermercado Carrefour",
  "amount": 145.50,
  "type": "EXPENSE"
}

// Output
{
  "suggestions": [
    {
      "categoryId": "alimentacao",
      "categoryName": "Alimentação",
      "confidence": 0.87,
      "reason": "Descrição similar a 'Compra Supermercado Extra' | Valor na faixa típica (R$ 50-300)",
      "matchType": "combined"
    }
  ]
}
```

### Processamento em Lote
```javascript
// Input: 10 transações
// Tempo médio: 200-500ms
// Taxa de sucesso: >95%
```

## Troubleshooting

### Problemas Comuns

1. **Baixa confiança nas sugestões**
   - Verificar dados históricos suficientes
   - Ajustar pesos dos algoritmos
   - Reduzir threshold de confiança

2. **Performance lenta**
   - Verificar cache funcionando
   - Reduzir limite de transações analisadas
   - Otimizar queries do banco

3. **Sugestões irrelevantes**
   - Revisar normalização de texto
   - Ajustar algoritmos de similaridade
   - Coletar feedback dos usuários

### Comandos de Debug
```bash
# Executar testes
npm test -- category-suggestion

# Verificar coverage
npm test -- --coverage category-suggestion

# Build e verificação
npm run build
```

## Roadmap Futuro

### Melhorias Planejadas
1. **Machine Learning**: Implementar modelos de ML para sugestões
2. **Feedback Loop**: Sistema de aprendizado baseado em feedback
3. **Categorização Automática**: Sugestão automática sem intervenção
4. **Análise Temporal**: Considerar padrões sazonais
5. **Integração com OCR**: Análise de recibos e notas fiscais

### Métricas de Sucesso
- Taxa de aceitação > 80%
- Tempo de resposta < 200ms
- Cobertura de testes > 90%
- Disponibilidade > 99.9%

## Guia de Implementação

### Integração no Frontend

```typescript
// Service para chamar a API
class CategorySuggestionService {
  async getSuggestions(transaction: TransactionData): Promise<CategorySuggestion[]> {
    const response = await fetch('/api/v1/category-suggestions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(transaction)
    });

    const data = await response.json();
    return data.data.suggestions;
  }
}

// Componente React de exemplo
function TransactionForm() {
  const [suggestions, setSuggestions] = useState([]);

  const handleDescriptionChange = async (description: string) => {
    if (description.length > 3) {
      const suggestions = await getSuggestions({ description });
      setSuggestions(suggestions);
    }
  };

  return (
    <div>
      <input onChange={(e) => handleDescriptionChange(e.target.value)} />
      <SuggestionList suggestions={suggestions} />
    </div>
  );
}
```

### Testes de Validação

```bash
# Executar todos os testes do sistema
npm test -- category-suggestion

# Testes específicos
npm test -- text-similarity.test.ts
npm test -- category-suggestion.controller.test.ts
npm test -- category-suggestion.integration.test.ts

# Coverage completo
npm test -- --coverage
```
