# 💸 Módulo de Transferências

## Visão Geral

O módulo de transferências permite realizar transferências de valores entre contas financeiras, com suporte a conversão de moedas, validações de saldo e auditoria completa.

## Funcionalidades

### ✅ Implementadas

- **Transferências básicas** entre contas
- **Conversão de moedas** com taxa manual
- **Validação de saldo** suficiente
- **Validação de limites** de cartão de crédito
- **Auditoria** de operações
- **Soft delete** para histórico
- **Transações atômicas** com Prisma

### 🔄 Em desenvolvimento

- Sistema de conversão automática de moedas
- Histórico de taxas de câmbio
- Transferências programadas

## Schema do Banco de Dados

### Campos Adicionais na Tabela `transactions`

```sql
-- Campos específicos para transferências
source_currency       VARCHAR(3),     -- <PERSON><PERSON> de origem (ex: "BRL")
destination_currency  VARCHAR(3),     -- <PERSON><PERSON> de destino (ex: "USD")
source_amount         DECIMAL(15,2),  -- Valor na moeda de origem
destination_amount    DECIMAL(15,2),  -- Valor na moeda de destino
transfer_reference    TEXT,           -- Referência/motivo da transferência
```

### Relacionamentos

- `account_id` → Conta de origem
- `destination_account_id` → Conta de destino
- `exchange_rate` → Taxa de câmbio aplicada

## API Endpoints

### Base URL: `/api/v1/transfers`

#### 1. Criar Transferência
```http
POST /api/v1/transfers
Authorization: Bearer <token>
Content-Type: application/json

{
  "description": "Transferência para poupança",
  "amount": 1000.00,
  "transactionDate": "2024-01-15T10:00:00Z",
  "accountId": "account_origem_id",
  "destinationAccountId": "account_destino_id",
  "exchangeRate": 1.0,
  "sourceCurrency": "BRL",
  "destinationCurrency": "BRL",
  "sourceAmount": 1000.00,
  "destinationAmount": 1000.00,
  "transferReference": "Reserva de emergência",
  "familyMemberIds": ["member_id"]
}
```

#### 2. Listar Transferências
```http
GET /api/v1/transfers?page=1&limit=20&sortBy=transactionDate&sortOrder=desc
Authorization: Bearer <token>
```

#### 3. Obter Transferência por ID
```http
GET /api/v1/transfers/{id}
Authorization: Bearer <token>
```

#### 4. Atualizar Transferência
```http
PUT /api/v1/transfers/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "description": "Nova descrição",
  "amount": 1500.00
}
```

#### 5. Excluir Transferência
```http
DELETE /api/v1/transfers/{id}
Authorization: Bearer <token>
```

#### 6. Calcular Conversão de Moeda
```http
POST /api/v1/transfers/calculate-conversion
Authorization: Bearer <token>
Content-Type: application/json

{
  "sourceAmount": 1000.00,
  "sourceCurrency": "BRL",
  "destinationCurrency": "USD",
  "exchangeRate": 0.20
}
```

#### 7. Validar Transferência
```http
POST /api/v1/transfers/validate
Authorization: Bearer <token>
Content-Type: application/json

{
  "accountId": "account_origem_id",
  "destinationAccountId": "account_destino_id",
  "amount": 1000.00
}
```

## Validações Implementadas

### 1. Validações de Negócio
- Conta origem ≠ conta destino
- Contas devem existir e estar ativas
- Saldo suficiente na conta origem
- Limite de crédito respeitado (cartões)

### 2. Validações de Conversão
- Códigos de moeda válidos (3 letras maiúsculas)
- Taxa de câmbio positiva
- Consistência entre valores convertidos
- Taxa = 1 para mesma moeda

### 3. Validações de Dados
- Valores positivos
- Datas válidas
- IDs no formato CUID
- Limites de caracteres

## Middleware de Validação

### `validateTransferRules`
- Valida regras de negócio específicas
- Verifica saldo suficiente
- Adiciona informações das contas ao request

### `validateCurrencyConversion`
- Valida dados de conversão de moeda
- Verifica consistência dos cálculos
- Valida códigos de moeda

### `auditTransferOperation`
- Registra operações para auditoria
- Log de criação, atualização e exclusão
- Rastreamento por usuário

## Tratamento de Erros

### Códigos de Erro Específicos
- `MISSING_DESTINATION_ACCOUNT` - Conta destino obrigatória
- `SAME_ACCOUNT_TRANSFER` - Contas iguais
- `SOURCE_ACCOUNT_NOT_FOUND` - Conta origem não encontrada
- `DESTINATION_ACCOUNT_NOT_FOUND` - Conta destino não encontrada
- `INSUFFICIENT_BALANCE` - Saldo insuficiente
- `INSUFFICIENT_CREDIT_LIMIT` - Limite de crédito insuficiente
- `INVALID_CURRENCY_CODE` - Código de moeda inválido
- `INVALID_EXCHANGE_RATE` - Taxa de câmbio inválida

## Auditoria e Logs

### Logs de Operação
```javascript
{
  timestamp: "2024-01-15T10:00:00Z",
  method: "POST",
  userId: "user_id",
  transferId: "transfer_id",
  operation: "CREATE",
  sourceAccount: "account_origem_id",
  destinationAccount: "account_destino_id",
  amount: 1000.00,
  currency: "BRL"
}
```

## Exemplos de Uso

### Transferência Simples (Mesma Moeda)
```javascript
const transfer = {
  description: "Transferência para poupança",
  amount: 500.00,
  accountId: "checking_account_id",
  destinationAccountId: "savings_account_id",
  familyMemberIds: ["member_id"]
};
```

### Transferência com Conversão de Moeda
```javascript
const transfer = {
  description: "Transferência USD → BRL",
  amount: 100.00,
  accountId: "usd_account_id",
  destinationAccountId: "brl_account_id",
  exchangeRate: 5.20,
  sourceCurrency: "USD",
  destinationCurrency: "BRL",
  sourceAmount: 100.00,
  destinationAmount: 520.00,
  familyMemberIds: ["member_id"]
};
```

## Próximos Passos

1. **Sistema de conversão automática** (Tarefa 9.3)
2. **Transações parceladas** (Tarefa 9.4)
3. **Interface de usuário** (Tarefa 9.6)
4. **Testes automatizados** (Tarefa 9.7)

## Arquivos Relacionados

- `src/controllers/transfer.controller.ts` - Controller REST
- `src/services/transfer.service.ts` - Lógica de negócio
- `src/routes/transfer.routes.ts` - Definição de rotas
- `src/middleware/transfer.middleware.ts` - Middlewares de validação
- `src/schemas/transaction.schemas.ts` - Schemas de validação
- `prisma/schema.prisma` - Schema do banco de dados
