# 💳 InstallmentService - Serviço de Transações Parceladas

## Visão Geral

O `InstallmentService` gerencia transações parceladas, permitindo dividir compras em múltiplas parcelas com controle completo sobre cada parcela individual.

## Funcionalidades

### ✅ Implementadas

- **Criação de parcelas** automática
- **Atualização de parcelas** futuras
- **Cancelamento de parcelas** restantes
- **Resumo de parcelamento** com progresso
- **Validações de negócio** robustas
- **Relacionamento parent-child** entre parcelas

## API do Serviço

### Métodos Principais

#### `createInstallmentTransaction(data: CreateInstallmentData): Promise<InstallmentResult>`

Cria uma transação parcelada com todas as parcelas futuras.

```typescript
interface CreateInstallmentData {
  description: string;
  totalAmount: number;
  installments: number;
  accountId: string;
  categoryId?: string;
  familyMemberIds?: string[];
  firstInstallmentDate?: Date;
}

interface InstallmentResult {
  parentTransaction: Transaction;
  installmentTransactions: Transaction[];
  summary: InstallmentSummary;
}
```

**Exemplo de uso:**
```typescript
const installmentService = new InstallmentService();

const result = await installmentService.createInstallmentTransaction({
  description: 'Compra parcelada - TV 55"',
  totalAmount: 2400.00,
  installments: 12,
  accountId: 'credit_card_id',
  categoryId: 'electronics_category_id',
  familyMemberIds: ['member_id'],
  firstInstallmentDate: new Date('2024-02-01')
});

// Resultado:
// - 1 transação principal (parent)
// - 12 transações de parcela (children)
// - Resumo com progresso
```

#### `updateFutureInstallments(parentId: string, updates: InstallmentUpdates): Promise<UpdateResult>`

Atualiza parcelas futuras de uma transação parcelada.

```typescript
interface InstallmentUpdates {
  newAmount?: number;
  newDescription?: string;
  newCategoryId?: string;
}

const result = await installmentService.updateFutureInstallments(
  'parent_transaction_id',
  {
    newAmount: 220.00, // Nova parcela de R$ 220
    newDescription: 'TV 55" - Parcela atualizada'
  }
);
```

#### `cancelRemainingInstallments(parentId: string): Promise<CancelResult>`

Cancela todas as parcelas futuras de uma transação parcelada.

```typescript
const result = await installmentService.cancelRemainingInstallments(
  'parent_transaction_id'
);

console.log(result.cancelledCount); // Número de parcelas canceladas
```

#### `getInstallmentSummary(parentId: string): Promise<InstallmentSummary>`

Obtém resumo completo de uma transação parcelada.

```typescript
interface InstallmentSummary {
  parentTransactionId: string;
  totalAmount: number;
  totalInstallments: number;
  paidInstallments: number;
  remainingInstallments: number;
  paidAmount: number;
  remainingAmount: number;
  nextInstallmentDate?: Date;
  nextInstallmentAmount?: number;
  progress: number; // Percentual de conclusão
}

const summary = await installmentService.getInstallmentSummary('parent_id');
console.log(`Progresso: ${summary.progress}%`);
```

#### `getInstallmentProgress(parentId: string): Promise<ProgressData>`

Obtém dados detalhados de progresso das parcelas.

```typescript
interface ProgressData {
  totalInstallments: number;
  completedInstallments: number;
  pendingInstallments: number;
  cancelledInstallments: number;
  progressPercentage: number;
  nextDueDate?: Date;
  monthlyBreakdown: MonthlyInstallment[];
}

const progress = await installmentService.getInstallmentProgress('parent_id');
```

## Schema do Banco de Dados

### Campos de Parcelamento na Tabela `transactions`

```sql
-- Campos específicos para parcelas
installment_number     INTEGER,        -- Número da parcela (1, 2, 3...)
total_installments     INTEGER,        -- Total de parcelas
parent_transaction_id  VARCHAR(30),    -- ID da transação principal
is_installment         BOOLEAN,        -- Se é uma parcela
installment_status     VARCHAR(20),    -- Status da parcela

-- Relacionamentos
FOREIGN KEY (parent_transaction_id) REFERENCES transactions(id)
```

### Status de Parcelas

```typescript
enum InstallmentStatus {
  PENDING = 'PENDING',     // Aguardando vencimento
  PAID = 'PAID',           // Paga
  OVERDUE = 'OVERDUE',     // Em atraso
  CANCELLED = 'CANCELLED'  // Cancelada
}
```

## Validações

### Regras de Negócio

1. **Número de parcelas**: Entre 2 e 60 parcelas
2. **Valor mínimo**: R$ 10,00 por parcela
3. **Valor total**: Deve ser positivo
4. **Conta**: Deve existir e estar ativa
5. **Datas**: Parcelas futuras devem ter datas futuras

### Validações de Entrada

```typescript
const installmentSchema = z.object({
  description: z.string().min(3).max(255),
  totalAmount: z.number().positive(),
  installments: z.number().int().min(2).max(60),
  accountId: z.string().cuid(),
  firstInstallmentDate: z.date().optional()
});
```

## Cálculo de Parcelas

### Divisão de Valores

```typescript
// Exemplo: R$ 1000 em 3 parcelas
// Parcela 1: R$ 333,33
// Parcela 2: R$ 333,33  
// Parcela 3: R$ 333,34 (resto vai para a última)

const calculateInstallmentAmounts = (total: number, installments: number) => {
  const baseAmount = Math.floor((total * 100) / installments) / 100;
  const remainder = total - (baseAmount * (installments - 1));
  
  const amounts = Array(installments - 1).fill(baseAmount);
  amounts.push(remainder);
  
  return amounts;
};
```

### Datas de Vencimento

```typescript
// Parcelas mensais a partir da primeira data
const calculateInstallmentDates = (firstDate: Date, installments: number) => {
  const dates = [];
  for (let i = 0; i < installments; i++) {
    const date = new Date(firstDate);
    date.setMonth(date.getMonth() + i);
    dates.push(date);
  }
  return dates;
};
```

## Integração com TransactionService

### Criação de Parcelas

```typescript
// No TransactionService
const createInstallmentTransactions = async (data: CreateInstallmentData) => {
  // 1. Criar transação principal (parent)
  const parentTransaction = await this.create({
    ...data,
    amount: data.totalAmount,
    isInstallment: false,
    totalInstallments: data.installments
  });

  // 2. Criar parcelas individuais
  const installmentAmounts = this.calculateInstallmentAmounts(
    data.totalAmount, 
    data.installments
  );

  const installments = [];
  for (let i = 0; i < data.installments; i++) {
    const installment = await this.create({
      ...data,
      amount: installmentAmounts[i],
      installmentNumber: i + 1,
      totalInstallments: data.installments,
      parentTransactionId: parentTransaction.id,
      isInstallment: true,
      isFuture: i > 0 // Primeira parcela é imediata
    });
    installments.push(installment);
  }

  return { parentTransaction, installments };
};
```

## Relatórios e Analytics

### Resumo Mensal

```typescript
interface MonthlyInstallmentReport {
  month: string;
  totalInstallments: number;
  totalAmount: number;
  paidInstallments: number;
  paidAmount: number;
  pendingInstallments: number;
  pendingAmount: number;
}

const getMonthlyReport = async (year: number, month: number) => {
  // Buscar todas as parcelas do mês
  // Agrupar por status
  // Calcular totais
};
```

### Dashboard de Parcelas

```typescript
interface InstallmentDashboard {
  totalActiveInstallments: number;
  totalMonthlyCommitment: number;
  upcomingInstallments: Transaction[];
  overdueInstallments: Transaction[];
  completionRate: number;
}
```

## Testes

### Cobertura de Testes
- ✅ Criação de parcelas
- ✅ Atualização de parcelas futuras
- ✅ Cancelamento de parcelas
- ✅ Cálculo de valores
- ✅ Validações de entrada
- ✅ Resumos e progresso

### Executar Testes

```bash
npm test -- --testPathPattern="installment-transactions.test.ts"
```

## Casos de Uso Comuns

### 1. Compra Parcelada no Cartão

```typescript
const purchase = await installmentService.createInstallmentTransaction({
  description: 'Notebook Dell - 12x sem juros',
  totalAmount: 3600.00,
  installments: 12,
  accountId: 'credit_card_id',
  categoryId: 'technology_id'
});
```

### 2. Financiamento de Veículo

```typescript
const financing = await installmentService.createInstallmentTransaction({
  description: 'Financiamento Honda Civic',
  totalAmount: 80000.00,
  installments: 48,
  accountId: 'financing_account_id',
  firstInstallmentDate: new Date('2024-03-01')
});
```

### 3. Atualização de Parcelas

```typescript
// Reduzir valor das parcelas restantes
await installmentService.updateFutureInstallments(
  'financing_id',
  { newAmount: 1500.00 }
);
```

## Arquivos Relacionados

- `src/services/installment.service.ts` - Implementação principal
- `src/tests/installment-transactions.test.ts` - Testes unitários
- `src/types/installment.types.ts` - Tipos TypeScript
- `src/schemas/installment.schemas.ts` - Validações Zod

## Próximos Passos

1. **Juros e correção** monetária
2. **Renegociação** de parcelas
3. **Alertas** de vencimento
4. **Relatórios** avançados
5. **Interface** de usuário

---

**Última Atualização**: Dezembro 2024  
**Versão**: 1.0.0  
**Status**: ✅ Implementado e Testado
