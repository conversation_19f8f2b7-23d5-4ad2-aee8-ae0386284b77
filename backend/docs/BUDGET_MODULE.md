# 💰 Budget Module

O módulo de orçamentos permite criar e gerenciar orçamentos mensais por categoria e membro da família, com cálculos avançados de progresso e relatórios detalhados.

## 🎯 Funcionalidades

### ✅ Implementadas
- ✅ **CRUD de Orçamentos** - <PERSON><PERSON><PERSON>, ler, atualizar e deletar orçamentos
- ✅ **Orçamentos por Categoria** - Associar orçamentos a categorias específicas
- ✅ **Orçamentos por Membro** - Opcional: associar a membros da família
- ✅ **Cálculos de Progresso** - Gastos reais vs planejados em tempo real
- ✅ **Relatórios Avançados** - Breakdown por categoria e membro
- ✅ **Validações de Negócio** - Prevenção de duplicatas e validações de data
- ✅ **Soft Delete** - Exclusão lógica para auditoria
- ✅ **Paginação e Filtros** - Busca eficiente com filtros múltiplos

## 🏗️ Arquitetura

### Modelo de Dados (Prisma)

```prisma
model Budget {
  id             String   @id @default(cuid())
  plannedAmount  Decimal  @map("planned_amount") @db.Decimal(15, 2)
  month          Int
  year           Int
  categoryId     String   @map("category_id")
  familyMemberId String?  @map("family_member_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")
  version        Int      @default(1)

  // Relationships
  category     Category      @relation(fields: [categoryId], references: [id])
  familyMember FamilyMember? @relation(fields: [familyMemberId], references: [id])

  @@unique([categoryId, familyMemberId, month, year])
  @@map("budgets")
}
```

### Estrutura de Arquivos

```
backend/src/
├── schemas/budget.schemas.ts      # Validações Zod e tipos
├── services/budget.service.ts     # Lógica de negócio
├── controllers/budget.controller.ts # Endpoints REST
├── routes/budget.routes.ts        # Definição de rotas
└── tests/budget.test.ts          # Testes unitários
```

## 🔌 API Endpoints

### Base URL: `/api/v1/budgets`

| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `POST` | `/` | Criar novo orçamento |
| `GET` | `/` | Listar orçamentos com filtros |
| `GET` | `/:id` | Buscar orçamento por ID |
| `PUT` | `/:id` | Atualizar orçamento |
| `DELETE` | `/:id` | Deletar orçamento (soft delete) |
| `GET` | `/:id/progress` | Progresso específico do orçamento |
| `GET` | `/report` | Relatório completo de orçamentos |

### Exemplos de Uso

#### Criar Orçamento

```bash
POST /api/v1/budgets
Content-Type: application/json
Authorization: Bearer <token>

{
  "plannedAmount": 1500.00,
  "month": 12,
  "year": 2024,
  "categoryId": "clx123456789",
  "familyMemberId": "clx987654321"  // Opcional
}
```

#### Listar com Filtros

```bash
GET /api/v1/budgets?month=12&year=2024&includeProgress=true&page=1&limit=20
```

#### Relatório de Orçamentos

```bash
GET /api/v1/budgets/report?month=12&year=2024&includeSubcategories=true
```

## 📊 Cálculos de Progresso

### Métricas Calculadas

- **Valor Gasto Real** - Soma das transações do período
- **Valor Restante** - Planejado - Gasto
- **Percentual Utilizado** - (Gasto / Planejado) × 100
- **Dias Restantes** - Dias até o fim do mês
- **Orçamento Diário Restante** - Restante / Dias Restantes
- **Projeção Total** - Baseada na taxa de gasto atual

### Status Automático

- `under_budget` - Gasto < 70% do planejado
- `on_track` - Gasto entre 70-90% do planejado
- `warning` - Gasto > 90% ou projeção > 110%
- `over_budget` - Gasto > 100% do planejado

## 🧪 Testes

### Cobertura de Testes

- ✅ **30 testes unitários** passando
- ✅ **CRUD completo** testado
- ✅ **Validações de negócio** testadas
- ✅ **Cálculos de progresso** testados
- ✅ **Schemas Zod** validados
- ✅ **Tratamento de erros** testado

### Executar Testes

```bash
# Testes específicos do módulo
npm test -- budget.test.ts

# Com watch mode
npm run test:watch -- budget.test.ts

# Coverage
npm run test:coverage
```

## 🔒 Validações e Regras de Negócio

### Validações de Entrada

- **Valor Planejado**: Positivo, máximo 999.999.999,99
- **Mês**: Entre 1 e 12
- **Ano**: Entre 2020 e 2050
- **Categoria**: Deve existir e não estar arquivada
- **Membro**: Se fornecido, deve existir e não estar arquivado

### Regras de Negócio

- **Unicidade**: Um orçamento por categoria/membro/mês/ano
- **Datas**: Não permite orçamentos muito antigos (> 1 mês)
- **Integridade**: Usa transações Prisma para operações críticas
- **Auditoria**: Soft delete preserva histórico

## 📈 Relatórios Disponíveis

### Resumo Geral

```typescript
interface BudgetSummary {
  totalPlanned: number;
  totalActual: number;
  totalRemaining: number;
  overallPercentageUsed: number;
  budgetCount: number;
  overBudgetCount: number;
  underBudgetCount: number;
  onTrackCount: number;
  warningCount: number;
}
```

### Breakdown por Categoria

```typescript
interface CategoryBreakdown {
  categoryId: string;
  categoryName: string;
  totalPlanned: number;
  totalActual: number;
  percentageUsed: number;
  status: 'under_budget' | 'on_track' | 'warning' | 'over_budget';
}
```

### Breakdown por Membro

```typescript
interface MemberBreakdown {
  familyMemberId: string;
  familyMemberName: string;
  totalPlanned: number;
  totalActual: number;
  percentageUsed: number;
  budgetCount: number;
}
```

## 🚀 Próximos Passos

### Melhorias Futuras

- [ ] **Orçamentos Anuais** - Suporte a orçamentos de longo prazo
- [ ] **Metas de Economia** - Integração com módulo de metas
- [ ] **Alertas Automáticos** - Notificações quando próximo do limite
- [ ] **Orçamentos Flexíveis** - Ajuste automático baseado em histórico
- [ ] **Comparação de Períodos** - Análise mês a mês
- [ ] **Exportação** - Relatórios em PDF/Excel

### Integrações

- ✅ **Categorias** - Integrado com sistema de categorias
- ✅ **Membros da Família** - Suporte a orçamentos por pessoa
- ✅ **Transações** - Cálculos baseados em gastos reais
- [ ] **Dashboard** - Widgets de orçamento no painel principal
- [ ] **Notificações** - Sistema de alertas

## 🔧 Configuração e Manutenção

### Variáveis de Ambiente

Nenhuma configuração específica necessária. Usa as configurações padrão do banco de dados.

### Performance

- **Índices**: Otimizado para consultas por categoria, membro e período
- **Agregações**: Usa agregações Prisma para cálculos eficientes
- **Cache**: Considera implementar cache para relatórios frequentes

### Monitoramento

- **Logs**: Erros são logados com contexto completo
- **Métricas**: Monitore tempo de resposta dos cálculos de progresso
- **Alertas**: Configure alertas para falhas de cálculo

---

**Status**: ✅ **Implementado e Testado**  
**Versão**: 1.0.0  
**Última Atualização**: Dezembro 2024
