# 🔐 Authentication API Documentation

## Visão Geral

A API de autenticação do Personal Finance Manager fornece endpoints para registro, login, verificação de token e gerenciamento de perfil de usuários. Todos os endpoints seguem o padrão REST e retornam respostas JSON estruturadas.

## Base URL

```
/api/v1/auth
```

## Estrutura de Resposta

Todas as respostas seguem o padrão:

```json
{
  "success": boolean,
  "data": object | null,
  "message": string,
  "error": {
    "message": string,
    "code": string,
    "field": string (opcional)
  } (apenas em caso de erro)
}
```

## 📋 Endpoints Disponíveis

### 1. Registro de Usuário

**POST** `/api/v1/auth/register`

Cria uma nova conta de usuário.

#### Request Body
```json
{
  "email": "<EMAIL>",
  "password": "senha123",
  "name": "Nome do Usuário"
}
```

#### Validações
- **email**: Deve ser um email válido
- **password**: <PERSON><PERSON><PERSON> de 6 caracteres
- **name**: <PERSON><PERSON><PERSON> de 2 caracteres

#### Response (201 - Sucesso)
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "cmbm8zo0l0000dx733kfw73ij",
      "email": "<EMAIL>",
      "name": "Nome do Usuário",
      "isActive": true,
      "createdAt": "2025-06-07T13:06:12.693Z",
      "updatedAt": "2025-06-07T13:06:12.693Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "7d"
  },
  "message": "Usuário registrado com sucesso"
}
```

#### Response (400 - Email já existe)
```json
{
  "success": false,
  "error": {
    "message": "Email já está em uso",
    "code": "REGISTRATION_ERROR"
  }
}
```

### 2. Login de Usuário

**POST** `/api/v1/auth/login`

Autentica um usuário existente.

#### Request Body
```json
{
  "email": "<EMAIL>",
  "password": "senha123"
}
```

#### Response (200 - Sucesso)
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "cmbm8zo0l0000dx733kfw73ij",
      "email": "<EMAIL>",
      "name": "Nome do Usuário",
      "isActive": true,
      "createdAt": "2025-06-07T13:06:12.693Z",
      "updatedAt": "2025-06-07T13:06:12.693Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "7d"
  },
  "message": "Login realizado com sucesso"
}
```

#### Response (401 - Credenciais inválidas)
```json
{
  "success": false,
  "error": {
    "message": "Credenciais inválidas",
    "code": "LOGIN_ERROR"
  }
}
```

### 3. Obter Perfil do Usuário

**GET** `/api/v1/auth/profile`

Retorna o perfil do usuário autenticado.

#### Headers Obrigatórios
```
Authorization: Bearer <jwt_token>
```

#### Response (200 - Sucesso)
```json
{
  "success": true,
  "data": {
    "id": "cmbm8zo0l0000dx733kfw73ij",
    "email": "<EMAIL>",
    "name": "Nome do Usuário",
    "isActive": true,
    "createdAt": "2025-06-07T13:06:12.693Z",
    "updatedAt": "2025-06-07T13:06:12.693Z"
  },
  "message": "Perfil obtido com sucesso"
}
```

### 4. Verificar Token

**POST** `/api/v1/auth/verify`

Verifica se um token JWT é válido.

#### Request Body
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Response (200 - Token válido)
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "cmbm8zo0l0000dx733kfw73ij",
      "email": "<EMAIL>",
      "name": "Nome do Usuário",
      "isActive": true,
      "createdAt": "2025-06-07T13:06:12.693Z",
      "updatedAt": "2025-06-07T13:06:12.693Z"
    },
    "valid": true
  },
  "message": "Token válido"
}
```

### 5. Logout

**POST** `/api/v1/auth/logout`

Realiza logout do usuário (principalmente para limpeza no lado cliente).

#### Response (200 - Sucesso)
```json
{
  "success": true,
  "message": "Logout realizado com sucesso"
}
```

### 6. Atualizar Senha

**PUT** `/api/v1/auth/password`

Atualiza a senha do usuário autenticado.

#### Headers Obrigatórios
```
Authorization: Bearer <jwt_token>
```

#### Request Body
```json
{
  "currentPassword": "senhaAtual123",
  "newPassword": "novaSenha456"
}
```

#### Response (200 - Sucesso)
```json
{
  "success": true,
  "message": "Senha atualizada com sucesso"
}
```

## 🔒 Segurança

### JWT Token
- **Algoritmo**: HS256
- **Expiração**: 7 dias (configurável via `JWT_EXPIRES_IN`)
- **Payload**: `{ userId, email, iat, exp }`

### Rate Limiting
- **Limite**: 100 requisições por 15 minutos por IP
- **Headers de resposta**: 
  - `X-RateLimit-Limit`
  - `X-RateLimit-Remaining` 
  - `X-RateLimit-Reset`

### Validações
- Todas as entradas são validadas com **Zod**
- Senhas são hasheadas com **bcrypt** (12 rounds)
- Emails devem ser únicos no sistema

## 🚨 Códigos de Erro Comuns

| Código HTTP | Código de Erro | Descrição |
|-------------|----------------|-----------|
| 400 | `VALIDATION_ERROR` | Dados de entrada inválidos |
| 400 | `REGISTRATION_ERROR` | Erro no registro (ex: email já existe) |
| 401 | `LOGIN_ERROR` | Credenciais inválidas |
| 401 | `MISSING_TOKEN` | Token de autorização não fornecido |
| 401 | `INVALID_TOKEN` | Token inválido ou expirado |
| 401 | `NOT_AUTHENTICATED` | Usuário não autenticado |
| 403 | `ACCOUNT_DEACTIVATED` | Conta desativada |
| 429 | `RATE_LIMIT_EXCEEDED` | Limite de requisições excedido |
| 500 | `INTERNAL_ERROR` | Erro interno do servidor |

## 📝 Exemplos de Uso

### Fluxo Completo de Autenticação

```bash
# 1. Registrar usuário
curl -X POST http://localhost:3001/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "123456",
    "name": "Test User"
  }'

# 2. Fazer login
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "123456"
  }'

# 3. Usar token para acessar perfil
curl -X GET http://localhost:3001/api/v1/auth/profile \
  -H "Authorization: Bearer <token_obtido_no_login>"
```

## ⚠️ Problemas Conhecidos e Soluções

### 1. Inconsistência na URL Base
**Problema**: Frontend configurado para `/api` em vez de `/api/v1`
**Solução**: Sempre usar `/api/v1` como base URL

### 2. Rota de Perfil
**Problema**: Frontend tentando acessar `/auth/me` em vez de `/auth/profile`
**Solução**: Usar `/auth/profile` conforme implementado no backend

### 3. CORS
**Problema**: Erro de CORS em desenvolvimento
**Solução**: Backend configurado para aceitar `http://localhost:5173` (Vite padrão)

---

**Status**: ✅ **Implementado e Testado**  
**Versão**: 1.0.0  
**Última Atualização**: Junho 2025
