# Dashboard API Documentation

## Overview

A API de Dashboard fornece endpoints para visualização e análise de dados financeiros agregados. Todos os endpoints requerem autenticação JWT e implementam cache inteligente para otimização de performance.

## Base URL

```
/api/v1/dashboard
```

## Authentication

Todos os endpoints requerem um token JWT válido no header:

```
Authorization: Bearer <jwt_token>
```

## Rate Limiting

- **Limite**: 100 requisições por minuto por IP
- **Headers de resposta**: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

## Cache

- **Cache automático** com Redis para otimização de performance
- **Headers de cache** incluídos nas respostas (`Cache-Control`, `ETag`)
- **Invalidação inteligente** baseada em mudanças de dados

## Common Query Parameters

Todos os endpoints suportam os seguintes filtros opcionais:

### Filtros de Período

```typescript
// Período específico (mês/ano)
?month=12&year=2024

// Range de datas
?startDate=2024-01-01T00:00:00.000Z&endDate=2024-12-31T23:59:59.999Z

// Período pré-definido
?period=current_month | last_month | current_year | last_year
```

### Filtros de Conta

```typescript
// IDs específicos de contas
?accountIds=clp123...,clp456...

// Tipos de conta
?accountTypes=CHECKING,SAVINGS,CREDIT_CARD,INVESTMENT

// Moedas
?currencies=BRL,USD,EUR
```

### Filtros de Categoria

```typescript
// IDs específicos de categorias
?categoryIds=clp123...,clp456...

// Incluir subcategorias
?includeSubcategories=true
```

### Filtros de Membro da Família

```typescript
// IDs específicos de membros
?familyMemberIds=clp123...,clp456...
```

## Endpoints

### 1. Overview Completo

**GET** `/overview`

Retorna um resumo completo do dashboard com todos os dados agregados.

**Response:**
```typescript
{
  success: true,
  data: {
    accountBalances: AccountBalancesResponse,
    netWorth: NetWorthResponse,
    creditCardUsage: CreditCardUsageResponse,
    expensesByCategory: ExpensesByCategoryResponse,
    expensesByMember: ExpensesByMemberResponse,
    budgetComparison: BudgetComparisonResponse,
    goalProgress: GoalProgressResponse,
    generatedAt: string,
    filters: DashboardFilters
  }
}
```

### 2. Saldos de Contas

**GET** `/account-balances`

Retorna agregação de saldos por tipo de conta e moeda.

**Response:**
```typescript
{
  success: true,
  data: {
    totalBalance: number,
    totalBalanceInBRL: number,
    projectedBalance: number,
    projectedBalanceInBRL: number,
    balanceByType: Array<{
      type: AccountType,
      balance: number,
      balanceInBRL: number,
      count: number
    }>,
    balanceByCurrency: Array<{
      currency: string,
      balance: number,
      balanceInBRL: number,
      exchangeRate?: number,
      count: number
    }>
  }
}
```

### 3. Patrimônio Líquido

**GET** `/net-worth`

Calcula patrimônio líquido atual e histórico.

**Response:**
```typescript
{
  success: true,
  data: {
    currentNetWorth: number,
    assets: {
      total: number,
      totalInBRL: number,
      byType: Array<{
        type: AccountType,
        amount: number,
        amountInBRL: number
      }>
    },
    liabilities: {
      total: number,
      totalInBRL: number,
      byType: Array<{
        type: AccountType,
        amount: number,
        amountInBRL: number
      }>
    },
    monthlyHistory: Array<{
      month: string,
      netWorth: number,
      assets: number,
      liabilities: number
    }>
  }
}
```

### 4. Uso de Cartão de Crédito

**GET** `/credit-card-usage`

Analisa uso e limites de cartões de crédito.

**Response:**
```typescript
{
  success: true,
  data: {
    totalUsage: number,
    totalLimit: number,
    usagePercentage: number,
    availableCredit: number,
    cardDetails: Array<{
      accountId: string,
      accountName: string,
      usage: number,
      limit: number,
      usagePercentage: number,
      availableCredit: number,
      currency: string
    }>,
    alerts: Array<{
      type: 'HIGH_USAGE' | 'NEAR_LIMIT' | 'OVER_LIMIT',
      accountId: string,
      accountName: string,
      message: string,
      severity: 'low' | 'medium' | 'high'
    }>
  }
}
```

### 5. Despesas por Categoria

**GET** `/expenses-by-category`

Agrega despesas por categoria com hierarquia.

**Response:**
```typescript
{
  success: true,
  data: {
    totalExpenses: number,
    categories: Array<{
      categoryId: string,
      categoryName: string,
      parentCategoryId?: string,
      parentCategoryName?: string,
      amount: number,
      amountInBRL: number,
      transactionCount: number,
      percentage: number,
      subcategories?: Array<CategoryExpense>
    }>
  }
}
```

### 6. Despesas por Membro

**GET** `/expenses-by-member`

Agrega despesas por membro da família.

**Response:**
```typescript
{
  success: true,
  data: {
    totalExpenses: number,
    members: Array<{
      familyMemberId: string,
      familyMemberName: string,
      amount: number,
      amountInBRL: number,
      transactionCount: number,
      percentage: number,
      topCategories: Array<{
        categoryId: string,
        categoryName: string,
        amount: number,
        percentage: number
      }>
    }>,
    comparison: {
      highest: { memberId: string, amount: number },
      lowest: { memberId: string, amount: number },
      average: number
    }
  }
}
```

### 7. Comparação Orçamentária

**GET** `/budget-comparison`

Compara orçamentos planejados vs gastos reais.

**Response:**
```typescript
{
  success: true,
  data: {
    totalBudgeted: number,
    totalSpent: number,
    totalRemaining: number,
    overallProgress: number,
    categories: Array<{
      categoryId: string,
      categoryName: string,
      budgeted: number,
      spent: number,
      remaining: number,
      progress: number,
      status: 'under_budget' | 'on_track' | 'over_budget'
    }>,
    alerts: Array<{
      type: 'OVER_BUDGET' | 'NEAR_LIMIT',
      categoryId: string,
      categoryName: string,
      message: string,
      severity: 'low' | 'medium' | 'high'
    }>
  }
}
```

### 8. Progresso de Metas

**GET** `/goal-progress`

Acompanha progresso de metas financeiras.

**Response:**
```typescript
{
  success: true,
  data: {
    totalGoals: number,
    activeGoals: number,
    completedGoals: number,
    goals: Array<{
      goalId: string,
      goalName: string,
      targetAmount: number,
      currentAmount: number,
      progress: number,
      targetDate: string,
      daysRemaining: number,
      isCompleted: boolean,
      members: Array<{
        familyMemberId: string,
        familyMemberName: string
      }>,
      milestones: Array<{
        id: string,
        name: string,
        targetAmount: number,
        isCompleted: boolean
      }>
    }>,
    summary: {
      totalTargetAmount: number,
      totalCurrentAmount: number,
      overallProgress: number,
      onTrackGoals: number,
      behindScheduleGoals: number
    }
  }
}
```

### 9. Métricas de Performance

**GET** `/performance-metrics`

Retorna métricas de performance do sistema.

**Response:**
```typescript
{
  success: true,
  data: {
    slowQueries: Array<{
      operation: string,
      duration: number,
      timestamp: number,
      filters: object
    }>,
    averageQueryTime: number,
    totalQueries: number
  }
}
```

## Error Responses

Todos os endpoints seguem o padrão de resposta de erro:

```typescript
{
  success: false,
  error: string,
  details?: Array<{
    field: string,
    message: string,
    received: any
  }>
}
```

### Códigos de Status HTTP

- **200**: Sucesso
- **400**: Filtros inválidos ou dados malformados
- **401**: Token JWT inválido ou ausente
- **429**: Rate limit excedido
- **500**: Erro interno do servidor

## Performance

### Cache Strategy

- **Account Balances**: Cache de 5 minutos
- **Net Worth**: Cache de 10 minutos
- **Credit Card Usage**: Cache de 5 minutos
- **Expenses**: Cache de 15 minutos
- **Budget/Goals**: Cache de 30 minutos
- **Overview**: Cache de 5 minutos

### Query Optimization

- Queries otimizadas com índices apropriados
- Agregações realizadas no banco de dados
- Lazy loading para dados relacionados
- Monitoramento de queries lentas (>2s)

## Examples

### Obter overview do mês atual

```bash
curl -X GET \
  "http://localhost:3001/api/v1/dashboard/overview" \
  -H "Authorization: Bearer <token>"
```

### Filtrar por período específico

```bash
curl -X GET \
  "http://localhost:3001/api/v1/dashboard/overview?month=12&year=2024" \
  -H "Authorization: Bearer <token>"
```

### Filtrar por contas específicas

```bash
curl -X GET \
  "http://localhost:3001/api/v1/dashboard/account-balances?accountIds=clp123...,clp456...&currencies=BRL,USD" \
  -H "Authorization: Bearer <token>"
```
