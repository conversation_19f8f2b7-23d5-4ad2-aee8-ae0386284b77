# Sistema de Cache Redis - Personal Finance Manager

## Visão Geral

O sistema de cache Redis foi implementado para otimizar significativamente a performance do dashboard, reduzindo o tempo de resposta das queries mais complexas de segundos para milissegundos.

## Arquitetura

### Componentes Principais

1. **RedisCacheManager** (`src/lib/redis.ts`)
   - Gerenciamento de conexão Redis com reconexão automática
   - Operações básicas de cache (get, set, del, exists)
   - Tratamento robusto de erros e fallbacks

2. **DashboardCacheService** (`src/services/dashboard-cache.service.ts`)
   - Cache específico para operações do dashboard
   - Estratégias de TTL diferenciadas por tipo de operação
   - Invalidação inteligente baseada em relacionamentos

3. **Cache Middleware** (`src/middleware/cache-invalidation.middleware.ts`)
   - Invalidação automática quando dados são modificados
   - Middleware inteligente baseado em rotas
   - Aquecimento proativo de cache

## Configuração

### Variáveis de Ambiente

```bash
REDIS_URL=redis://localhost:6379  # URL do Redis (opcional, padrão: localhost:6379)
```

### TTL (Time To Live) por Operação

| Operação | TTL | Justificativa |
|----------|-----|---------------|
| ACCOUNT_BALANCES | 5 min | Dados que mudam frequentemente |
| NET_WORTH | 10 min | Cálculo complexo, mudança moderada |
| CREDIT_CARD_USAGE | 5 min | Dados importantes para controle |
| EXPENSES_BY_CATEGORY | 15 min | Análise histórica, mudança lenta |
| EXPENSES_BY_MEMBER | 15 min | Análise histórica, mudança lenta |
| BUDGET_COMPARISON | 10 min | Dados mensais, mudança moderada |
| GOAL_PROGRESS | 30 min | Dados de longo prazo |
| OVERVIEW | 5 min | Dados críticos do dashboard |

## Estratégias de Invalidação

### Invalidação por Entidade

Quando uma entidade é modificada, o sistema invalida automaticamente os caches relacionados:

- **Transaction**: Invalida ACCOUNT_BALANCES, NET_WORTH, EXPENSES_BY_CATEGORY, EXPENSES_BY_MEMBER, BUDGET_COMPARISON, OVERVIEW
- **Account**: Invalida ACCOUNT_BALANCES, NET_WORTH, CREDIT_CARD_USAGE, OVERVIEW
- **Budget**: Invalida BUDGET_COMPARISON, OVERVIEW
- **Goal**: Invalida GOAL_PROGRESS, OVERVIEW

### Invalidação Automática

O middleware de invalidação é aplicado automaticamente nas rotas que modificam dados:

```typescript
// Exemplo de uso
app.use('/api/transactions', invalidateTransactionCache, transactionRoutes);
app.use('/api/accounts', invalidateAccountCache, accountRoutes);
```

## API de Monitoramento

### Endpoints Disponíveis

#### GET /api/cache/health
Verifica o status de saúde do cache.

**Resposta:**
```json
{
  "success": true,
  "data": {
    "status": "healthy|degraded|unhealthy",
    "redis": true,
    "latency": 15
  }
}
```

#### GET /api/cache/stats
Obtém estatísticas detalhadas do cache.

**Resposta:**
```json
{
  "success": true,
  "data": {
    "isAvailable": true,
    "keyCount": 42,
    "memoryUsage": "1.5M",
    "operations": {
      "ACCOUNT_BALANCES": {
        "ttl": 300,
        "keyPattern": "dashboard:ACCOUNT_BALANCES:*"
      }
    }
  }
}
```

#### POST /api/cache/invalidate
Invalida entradas de cache.

**Body:**
```json
{
  "entity": "transaction",  // ou "account", "budget", "goal"
  "operation": "ACCOUNT_BALANCES"  // operação específica (opcional)
}
```

#### POST /api/cache/warm-up
Aquece o cache com queries comuns.

**Body:**
```json
{
  "filters": [
    {},  // filtros padrão
    { "accountIds": ["acc1", "acc2"] }  // filtros específicos
  ]
}
```

## Uso no Código

### Cache Wrapper

O método `withCache` encapsula automaticamente operações com cache:

```typescript
// No DashboardService
async getAccountBalances(filters: DashboardFilters = {}): Promise<AccountBalanceAggregation> {
  const validatedFilters = this.validateFilters(filters);
  
  return dashboardCache.withCache('ACCOUNT_BALANCES', validatedFilters, async () => {
    // Lógica de busca no banco de dados
    const accounts = await prisma.account.findMany(/* ... */);
    return processAccountBalances(accounts);
  });
}
```

### Invalidação Manual

```typescript
// Invalidar cache específico
await dashboardCache.invalidate('ACCOUNT_BALANCES');

// Invalidar por entidade
await dashboardCache.invalidateRelated('transaction');

// Invalidar tudo
await dashboardCache.invalidateAll();
```

## Fallbacks e Robustez

### Graceful Degradation

O sistema foi projetado para funcionar mesmo quando o Redis está indisponível:

1. **Conexão Falha**: Aplicação continua funcionando sem cache
2. **Operações Falham**: Retorna dados diretamente do banco
3. **Timeout**: Configurado para não bloquear requests
4. **Reconexão**: Tentativas automáticas com backoff exponencial

### Monitoramento de Performance

```typescript
// Logs automáticos para queries lentas
console.warn('🐌 Slow dashboard query detected: getAccountBalances took 2500ms');

// Métricas de cache
console.log('Cache HIT for ACCOUNT_BALANCES (age: 45s)');
console.log('Cache MISS for NET_WORTH');
```

## Scripts de Manutenção

### Teste do Sistema de Cache

```bash
npm run cache:test
```

Executa testes abrangentes do sistema de cache:
- Conectividade Redis
- Operações básicas (GET/SET/DELETE)
- Invalidação
- Integração com Dashboard
- Performance
- Failover

### Otimização de Índices

```bash
npm run db:optimize
```

Aplica índices otimizados para queries de dashboard e analisa performance.

## Métricas de Performance

### Resultados Esperados

- **Cache Hit**: < 50ms
- **Cache Miss**: 200-2000ms (dependendo da query)
- **Invalidação**: < 100ms
- **Melhoria de Performance**: 5-10x em queries complexas

### Monitoramento

O sistema registra automaticamente:
- Hit/Miss ratio
- Latência de operações
- Queries lentas (>2s)
- Erros de cache
- Status de conectividade

## Troubleshooting

### Redis Não Conecta

1. Verificar se Redis está rodando: `redis-cli ping`
2. Verificar URL de conexão: `REDIS_URL`
3. Verificar logs: `console.error('Redis Client Error:', err)`

### Cache Não Invalida

1. Verificar middleware aplicado nas rotas
2. Verificar logs de invalidação
3. Testar invalidação manual via API

### Performance Degradada

1. Verificar hit ratio: `GET /api/cache/stats`
2. Verificar TTL das operações
3. Analisar logs de queries lentas

## Desenvolvimento

### Adicionando Nova Operação de Cache

1. Adicionar ao enum `cacheTTL` em `DashboardCacheService`
2. Implementar no `DashboardService` usando `withCache`
3. Configurar invalidação relacionada
4. Adicionar testes

### Testando Localmente

```bash
# Iniciar Redis local
docker run -d -p 6379:6379 redis:alpine

# Testar sistema
npm run cache:test

# Verificar saúde
curl http://localhost:3000/api/cache/health
```

## Considerações de Produção

1. **Memória Redis**: Monitorar uso de memória
2. **Persistência**: Configurar snapshots se necessário
3. **Clustering**: Para alta disponibilidade
4. **Monitoring**: Integrar com ferramentas de monitoramento
5. **Backup**: Estratégia de backup para dados críticos
