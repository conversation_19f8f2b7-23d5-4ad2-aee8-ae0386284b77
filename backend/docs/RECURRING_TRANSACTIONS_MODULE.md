# 🔄 Recurring Transactions Module

O módulo de transações recorrentes permite criar e gerenciar transações que se repetem automaticamente em intervalos definidos, com suporte a parcelamentos e processamento automático via jobs.

## 🎯 Funcionalidades

### ✅ Implementadas
- ✅ **CRUD de Transações Recorrentes** - C<PERSON>r, ler, atualizar e deletar recorrências
- ✅ **Múl<PERSON>las Frequências** - Diária, semanal, mensal, anual
- ✅ **Processamento Automático** - Jobs schedulados com node-schedule
- ✅ **Geração de Parcelamentos** - Criação automática de installments
- ✅ **Controle de Status** - Ativo, pausado, concluído, cancelado
- ✅ **Validações Avançadas** - Regras de negócio específicas
- ✅ **Soft Delete** - Exclusão lógica para auditoria
- ✅ **Integração com Transações** - Criação automática de transações

## 🏗️ Arquitetura

### Modelo de Dados (Prisma)

```prisma
model RecurringTransaction {
  id                String   @id @default(cuid())
  title             String
  description       String?
  amount            Decimal  @db.Decimal(15, 2)
  type              TransactionType
  frequency         RecurrenceFrequency
  interval          Int      @default(1)
  startDate         DateTime @map("start_date")
  endDate           DateTime? @map("end_date")
  nextProcessDate   DateTime @map("next_process_date")
  lastProcessedAt   DateTime? @map("last_processed_at")
  status            RecurrenceStatus @default(ACTIVE)
  accountId         String   @map("account_id")
  categoryId        String   @map("category_id")
  familyMemberId    String?  @map("family_member_id")
  installments      Int?
  processedCount    Int      @default(0) @map("processed_count")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  // Relationships
  account      Account       @relation(fields: [accountId], references: [id])
  category     Category      @relation(fields: [categoryId], references: [id])
  familyMember FamilyMember? @relation(fields: [familyMemberId], references: [id])
  tags         RecurringTransactionTag[]

  @@map("recurring_transactions")
}

enum RecurrenceFrequency {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
}

enum RecurrenceStatus {
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}
```

### Estrutura de Arquivos

```
backend/src/
├── schemas/recurring-transaction.schemas.ts  # Validações Zod
├── services/recurring-transaction.service.ts # Lógica de negócio
├── controllers/recurring-transaction.controller.ts # Endpoints REST
├── routes/recurring-transaction.routes.ts    # Definição de rotas
├── jobs/recurring-transaction.job.ts         # Jobs de processamento
└── tests/recurring-transaction.test.ts       # Testes unitários
```

## 🔌 API Endpoints

### Base URL: `/api/v1/recurring-transactions`

| Método | Endpoint | Descrição |
|--------|----------|-----------|
| `POST` | `/` | Criar nova transação recorrente |
| `GET` | `/` | Listar transações recorrentes |
| `GET` | `/:id` | Buscar transação recorrente por ID |
| `PUT` | `/:id` | Atualizar transação recorrente |
| `DELETE` | `/:id` | Deletar transação recorrente |
| `POST` | `/:id/pause` | Pausar processamento |
| `POST` | `/:id/resume` | Retomar processamento |
| `POST` | `/:id/process` | Processar manualmente |

### Exemplos de Uso

#### Criar Transação Recorrente

```bash
POST /api/v1/recurring-transactions
Content-Type: application/json
Authorization: Bearer <token>

{
  "title": "Salário Mensal",
  "description": "Recebimento de salário",
  "amount": 5000.00,
  "type": "INCOME",
  "frequency": "MONTHLY",
  "interval": 1,
  "startDate": "2024-01-01T00:00:00Z",
  "accountId": "clx123456789",
  "categoryId": "clx987654321",
  "familyMemberId": "clx456789123"
}
```

#### Criar Parcelamento

```bash
POST /api/v1/recurring-transactions
Content-Type: application/json
Authorization: Bearer <token>

{
  "title": "Financiamento Carro",
  "amount": 800.00,
  "type": "EXPENSE",
  "frequency": "MONTHLY",
  "startDate": "2024-01-15T00:00:00Z",
  "installments": 48,
  "accountId": "clx123456789",
  "categoryId": "clx987654321"
}
```

## ⚙️ Sistema de Jobs

### Configuração do Scheduler

```typescript
// Executa a cada hora para verificar transações pendentes
schedule.scheduleJob('0 * * * *', async () => {
  await recurringTransactionJob.processRecurringTransactions();
});
```

### Lógica de Processamento

1. **Busca Pendentes** - Identifica transações com `nextProcessDate` <= agora
2. **Validações** - Verifica status ativo e conta válida
3. **Criação de Transação** - Gera transação real no sistema
4. **Atualização de Dados** - Atualiza contadores e próxima data
5. **Controle de Finalização** - Marca como concluída se necessário

### Tratamento de Erros

- **Retry Logic** - Tenta novamente em caso de falha
- **Logging Detalhado** - Registra todos os processamentos
- **Notificações** - Alerta sobre falhas críticas
- **Rollback** - Desfaz operações em caso de erro

## 🧪 Testes

### Cobertura de Testes

- ✅ **CRUD completo** testado
- ✅ **Validações de frequência** testadas
- ✅ **Lógica de processamento** testada
- ✅ **Cálculo de próximas datas** testado
- ✅ **Geração de parcelamentos** testada
- ✅ **Tratamento de erros** testado

### Executar Testes

```bash
# Testes específicos do módulo
npm test -- recurring-transaction.test.ts

# Com watch mode
npm run test:watch -- recurring-transaction.test.ts
```

## 🔒 Validações e Regras de Negócio

### Validações de Entrada

- **Título**: Obrigatório, máximo 255 caracteres
- **Valor**: Positivo, máximo 999.999.999,99
- **Frequência**: DAILY, WEEKLY, MONTHLY, YEARLY
- **Intervalo**: Mínimo 1, máximo 365 (para diário)
- **Data Início**: Não pode ser no passado
- **Parcelas**: Se fornecido, mínimo 2, máximo 999

### Regras de Negócio

- **Status Automático**: Muda para COMPLETED quando todas as parcelas são processadas
- **Próxima Data**: Calculada automaticamente baseada na frequência
- **Validação de Conta**: Conta deve existir e estar ativa
- **Limite de Processamento**: Máximo 100 transações por execução do job

## 📊 Frequências Suportadas

### Tipos de Recorrência

| Frequência | Intervalo | Exemplo |
|------------|-----------|---------|
| `DAILY` | 1-365 dias | A cada 1 dia, a cada 7 dias |
| `WEEKLY` | 1-52 semanas | Toda semana, a cada 2 semanas |
| `MONTHLY` | 1-12 meses | Todo mês, a cada 3 meses |
| `YEARLY` | 1-10 anos | Todo ano, a cada 2 anos |

### Cálculo de Próximas Datas

```typescript
// Exemplo de cálculo para frequência mensal
const nextDate = new Date(lastProcessDate);
nextDate.setMonth(nextDate.getMonth() + interval);

// Para frequência diária
const nextDate = new Date(lastProcessDate);
nextDate.setDate(nextDate.getDate() + interval);
```

## 🚀 Casos de Uso Comuns

### 1. Salário Mensal

```json
{
  "title": "Salário",
  "amount": 5000.00,
  "type": "INCOME",
  "frequency": "MONTHLY",
  "interval": 1,
  "startDate": "2024-01-01"
}
```

### 2. Financiamento em Parcelas

```json
{
  "title": "Financiamento Casa",
  "amount": 1200.00,
  "type": "EXPENSE",
  "frequency": "MONTHLY",
  "installments": 360,
  "startDate": "2024-01-15"
}
```

### 3. Conta de Luz Bimestral

```json
{
  "title": "Conta de Luz",
  "amount": 150.00,
  "type": "EXPENSE",
  "frequency": "MONTHLY",
  "interval": 2,
  "startDate": "2024-01-10"
}
```

## 🔧 Configuração e Manutenção

### Variáveis de Ambiente

```env
# Configuração do job scheduler
RECURRING_TRANSACTION_JOB_ENABLED=true
RECURRING_TRANSACTION_JOB_CRON="0 * * * *"
RECURRING_TRANSACTION_MAX_BATCH_SIZE=100
```

### Monitoramento

- **Logs de Processamento** - Cada execução é logada
- **Métricas de Performance** - Tempo de processamento
- **Alertas de Falha** - Notificações para erros críticos
- **Dashboard de Status** - Visualização do status dos jobs

### Backup e Recuperação

- **Backup Regular** - Dados de recorrência são críticos
- **Teste de Recuperação** - Validar integridade após restore
- **Versionamento** - Controle de versão para mudanças

## 🚀 Próximos Passos

### Melhorias Futuras

- [ ] **Recorrência Complexa** - Suporte a padrões como "toda segunda-feira"
- [ ] **Ajuste Automático** - Valores que se ajustam por inflação
- [ ] **Notificações** - Alertas antes do processamento
- [ ] **Dashboard** - Interface visual para gerenciar recorrências
- [ ] **Importação** - Importar recorrências de outros sistemas
- [ ] **Relatórios** - Análise de recorrências e projeções

### Integrações

- ✅ **Transações** - Criação automática de transações
- ✅ **Contas** - Validação de contas ativas
- ✅ **Categorias** - Associação com categorias
- [ ] **Orçamentos** - Impacto em orçamentos mensais
- [ ] **Notificações** - Sistema de alertas

---

**Status**: ✅ **Implementado e Testado**  
**Versão**: 1.0.0  
**Última Atualização**: Dezembro 2024
