# Módulo de Insights Automáticos

## Visão Geral

O módulo de insights automáticos é responsável por analisar dados financeiros dos usuários e gerar insights acionáveis baseados em padrões de gastos, tendências, anomalias e outros indicadores financeiros importantes.

## Arquitetura

### Componentes Principais

1. **InsightService** - Serviço principal para gerenciamento de insights
2. **InsightJobService** - Serviço para geração automática agendada
3. **InsightCacheService** - Sistema de cache inteligente
4. **InsightController** - Controller REST API
5. **Utilitários de Análise** - Algoritmos estatísticos e financeiros

### Fluxo de Dados

```
Dados Transacionais → Algoritmos de Análise → Geração de Insights → Cache → API → Frontend
                                    ↓
                            Job Agendado (06:30 diário)
```

## Tipos de Insights

### 1. SPENDING_PATTERN
- **Descrição**: Identifica padrões de gastos por categoria
- **Trigger**: Categoria com >30% do orçamento total
- **Exemplo**: "Alto gasto em Alimentação (45% do orçamento)"

### 2. BUDGET_ALERT
- **Descrição**: Alertas sobre uso do orçamento
- **Trigger**: >80% do orçamento mensal usado
- **Exemplo**: "Orçamento quase esgotado em Transporte (95%)"

### 3. ANOMALY_DETECTION
- **Descrição**: Detecta gastos incomuns
- **Trigger**: Transação >2 desvios padrão da média
- **Exemplo**: "Gasto incomum detectado: R$ 1.000 em Alimentação"

### 4. TREND_ANALYSIS
- **Descrição**: Analisa tendências de gastos
- **Trigger**: Mudança >15% com força >0.6
- **Exemplo**: "Gastos aumentaram 25% nos últimos meses"

### 5. CATEGORY_ANALYSIS
- **Descrição**: Compara gastos entre períodos
- **Trigger**: Mudança >50% e >R$ 100 em categoria
- **Exemplo**: "Aumento significativo em Lazer (80% este mês)"

## API Endpoints

### Insights CRUD

```http
POST   /api/v1/insights              # Criar insight manual
GET    /api/v1/insights              # Listar insights com filtros
GET    /api/v1/insights/:id          # Obter insight específico
PUT    /api/v1/insights/:id          # Atualizar insight
DELETE /api/v1/insights/:id          # Deletar insight
```

### Operações Especiais

```http
POST   /api/v1/insights/generate     # Gerar insights automaticamente
POST   /api/v1/insights/bulk-action  # Ações em lote
GET    /api/v1/insights/analytics    # Analytics de insights
```

### Parâmetros de Filtro

- `type` - Tipo do insight
- `priority` - Prioridade (LOW, MEDIUM, HIGH, CRITICAL)
- `status` - Status (NEW, VIEWED, DISMISSED, ACTED_UPON)
- `categoryId` - Filtrar por categoria
- `accountId` - Filtrar por conta
- `goalId` - Filtrar por meta
- `fromDate` / `toDate` - Período de criação
- `includeExpired` - Incluir insights expirados
- `minRelevanceScore` - Score mínimo de relevância
- `page` / `limit` - Paginação

## Algoritmos de Análise

### Análise Estatística

#### Detecção de Anomalias
```typescript
// Usa Z-score para detectar outliers
const zScore = (amount - mean) / standardDeviation;
if (Math.abs(zScore) >= threshold) {
  // Transação anômala detectada
}
```

#### Análise de Tendências
```typescript
// Regressão linear simples
const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
const rSquared = 1 - (ssResidual / ssTotal);
```

#### Detecção de Sazonalidade
```typescript
// Análise de padrões periódicos
const seasonalStrength = coefficientOfVariation;
const peaks = detectPeaksAndTroughs(periodAverages);
```

### Análise Financeira

#### Gastos por Categoria
```typescript
const categorySpending = transactions
  .groupBy('categoryId')
  .map(group => ({
    totalAmount: sum(group.amounts),
    percentage: (totalAmount / totalSpending) * 100
  }));
```

#### Comparação de Períodos
```typescript
const change = currentAmount - previousAmount;
const changePercentage = (change / previousAmount) * 100;
```

## Sistema de Cache

### Estratégia de Cache

- **Insights individuais**: 24 horas
- **Resultados de geração**: 30 minutos
- **Analytics**: 2 horas
- **Análise de padrões**: 6 horas
- **Detecção de anomalias**: 15 minutos

### Invalidação Inteligente

```typescript
// Invalidação automática quando dados mudam
await insightCacheService.invalidateOnTransactionChange(userId, categoryId);
await insightCacheService.invalidateOnBudgetChange(userId, categoryId);
await insightCacheService.invalidateOnGoalChange(userId, goalId);
```

## Jobs Agendados

### Configuração

```typescript
{
  name: 'Generate Insights',
  type: JobType.INSIGHTS_GENERATION,
  cronPattern: '30 6 * * *', // Diário às 06:30
  timezone: 'America/Sao_Paulo',
  enabled: true,
  retryAttempts: 2,
  retryDelay: 15000
}
```

### Processo de Execução

1. **Verificar usuários ativos**
2. **Validar suficiência de dados** (mín. 5 transações/30 dias)
3. **Gerar insights por tipo**
4. **Limpar insights expirados**
5. **Invalidar cache**
6. **Reportar estatísticas**

## Configuração e Deployment

### Variáveis de Ambiente

```env
# Cache Redis
REDIS_URL=redis://localhost:6379

# Database
DATABASE_URL=postgresql://...

# Scheduler
NODE_ENV=production
```

### Dependências

```json
{
  "redis": "^5.5.5",
  "node-schedule": "^2.1.1",
  "@prisma/client": "^5.7.0",
  "zod": "^3.22.4"
}
```

## Monitoramento e Métricas

### Métricas Importantes

- **Taxa de geração**: Insights gerados por execução
- **Taxa de sucesso**: % de usuários processados com sucesso
- **Tempo de processamento**: Duração média dos jobs
- **Cache hit rate**: Eficiência do cache
- **Relevância média**: Score médio dos insights

### Logs

```typescript
console.log('[InsightJob] Starting insight generation job');
console.log('[InsightJob] User user-1: 5 generated, 2 updated (1500ms)');
console.log('[InsightJob] Generated 15 insights for 3/3 users in 4500ms');
```

## Troubleshooting

### Problemas Comuns

1. **Insights não sendo gerados**
   - Verificar dados suficientes (>5 transações)
   - Verificar job scheduler ativo
   - Verificar logs de erro

2. **Performance lenta**
   - Verificar status do Redis
   - Analisar queries do banco
   - Verificar cache hit rate

3. **Insights irrelevantes**
   - Ajustar thresholds de detecção
   - Verificar qualidade dos dados
   - Revisar algoritmos de scoring

### Comandos de Debug

```bash
# Verificar status do scheduler
curl http://localhost:3000/api/v1/insights/analytics

# Gerar insights manualmente
curl -X POST http://localhost:3000/api/v1/insights/generate \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"forceRegenerate": true}'

# Verificar cache
redis-cli keys "insights:*"
```

## Roadmap Futuro

### Melhorias Planejadas

1. **Machine Learning**
   - Modelos preditivos avançados
   - Personalização baseada em comportamento
   - Detecção de fraude

2. **Insights Avançados**
   - Análise de fluxo de caixa
   - Projeções financeiras
   - Recomendações de investimento

3. **Integração Externa**
   - APIs bancárias (Open Banking)
   - Dados de mercado financeiro
   - Comparação com benchmarks

4. **Interface Melhorada**
   - Visualizações interativas
   - Notificações push
   - Insights por voz/chat

## Contribuição

Para contribuir com o módulo de insights:

1. Seguir padrões de código estabelecidos
2. Adicionar testes para novos algoritmos
3. Documentar novos tipos de insights
4. Considerar impacto na performance
5. Validar com dados reais

## Licença

Este módulo faz parte do Personal Finance Manager e segue a mesma licença do projeto principal.
