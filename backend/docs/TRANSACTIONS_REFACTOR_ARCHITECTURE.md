# 🔄 Refatoração da Arquitetura de Transações e Parcelas

## 🎯 Objetivo

Refatorar a arquitetura atual de transações e parcelas para eliminar bugs de duplicação, simplificar a lógica e melhorar a manutenibilidade do código.

## ❌ Problemas da Arquitetura Atual

### 1. **Complexidade Excessiva**
- Transações pai/filho com `parentTransactionId`
- Lógica condicional complexa para diferentes tipos
- Múltiplos endpoints para gerenciar parcelas
- Casos especiais e edge cases difíceis de manter

### 2. **Bugs Recorrentes**
- ✅ **Duplicação de parcelas** durante edição
- ✅ **Perda de associação** entre transações e parcelas
- ✅ **Erros 404** durante atualizações
- ✅ **Inconsistências** de estado

### 3. **Manutenibilidade Baixa**
- Código difícil de entender e debugar
- Testes complexos e frágeis
- Desenvolvimento lento de novas features
- Debugging demorado

## ✅ Nova Arquitetura Proposta

### 🏗️ **Estrutura Simplificada**

```sql
-- 1 Transação Principal
transactions {
  id: string (PK)
  description: string
  totalAmount: decimal
  totalInstallments: integer
  type: enum (INCOME, EXPENSE, TRANSFER)
  accountId: string (FK)
  categoryId: string (FK)
  destinationAccountId: string (FK, nullable)
  exchangeRate: decimal (nullable)
  transactionDate: datetime
  isFuture: boolean
  createdAt: datetime
  updatedAt: datetime
  deletedAt: datetime (nullable)
}

-- N Parcelas Vinculadas (sempre existe, mínimo 1)
installments {
  id: string (PK)
  transactionId: string (FK) -- OBRIGATÓRIO
  installmentNumber: integer -- 1, 2, 3...
  amount: decimal
  dueDate: datetime
  isPaid: boolean
  paidAt: datetime (nullable)
  description: string (nullable)
  createdAt: datetime
  updatedAt: datetime
}
```

### 📋 **Regras de Negócio**

1. **Relacionamento Obrigatório**: Toda transação TEM parcelas (mínimo 1)
2. **Transação à Vista**: 1 transação + 1 parcela
3. **Transação Parcelada**: 1 transação + N parcelas
4. **Update Strategy**: SEMPRE delete todas parcelas + create novas
5. **Operações Atômicas**: Tudo em transação de banco

### 🔄 **Fluxos Simplificados**

#### **Criar Transação**
```typescript
async createTransaction(data) {
  return db.transaction(async (tx) => {
    // 1. Criar transação principal
    const transaction = await tx.transaction.create({...})
    
    // 2. Criar parcelas
    for (let installment of data.installments) {
      await tx.installment.create({
        transactionId: transaction.id,
        installmentNumber: i + 1,
        amount: installment.amount,
        dueDate: installment.dueDate,
        isPaid: false
      })
    }
    
    return transaction
  })
}
```

#### **Atualizar Transação**
```typescript
async updateTransaction(id, data) {
  return db.transaction(async (tx) => {
    // 1. Atualizar transação principal
    await tx.transaction.update({where: {id}, data: {...}})
    
    // 2. DELETAR todas as parcelas antigas
    await tx.installment.deleteMany({where: {transactionId: id}})
    
    // 3. CRIAR todas as parcelas novas
    for (let installment of data.installments) {
      await tx.installment.create({...})
    }
    
    return transaction
  })
}
```

## 🎯 Benefícios da Nova Arquitetura

### ✅ **Simplicidade**
- Lógica linear e previsível
- Apenas 2 operações: CREATE e UPDATE
- Sem casos especiais ou condicionais complexas
- Código fácil de entender e manter

### ✅ **Robustez**
- Operações atômicas garantem consistência
- Impossível ter parcelas órfãs
- Impossível ter duplicação
- Estado sempre consistente

### ✅ **Performance**
- Operações em lote
- Menos round-trips ao banco
- Transações atômicas rápidas
- Queries otimizadas

### ✅ **Manutenibilidade**
- Código limpo e direto
- Testes simples e confiáveis
- Debugging fácil
- Desenvolvimento mais rápido

## 📋 Plano de Implementação

### **FASE 1: Preparação e Backup** ⏱️ *30 min*
- ✅ Backup completo do banco de dados
- ✅ Análise de dados existentes
- ✅ Validação do design do schema

### **FASE 2: Database Foundation** ⏱️ *45 min*
- ✅ Atualizar Prisma schema
- ✅ Criar migration para tabela `installments`
- ✅ Executar migração de dados existentes

### **FASE 3: Backend Core Rewrite** ⏱️ *90 min*
- ✅ Reescrever `TransactionService`
- ✅ Atualizar schemas de validação
- ✅ Simplificar controllers

### **FASE 4: Frontend Adaptation** ⏱️ *60 min*
- ✅ Atualizar types TypeScript
- ✅ Simplificar API layer
- ✅ Unificar componentes

### **FASE 5: Testing & Cleanup** ⏱️ *30 min*
- ✅ Testes funcionais completos
- ✅ Limpeza de código antigo
- ✅ Validação de performance

**Tempo Total Estimado**: 3.5 horas
**ROI**: Semanas de debugging evitadas

## 🛡️ Estratégias de Segurança

### **Backup e Rollback**
- Backup completo antes de iniciar
- Git branches para cada fase
- Scripts de rollback preparados
- Possibilidade de reverter a qualquer momento

### **Implementação Incremental**
- Cada fase tem checkpoints claros
- Validação após cada etapa
- Testes contínuos
- Rollback disponível entre fases

### **Validações**
- Integridade de dados garantida
- Operações atômicas
- Constraints de banco
- Validações de negócio

## 📊 Critérios de Sucesso

- ✅ Zero bugs de duplicação
- ✅ Performance igual ou melhor
- ✅ Código 50% mais simples
- ✅ Funcionalidades mantidas
- ✅ Testes passando
- ✅ Documentação atualizada

---

**Status**: 🔄 Em implementação
**TaskMaster-AI**: Tarefa #48 criada com 5 subtarefas
**Responsável**: Equipe de desenvolvimento
**Prazo**: 1 dia útil
