# <PERSON><PERSON><PERSON><PERSON> - Personal Finance Manager

## Visão Geral

O módulo de contas é responsável por gerenciar todas as contas financeiras dos usuários, incluindo contas correntes, poupança, cartões de crédito, investimentos, dinheiro em espécie e ativos.

## Funcionalidades Implementadas

### ✅ Tipos de Conta Suportados
- **CHECKING** - Conta Corrente
- **SAVINGS** - <PERSON><PERSON>  
- **INVESTMENT** - Conta de Investimento
- **CREDIT_CARD** - Cartão de Crédito
- **CASH** - Dinheiro em Espécie
- **ASSETS** - Ativos (imóveis, veículos, etc.)

### ✅ Recursos Principais

1. **CRUD Completo**
   - Criar, listar, visualizar, atualizar e deletar contas
   - Arquivar/desarquivar contas (soft delete)
   - Validação robusta com Zod

2. **Suporte a <PERSON>**
   - <PERSON><PERSON> suportadas: BRL, USD, EUR, GBP, JPY, CAD, AUD, CHF
   - Taxa de câmbio manual para moedas estrangeiras
   - Conversão automática para BRL (quando implementado)

3. **Cartões de Crédito**
   - Limite de crédito obrigatório
   - Cálculo de saldo disponível (limite - saldo atual)
   - Validações específicas

4. **Upload de Logos**
   - Suporte a arquivos SVG
   - Armazenamento em `/public/icons/brands/`
   - Validação de formato e tamanho (máx 1MB)

5. **Associação com Membros da Família**
   - Múltiplos membros por conta
   - Validação de existência dos membros

6. **Integridade de Dados**
   - Transações Prisma para operações críticas
   - Validações de integridade referencial
   - Prevenção de deleção com dependências

## Estrutura de Arquivos

```
src/
├── controllers/
│   └── account.controller.ts     # Controller com endpoints REST
├── services/
│   └── account.service.ts        # Lógica de negócio e operações Prisma
├── schemas/
│   └── account.schemas.ts        # Validações Zod e tipos TypeScript
├── routes/
│   └── account.routes.ts         # Definição das rotas Express
└── tests/
    └── account.test.ts           # Testes unitários e de integração
```

## API Endpoints

### Contas

| Método | Endpoint | Descrição |
|--------|----------|-----------|
| POST | `/api/v1/accounts` | Criar nova conta |
| GET | `/api/v1/accounts` | Listar contas com filtros |
| GET | `/api/v1/accounts/:id` | Obter conta por ID |
| PUT | `/api/v1/accounts/:id` | Atualizar conta |
| PATCH | `/api/v1/accounts/:id/archive` | Arquivar/desarquivar conta |
| DELETE | `/api/v1/accounts/:id` | Deletar conta permanentemente |
| POST | `/api/v1/accounts/:id/logo` | Upload de logo SVG |

### Parâmetros de Consulta (GET /accounts)

- `name` - Filtro por nome (busca parcial, case-insensitive)
- `type` - Filtro por tipo de conta
- `currency` - Filtro por moeda
- `includeInTotal` - Filtro por flag de inclusão no total
- `familyMemberId` - Filtro por membro da família
- `includeArchived` - Incluir contas arquivadas (padrão: false)
- `page` - Número da página (padrão: 1)
- `limit` - Itens por página (padrão: 20, máx: 100)

## Exemplos de Uso

### Criar Conta Corrente

```json
POST /api/v1/accounts
{
  "name": "Conta Corrente Banco do Brasil",
  "type": "CHECKING",
  "currency": "BRL",
  "includeInTotal": true,
  "familyMemberIds": ["cm123abc456def"]
}
```

### Criar Cartão de Crédito

```json
POST /api/v1/accounts
{
  "name": "Cartão Visa Platinum",
  "type": "CREDIT_CARD",
  "currency": "BRL",
  "creditLimit": 5000.00,
  "includeInTotal": false,
  "familyMemberIds": ["cm123abc456def"]
}
```

### Criar Conta em Moeda Estrangeira

```json
POST /api/v1/accounts
{
  "name": "Conta USD",
  "type": "CHECKING",
  "currency": "USD",
  "exchangeRate": 5.25,
  "includeInTotal": true,
  "familyMemberIds": ["cm123abc456def"]
}
```

### Upload de Logo

```bash
curl -X POST \
  http://localhost:3001/api/v1/accounts/cm123abc456def/logo \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "logo=@bank-logo.svg"
```

## Validações Implementadas

### Criação de Conta
- Nome obrigatório (2-100 caracteres)
- Tipo de conta válido
- Moeda suportada
- Limite de crédito obrigatório para cartões de crédito
- Taxa de câmbio obrigatória para moedas estrangeiras
- Pelo menos um membro da família associado

### Upload de Logo
- Apenas arquivos SVG
- Tamanho máximo de 1MB
- Extensão .svg obrigatória

### Atualização
- Validação de existência da conta
- Unicidade do nome (case-insensitive)
- Validações específicas por tipo de conta

## Tratamento de Erros

O módulo implementa tratamento robusto de erros:

- **Validação (400)** - Dados inválidos com detalhes específicos
- **Não Encontrado (404)** - Conta não existe
- **Conflito (409)** - Nome duplicado ou estado inválido
- **Erro de Integridade (400)** - Violação de restrições de dados
- **Erro Interno (500)** - Erros não tratados

## Próximos Passos

1. **Integração com Transações** - Cálculo real de saldos
2. **API de Taxas de Câmbio** - Atualização automática de taxas
3. **Histórico de Saldos** - Tracking de mudanças de saldo
4. **Relatórios** - Análises e insights sobre contas
5. **Backup de Logos** - Sistema de backup para arquivos SVG

## Testes

Execute os testes com:

```bash
npm test                    # Executar todos os testes
npm run test:watch         # Executar em modo watch
npm run test:coverage      # Executar com cobertura
```

## Dependências

- **Express.js** - Framework web
- **Prisma** - ORM para banco de dados
- **Zod** - Validação de schemas
- **Multer** - Upload de arquivos
- **TypeScript** - Tipagem estática
- **Jest** - Framework de testes
