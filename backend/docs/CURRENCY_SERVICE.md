# 💱 CurrencyService - Serviço de Conversão de Moedas

## Visão Geral

O `CurrencyService` é responsável por gerenciar conversões de moedas, formatação de valores monetários e cálculo de taxas de conversão. Suporta tanto taxas manuais quanto automáticas via API externa.

## Funcionalidades

### ✅ Implementadas

- **Conversão de moedas** com taxa manual ou automática
- **Formatação de valores** com localização adequada
- **Validação de códigos** de moeda (ISO 4217)
- **Cálculo de taxas** de conversão
- **Cache de taxas** para otimização
- **Tratamento de erros** robusto

## API do Serviço

### Métodos Principais

#### `convertAmount(params: ConvertAmountParams): Promise<ConversionResult>`

Converte um valor de uma moeda para outra.

```typescript
interface ConvertAmountParams {
  amount: number;
  fromCurrency: string;
  toCurrency: string;
  manualRate?: number;
}

interface ConversionResult {
  originalAmount: number;
  convertedAmount: number;
  exchangeRate: number;
  isManualRate: boolean;
  fromCurrency: string;
  toCurrency: string;
}
```

**Exemplo de uso:**
```typescript
const currencyService = new CurrencyService();

// Conversão com taxa manual
const result = await currencyService.convertAmount({
  amount: 100,
  fromCurrency: 'USD',
  toCurrency: 'BRL',
  manualRate: 5.25
});
// Resultado: { originalAmount: 100, convertedAmount: 525, exchangeRate: 5.25, isManualRate: true }

// Conversão com taxa automática
const result = await currencyService.convertAmount({
  amount: 100,
  fromCurrency: 'USD',
  toCurrency: 'BRL'
});
// Busca taxa atual da API
```

#### `getCurrentRate(fromCurrency: string, toCurrency: string): Promise<number>`

Obtém a taxa de câmbio atual entre duas moedas.

```typescript
const rate = await currencyService.getCurrentRate('USD', 'BRL');
console.log(rate); // 5.25
```

#### `getSupportedCurrencies(): string[]`

Retorna lista de moedas suportadas.

```typescript
const currencies = currencyService.getSupportedCurrencies();
console.log(currencies); // ['BRL', 'USD', 'EUR', 'GBP', 'JPY', ...]
```

#### `formatCurrency(amount: number, currency: string): string`

Formata um valor monetário com a localização adequada.

```typescript
const formatted = currencyService.formatCurrency(1234.56, 'BRL');
console.log(formatted); // "R$ 1.234,56"

const formatted = currencyService.formatCurrency(1234.56, 'USD');
console.log(formatted); // "$1,234.56"
```

#### `calculateConversionFee(amount: number, fromCurrency: string, toCurrency: string): number`

Calcula a taxa de conversão baseada no valor.

```typescript
const fee = currencyService.calculateConversionFee(1000, 'USD', 'BRL');
console.log(fee); // 15.00 (1.5% com mínimo de R$ 5)
```

## Configuração

### Variáveis de Ambiente

```env
# API de conversão de moedas (opcional)
EXCHANGE_RATE_API_KEY=your_api_key_here
EXCHANGE_RATE_API_URL=https://api.exchangerate-api.com/v4/latest

# Configurações de taxa
DEFAULT_CONVERSION_FEE_PERCENT=1.5
MIN_CONVERSION_FEE=5.00
MAX_CONVERSION_FEE=100.00
```

### Moedas Suportadas

```typescript
const SUPPORTED_CURRENCIES = [
  'BRL', // Real Brasileiro
  'USD', // Dólar Americano
  'EUR', // Euro
  'GBP', // Libra Esterlina
  'JPY', // Iene Japonês
  'CAD', // Dólar Canadense
  'AUD', // Dólar Australiano
  'CHF', // Franco Suíço
  'CNY', // Yuan Chinês
  'ARS'  // Peso Argentino
];
```

## Validações

### Códigos de Moeda
- Devem ter exatamente 3 caracteres
- Devem estar em maiúsculas
- Devem estar na lista de moedas suportadas

### Valores
- Devem ser números positivos
- Máximo de 2 casas decimais
- Não podem ser zero ou negativos

### Taxas de Câmbio
- Devem ser números positivos
- Taxa = 1.0 para conversões da mesma moeda
- Máximo de 6 casas decimais

## Tratamento de Erros

### Códigos de Erro

```typescript
enum CurrencyErrorCodes {
  INVALID_CURRENCY_CODE = 'INVALID_CURRENCY_CODE',
  UNSUPPORTED_CURRENCY = 'UNSUPPORTED_CURRENCY',
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  INVALID_EXCHANGE_RATE = 'INVALID_EXCHANGE_RATE',
  API_ERROR = 'API_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR'
}
```

### Exemplos de Tratamento

```typescript
try {
  const result = await currencyService.convertAmount({
    amount: 100,
    fromCurrency: 'USD',
    toCurrency: 'BRL'
  });
} catch (error) {
  if (error.code === 'INVALID_CURRENCY_CODE') {
    console.error('Código de moeda inválido');
  } else if (error.code === 'API_ERROR') {
    console.error('Erro na API de câmbio, usando taxa padrão');
    // Fallback para taxa manual
  }
}
```

## Cache e Performance

### Cache de Taxas
- Taxas são cacheadas por 5 minutos
- Cache em memória para desenvolvimento
- Redis recomendado para produção

### Otimizações
- Debounce para múltiplas consultas
- Batch requests para múltiplas moedas
- Fallback para taxas offline

## Integração com Transações

### Uso em Transferências

```typescript
// No TransactionService
const conversionResult = await this.currencyService.convertAmount({
  amount: transferData.amount,
  fromCurrency: sourceAccount.currency,
  toCurrency: destinationAccount.currency,
  manualRate: transferData.exchangeRate
});

// Atualizar transação com dados de conversão
transaction.sourceAmount = conversionResult.originalAmount;
transaction.destinationAmount = conversionResult.convertedAmount;
transaction.exchangeRate = conversionResult.exchangeRate;
```

## Testes

### Cobertura de Testes
- ✅ Conversão com taxa manual
- ✅ Conversão com taxa automática
- ✅ Validação de códigos de moeda
- ✅ Formatação de valores
- ✅ Cálculo de taxas
- ✅ Tratamento de erros
- ✅ Cache de taxas

### Executar Testes

```bash
npm test -- --testPathPattern="currency-conversion.test.ts"
```

## Arquivos Relacionados

- `src/services/currency.service.ts` - Implementação principal
- `src/tests/currency-conversion.test.ts` - Testes unitários
- `src/types/currency.types.ts` - Tipos TypeScript
- `src/config/currency.config.ts` - Configurações

## Próximos Passos

1. **Integração com Redis** para cache distribuído
2. **Múltiplas APIs** de câmbio para redundância
3. **Histórico de taxas** para análise
4. **Alertas de variação** de câmbio
5. **Dashboard de moedas** no frontend

---

**Última Atualização**: Dezembro 2024  
**Versão**: 1.0.0  
**Status**: ✅ Implementado e Testado
