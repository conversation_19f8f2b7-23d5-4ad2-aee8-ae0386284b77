# Database Schema Design

## Entity-Relationship Model

### Core Entities

#### 1. FamilyMembers
Representa os membros da família para organização de gastos e orçamentos.

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `name` (String) - Nome do membro
- `color` (String) - Cor para identificação visual
- Audit fields: `created_at`, `updated_at`, `deleted_at`, `version`

#### 2. Accounts
Contas financeiras (corrente, poupança, cartão de crédito, etc.).

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `name` (String) - Nome da conta
- `type` (Enum) - CHECKING, SAVINGS, INVESTMENT, CREDIT_CARD, CASH, ASSETS
- `currency` (String) - Código da moeda (BRL, USD, EUR, etc.)
- `credit_limit` (Decimal, nullable) - Limite para cartões de crédito
- `exchange_rate` (Decimal, nullable) - Taxa de câmbio manual para moedas estrangeiras
- `include_in_total` (Boolean) - Se deve ser incluída no total geral
- `logo_path` (String, nullable) - Caminho para logomarca SVG
- Audit fields: `created_at`, `updated_at`, `deleted_at`, `version`

#### 3. Categories
Categorias e subcategorias para classificação de transações.

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `name` (String) - Nome da categoria
- `color` (String, nullable) - Cor para identificação visual
- `parent_id` (UUID, FK, nullable) - Referência para categoria pai (hierarquia 2 níveis)
- Audit fields: `created_at`, `updated_at`, `deleted_at`, `version`

#### 4. Tags
Tags para marcação adicional de transações.

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `name` (String) - Nome da tag
- `color` (String) - Cor para identificação visual
- Audit fields: `created_at`, `updated_at`, `deleted_at`, `version`

#### 5. Transactions
Transações financeiras (despesas, receitas, transferências).

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `description` (String) - Descrição da transação
- `amount` (Decimal) - Valor da transação
- `transaction_date` (Date) - Data da transação
- `type` (Enum) - EXPENSE, INCOME, TRANSFER
- `account_id` (UUID, FK) - Conta de origem
- `category_id` (UUID, FK, nullable) - Categoria (obrigatória para despesas)
- `parent_transaction_id` (UUID, FK, nullable) - Transação pai (para parcelamento)
- `installment_number` (Int, nullable) - Número da parcela
- `total_installments` (Int, nullable) - Total de parcelas
- `exchange_rate` (Decimal, nullable) - Taxa de câmbio para transferências entre moedas
- `destination_account_id` (UUID, FK, nullable) - Conta destino (para transferências)
- `is_future` (Boolean) - Se é uma transação futura
- Audit fields: `created_at`, `updated_at`, `deleted_at`, `version`

#### 6. RecurringTransactions
Transações recorrentes agendadas.

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `description` (String) - Descrição da transação recorrente
- `fixed_amount` (Decimal, nullable) - Valor fixo
- `percentage_amount` (Decimal, nullable) - Percentual sobre saldo da conta
- `frequency` (Enum) - DAILY, WEEKLY, MONTHLY, YEARLY
- `start_date` (Date) - Data de início
- `end_date` (Date, nullable) - Data de fim
- `type` (Enum) - EXPENSE, INCOME, TRANSFER
- `account_id` (UUID, FK) - Conta
- `category_id` (UUID, FK, nullable) - Categoria
- `is_active` (Boolean) - Se está ativa
- Audit fields: `created_at`, `updated_at`, `deleted_at`, `version`

#### 7. Budgets
Orçamentos mensais por categoria e membro.

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `planned_amount` (Decimal) - Valor planejado
- `month` (Int) - Mês (1-12)
- `year` (Int) - Ano
- `category_id` (UUID, FK) - Categoria
- `family_member_id` (UUID, FK, nullable) - Membro da família (opcional)
- Audit fields: `created_at`, `updated_at`, `deleted_at`, `version`

#### 8. Goals
Metas financeiras.

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `name` (String) - Nome da meta
- `target_amount` (Decimal) - Valor alvo
- `current_amount` (Decimal) - Valor atual (atualizado manualmente)
- `target_date` (Date, nullable) - Data alvo
- Audit fields: `created_at`, `updated_at`, `deleted_at`, `version`

#### 9. GoalMilestones
Marcos das metas financeiras.

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `goal_id` (UUID, FK) - Meta associada
- `name` (String) - Nome do marco
- `target_amount` (Decimal) - Valor alvo do marco
- `target_date` (Date) - Data alvo do marco
- `is_completed` (Boolean) - Se foi completado
- Audit fields: `created_at`, `updated_at`, `version`

#### 10. AccountBalanceHistory
Histórico de saldos das contas (populado por job diário).

**Attributes:**
- `id` (UUID, PK) - Identificador único
- `account_id` (UUID, FK) - Conta
- `balance` (Decimal) - Saldo na data
- `balance_date` (Date) - Data do saldo
- `created_at` (DateTime) - Data de criação do registro

### Junction Tables (Many-to-Many Relationships)

#### AccountMembers
Relaciona contas com membros da família.

#### TransactionTags
Relaciona transações com tags.

#### TransactionMembers
Relaciona transações com membros da família.

#### GoalMembers
Relaciona metas com membros da família.

#### RecurringTransactionMembers
Relaciona transações recorrentes com membros da família.

## Key Design Decisions

1. **UUIDs como Primary Keys** - Para melhor distribuição e segurança
2. **Soft Delete** - Campo `deleted_at` para manter histórico
3. **Audit Fields** - Rastreamento completo de mudanças
4. **Hierarquia de Categorias** - Máximo 2 níveis (categoria → subcategoria)
5. **Flexibilidade de Moedas** - Suporte a múltiplas moedas com conversão
6. **Parcelamento** - Transações pai/filho para controle de parcelas
7. **Transações Futuras** - Flag para distinguir transações já realizadas das futuras
