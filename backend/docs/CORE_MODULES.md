# 🏗️ Core Modules Documentation

Documentação dos módulos fundamentais do Personal Finance Manager.

## 📂 Categories Module

### Funcionalidades
- ✅ **CRUD de Categorias** - <PERSON><PERSON><PERSON>, ler, atualizar e deletar categorias
- ✅ **Hierarquia de 2 Níveis** - Categorias pai e subcategorias
- ✅ **Validação de Hierarquia** - Previne loops e níveis excessivos
- ✅ **Soft Delete** - Exclusão lógica para auditoria
- ✅ **Cores Personalizadas** - Sistema de cores para organização visual

### API Endpoints
- `POST /api/v1/categories` - Criar categoria
- `GET /api/v1/categories` - Listar categorias (com hierarquia)
- `GET /api/v1/categories/:id` - Buscar categoria por ID
- `PUT /api/v1/categories/:id` - Atualizar categoria
- `DELETE /api/v1/categories/:id` - Deletar categoria

### Modelo de Dados
```prisma
model Category {
  id          String    @id @default(cuid())
  name        String
  color       String?
  parentId    String?   @map("parent_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  transactions Transaction[]
  budgets     Budget[]

  @@map("categories")
}
```

### Exemplo de Uso
```bash
POST /api/v1/categories
{
  "name": "Alimentação",
  "color": "#10B981"
}

# Criar subcategoria
POST /api/v1/categories
{
  "name": "Restaurantes",
  "color": "#059669",
  "parentId": "clx123456789"
}
```

---

## 🏷️ Tags Module

### Funcionalidades
- ✅ **CRUD de Tags** - Criar, ler, atualizar e deletar tags
- ✅ **Validação de Nome Único** - Previne duplicatas
- ✅ **Soft Delete** - Exclusão lógica para auditoria
- ✅ **Cores Personalizadas** - Sistema de cores para organização
- ✅ **Associação com Transações** - Many-to-many relationship

### API Endpoints
- `POST /api/v1/tags` - Criar tag
- `GET /api/v1/tags` - Listar tags
- `GET /api/v1/tags/:id` - Buscar tag por ID
- `PUT /api/v1/tags/:id` - Atualizar tag
- `DELETE /api/v1/tags/:id` - Deletar tag

### Modelo de Dados
```prisma
model Tag {
  id          String    @id @default(cuid())
  name        String    @unique
  color       String?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  transactions TransactionTag[]

  @@map("tags")
}
```

### Exemplo de Uso
```bash
POST /api/v1/tags
{
  "name": "Urgente",
  "color": "#EF4444"
}
```

---

## 💳 Transactions Module

### Funcionalidades
- ✅ **CRUD de Transações** - Criar, ler, atualizar e deletar transações
- ✅ **Tipos de Transação** - INCOME, EXPENSE, TRANSFER
- ✅ **Associação com Contas** - Vinculação obrigatória com conta
- ✅ **Categorização** - Associação com categorias
- ✅ **Tags Múltiplas** - Sistema de tags flexível
- ✅ **Membros da Família** - Associação opcional com membros
- ✅ **Validações de Saldo** - Verificação de saldo suficiente
- ✅ **Soft Delete** - Exclusão lógica para auditoria

### API Endpoints
- `POST /api/v1/transactions` - Criar transação
- `GET /api/v1/transactions` - Listar transações (com filtros)
- `GET /api/v1/transactions/:id` - Buscar transação por ID
- `PUT /api/v1/transactions/:id` - Atualizar transação
- `DELETE /api/v1/transactions/:id` - Deletar transação

### Modelo de Dados
```prisma
model Transaction {
  id               String      @id @default(cuid())
  amount           Decimal     @db.Decimal(15, 2)
  description      String
  transactionDate  DateTime    @map("transaction_date")
  type             TransactionType
  accountId        String      @map("account_id")
  categoryId       String      @map("category_id")
  createdAt        DateTime    @default(now()) @map("created_at")
  updatedAt        DateTime    @updatedAt @map("updated_at")
  deletedAt        DateTime?   @map("deleted_at")

  account    Account @relation(fields: [accountId], references: [id])
  category   Category @relation(fields: [categoryId], references: [id])
  tags       TransactionTag[]
  members    TransactionMember[]

  @@map("transactions")
}

enum TransactionType {
  INCOME
  EXPENSE
  TRANSFER
}
```

### Filtros Disponíveis
- **Por Período** - Data inicial e final
- **Por Tipo** - INCOME, EXPENSE, TRANSFER
- **Por Conta** - ID da conta específica
- **Por Categoria** - ID da categoria
- **Por Membro** - ID do membro da família
- **Por Tag** - IDs de tags
- **Por Valor** - Faixa de valores

### Exemplo de Uso
```bash
POST /api/v1/transactions
{
  "amount": 50.00,
  "description": "Almoço no restaurante",
  "transactionDate": "2024-12-01T12:00:00Z",
  "type": "EXPENSE",
  "accountId": "clx123456789",
  "categoryId": "clx987654321",
  "tagIds": ["clx111111111", "clx222222222"],
  "memberIds": ["clx333333333"]
}

# Listar com filtros
GET /api/v1/transactions?type=EXPENSE&startDate=2024-12-01&endDate=2024-12-31&page=1&limit=20
```

---

## 👥 Family Members Module

### Funcionalidades
- ✅ **CRUD de Membros** - Criar, ler, atualizar e deletar membros
- ✅ **Cores Personalizadas** - Sistema de cores para identificação
- ✅ **Soft Delete** - Exclusão lógica para auditoria
- ✅ **Associação com Transações** - Many-to-many relationship
- ✅ **Associação com Orçamentos** - Orçamentos por pessoa

### API Endpoints
- `POST /api/v1/family-members` - Criar membro
- `GET /api/v1/family-members` - Listar membros
- `GET /api/v1/family-members/:id` - Buscar membro por ID
- `PUT /api/v1/family-members/:id` - Atualizar membro
- `DELETE /api/v1/family-members/:id` - Deletar membro

### Modelo de Dados
```prisma
model FamilyMember {
  id        String    @id @default(cuid())
  name      String
  color     String
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  transactions TransactionMember[]
  budgets      Budget[]

  @@map("family_members")
}
```

### Exemplo de Uso
```bash
POST /api/v1/family-members
{
  "name": "João Silva",
  "color": "#3B82F6"
}
```

---

## 🔍 Category Suggestions Module

### Funcionalidades
- ✅ **Sugestões Inteligentes** - IA baseada em histórico
- ✅ **Múltiplos Algoritmos** - Similaridade de texto e padrões
- ✅ **Análise de Descrição** - Processamento de linguagem natural
- ✅ **Score de Confiança** - Ranking das sugestões
- ✅ **Aprendizado Contínuo** - Melhora com uso

### API Endpoints
- `POST /api/v1/category-suggestions` - Obter sugestões para transação

### Algoritmos Utilizados
- **Levenshtein Distance** - Similaridade de strings
- **Cosine Similarity** - Análise vetorial de texto
- **Frequency Analysis** - Padrões de uso histórico
- **Keyword Matching** - Correspondência de palavras-chave

### Exemplo de Uso
```bash
POST /api/v1/category-suggestions
{
  "description": "Pagamento conta de luz",
  "amount": 150.00,
  "type": "EXPENSE"
}

# Resposta
{
  "suggestions": [
    {
      "categoryId": "clx123456789",
      "categoryName": "Utilidades",
      "confidence": 0.95,
      "reason": "Similaridade alta com 'conta de luz'"
    }
  ]
}
```

---

## 🧪 Testes

### Cobertura Geral
- ✅ **Categories**: 100% cobertura de CRUD e hierarquia
- ✅ **Tags**: 100% cobertura de CRUD e validações
- ✅ **Transactions**: 100% cobertura de CRUD e filtros
- ✅ **Family Members**: 100% cobertura de CRUD
- ✅ **Category Suggestions**: 100% cobertura de algoritmos

### Executar Testes
```bash
# Todos os módulos core
npm test -- --testPathPattern="(category|tag|transaction|family-member|suggestion)"

# Módulo específico
npm test -- category.test.ts
npm test -- tag.test.ts
npm test -- transaction.test.ts
```

---

## 🔒 Segurança e Validações

### Validações Comuns
- **Autenticação JWT** - Todos os endpoints protegidos
- **Validação Zod** - Schemas rigorosos para entrada
- **Sanitização** - Limpeza de dados de entrada
- **Rate Limiting** - Proteção contra abuso
- **Soft Delete** - Preservação de dados para auditoria

### Regras de Negócio
- **Integridade Referencial** - Validação de relacionamentos
- **Prevenção de Loops** - Hierarquias de categorias
- **Unicidade** - Nomes únicos onde necessário
- **Validação de Datas** - Datas consistentes e válidas

---

**Status**: ✅ **Todos os Módulos Implementados e Testados**  
**Versão**: 1.0.0  
**Última Atualização**: Dezembro 2024
