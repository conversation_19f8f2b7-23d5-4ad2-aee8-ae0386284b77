const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testNewService() {
  try {
    console.log('🧪 Testando nova arquitetura de transações...');
    
    // Buscar dados existentes
    const accounts = await prisma.account.findMany({ take: 1 });
    const categories = await prisma.category.findMany({ take: 1 });
    const familyMembers = await prisma.familyMember.findMany({ take: 1 });
    
    if (accounts.length === 0 || categories.length === 0 || familyMembers.length === 0) {
      console.log('❌ Dados necessários não encontrados');
      return;
    }
    
    console.log('✅ Dados encontrados:', {
      accounts: accounts.length,
      categories: categories.length,
      familyMembers: familyMembers.length
    });
    
    // Teste 1: Criar transação simples (1 parcela)
    console.log('\n📝 Teste 1: Transação simples (1 parcela)');
    
    const transaction1 = await prisma.transaction.create({
      data: {
        description: 'Teste transação simples',
        totalAmount: 100.00,
        totalInstallments: 1,
        transactionDate: new Date(),
        type: 'EXPENSE',
        accountId: accounts[0].id,
        categoryId: categories[0].id,
        isFuture: false
      }
    });
    
    console.log('✅ Transação criada:', transaction1.id);
    
    // Criar parcela
    const installment1 = await prisma.installment.create({
      data: {
        transactionId: transaction1.id,
        installmentNumber: 1,
        amount: 100.00,
        dueDate: new Date(),
        isPaid: false,
        description: 'Teste transação simples - Parcela 1/1'
      }
    });
    
    console.log('✅ Parcela criada:', installment1.id);
    
    // Teste 2: Criar transação parcelada (3 parcelas)
    console.log('\n📝 Teste 2: Transação parcelada (3 parcelas)');
    
    const transaction2 = await prisma.transaction.create({
      data: {
        description: 'Teste transação parcelada',
        totalAmount: 300.00,
        totalInstallments: 3,
        transactionDate: new Date(),
        type: 'EXPENSE',
        accountId: accounts[0].id,
        categoryId: categories[0].id,
        isFuture: false
      }
    });
    
    console.log('✅ Transação parcelada criada:', transaction2.id);
    
    // Criar 3 parcelas
    for (let i = 1; i <= 3; i++) {
      const dueDate = new Date();
      dueDate.setMonth(dueDate.getMonth() + (i - 1));
      
      const installment = await prisma.installment.create({
        data: {
          transactionId: transaction2.id,
          installmentNumber: i,
          amount: 100.00,
          dueDate: dueDate,
          isPaid: i === 1, // Primeira parcela paga
          paidAt: i === 1 ? dueDate : null,
          description: `Teste transação parcelada - Parcela ${i}/3`
        }
      });
      
      console.log(`✅ Parcela ${i} criada:`, installment.id);
    }
    
    // Teste 3: Buscar transação com parcelas
    console.log('\n📝 Teste 3: Buscar transação com parcelas');
    
    const transactionWithInstallments = await prisma.transaction.findUnique({
      where: { id: transaction2.id },
      include: {
        installments: {
          orderBy: { installmentNumber: 'asc' }
        },
        account: true,
        category: true
      }
    });
    
    console.log('✅ Transação encontrada:', {
      id: transactionWithInstallments.id,
      description: transactionWithInstallments.description,
      totalAmount: Number(transactionWithInstallments.totalAmount),
      totalInstallments: transactionWithInstallments.totalInstallments,
      installments: transactionWithInstallments.installments.length
    });
    
    // Verificar integridade
    const installmentsSum = transactionWithInstallments.installments.reduce(
      (sum, inst) => sum + Number(inst.amount), 0
    );
    
    const totalAmount = Number(transactionWithInstallments.totalAmount);
    
    if (Math.abs(installmentsSum - totalAmount) < 0.01) {
      console.log('✅ Integridade verificada: soma das parcelas = total da transação');
    } else {
      console.log('❌ Erro de integridade:', { installmentsSum, totalAmount });
    }
    
    // Teste 4: Atualizar parcelas (DELETE + CREATE)
    console.log('\n📝 Teste 4: Atualizar parcelas (DELETE + CREATE)');
    
    // Deletar parcelas existentes
    await prisma.installment.deleteMany({
      where: { transactionId: transaction2.id }
    });
    
    console.log('✅ Parcelas antigas deletadas');
    
    // Atualizar transação
    await prisma.transaction.update({
      where: { id: transaction2.id },
      data: {
        totalAmount: 500.00,
        totalInstallments: 5,
        description: 'Teste transação parcelada ATUALIZADA'
      }
    });
    
    // Criar novas parcelas
    for (let i = 1; i <= 5; i++) {
      const dueDate = new Date();
      dueDate.setMonth(dueDate.getMonth() + (i - 1));
      
      await prisma.installment.create({
        data: {
          transactionId: transaction2.id,
          installmentNumber: i,
          amount: 100.00,
          dueDate: dueDate,
          isPaid: false,
          description: `Teste transação parcelada ATUALIZADA - Parcela ${i}/5`
        }
      });
    }
    
    console.log('✅ Novas parcelas criadas (5 parcelas de R$ 100,00)');
    
    // Verificar resultado final
    const updatedTransaction = await prisma.transaction.findUnique({
      where: { id: transaction2.id },
      include: {
        installments: {
          orderBy: { installmentNumber: 'asc' }
        }
      }
    });
    
    console.log('✅ Transação atualizada:', {
      id: updatedTransaction.id,
      description: updatedTransaction.description,
      totalAmount: Number(updatedTransaction.totalAmount),
      totalInstallments: updatedTransaction.totalInstallments,
      installments: updatedTransaction.installments.length
    });
    
    console.log('\n🎉 Todos os testes passaram! Nova arquitetura funcionando corretamente.');
    
  } catch (error) {
    console.error('❌ Erro durante teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testNewService();
