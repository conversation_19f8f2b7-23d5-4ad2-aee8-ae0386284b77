# Test Environment Configuration
NODE_ENV=test

# Database Configuration
DATABASE_URL=postgresql://test:test@localhost:5432/test_finance_db
DIRECT_URL=postgresql://test:test@localhost:5432/test_finance_db

# JWT Configuration
JWT_SECRET=test-jwt-secret-for-testing-only
JWT_EXPIRES_IN=1h

# Redis Configuration (will be overridden by redis-memory-server)
REDIS_URL=redis://localhost:6380
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=

# API Configuration
PORT=3001
API_VERSION=v1

# External APIs (mock endpoints for testing)
EXCHANGE_RATE_API_KEY=test-api-key
EXCHANGE_RATE_API_URL=https://api.exchangerate-api.com/v4/latest

# Logging Configuration
LOG_LEVEL=error
LOG_FORMAT=json

# Security Configuration
BCRYPT_ROUNDS=4
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads/test

# Email Configuration (mock for testing)
EMAIL_SERVICE=test
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=test-password
EMAIL_FROM=<EMAIL>

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_SIZE=100

# Feature Flags for Testing
ENABLE_ANALYTICS=false
ENABLE_NOTIFICATIONS=false
ENABLE_BACKGROUND_JOBS=true

# Test Database Cleanup
AUTO_CLEANUP_TEST_DATA=true
TEST_DATA_RETENTION_HOURS=1
