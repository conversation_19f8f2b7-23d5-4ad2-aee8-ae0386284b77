// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum AccountType {
  CHECKING
  SAVINGS
  INVESTMENT
  CREDIT_CARD
  CASH
  ASSETS
}

enum TransactionType {
  EXPENSE
  INCOME
  TRANSFER
}

enum RecurrenceFrequency {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
}

enum InsightType {
  SPENDING_PATTERN
  BUDGET_ALERT
  GOAL_PROGRESS
  ANOMALY_DETECTION
  TREND_ANALYSIS
  CATEGORY_ANALYSIS
  SAVINGS_OPPORTUNITY
  CASH_FLOW_ANALYSIS
  RECURRING_EXPENSE
  SEASONAL_PATTERN
}

enum InsightPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum InsightStatus {
  NEW
  VIEWED
  DISMISSED
  ACTED_UPON
}

// Core Models
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")
  version   Int      @default(1)

  @@map("users")
}

model FamilyMember {
  id        String   @id @default(cuid())
  name      String
  color     String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")
  version   Int      @default(1)

  // Relationships
  accounts            AccountMember[]
  transactionMembers  TransactionMember[]
  budgets             Budget[]
  goalMembers         GoalMember[]
  recurringTransactionMembers RecurringTransactionMember[]

  @@map("family_members")
}

model Account {
  id             String      @id @default(cuid())
  name           String
  type           AccountType
  currency       String      @default("BRL")
  currentBalance Decimal     @default(0) @map("current_balance") @db.Decimal(15, 2)
  creditLimit    Decimal?    @map("credit_limit") @db.Decimal(15, 2)
  exchangeRate   Decimal?    @map("exchange_rate") @db.Decimal(10, 6)
  includeInTotal Boolean     @default(true) @map("include_in_total")
  logoPath       String?     @map("logo_path")
  createdAt      DateTime    @default(now()) @map("created_at")
  updatedAt      DateTime    @updatedAt @map("updated_at")
  deletedAt      DateTime?   @map("deleted_at")
  version        Int         @default(1)

  // Relationships
  members                AccountMember[]
  transactions           Transaction[] @relation("AccountTransactions")
  transferDestinations   Transaction[] @relation("TransferDestinations")
  recurringTransactions  RecurringTransaction[]
  balanceHistory         AccountBalanceHistory[]
  insights               Insight[]

  // Performance indexes for dashboard queries
  @@index([type])
  @@index([currency])
  @@index([includeInTotal])
  @@index([type, currency, includeInTotal])
  @@index([deletedAt])
  @@map("accounts")
}

model Category {
  id        String   @id @default(cuid())
  name      String
  color     String?
  parentId  String?  @map("parent_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")
  version   Int      @default(1)

  // Self-referencing relationship for hierarchy
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")

  // Relationships
  transactions          Transaction[]
  budgets              Budget[]
  recurringTransactions RecurringTransaction[]
  insights             Insight[]

  // Performance indexes for dashboard queries
  @@index([parentId])
  @@index([parentId, name])
  @@index([deletedAt])
  @@map("categories")
}

model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  color     String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")
  version   Int      @default(1)

  // Relationships
  transactionTags TransactionTag[]

  @@map("tags")
}

model Transaction {
  id                   String          @id @default(cuid())
  description          String
  totalAmount          Decimal         @map("total_amount") @db.Decimal(15, 2)
  totalInstallments    Int             @default(1) @map("total_installments")
  transactionDate      DateTime        @map("transaction_date") @db.Date
  type                 TransactionType
  accountId            String          @map("account_id")
  categoryId           String?         @map("category_id")
  exchangeRate         Decimal?        @map("exchange_rate") @db.Decimal(10, 6)
  destinationAccountId String?         @map("destination_account_id")
  isFuture             Boolean         @default(false) @map("is_future")

  // Advanced transfer fields
  sourceCurrency       String?         @map("source_currency") @db.VarChar(3)
  destinationCurrency  String?         @map("destination_currency") @db.VarChar(3)
  sourceAmount         Decimal?        @map("source_amount") @db.Decimal(15, 2)
  destinationAmount    Decimal?        @map("destination_amount") @db.Decimal(15, 2)
  transferReference    String?         @map("transfer_reference")

  createdAt            DateTime        @default(now()) @map("created_at")
  updatedAt            DateTime        @updatedAt @map("updated_at")
  deletedAt            DateTime?       @map("deleted_at")
  version              Int             @default(1)

  // Relationships
  account            Account            @relation("AccountTransactions", fields: [accountId], references: [id])
  category           Category?          @relation(fields: [categoryId], references: [id])
  destinationAccount Account?           @relation("TransferDestinations", fields: [destinationAccountId], references: [id])
  installments       Installment[]      // Nova relação 1:N com parcelas
  tags               TransactionTag[]
  members            TransactionMember[]

  // Performance indexes for dashboard queries
  @@index([transactionDate])
  @@index([type])
  @@index([accountId])
  @@index([categoryId])
  @@index([type, transactionDate, accountId])
  @@index([isFuture, transactionDate])
  @@index([destinationAccountId])
  @@index([totalInstallments])
  @@index([deletedAt])
  @@map("transactions")
}

model Installment {
  id                String      @id @default(cuid())
  transactionId     String      @map("transaction_id")
  installmentNumber Int         @map("installment_number")
  amount            Decimal     @db.Decimal(15, 2)
  dueDate           DateTime    @map("due_date") @db.Date
  isPaid            Boolean     @default(false) @map("is_paid")
  paidAt            DateTime?   @map("paid_at")
  description       String?
  createdAt         DateTime    @default(now()) @map("created_at")
  updatedAt         DateTime    @updatedAt @map("updated_at")

  // Relationships
  transaction       Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  // Constraints and indexes
  @@unique([transactionId, installmentNumber])
  @@index([transactionId])
  @@index([dueDate])
  @@index([isPaid])
  @@index([transactionId, installmentNumber])
  @@index([dueDate, isPaid])
  @@map("installments")
}

model RecurringTransaction {
  id               String              @id @default(cuid())
  description      String
  fixedAmount      Decimal?            @map("fixed_amount") @db.Decimal(15, 2)
  percentageAmount Decimal?            @map("percentage_amount") @db.Decimal(5, 4)
  frequency        RecurrenceFrequency
  startDate        DateTime            @map("start_date") @db.Date
  endDate          DateTime?           @map("end_date") @db.Date
  type             TransactionType
  accountId        String              @map("account_id")
  categoryId       String?             @map("category_id")
  isActive         Boolean             @default(true) @map("is_active")
  createdAt        DateTime            @default(now()) @map("created_at")
  updatedAt        DateTime            @updatedAt @map("updated_at")
  deletedAt        DateTime?           @map("deleted_at")
  version          Int                 @default(1)

  // Relationships
  account  Account   @relation(fields: [accountId], references: [id])
  category Category? @relation(fields: [categoryId], references: [id])
  members  RecurringTransactionMember[]

  @@map("recurring_transactions")
}

model Budget {
  id             String   @id @default(cuid())
  plannedAmount  Decimal  @map("planned_amount") @db.Decimal(15, 2)
  month          Int
  year           Int
  categoryId     String   @map("category_id")
  familyMemberId String?  @map("family_member_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")
  version        Int      @default(1)

  // Relationships
  category     Category      @relation(fields: [categoryId], references: [id])
  familyMember FamilyMember? @relation(fields: [familyMemberId], references: [id])

  // Performance indexes for dashboard queries
  @@index([year, month])
  @@index([categoryId])
  @@index([familyMemberId])
  @@index([deletedAt])
  @@unique([categoryId, familyMemberId, month, year])
  @@map("budgets")
}

model Goal {
  id            String    @id @default(cuid())
  name          String
  targetAmount  Decimal   @map("target_amount") @db.Decimal(15, 2)
  currentAmount Decimal   @default(0) @map("current_amount") @db.Decimal(15, 2)
  targetDate    DateTime? @map("target_date") @db.Date
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")
  version       Int       @default(1)

  // Relationships
  milestones GoalMilestone[]
  members    GoalMember[]
  insights   Insight[]
  progressHistory GoalProgressHistory[]

  // Performance indexes for dashboard queries
  @@index([targetDate])
  @@index([targetDate, currentAmount, targetAmount])
  @@index([deletedAt])
  @@map("goals")
}

model GoalMilestone {
  id           String    @id @default(cuid())
  goalId       String    @map("goal_id")
  name         String
  targetAmount Decimal   @map("target_amount") @db.Decimal(15, 2)
  targetDate   DateTime  @map("target_date") @db.Date
  isCompleted  Boolean   @default(false) @map("is_completed")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  version      Int       @default(1)

  // Relationships
  goal Goal @relation(fields: [goalId], references: [id], onDelete: Cascade)

  @@map("goal_milestones")
}

model Insight {
  id          String         @id @default(cuid())
  type        InsightType
  priority    InsightPriority
  status      InsightStatus  @default(NEW)
  title       String
  description String

  // Data payload as JSON for flexibility
  data        Json?

  // Metadata
  categoryId  String?        @map("category_id")
  accountId   String?        @map("account_id")
  goalId      String?        @map("goal_id")

  // Time-based fields
  periodStart DateTime?      @map("period_start") @db.Date
  periodEnd   DateTime?      @map("period_end") @db.Date

  // Recommendations and actions
  recommendations Json?
  actionTaken     Boolean       @default(false) @map("action_taken")

  // Expiration and relevance
  expiresAt   DateTime?      @map("expires_at")
  relevanceScore Decimal?    @map("relevance_score") @db.Decimal(3, 2)

  // Audit fields
  createdAt   DateTime       @default(now()) @map("created_at")
  updatedAt   DateTime       @updatedAt @map("updated_at")
  deletedAt   DateTime?      @map("deleted_at")
  version     Int            @default(1)

  // Relationships
  category Category? @relation(fields: [categoryId], references: [id])
  account  Account?  @relation(fields: [accountId], references: [id])
  goal     Goal?     @relation(fields: [goalId], references: [id])

  // Performance indexes for insights queries
  @@index([type])
  @@index([priority])
  @@index([status])
  @@index([type, priority, status])
  @@index([createdAt])
  @@index([expiresAt])
  @@index([categoryId])
  @@index([accountId])
  @@index([goalId])
  @@index([deletedAt])
  @@map("insights")
}

model AccountBalanceHistory {
  id          String   @id @default(cuid())
  accountId   String   @map("account_id")
  balance     Decimal  @db.Decimal(15, 2)
  balanceDate DateTime @map("balance_date") @db.Date
  createdAt   DateTime @default(now()) @map("created_at")

  // Relationships
  account Account @relation(fields: [accountId], references: [id], onDelete: Cascade)

  // Performance indexes for dashboard queries
  @@index([balanceDate])
  @@index([balanceDate, accountId, balance])
  @@unique([accountId, balanceDate])
  @@map("account_balance_history")
}

model GoalProgressHistory {
  id             String   @id @default(cuid())
  goalId         String   @map("goal_id")
  previousAmount Decimal  @map("previous_amount") @db.Decimal(15, 2)
  newAmount      Decimal  @map("new_amount") @db.Decimal(15, 2)
  amountChanged  Decimal  @map("amount_changed") @db.Decimal(15, 2)
  operation      String   // 'add', 'subtract', 'set'
  description    String?
  createdAt      DateTime @default(now()) @map("created_at")

  // Relationships
  goal Goal @relation(fields: [goalId], references: [id], onDelete: Cascade)

  // Performance indexes
  @@index([goalId])
  @@index([createdAt])
  @@map("goal_progress_history")
}

// Junction Tables for Many-to-Many Relationships
model AccountMember {
  accountId      String   @map("account_id")
  familyMemberId String   @map("family_member_id")
  createdAt      DateTime @default(now()) @map("created_at")

  // Relationships
  account      Account      @relation(fields: [accountId], references: [id], onDelete: Cascade)
  familyMember FamilyMember @relation(fields: [familyMemberId], references: [id], onDelete: Cascade)

  // Performance indexes for dashboard queries
  @@index([accountId])
  @@index([familyMemberId])
  @@id([accountId, familyMemberId])
  @@map("account_members")
}

model TransactionTag {
  transactionId String   @map("transaction_id")
  tagId         String   @map("tag_id")
  createdAt     DateTime @default(now()) @map("created_at")

  // Relationships
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  tag         Tag         @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([transactionId, tagId])
  @@map("transaction_tags")
}

model TransactionMember {
  transactionId  String   @map("transaction_id")
  familyMemberId String   @map("family_member_id")
  createdAt      DateTime @default(now()) @map("created_at")

  // Relationships
  transaction  Transaction  @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  familyMember FamilyMember @relation(fields: [familyMemberId], references: [id], onDelete: Cascade)

  // Performance indexes for dashboard queries
  @@index([transactionId])
  @@index([familyMemberId])
  @@id([transactionId, familyMemberId])
  @@map("transaction_members")
}

model GoalMember {
  goalId         String   @map("goal_id")
  familyMemberId String   @map("family_member_id")
  createdAt      DateTime @default(now()) @map("created_at")

  // Relationships
  goal         Goal         @relation(fields: [goalId], references: [id], onDelete: Cascade)
  familyMember FamilyMember @relation(fields: [familyMemberId], references: [id], onDelete: Cascade)

  // Performance indexes for dashboard queries
  @@index([goalId])
  @@index([familyMemberId])
  @@id([goalId, familyMemberId])
  @@map("goal_members")
}

model RecurringTransactionMember {
  recurringTransactionId String   @map("recurring_transaction_id")
  familyMemberId         String   @map("family_member_id")
  createdAt              DateTime @default(now()) @map("created_at")

  // Relationships
  recurringTransaction RecurringTransaction @relation(fields: [recurringTransactionId], references: [id], onDelete: Cascade)
  familyMember         FamilyMember         @relation(fields: [familyMemberId], references: [id], onDelete: Cascade)

  @@id([recurringTransactionId, familyMemberId])
  @@map("recurring_transaction_members")
}
