-- CreateTable
CREATE TABLE "goal_progress_history" (
    "id" TEXT NOT NULL,
    "goal_id" TEXT NOT NULL,
    "previous_amount" DECIMAL(15,2) NOT NULL,
    "new_amount" DECIMAL(15,2) NOT NULL,
    "amount_changed" DECIMAL(15,2) NOT NULL,
    "operation" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "goal_progress_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "goal_progress_history_goal_id_idx" ON "goal_progress_history"("goal_id");

-- CreateIndex
CREATE INDEX "goal_progress_history_created_at_idx" ON "goal_progress_history"("created_at");

-- AddForeignKey
ALTER TABLE "goal_progress_history" ADD CONSTRAINT "goal_progress_history_goal_id_fkey" FOREIGN KEY ("goal_id") REFERENCES "goals"("id") ON DELETE CASCADE ON UPDATE CASCADE;
