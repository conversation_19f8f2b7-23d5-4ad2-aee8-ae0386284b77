const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function backupDatabase() {
  try {
    console.log('🔄 Iniciando backup do banco de dados...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(__dirname, '..', 'backups');
    
    // Criar diretório de backup se não existir
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const backupFile = path.join(backupDir, `backup_${timestamp}.json`);
    
    // Backup de todas as tabelas principais
    console.log('📊 Fazendo backup das transações...');
    const transactions = await prisma.transaction.findMany({
      include: {
        members: true,
        tags: true
      }
    });
    
    console.log('👥 Fazendo backup dos membros da família...');
    const familyMembers = await prisma.familyMember.findMany();
    
    console.log('🏦 Fazendo backup das contas...');
    const accounts = await prisma.account.findMany({
      include: {
        members: true
      }
    });
    
    console.log('📂 Fazendo backup das categorias...');
    const categories = await prisma.category.findMany();
    
    console.log('🏷️ Fazendo backup das tags...');
    const tags = await prisma.tag.findMany();
    
    // Backup opcional de tabelas que podem não existir
    let budgets = [];
    let goals = [];
    let recurringTransactions = [];

    try {
      console.log('💰 Fazendo backup dos orçamentos...');
      budgets = await prisma.budget.findMany();
    } catch (e) {
      console.log('⚠️ Tabela budgets não encontrada, pulando...');
    }

    try {
      console.log('🎯 Fazendo backup das metas...');
      goals = await prisma.financialGoal.findMany({
        include: {
          milestones: true
        }
      });
    } catch (e) {
      console.log('⚠️ Tabela financialGoal não encontrada, pulando...');
    }

    try {
      console.log('🔄 Fazendo backup das transações recorrentes...');
      recurringTransactions = await prisma.recurringTransaction.findMany();
    } catch (e) {
      console.log('⚠️ Tabela recurringTransaction não encontrada, pulando...');
    }
    
    // Estrutura do backup
    const backupData = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      tables: {
        transactions: {
          count: transactions.length,
          data: transactions
        },
        familyMembers: {
          count: familyMembers.length,
          data: familyMembers
        },
        accounts: {
          count: accounts.length,
          data: accounts
        },
        categories: {
          count: categories.length,
          data: categories
        },
        tags: {
          count: tags.length,
          data: tags
        },
        budgets: {
          count: budgets.length,
          data: budgets
        },
        goals: {
          count: goals.length,
          data: goals
        },
        recurringTransactions: {
          count: recurringTransactions.length,
          data: recurringTransactions
        }
      }
    };
    
    // Salvar backup
    fs.writeFileSync(backupFile, JSON.stringify(backupData, null, 2));
    
    console.log('✅ Backup concluído com sucesso!');
    console.log(`📁 Arquivo: ${backupFile}`);
    console.log(`📊 Estatísticas do backup:`);
    console.log(`   - Transações: ${transactions.length}`);
    console.log(`   - Membros da família: ${familyMembers.length}`);
    console.log(`   - Contas: ${accounts.length}`);
    console.log(`   - Categorias: ${categories.length}`);
    console.log(`   - Tags: ${tags.length}`);
    console.log(`   - Orçamentos: ${budgets.length}`);
    console.log(`   - Metas: ${goals.length}`);
    console.log(`   - Transações recorrentes: ${recurringTransactions.length}`);
    
    return backupFile;
    
  } catch (error) {
    console.error('❌ Erro durante backup:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar backup se chamado diretamente
if (require.main === module) {
  backupDatabase();
}

module.exports = { backupDatabase };
