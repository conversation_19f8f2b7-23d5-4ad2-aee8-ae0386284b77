const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateToInstallments() {
  try {
    console.log('🔄 Iniciando migração para nova arquitetura de parcelas...');
    
    // Como o banco foi resetado, vamos criar dados de exemplo para testar
    console.log('📊 Criando dados de exemplo para testar nova arquitetura...');
    
    // 1. Criar usuário de exemplo
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: '$2b$10$example', // Hash de exemplo
        name: 'Administrador'
      }
    });
    console.log(`✅ Usuário criado: ${user.name}`);
    
    // 2. Criar membros da família
    const familyMembers = await Promise.all([
      prisma.familyMember.create({
        data: { name: '<PERSON>', color: '#3B82F6' }
      }),
      prisma.familyMember.create({
        data: { name: '<PERSON>', color: '#EF4444' }
      }),
      prisma.familyMember.create({
        data: { name: '<PERSON>', color: '#10B981' }
      })
    ]);
    console.log(`✅ ${familyMembers.length} membros da família criados`);
    
    // 3. Criar contas
    const accounts = await Promise.all([
      prisma.account.create({
        data: {
          name: 'Conta Corrente',
          type: 'CHECKING',
          currency: 'BRL',
          currentBalance: 5000.00
        }
      }),
      prisma.account.create({
        data: {
          name: 'Cartão de Crédito',
          type: 'CREDIT_CARD',
          currency: 'BRL',
          currentBalance: -1200.00,
          creditLimit: 5000.00
        }
      }),
      prisma.account.create({
        data: {
          name: 'Poupança',
          type: 'SAVINGS',
          currency: 'BRL',
          currentBalance: 15000.00
        }
      })
    ]);
    console.log(`✅ ${accounts.length} contas criadas`);
    
    // 4. Criar categorias
    const categories = await Promise.all([
      prisma.category.create({
        data: { name: 'Alimentação', color: '#F59E0B' }
      }),
      prisma.category.create({
        data: { name: 'Transporte', color: '#8B5CF6' }
      }),
      prisma.category.create({
        data: { name: 'Moradia', color: '#06B6D4' }
      }),
      prisma.category.create({
        data: { name: 'Eletrônicos', color: '#EC4899' }
      })
    ]);
    console.log(`✅ ${categories.length} categorias criadas`);
    
    // 5. Criar transações com nova arquitetura
    console.log('💰 Criando transações de exemplo com nova arquitetura...');
    
    // Transação à vista (1 parcela)
    const transaction1 = await prisma.transaction.create({
      data: {
        description: 'Compra no supermercado',
        totalAmount: 150.00,
        totalInstallments: 1,
        transactionDate: new Date('2024-01-15'),
        type: 'EXPENSE',
        accountId: accounts[0].id,
        categoryId: categories[0].id
      }
    });
    
    // Criar parcela única
    await prisma.installment.create({
      data: {
        transactionId: transaction1.id,
        installmentNumber: 1,
        amount: 150.00,
        dueDate: new Date('2024-01-15'),
        isPaid: true,
        paidAt: new Date('2024-01-15'),
        description: 'Compra no supermercado'
      }
    });
    
    // Transação parcelada (3 parcelas)
    const transaction2 = await prisma.transaction.create({
      data: {
        description: 'Notebook Dell',
        totalAmount: 3000.00,
        totalInstallments: 3,
        transactionDate: new Date('2024-01-10'),
        type: 'EXPENSE',
        accountId: accounts[1].id,
        categoryId: categories[3].id
      }
    });
    
    // Criar 3 parcelas
    for (let i = 1; i <= 3; i++) {
      const dueDate = new Date('2024-01-10');
      dueDate.setMonth(dueDate.getMonth() + (i - 1));
      
      await prisma.installment.create({
        data: {
          transactionId: transaction2.id,
          installmentNumber: i,
          amount: 1000.00,
          dueDate: dueDate,
          isPaid: i === 1, // Primeira parcela paga
          paidAt: i === 1 ? dueDate : null,
          description: `Notebook Dell - Parcela ${i}/3`
        }
      });
    }
    
    // Transação parcelada (12 parcelas)
    const transaction3 = await prisma.transaction.create({
      data: {
        description: 'Financiamento do carro',
        totalAmount: 24000.00,
        totalInstallments: 12,
        transactionDate: new Date('2024-01-01'),
        type: 'EXPENSE',
        accountId: accounts[0].id,
        categoryId: categories[1].id
      }
    });
    
    // Criar 12 parcelas
    for (let i = 1; i <= 12; i++) {
      const dueDate = new Date('2024-01-01');
      dueDate.setMonth(dueDate.getMonth() + (i - 1));
      
      await prisma.installment.create({
        data: {
          transactionId: transaction3.id,
          installmentNumber: i,
          amount: 2000.00,
          dueDate: dueDate,
          isPaid: i <= 3, // Primeiras 3 parcelas pagas
          paidAt: i <= 3 ? dueDate : null,
          description: `Financiamento do carro - Parcela ${i}/12`
        }
      });
    }
    
    console.log('✅ Transações de exemplo criadas com nova arquitetura');
    
    // 6. Verificar integridade dos dados
    console.log('\n🔍 Verificando integridade dos dados...');
    
    const totalTransactions = await prisma.transaction.count();
    const totalInstallments = await prisma.installment.count();
    
    console.log(`   - Transações criadas: ${totalTransactions}`);
    console.log(`   - Parcelas criadas: ${totalInstallments}`);
    
    // Verificar se todas as transações têm parcelas
    const transactionsWithInstallments = await prisma.transaction.findMany({
      include: {
        installments: true
      }
    });
    
    let integrityCheck = true;
    transactionsWithInstallments.forEach(transaction => {
      const expectedInstallments = transaction.totalInstallments;
      const actualInstallments = transaction.installments.length;
      
      if (expectedInstallments !== actualInstallments) {
        console.log(`   ⚠️ Inconsistência: Transação ${transaction.id} esperava ${expectedInstallments} parcelas, mas tem ${actualInstallments}`);
        integrityCheck = false;
      }
      
      // Verificar se soma das parcelas = total da transação
      const installmentsSum = transaction.installments.reduce((sum, inst) => sum + Number(inst.amount), 0);
      const totalAmount = Number(transaction.totalAmount);
      
      if (Math.abs(installmentsSum - totalAmount) > 0.01) {
        console.log(`   ⚠️ Inconsistência: Transação ${transaction.id} total ${totalAmount} != soma parcelas ${installmentsSum}`);
        integrityCheck = false;
      }
    });
    
    if (integrityCheck) {
      console.log('   ✅ Integridade dos dados verificada com sucesso');
    } else {
      console.log('   ❌ Problemas de integridade encontrados');
    }
    
    console.log('\n🎉 Migração concluída com sucesso!');
    console.log('\n📋 RESUMO DA NOVA ARQUITETURA:');
    console.log('   - Cada transação tem relacionamento 1:N com parcelas');
    console.log('   - Transações à vista têm 1 parcela');
    console.log('   - Transações parceladas têm N parcelas');
    console.log('   - Operações sempre DELETE + CREATE para parcelas');
    console.log('   - Dados sempre consistentes e íntegros');
    
    return {
      totalTransactions,
      totalInstallments,
      integrityCheck
    };
    
  } catch (error) {
    console.error('❌ Erro durante migração:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar migração se chamado diretamente
if (require.main === module) {
  migrateToInstallments();
}

module.exports = { migrateToInstallments };
