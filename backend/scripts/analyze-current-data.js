const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function analyzeCurrentData() {
  try {
    console.log('🔍 Analisando estrutura atual de transações...');
    
    // 1. Análise geral de transações
    const totalTransactions = await prisma.transaction.count({
      where: { deletedAt: null }
    });
    
    const transactionsWithParent = await prisma.transaction.count({
      where: { 
        parentTransactionId: { not: null },
        deletedAt: null 
      }
    });
    
    const parentTransactions = await prisma.transaction.count({
      where: { 
        parentTransactionId: null,
        totalInstallments: { gt: 1 },
        deletedAt: null 
      }
    });
    
    const singleTransactions = await prisma.transaction.count({
      where: { 
        parentTransactionId: null,
        OR: [
          { totalInstallments: null },
          { totalInstallments: 1 }
        ],
        deletedAt: null 
      }
    });
    
    console.log('\n📊 ESTATÍSTICAS ATUAIS:');
    console.log(`   Total de transações: ${totalTransactions}`);
    console.log(`   Transações filhas (parcelas): ${transactionsWithParent}`);
    console.log(`   Transações pai (parceladas): ${parentTransactions}`);
    console.log(`   Transações à vista: ${singleTransactions}`);
    
    // 2. Análise detalhada de parcelas
    const installmentAnalysis = await prisma.transaction.findMany({
      where: {
        OR: [
          { parentTransactionId: { not: null } },
          { totalInstallments: { gt: 1 } }
        ],
        deletedAt: null
      },
      select: {
        id: true,
        description: true,
        amount: true,
        parentTransactionId: true,
        installmentNumber: true,
        totalInstallments: true,
        transactionDate: true
      },
      orderBy: [
        { parentTransactionId: 'asc' },
        { installmentNumber: 'asc' }
      ]
    });
    
    console.log('\n🔍 ANÁLISE DE PARCELAS:');
    
    // Agrupar por transação pai
    const groupedByParent = {};
    installmentAnalysis.forEach(transaction => {
      const parentId = transaction.parentTransactionId || transaction.id;
      if (!groupedByParent[parentId]) {
        groupedByParent[parentId] = [];
      }
      groupedByParent[parentId].push(transaction);
    });
    
    console.log(`   Grupos de parcelas encontrados: ${Object.keys(groupedByParent).length}`);
    
    // Verificar inconsistências
    let inconsistencies = 0;
    Object.entries(groupedByParent).forEach(([parentId, transactions]) => {
      const parentTx = transactions.find(t => !t.parentTransactionId);
      const childTxs = transactions.filter(t => t.parentTransactionId);
      
      if (parentTx && childTxs.length > 0) {
        const expectedTotal = parentTx.totalInstallments;
        const actualTotal = childTxs.length + 1; // +1 para incluir a transação pai
        
        if (expectedTotal !== actualTotal) {
          console.log(`   ⚠️ Inconsistência em ${parentId}: esperado ${expectedTotal}, encontrado ${actualTotal}`);
          inconsistencies++;
        }
      }
    });
    
    console.log(`   Inconsistências encontradas: ${inconsistencies}`);
    
    // 3. Análise de relacionamentos
    console.log('\n🔗 ANÁLISE DE RELACIONAMENTOS:');
    
    const transactionMembers = await prisma.transactionMember.count();
    const transactionTags = await prisma.transactionTag.count();
    
    console.log(`   Relacionamentos com membros: ${transactionMembers}`);
    console.log(`   Relacionamentos com tags: ${transactionTags}`);
    
    // 4. Preparar dados para migração
    console.log('\n📋 PREPARAÇÃO PARA MIGRAÇÃO:');
    
    const migrationData = {
      totalTransactions,
      transactionsWithParent,
      parentTransactions,
      singleTransactions,
      inconsistencies,
      transactionMembers,
      transactionTags,
      groupedTransactions: Object.keys(groupedByParent).length
    };
    
    console.log('   Dados preparados para migração ✅');
    
    // 5. Validar schema atual
    console.log('\n🏗️ VALIDAÇÃO DO SCHEMA ATUAL:');
    
    // Verificar se campos necessários existem
    const sampleTransaction = await prisma.transaction.findFirst({
      where: { deletedAt: null }
    });
    
    if (sampleTransaction) {
      const hasParentId = 'parentTransactionId' in sampleTransaction;
      const hasInstallmentNumber = 'installmentNumber' in sampleTransaction;
      const hasTotalInstallments = 'totalInstallments' in sampleTransaction;
      
      console.log(`   Campo parentTransactionId: ${hasParentId ? '✅' : '❌'}`);
      console.log(`   Campo installmentNumber: ${hasInstallmentNumber ? '✅' : '❌'}`);
      console.log(`   Campo totalInstallments: ${hasTotalInstallments ? '✅' : '❌'}`);
    }
    
    return migrationData;
    
  } catch (error) {
    console.error('❌ Erro durante análise:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar análise se chamado diretamente
if (require.main === module) {
  analyzeCurrentData();
}

module.exports = { analyzeCurrentData };
