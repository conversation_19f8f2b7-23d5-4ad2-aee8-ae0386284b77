const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function deleteAllTransactions() {
  try {
    console.log('🗑️  Iniciando exclusão de todas as transações...');

    // 1. Contar transações existentes
    const totalTransactions = await prisma.transaction.count({
      where: {
        deletedAt: null
      }
    });
    
    console.log(`📊 Total de transações ativas: ${totalTransactions}`);

    if (totalTransactions === 0) {
      console.log('✅ Não há transações para excluir.');
      return;
    }

    // 2. Deletar relacionamentos primeiro
    console.log('🔗 Removendo relacionamentos...');
    
    // Deletar transaction_members
    const deletedMembers = await prisma.transactionMember.deleteMany({});
    console.log(`   - ${deletedMembers.count} relacionamentos com membros removidos`);
    
    // Deletar transaction_tags
    const deletedTags = await prisma.transactionTag.deleteMany({});
    console.log(`   - ${deletedTags.count} relacionamentos com tags removidos`);

    // 3. Soft delete de todas as transações
    console.log('🗑️  Fazendo soft delete das transações...');
    const deletedTransactions = await prisma.transaction.updateMany({
      where: {
        deletedAt: null
      },
      data: {
        deletedAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log(`✅ ${deletedTransactions.count} transações marcadas como deletadas (soft delete)`);

    // 4. Opcional: Hard delete (remover permanentemente)
    console.log('💥 Fazendo hard delete (remoção permanente)...');
    const hardDeleted = await prisma.transaction.deleteMany({});
    console.log(`✅ ${hardDeleted.count} transações removidas permanentemente`);

    console.log('🎉 Todas as transações foram excluídas com sucesso!');

  } catch (error) {
    console.error('❌ Erro ao excluir transações:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
deleteAllTransactions();
