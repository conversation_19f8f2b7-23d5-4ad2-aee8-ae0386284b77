/**
 * Performance Quality Gates Script
 * Runs performance tests with strict thresholds for CI/CD pipeline
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');

// Test configuration with strict thresholds for quality gates
export const options = {
  stages: [
    { duration: '30s', target: 10 },  // Ramp up
    { duration: '1m', target: 20 },   // Stay at 20 users
    { duration: '30s', target: 0 },   // Ramp down
  ],
  
  // Quality gate thresholds
  thresholds: {
    // Response time thresholds
    'http_req_duration': ['p(95)<500', 'p(99)<1000'], // 95% under 500ms, 99% under 1s
    'http_req_duration{endpoint:auth}': ['p(95)<200'], // Auth should be fast
    'http_req_duration{endpoint:transactions}': ['p(95)<300'],
    'http_req_duration{endpoint:accounts}': ['p(95)<250'],
    
    // Error rate thresholds
    'http_req_failed': ['rate<0.01'], // Less than 1% errors
    'errors': ['rate<0.01'],
    
    // Throughput thresholds
    'http_reqs': ['rate>50'], // At least 50 requests per second
    
    // Custom metrics
    'response_time': ['p(95)<500'],
  },
  
  // Output results for CI/CD
  summaryTrendStats: ['avg', 'min', 'med', 'max', 'p(90)', 'p(95)', 'p(99)'],
  summaryTimeUnit: 'ms',
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api/v1`;

// Test data
let authToken = '';
let userId = '';
let accountId = '';

export function setup() {
  console.log('🚀 Setting up performance quality gates test...');
  
  // Create test user and get auth token
  const registerResponse = http.post(`${API_BASE}/auth/register`, JSON.stringify({
    name: 'Performance Test User',
    email: `perf-test-${Date.now()}@example.com`,
    password: 'password123'
  }), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  if (registerResponse.status === 201) {
    const userData = JSON.parse(registerResponse.body);
    authToken = userData.token;
    userId = userData.user.id;
    
    // Create test account
    const accountResponse = http.post(`${API_BASE}/accounts`, JSON.stringify({
      name: 'Performance Test Account',
      type: 'CHECKING',
      currency: 'BRL',
      balance: 1000.00
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
    });
    
    if (accountResponse.status === 201) {
      const accountData = JSON.parse(accountResponse.body);
      accountId = accountData.id;
    }
  }
  
  return { authToken, userId, accountId };
}

export default function(data) {
  const { authToken, userId, accountId } = data;
  
  // Test 1: Authentication endpoint
  const loginResponse = http.post(`${API_BASE}/auth/login`, JSON.stringify({
    email: `perf-test-${Date.now()}@example.com`,
    password: 'password123'
  }), {
    headers: { 'Content-Type': 'application/json' },
    tags: { endpoint: 'auth' },
  });
  
  const loginSuccess = check(loginResponse, {
    'login status is 200 or 404': (r) => r.status === 200 || r.status === 404,
    'login response time < 200ms': (r) => r.timings.duration < 200,
  });
  
  errorRate.add(!loginSuccess);
  responseTime.add(loginResponse.timings.duration);
  
  // Test 2: Get user profile
  if (authToken) {
    const profileResponse = http.get(`${API_BASE}/users/profile`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      tags: { endpoint: 'profile' },
    });
    
    const profileSuccess = check(profileResponse, {
      'profile status is 200': (r) => r.status === 200,
      'profile response time < 150ms': (r) => r.timings.duration < 150,
    });
    
    errorRate.add(!profileSuccess);
    responseTime.add(profileResponse.timings.duration);
  }
  
  // Test 3: Get accounts
  if (authToken) {
    const accountsResponse = http.get(`${API_BASE}/accounts`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      tags: { endpoint: 'accounts' },
    });
    
    const accountsSuccess = check(accountsResponse, {
      'accounts status is 200': (r) => r.status === 200,
      'accounts response time < 250ms': (r) => r.timings.duration < 250,
    });
    
    errorRate.add(!accountsSuccess);
    responseTime.add(accountsResponse.timings.duration);
  }
  
  // Test 4: Get transactions
  if (authToken && accountId) {
    const transactionsResponse = http.get(`${API_BASE}/transactions?accountId=${accountId}`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      tags: { endpoint: 'transactions' },
    });
    
    const transactionsSuccess = check(transactionsResponse, {
      'transactions status is 200': (r) => r.status === 200,
      'transactions response time < 300ms': (r) => r.timings.duration < 300,
    });
    
    errorRate.add(!transactionsSuccess);
    responseTime.add(transactionsResponse.timings.duration);
  }
  
  // Test 5: Create transaction
  if (authToken && accountId) {
    const createTransactionResponse = http.post(`${API_BASE}/transactions`, JSON.stringify({
      description: `Performance Test Transaction ${Date.now()}`,
      amount: -50.00,
      type: 'EXPENSE',
      accountId: accountId,
      date: new Date().toISOString()
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      tags: { endpoint: 'create_transaction' },
    });
    
    const createSuccess = check(createTransactionResponse, {
      'create transaction status is 201': (r) => r.status === 201,
      'create transaction response time < 400ms': (r) => r.timings.duration < 400,
    });
    
    errorRate.add(!createSuccess);
    responseTime.add(createTransactionResponse.timings.duration);
  }
  
  // Test 6: Dashboard data
  if (authToken) {
    const dashboardResponse = http.get(`${API_BASE}/dashboard/summary`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      tags: { endpoint: 'dashboard' },
    });
    
    const dashboardSuccess = check(dashboardResponse, {
      'dashboard status is 200': (r) => r.status === 200,
      'dashboard response time < 500ms': (r) => r.timings.duration < 500,
    });
    
    errorRate.add(!dashboardSuccess);
    responseTime.add(dashboardResponse.timings.duration);
  }
  
  sleep(1); // Wait 1 second between iterations
}

export function teardown(data) {
  console.log('🧹 Cleaning up performance test data...');
  
  // Clean up test data if needed
  const { authToken, userId, accountId } = data;
  
  if (authToken && accountId) {
    // Delete test account
    http.del(`${API_BASE}/accounts/${accountId}`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
    });
  }
  
  if (authToken && userId) {
    // Delete test user
    http.del(`${API_BASE}/users/${userId}`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
    });
  }
}

export function handleSummary(data) {
  console.log('📊 Generating performance quality gate results...');
  
  // Extract key metrics
  const results = {
    timestamp: new Date().toISOString(),
    metrics: {
      http_req_duration: {
        avg: data.metrics.http_req_duration.values.avg,
        p95: data.metrics.http_req_duration.values['p(95)'],
        p99: data.metrics.http_req_duration.values['p(99)'],
      },
      http_req_failed: {
        rate: data.metrics.http_req_failed.values.rate,
      },
      http_reqs: {
        rate: data.metrics.http_reqs.values.rate,
      },
      errors: {
        rate: data.metrics.errors ? data.metrics.errors.values.rate : 0,
      },
      response_time: {
        avg: data.metrics.response_time ? data.metrics.response_time.values.avg : 0,
        p95: data.metrics.response_time ? data.metrics.response_time.values['p(95)'] : 0,
      },
    },
    thresholds: data.thresholds,
    passed: Object.values(data.thresholds).every(threshold => !threshold.failed),
  };
  
  // Write results to file for CI/CD consumption
  return {
    'performance/results/quality-gate-results.json': JSON.stringify(results, null, 2),
    'stdout': generateSummaryReport(results),
  };
}

function generateSummaryReport(results) {
  const { metrics, passed } = results;
  
  return `
🎯 Performance Quality Gate Results
=====================================

📊 Key Metrics:
- Average Response Time: ${metrics.http_req_duration.avg.toFixed(2)}ms
- 95th Percentile: ${metrics.http_req_duration.p95.toFixed(2)}ms
- 99th Percentile: ${metrics.http_req_duration.p99.toFixed(2)}ms
- Error Rate: ${(metrics.http_req_failed.rate * 100).toFixed(2)}%
- Throughput: ${metrics.http_reqs.rate.toFixed(2)} req/s

🛡️ Quality Gates:
- Response Time P95 < 500ms: ${metrics.http_req_duration.p95 < 500 ? '✅ PASS' : '❌ FAIL'}
- Response Time P99 < 1000ms: ${metrics.http_req_duration.p99 < 1000 ? '✅ PASS' : '❌ FAIL'}
- Error Rate < 1%: ${metrics.http_req_failed.rate < 0.01 ? '✅ PASS' : '❌ FAIL'}
- Throughput > 50 req/s: ${metrics.http_reqs.rate > 50 ? '✅ PASS' : '❌ FAIL'}

🎉 Overall Result: ${passed ? '✅ ALL QUALITY GATES PASSED' : '❌ SOME QUALITY GATES FAILED'}
`;
}
