import prisma from '../lib/prisma';
import { Prisma } from '@prisma/client';
import {
  DashboardFilters,
  DashboardFiltersSchema,
  AccountBalanceAggregation,
  NetWorthAggregation,
  CreditCardUsage,
  ExpenseByCategory,
  ExpenseByMember,
  BudgetComparison,
  GoalProgress,
  DashboardOverview
} from '../schemas/dashboard.schemas';
import { dashboardCache } from './dashboard-cache.service';

/**
 * Cache configuration for different data types
 */
export interface CacheConfig {
  ttl: number; // Time to live in seconds
  key: string;
}

/**
 * Performance logging interface
 */
export interface PerformanceLog {
  operation: string;
  duration: number;
  timestamp: Date;
  filters?: DashboardFilters;
}

export class DashboardService {
  private performanceLogs: PerformanceLog[] = [];
  
  // Cache TTL configurations (in seconds)
  private readonly CACHE_CONFIG = {
    ACCOUNT_BALANCES: { ttl: 300, key: 'dashboard:account_balances' }, // 5 minutes
    NET_WORTH: { ttl: 600, key: 'dashboard:net_worth' }, // 10 minutes
    CREDIT_CARD_USAGE: { ttl: 300, key: 'dashboard:credit_usage' }, // 5 minutes
    EXPENSES_BY_CATEGORY: { ttl: 900, key: 'dashboard:expenses_category' }, // 15 minutes
    EXPENSES_BY_MEMBER: { ttl: 900, key: 'dashboard:expenses_member' }, // 15 minutes
    BUDGET_COMPARISON: { ttl: 600, key: 'dashboard:budget_comparison' }, // 10 minutes
    GOAL_PROGRESS: { ttl: 1800, key: 'dashboard:goal_progress' }, // 30 minutes
    OVERVIEW: { ttl: 300, key: 'dashboard:overview' } // 5 minutes
  };

  /**
   * Get complete dashboard overview with all aggregations
   */
  async getOverview(filters: DashboardFilters = {}): Promise<DashboardOverview> {
    const startTime = Date.now();

    try {
      // Validate input filters
      const validatedFilters = DashboardFiltersSchema.parse(filters);
      // Execute all aggregations in parallel for better performance
      const [
        accountBalances,
        netWorth,
        creditCardUsage,
        expensesByCategory,
        expensesByMember,
        budgetComparison,
        goalProgress
      ] = await Promise.all([
        this.getAccountBalances(validatedFilters),
        this.getNetWorth(validatedFilters),
        this.getCreditCardUsage(validatedFilters),
        this.getExpensesByCategory(validatedFilters),
        this.getExpensesByMember(validatedFilters),
        this.getBudgetComparison(validatedFilters),
        this.getGoalProgress(validatedFilters)
      ]);

      const overview: DashboardOverview = {
        accountBalances,
        netWorth,
        creditCardUsage,
        expensesByCategory,
        expensesByMember,
        budgetComparison,
        goalProgress,
        generatedAt: new Date().toISOString(),
        filters: validatedFilters
      };

      this.logPerformance('getOverview', Date.now() - startTime, filters);
      return overview;
    } catch (error) {
      this.logPerformance('getOverview_ERROR', Date.now() - startTime, filters);
      throw error;
    }
  }

  /**
   * Get account balances aggregation
   */
  async getAccountBalances(filters: DashboardFilters = {}): Promise<AccountBalanceAggregation> {
    const validatedFilters = this.validateFilters(filters);

    return dashboardCache.withCache('ACCOUNT_BALANCES', validatedFilters, async () => {
      const startTime = Date.now();
      // Build account filter
      const accountWhere: Prisma.AccountWhereInput = {
        deletedAt: null,
        includeInTotal: filters.includeInTotal ?? true
      };

      // Apply account filters
      if (validatedFilters.accountIds?.length) {
        accountWhere.id = { in: validatedFilters.accountIds };
      }

      if (validatedFilters.accountTypes?.length) {
        accountWhere.type = { in: validatedFilters.accountTypes };
      }

      if (validatedFilters.currencies?.length) {
        accountWhere.currency = { in: validatedFilters.currencies };
      }

      if (validatedFilters.familyMemberIds?.length) {
        accountWhere.members = {
          some: {
            familyMemberId: { in: validatedFilters.familyMemberIds }
          }
        };
      }

      // Get accounts with current balances
      const accounts = await prisma.account.findMany({
        where: accountWhere,
        select: {
          id: true,
          type: true,
          currency: true,
          currentBalance: true,
          exchangeRate: true,
          creditLimit: true,
          includeInTotal: true
        }
      });

      // Calculate current balances
      let totalBalance = 0;
      let totalBalanceInBRL = 0;
      const balanceByType = new Map<string, { balance: number; balanceInBRL: number; count: number }>();
      const balanceByCurrency = new Map<string, { balance: number; balanceInBRL: number; exchangeRate?: number; count: number }>();

      for (const account of accounts) {
        const balance = Number(account.currentBalance);
        const exchangeRate = this.getExchangeRate(account.exchangeRate);
        const balanceInBRL = this.convertToBRL(balance, account.currency, exchangeRate);

        if (account.includeInTotal) {
          totalBalance += balance;
          totalBalanceInBRL += balanceInBRL;
        }

        // Aggregate by type
        const typeKey = account.type;
        const typeData = balanceByType.get(typeKey) || { balance: 0, balanceInBRL: 0, count: 0 };
        typeData.balance += balance;
        typeData.balanceInBRL += balanceInBRL;
        typeData.count += 1;
        balanceByType.set(typeKey, typeData);

        // Aggregate by currency
        const currencyKey = account.currency;
        const currencyData = balanceByCurrency.get(currencyKey) || {
          balance: 0,
          balanceInBRL: 0,
          exchangeRate: account.currency === 'BRL' ? undefined : exchangeRate,
          count: 0
        };
        currencyData.balance += balance;
        currencyData.balanceInBRL += balanceInBRL;
        currencyData.count += 1;
        balanceByCurrency.set(currencyKey, currencyData);
      }

      // Calculate projected balances (including future transactions)
      const { projectedBalance, projectedBalanceInBRL } = await this.calculateProjectedBalances(validatedFilters, accounts);

      const result: AccountBalanceAggregation = {
        totalBalance: Number(totalBalance.toFixed(2)),
        totalBalanceInBRL: Number(totalBalanceInBRL.toFixed(2)),
        projectedBalance: Number(projectedBalance.toFixed(2)),
        projectedBalanceInBRL: Number(projectedBalanceInBRL.toFixed(2)),
        balanceByType: Array.from(balanceByType.entries()).map(([type, data]) => ({
          type: type as any,
          balance: Number(data.balance.toFixed(2)),
          balanceInBRL: Number(data.balanceInBRL.toFixed(2)),
          count: data.count
        })),
        balanceByCurrency: Array.from(balanceByCurrency.entries()).map(([currency, data]) => ({
          currency: currency as any,
          balance: Number(data.balance.toFixed(2)),
          balanceInBRL: Number(data.balanceInBRL.toFixed(2)),
          exchangeRate: data.exchangeRate,
          count: data.count
        }))
      };

      this.logPerformance('getAccountBalances', Date.now() - startTime, validatedFilters);
      return result;
    });
  }

  /**
   * Get net worth calculation
   */
  async getNetWorth(filters: DashboardFilters = {}): Promise<NetWorthAggregation> {
    const validatedFilters = this.validateFilters(filters);

    return dashboardCache.withCache('NET_WORTH', validatedFilters, async () => {
      const startTime = Date.now();
      // Build account filter
      const accountWhere: Prisma.AccountWhereInput = {
        deletedAt: null,
        includeInTotal: true
      };

      // Apply filters
      if (filters.accountIds?.length) {
        accountWhere.id = { in: filters.accountIds };
      }

      if (filters.familyMemberIds?.length) {
        accountWhere.members = {
          some: {
            familyMemberId: { in: filters.familyMemberIds }
          }
        };
      }

      // Get all accounts with balances
      const accounts = await prisma.account.findMany({
        where: accountWhere,
        select: {
          id: true,
          type: true,
          currency: true,
          currentBalance: true,
          exchangeRate: true,
          creditLimit: true
        }
      });

      // Calculate assets and liabilities
      const assets = {
        total: 0,
        checking: 0,
        savings: 0,
        investment: 0,
        cash: 0,
        other: 0
      };

      const liabilities = {
        total: 0,
        creditCards: 0,
        loans: 0,
        other: 0
      };

      for (const account of accounts) {
        const balance = Number(account.currentBalance);
        const exchangeRate = Number(account.exchangeRate) || 1;
        const balanceInBRL = account.currency === 'BRL' ? balance : balance * exchangeRate;

        // Categorize as asset or liability
        if (account.type === 'CREDIT_CARD') {
          // Credit cards are liabilities (negative balance = debt)
          const debt = Math.abs(Math.min(balance, 0)); // Only count negative balances as debt
          const debtInBRL = account.currency === 'BRL' ? debt : debt * exchangeRate;
          liabilities.creditCards += debtInBRL;
          liabilities.total += debtInBRL;
        } else {
          // Other account types are assets (positive balances)
          if (balance > 0) {
            assets.total += balanceInBRL;

            switch (account.type) {
              case 'CHECKING':
                assets.checking += balanceInBRL;
                break;
              case 'SAVINGS':
                assets.savings += balanceInBRL;
                break;
              case 'INVESTMENT':
                assets.investment += balanceInBRL;
                break;
              case 'CASH':
                assets.cash += balanceInBRL;
                break;
              default:
                assets.other += balanceInBRL;
                break;
            }
          }
        }
      }

      // Calculate current net worth
      const currentNetWorth = assets.total - liabilities.total;

      // Get monthly history (last 24 months)
      const monthlyHistory = await this.getNetWorthHistory(filters);

      const result: NetWorthAggregation = {
        currentNetWorth: Number(currentNetWorth.toFixed(2)),
        assets: {
          total: Number(assets.total.toFixed(2)),
          checking: Number(assets.checking.toFixed(2)),
          savings: Number(assets.savings.toFixed(2)),
          investment: Number(assets.investment.toFixed(2)),
          cash: Number(assets.cash.toFixed(2)),
          other: Number(assets.other.toFixed(2))
        },
        liabilities: {
          total: Number(liabilities.total.toFixed(2)),
          creditCards: Number(liabilities.creditCards.toFixed(2)),
          loans: Number(liabilities.loans.toFixed(2)),
          other: Number(liabilities.other.toFixed(2))
        },
        monthlyHistory
      };

      this.logPerformance('getNetWorth', Date.now() - startTime, validatedFilters);
      return result;
    });
  }

  /**
   * Get credit card usage analysis
   */
  async getCreditCardUsage(filters: DashboardFilters = {}): Promise<CreditCardUsage> {
    const validatedFilters = this.validateFilters(filters);

    return dashboardCache.withCache('CREDIT_CARD_USAGE', validatedFilters, async () => {
      const startTime = Date.now();
      // Build filter for credit card accounts only
      const accountWhere: Prisma.AccountWhereInput = {
        deletedAt: null,
        type: 'CREDIT_CARD'
      };

      // Apply filters
      if (filters.accountIds?.length) {
        accountWhere.id = { in: filters.accountIds };
      }

      if (filters.currencies?.length) {
        accountWhere.currency = { in: filters.currencies };
      }

      if (filters.familyMemberIds?.length) {
        accountWhere.members = {
          some: {
            familyMemberId: { in: filters.familyMemberIds }
          }
        };
      }

      // Get credit card accounts
      const creditCards = await prisma.account.findMany({
        where: accountWhere,
        select: {
          id: true,
          name: true,
          currency: true,
          currentBalance: true,
          creditLimit: true,
          exchangeRate: true
        }
      });

      let totalUsage = 0;
      let totalLimit = 0;
      const cardDetails = [];
      const alerts = [];

      for (const card of creditCards) {
        const currentBalance = Number(card.currentBalance);
        const creditLimit = Number(card.creditLimit) || 0;
        const exchangeRate = Number(card.exchangeRate) || 1;

        // For credit cards, we need to calculate usage correctly
        // If currentBalance is negative, it means there's debt (usage)
        // If currentBalance is positive, it means there's credit available (overpayment)
        const debt = Math.abs(Math.min(currentBalance, 0)); // Only negative balances count as debt
        const usage = debt; // Usage is the debt amount
        const usageInBRL = card.currency === 'BRL' ? usage : usage * exchangeRate;
        const limitInBRL = card.currency === 'BRL' ? creditLimit : creditLimit * exchangeRate;

        const usagePercentage = creditLimit > 0 ? (usage / creditLimit) * 100 : 0;
        const availableCredit = Math.max(creditLimit - usage, 0);
        const isNearLimit = usagePercentage >= 80; // 80% threshold

        totalUsage += usageInBRL;
        totalLimit += limitInBRL;

        cardDetails.push({
          accountId: card.id,
          accountName: card.name,
          currentBalance: Number(currentBalance.toFixed(2)), // Show actual balance (negative = debt)
          creditLimit: Number(creditLimit.toFixed(2)),
          usagePercentage: Number(usagePercentage.toFixed(2)),
          availableCredit: Number(availableCredit.toFixed(2)),
          isNearLimit,
          currency: card.currency as any
        });

        // Generate alerts
        if (usagePercentage >= 100) {
          alerts.push({
            accountId: card.id,
            accountName: card.name,
            alertType: 'OVER_LIMIT' as const,
            message: `Cartão ${card.name} está acima do limite (${usagePercentage.toFixed(1)}%)`,
            severity: 'HIGH' as const
          });
        } else if (usagePercentage >= 90) {
          alerts.push({
            accountId: card.id,
            accountName: card.name,
            alertType: 'NEAR_LIMIT' as const,
            message: `Cartão ${card.name} está próximo do limite (${usagePercentage.toFixed(1)}%)`,
            severity: 'HIGH' as const
          });
        } else if (usagePercentage >= 80) {
          alerts.push({
            accountId: card.id,
            accountName: card.name,
            alertType: 'HIGH_USAGE' as const,
            message: `Cartão ${card.name} tem alto uso (${usagePercentage.toFixed(1)}%)`,
            severity: 'MEDIUM' as const
          });
        }
      }

      const overallUsagePercentage = totalLimit > 0 ? (totalUsage / totalLimit) * 100 : 0;
      const totalAvailableCredit = Math.max(totalLimit - totalUsage, 0);

      const result: CreditCardUsage = {
        totalUsage: Number(totalUsage.toFixed(2)),
        totalLimit: Number(totalLimit.toFixed(2)),
        usagePercentage: Number(overallUsagePercentage.toFixed(2)),
        availableCredit: Number(totalAvailableCredit.toFixed(2)),
        cardDetails,
        alerts
      };

      this.logPerformance('getCreditCardUsage', Date.now() - startTime, validatedFilters);
      return result;
    });
  }

  /**
   * Get expenses breakdown by category
   */
  async getExpensesByCategory(filters: DashboardFilters = {}): Promise<ExpenseByCategory> {
    const startTime = Date.now();

    try {
      // Build transaction filter for expenses only
      const transactionWhere: Prisma.TransactionWhereInput = {
        deletedAt: null,
        type: 'EXPENSE',
        isFuture: false // Only actual transactions, not future ones
      };

      // Apply date filters
      const dateFilter = this.buildDateFilter(filters);
      if (dateFilter) {
        transactionWhere.transactionDate = dateFilter;
      }

      // Apply account filters
      if (filters.accountIds?.length) {
        transactionWhere.accountId = { in: filters.accountIds };
      }

      if (filters.accountTypes?.length) {
        transactionWhere.account = {
          type: { in: filters.accountTypes }
        };
      }

      // Apply category filters
      if (filters.categoryIds?.length) {
        transactionWhere.categoryId = { in: filters.categoryIds };
      }

      // Apply family member filters
      if (filters.familyMemberIds?.length) {
        transactionWhere.members = {
          some: {
            familyMemberId: { in: filters.familyMemberIds }
          }
        };
      }

      // Get expense transactions with category information
      const expenses = await prisma.transaction.findMany({
        where: transactionWhere,
        select: {
          id: true,
          totalAmount: true,
          transactionDate: true,
          account: {
            select: {
              currency: true,
              exchangeRate: true
            }
          },
          category: {
            select: {
              id: true,
              name: true,
              parentId: true,
              parent: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      // Calculate total expenses in BRL
      let totalExpenses = 0;
      const categoryMap = new Map<string, {
        categoryId: string;
        categoryName: string;
        parentCategoryId?: string;
        parentCategoryName?: string;
        amount: number;
        transactionCount: number;
        subcategories: Map<string, { categoryId: string; categoryName: string; amount: number; transactionCount: number }>;
      }>();

      for (const expense of expenses) {
        const amount = Number(expense.totalAmount);
        const exchangeRate = Number(expense.account.exchangeRate) || 1;
        const amountInBRL = expense.account.currency === 'BRL' ? amount : amount * exchangeRate;

        totalExpenses += amountInBRL;

        if (expense.category) {
          const category = expense.category;
          const isSubcategory = !!category.parentId;

          if (isSubcategory && category.parent) {
            // This is a subcategory
            const parentId = category.parent.id;
            const parentName = category.parent.name;

            // Ensure parent category exists in map
            if (!categoryMap.has(parentId)) {
              categoryMap.set(parentId, {
                categoryId: parentId,
                categoryName: parentName,
                amount: 0,
                transactionCount: 0,
                subcategories: new Map()
              });
            }

            const parentData = categoryMap.get(parentId)!;
            parentData.amount += amountInBRL;
            parentData.transactionCount += 1;

            // Add to subcategory
            if (!parentData.subcategories.has(category.id)) {
              parentData.subcategories.set(category.id, {
                categoryId: category.id,
                categoryName: category.name,
                amount: 0,
                transactionCount: 0
              });
            }

            const subcategoryData = parentData.subcategories.get(category.id)!;
            subcategoryData.amount += amountInBRL;
            subcategoryData.transactionCount += 1;
          } else {
            // This is a parent category
            if (!categoryMap.has(category.id)) {
              categoryMap.set(category.id, {
                categoryId: category.id,
                categoryName: category.name,
                amount: 0,
                transactionCount: 0,
                subcategories: new Map()
              });
            }

            const categoryData = categoryMap.get(category.id)!;
            categoryData.amount += amountInBRL;
            categoryData.transactionCount += 1;
          }
        }
      }

      // Convert to array and calculate percentages
      const categories = Array.from(categoryMap.values())
        .map(cat => ({
          categoryId: cat.categoryId,
          categoryName: cat.categoryName,
          parentCategoryId: cat.parentCategoryId,
          parentCategoryName: cat.parentCategoryName,
          amount: Number(cat.amount.toFixed(2)),
          percentage: this.calculatePercentage(cat.amount, totalExpenses),
          transactionCount: cat.transactionCount,
          subcategories: Array.from(cat.subcategories.values()).map(sub => ({
            categoryId: sub.categoryId,
            categoryName: sub.categoryName,
            amount: Number(sub.amount.toFixed(2)),
            percentage: this.calculatePercentage(sub.amount, cat.amount),
            transactionCount: sub.transactionCount
          })).sort((a, b) => b.amount - a.amount)
        }))
        .sort((a, b) => b.amount - a.amount);

      const result: ExpenseByCategory = {
        totalExpenses: Number(totalExpenses.toFixed(2)),
        categories
      };

      this.logPerformance('getExpensesByCategory', Date.now() - startTime, filters);
      return result;
    } catch (error) {
      this.logPerformance('getExpensesByCategory_ERROR', Date.now() - startTime, filters);
      throw error;
    }
  }

  /**
   * Get expenses breakdown by family member
   */
  async getExpensesByMember(filters: DashboardFilters = {}): Promise<ExpenseByMember> {
    const startTime = Date.now();

    try {
      // Build transaction filter for expenses only
      const transactionWhere: Prisma.TransactionWhereInput = {
        deletedAt: null,
        type: 'EXPENSE',
        isFuture: false
      };

      // Apply date filters
      const dateFilter = this.buildDateFilter(filters);
      if (dateFilter) {
        transactionWhere.transactionDate = dateFilter;
      }

      // Apply account filters
      if (filters.accountIds?.length) {
        transactionWhere.accountId = { in: filters.accountIds };
      }

      if (filters.accountTypes?.length) {
        transactionWhere.account = {
          type: { in: filters.accountTypes }
        };
      }

      // Apply category filters
      if (filters.categoryIds?.length) {
        transactionWhere.categoryId = { in: filters.categoryIds };
      }

      // Apply family member filters
      if (filters.familyMemberIds?.length) {
        transactionWhere.members = {
          some: {
            familyMemberId: { in: filters.familyMemberIds }
          }
        };
      }

      // Get expense transactions with member information
      const expenses = await prisma.transaction.findMany({
        where: transactionWhere,
        select: {
          id: true,
          totalAmount: true,
          transactionDate: true,
          account: {
            select: {
              currency: true,
              exchangeRate: true
            }
          },
          category: {
            select: {
              id: true,
              name: true
            }
          },
          members: {
            select: {
              familyMember: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      // Calculate expenses by member
      let totalExpenses = 0;
      const memberMap = new Map<string, {
        memberId: string;
        memberName: string;
        amount: number;
        transactionCount: number;
        categoryAmounts: Map<string, { categoryId: string; categoryName: string; amount: number; percentage: number }>;
      }>();

      for (const expense of expenses) {
        const amount = Number(expense.totalAmount);
        const exchangeRate = Number(expense.account.exchangeRate) || 1;
        const amountInBRL = expense.account.currency === 'BRL' ? amount : amount * exchangeRate;

        totalExpenses += amountInBRL;

        // If transaction has members, split the expense among them
        if (expense.members.length > 0) {
          const amountPerMember = amountInBRL / expense.members.length;

          for (const member of expense.members) {
            const memberId = member.familyMember.id;
            const memberName = member.familyMember.name;

            if (!memberMap.has(memberId)) {
              memberMap.set(memberId, {
                memberId,
                memberName,
                amount: 0,
                transactionCount: 0,
                categoryAmounts: new Map()
              });
            }

            const memberData = memberMap.get(memberId)!;
            memberData.amount += amountPerMember;
            memberData.transactionCount += 1;

            // Track category spending for this member
            if (expense.category) {
              const categoryId = expense.category.id;
              const categoryName = expense.category.name;

              if (!memberData.categoryAmounts.has(categoryId)) {
                memberData.categoryAmounts.set(categoryId, {
                  categoryId,
                  categoryName,
                  amount: 0,
                  percentage: 0
                });
              }

              const categoryData = memberData.categoryAmounts.get(categoryId)!;
              categoryData.amount += amountPerMember;
            }
          }
        }
      }

      // Calculate percentages and format data
      const members = Array.from(memberMap.values()).map(member => {
        const topCategories = Array.from(member.categoryAmounts.values())
          .map(cat => ({
            ...cat,
            amount: Number(cat.amount.toFixed(2)),
            percentage: this.calculatePercentage(cat.amount, member.amount)
          }))
          .sort((a, b) => b.amount - a.amount)
          .slice(0, 5); // Top 5 categories

        return {
          memberId: member.memberId,
          memberName: member.memberName,
          amount: Number(member.amount.toFixed(2)),
          percentage: this.calculatePercentage(member.amount, totalExpenses),
          transactionCount: member.transactionCount,
          averageTransactionAmount: Number((member.amount / member.transactionCount).toFixed(2)),
          topCategories
        };
      }).sort((a, b) => b.amount - a.amount);

      // Calculate comparison data
      const highestSpender = members[0] || { memberId: '', memberName: '', amount: 0 };
      const lowestSpender = members[members.length - 1] || { memberId: '', memberName: '', amount: 0 };
      const averagePerMember = members.length > 0 ? totalExpenses / members.length : 0;

      const result: ExpenseByMember = {
        totalExpenses: Number(totalExpenses.toFixed(2)),
        members,
        comparison: {
          highestSpender: {
            memberId: highestSpender.memberId,
            memberName: highestSpender.memberName,
            amount: highestSpender.amount
          },
          lowestSpender: {
            memberId: lowestSpender.memberId,
            memberName: lowestSpender.memberName,
            amount: lowestSpender.amount
          },
          averagePerMember: Number(averagePerMember.toFixed(2))
        }
      };

      this.logPerformance('getExpensesByMember', Date.now() - startTime, filters);
      return result;
    } catch (error) {
      this.logPerformance('getExpensesByMember_ERROR', Date.now() - startTime, filters);
      throw error;
    }
  }

  /**
   * Get budget vs actual comparison
   */
  async getBudgetComparison(filters: DashboardFilters = {}): Promise<BudgetComparison> {
    const startTime = Date.now();

    try {
      // Determine the period for budget comparison
      let month: number;
      let year: number;

      if (filters.period?.month && filters.period?.year) {
        month = filters.period.month;
        year = filters.period.year;
      } else {
        // Default to current month
        const now = new Date();
        month = now.getMonth() + 1;
        year = now.getFullYear();
      }

      // Build budget filter
      const budgetWhere: Prisma.BudgetWhereInput = {
        deletedAt: null,
        month,
        year
      };

      // Apply category filters
      if (filters.categoryIds?.length) {
        budgetWhere.categoryId = { in: filters.categoryIds };
      }

      // Apply family member filters
      if (filters.familyMemberIds?.length) {
        budgetWhere.familyMemberId = { in: filters.familyMemberIds };
      }

      // Get budgets for the period
      const budgets = await prisma.budget.findMany({
        where: budgetWhere,
        include: {
          category: {
            select: {
              id: true,
              name: true
            }
          },
          familyMember: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      // Build transaction filter for actual spending
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0, 23, 59, 59, 999);

      const transactionWhere: Prisma.TransactionWhereInput = {
        deletedAt: null,
        type: 'EXPENSE',
        isFuture: false,
        transactionDate: {
          gte: startDate,
          lte: endDate
        }
      };

      // Apply account filters
      if (filters.accountIds?.length) {
        transactionWhere.accountId = { in: filters.accountIds };
      }

      if (filters.accountTypes?.length) {
        transactionWhere.account = {
          type: { in: filters.accountTypes }
        };
      }

      // Get actual expenses for the period
      const expenses = await prisma.transaction.findMany({
        where: transactionWhere,
        select: {
          id: true,
          totalAmount: true,
          categoryId: true,
          account: {
            select: {
              currency: true,
              exchangeRate: true
            }
          },
          members: {
            select: {
              familyMemberId: true
            }
          }
        }
      });

      // Calculate actual spending by category and member
      const actualSpending = new Map<string, number>(); // key: categoryId:memberId or categoryId

      for (const expense of expenses) {
        const amount = Number(expense.totalAmount);
        const exchangeRate = Number(expense.account.exchangeRate) || 1;
        const amountInBRL = expense.account.currency === 'BRL' ? amount : amount * exchangeRate;

        if (expense.categoryId) {
          if (expense.members.length > 0) {
            // Split expense among members
            const amountPerMember = amountInBRL / expense.members.length;
            for (const member of expense.members) {
              const key = `${expense.categoryId}:${member.familyMemberId}`;
              actualSpending.set(key, (actualSpending.get(key) || 0) + amountPerMember);
            }
          } else {
            // No specific member, add to category total
            const key = expense.categoryId;
            actualSpending.set(key, (actualSpending.get(key) || 0) + amountInBRL);
          }
        }
      }

      // Compare budgets with actual spending
      let totalBudgeted = 0;
      let totalSpent = 0;
      const categories = [];
      const alerts = [];

      for (const budget of budgets) {
        const budgeted = Number(budget.plannedAmount);
        const key = budget.familyMemberId
          ? `${budget.categoryId}:${budget.familyMemberId}`
          : budget.categoryId;
        const spent = actualSpending.get(key) || 0;
        const remaining = budgeted - spent;
        const progress = budgeted > 0 ? (spent / budgeted) * 100 : 0;

        totalBudgeted += budgeted;
        totalSpent += spent;

        // Determine status
        let status: 'ON_TRACK' | 'NEAR_LIMIT' | 'OVER_BUDGET';
        if (progress >= 100) {
          status = 'OVER_BUDGET';
        } else if (progress >= 90) {
          status = 'NEAR_LIMIT';
        } else {
          status = 'ON_TRACK';
        }

        // Calculate projected spend (assuming linear spending throughout the month)
        const daysInMonth = new Date(year, month, 0).getDate();
        const currentDay = new Date().getDate();
        const projectedSpend = month === new Date().getMonth() + 1 && year === new Date().getFullYear()
          ? (spent / currentDay) * daysInMonth
          : spent;

        categories.push({
          categoryId: budget.categoryId,
          categoryName: budget.category.name,
          budgeted: Number(budgeted.toFixed(2)),
          spent: Number(spent.toFixed(2)),
          remaining: Number(remaining.toFixed(2)),
          progress: Number(progress.toFixed(2)),
          status,
          projectedSpend: Number(projectedSpend.toFixed(2))
        });

        // Generate alerts
        if (status === 'OVER_BUDGET') {
          alerts.push({
            categoryId: budget.categoryId,
            categoryName: budget.category.name,
            alertType: 'OVER_BUDGET' as const,
            message: `Orçamento de ${budget.category.name} foi ultrapassado em ${((progress - 100)).toFixed(1)}%`,
            severity: 'HIGH' as const
          });
        } else if (status === 'NEAR_LIMIT') {
          alerts.push({
            categoryId: budget.categoryId,
            categoryName: budget.category.name,
            alertType: 'NEAR_BUDGET' as const,
            message: `Orçamento de ${budget.category.name} está próximo do limite (${progress.toFixed(1)}%)`,
            severity: 'MEDIUM' as const
          });
        } else if (projectedSpend > budgeted) {
          alerts.push({
            categoryId: budget.categoryId,
            categoryName: budget.category.name,
            alertType: 'PROJECTED_OVERSPEND' as const,
            message: `Projeção indica que orçamento de ${budget.category.name} será ultrapassado`,
            severity: 'MEDIUM' as const
          });
        }
      }

      const totalRemaining = totalBudgeted - totalSpent;
      const overallProgress = totalBudgeted > 0 ? (totalSpent / totalBudgeted) * 100 : 0;

      const result: BudgetComparison = {
        totalBudgeted: Number(totalBudgeted.toFixed(2)),
        totalSpent: Number(totalSpent.toFixed(2)),
        totalRemaining: Number(totalRemaining.toFixed(2)),
        overallProgress: Number(overallProgress.toFixed(2)),
        categories: categories.sort((a, b) => b.spent - a.spent),
        alerts
      };

      this.logPerformance('getBudgetComparison', Date.now() - startTime, filters);
      return result;
    } catch (error) {
      this.logPerformance('getBudgetComparison_ERROR', Date.now() - startTime, filters);
      throw error;
    }
  }

  /**
   * Get financial goals progress
   */
  async getGoalProgress(filters: DashboardFilters = {}): Promise<GoalProgress> {
    const startTime = Date.now();

    try {
      // Build goal filter
      const goalWhere: Prisma.GoalWhereInput = {
        deletedAt: null
      };

      // Apply goal filters
      if (filters.goalIds?.length) {
        goalWhere.id = { in: filters.goalIds };
      }

      // Apply family member filters
      if (filters.familyMemberIds?.length) {
        goalWhere.members = {
          some: {
            familyMemberId: { in: filters.familyMemberIds }
          }
        };
      }

      // Get goals with their milestones
      const goals = await prisma.goal.findMany({
        where: goalWhere,
        include: {
          milestones: {
            orderBy: {
              targetDate: 'asc'
            }
          },
          members: {
            include: {
              familyMember: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      const now = new Date();
      let totalGoals = goals.length;
      let activeGoals = 0;
      let completedGoals = 0;
      let totalTargetAmount = 0;
      let totalCurrentAmount = 0;
      let onTrackGoals = 0;
      let behindScheduleGoals = 0;

      const goalProgress = goals.map(goal => {
        const targetAmount = Number(goal.targetAmount);
        const currentAmount = Number(goal.currentAmount);
        const progress = targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0;
        const remaining = Math.max(targetAmount - currentAmount, 0);

        totalTargetAmount += targetAmount;
        totalCurrentAmount += currentAmount;

        // Calculate time-related metrics
        let daysRemaining: number | undefined;
        let estimatedCompletionDate: string | undefined;
        let monthlyRequiredAmount: number | undefined;
        let isOnTrack = true;
        let status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'OVERDUE';

        if (progress >= 100) {
          status = 'COMPLETED';
          completedGoals++;
        } else if (currentAmount > 0) {
          status = 'IN_PROGRESS';
          activeGoals++;
        } else {
          status = 'NOT_STARTED';
          activeGoals++;
        }

        if (goal.targetDate) {
          const targetDate = new Date(goal.targetDate);
          daysRemaining = Math.ceil((targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

          if (daysRemaining < 0 && progress < 100) {
            status = 'OVERDUE';
            isOnTrack = false;
            behindScheduleGoals++;
          } else if (daysRemaining > 0) {
            // Calculate if on track based on current progress vs time elapsed
            const totalDays = Math.ceil((targetDate.getTime() - new Date(goal.createdAt).getTime()) / (1000 * 60 * 60 * 24));
            const daysElapsed = totalDays - daysRemaining;
            const expectedProgress = totalDays > 0 ? (daysElapsed / totalDays) * 100 : 0;

            isOnTrack = progress >= expectedProgress * 0.8; // Allow 20% tolerance

            if (isOnTrack) {
              onTrackGoals++;
            } else {
              behindScheduleGoals++;
            }

            // Calculate monthly required amount
            const monthsRemaining = daysRemaining / 30;
            if (monthsRemaining > 0) {
              monthlyRequiredAmount = remaining / monthsRemaining;
            }

            // Estimate completion date based on current progress rate
            if (currentAmount > 0 && daysElapsed > 0) {
              const dailyRate = currentAmount / daysElapsed;
              if (dailyRate > 0) {
                const daysToComplete = remaining / dailyRate;
                estimatedCompletionDate = new Date(now.getTime() + daysToComplete * 24 * 60 * 60 * 1000).toISOString();
              }
            }
          } else {
            onTrackGoals++;
          }
        } else {
          // No target date, consider on track if making progress
          if (currentAmount > 0) {
            onTrackGoals++;
          } else {
            behindScheduleGoals++;
          }
        }

        return {
          goalId: goal.id,
          goalName: goal.name,
          targetAmount: Number(targetAmount.toFixed(2)),
          currentAmount: Number(currentAmount.toFixed(2)),
          progress: Number(progress.toFixed(2)),
          remaining: Number(remaining.toFixed(2)),
          targetDate: goal.targetDate?.toISOString(),
          daysRemaining,
          estimatedCompletionDate,
          isOnTrack,
          monthlyRequiredAmount: monthlyRequiredAmount ? Number(monthlyRequiredAmount.toFixed(2)) : undefined,
          status
        };
      });

      const overallProgress = totalTargetAmount > 0 ? (totalCurrentAmount / totalTargetAmount) * 100 : 0;

      const result: GoalProgress = {
        totalGoals,
        activeGoals,
        completedGoals,
        goals: goalProgress.sort((a, b) => {
          // Sort by status priority, then by progress
          const statusPriority = { 'OVERDUE': 0, 'IN_PROGRESS': 1, 'NOT_STARTED': 2, 'COMPLETED': 3 };
          const aPriority = statusPriority[a.status];
          const bPriority = statusPriority[b.status];

          if (aPriority !== bPriority) {
            return aPriority - bPriority;
          }

          return b.progress - a.progress;
        }),
        summary: {
          totalTargetAmount: Number(totalTargetAmount.toFixed(2)),
          totalCurrentAmount: Number(totalCurrentAmount.toFixed(2)),
          overallProgress: Number(overallProgress.toFixed(2)),
          onTrackGoals,
          behindScheduleGoals
        }
      };

      this.logPerformance('getGoalProgress', Date.now() - startTime, filters);
      return result;
    } catch (error) {
      this.logPerformance('getGoalProgress_ERROR', Date.now() - startTime, filters);
      throw error;
    }
  }

  /**
   * Get net worth history for the last 24 months
   */
  private async getNetWorthHistory(filters: DashboardFilters): Promise<Array<{
    month: string;
    netWorth: number;
    assets: number;
    liabilities: number;
    change: number;
    changePercentage: number;
  }>> {
    // Get the last 24 months of balance history
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 24);

    // Get account balance history grouped by month
    const balanceHistory = await prisma.accountBalanceHistory.findMany({
      where: {
        balanceDate: {
          gte: startDate,
          lte: endDate
        },
        account: {
          deletedAt: null,
          includeInTotal: true
        }
      },
      include: {
        account: {
          select: {
            type: true,
            currency: true,
            exchangeRate: true
          }
        }
      },
      orderBy: {
        balanceDate: 'asc'
      }
    });

    // Group by month and calculate net worth
    const monthlyData = new Map<string, { assets: number; liabilities: number }>();

    for (const history of balanceHistory) {
      const monthKey = history.balanceDate.toISOString().substring(0, 7); // YYYY-MM
      const balance = Number(history.balance);
      const exchangeRate = Number(history.account.exchangeRate) || 1;
      const balanceInBRL = history.account.currency === 'BRL' ? balance : balance * exchangeRate;

      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, { assets: 0, liabilities: 0 });
      }

      const monthData = monthlyData.get(monthKey)!;

      if (history.account.type === 'CREDIT_CARD') {
        // Credit card debt (negative balance)
        const debt = Math.abs(Math.min(balance, 0));
        const debtInBRL = history.account.currency === 'BRL' ? debt : debt * exchangeRate;
        monthData.liabilities += debtInBRL;
      } else if (balance > 0) {
        // Positive asset balance
        monthData.assets += balanceInBRL;
      }
    }

    // Convert to array and calculate changes
    const result = Array.from(monthlyData.entries())
      .map(([month, data]) => ({
        month,
        netWorth: data.assets - data.liabilities,
        assets: data.assets,
        liabilities: data.liabilities,
        change: 0,
        changePercentage: 0
      }))
      .sort((a, b) => a.month.localeCompare(b.month));

    // Calculate month-over-month changes
    for (let i = 1; i < result.length; i++) {
      const current = result[i];
      const previous = result[i - 1];
      current.change = current.netWorth - previous.netWorth;
      current.changePercentage = previous.netWorth !== 0
        ? (current.change / Math.abs(previous.netWorth)) * 100
        : 0;
    }

    return result.slice(-24).map(item => ({
      ...item,
      netWorth: Number(item.netWorth.toFixed(2)),
      assets: Number(item.assets.toFixed(2)),
      liabilities: Number(item.liabilities.toFixed(2)),
      change: Number(item.change.toFixed(2)),
      changePercentage: Number(item.changePercentage.toFixed(2))
    }));
  }

  /**
   * Calculate projected balances including future transactions
   */
  private async calculateProjectedBalances(
    filters: DashboardFilters,
    accounts: Array<{ id: string; currency: string; exchangeRate: Prisma.Decimal | null; currentBalance: Prisma.Decimal }>
  ): Promise<{ projectedBalance: number; projectedBalanceInBRL: number }> {
    const accountIds = accounts.map(a => a.id);

    // Get future transactions for these accounts
    const futureTransactions = await prisma.transaction.findMany({
      where: {
        accountId: { in: accountIds },
        isFuture: true,
        deletedAt: null,
        transactionDate: {
          gte: new Date() // Only future dates
        }
      },
      select: {
        accountId: true,
        totalAmount: true,
        type: true,
        destinationAccountId: true,
        sourceAmount: true,
        destinationAmount: true
      }
    });

    // Calculate projected balances
    let projectedBalance = 0;
    let projectedBalanceInBRL = 0;

    for (const account of accounts) {
      const currentBalance = Number(account.currentBalance);
      const exchangeRate = Number(account.exchangeRate) || 1;

      // Calculate future transaction impact for this account
      let futureImpact = 0;

      for (const transaction of futureTransactions) {
        if (transaction.accountId === account.id) {
          // This account is the source
          const amount = Number(transaction.totalAmount);
          if (transaction.type === 'EXPENSE') {
            futureImpact -= amount;
          } else if (transaction.type === 'INCOME') {
            futureImpact += amount;
          } else if (transaction.type === 'TRANSFER') {
            // For transfers, use sourceAmount if available
            const transferAmount = transaction.sourceAmount ? Number(transaction.sourceAmount) : amount;
            futureImpact -= transferAmount;
          }
        } else if (transaction.destinationAccountId === account.id) {
          // This account is the destination of a transfer
          const transferAmount = transaction.destinationAmount ? Number(transaction.destinationAmount) : Number(transaction.totalAmount);
          futureImpact += transferAmount;
        }
      }

      const accountProjectedBalance = currentBalance + futureImpact;
      const accountProjectedBalanceInBRL = account.currency === 'BRL'
        ? accountProjectedBalance
        : accountProjectedBalance * exchangeRate;

      projectedBalance += accountProjectedBalance;
      projectedBalanceInBRL += accountProjectedBalanceInBRL;
    }

    return { projectedBalance, projectedBalanceInBRL };
  }

  /**
   * Utility method to build date range filter for Prisma queries
   * Handles timezone properly by using UTC dates
   */
  protected buildDateFilter(filters: DashboardFilters): Prisma.DateTimeFilter | undefined {
    if (filters.dateRange) {
      const filter: Prisma.DateTimeFilter = {};

      if (filters.dateRange.startDate) {
        // Parse as UTC to avoid timezone issues
        const startDate = new Date(filters.dateRange.startDate);
        // Set to start of day in UTC
        startDate.setUTCHours(0, 0, 0, 0);
        filter.gte = startDate;
      }

      if (filters.dateRange.endDate) {
        // Parse as UTC to avoid timezone issues
        const endDate = new Date(filters.dateRange.endDate);
        // Set to end of day in UTC
        endDate.setUTCHours(23, 59, 59, 999);
        filter.lte = endDate;
      }

      return filter;
    }

    if (filters.period) {
      const { month, year } = filters.period;

      if (year && month) {
        // Create dates in UTC to avoid timezone issues
        const startDate = new Date(Date.UTC(year, month - 1, 1, 0, 0, 0, 0));
        const endDate = new Date(Date.UTC(year, month, 0, 23, 59, 59, 999));

        return {
          gte: startDate,
          lte: endDate
        };
      }

      if (year) {
        // Create dates in UTC to avoid timezone issues
        const startDate = new Date(Date.UTC(year, 0, 1, 0, 0, 0, 0));
        const endDate = new Date(Date.UTC(year, 11, 31, 23, 59, 59, 999));

        return {
          gte: startDate,
          lte: endDate
        };
      }
    }

    return undefined;
  }

  /**
   * Utility method to format currency values
   */
  protected formatCurrency(amount: number, currency = 'BRL'): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency
    }).format(amount);
  }

  /**
   * Utility method to calculate percentage
   */
  protected calculatePercentage(value: number, total: number): number {
    if (total === 0) return 0;
    return Number(((value / total) * 100).toFixed(2));
  }

  /**
   * Utility method to safely convert currency with fallback
   */
  protected convertToBRL(amount: number, currency: string, exchangeRate: number | null): number {
    if (currency === 'BRL') {
      return amount;
    }

    // Use provided exchange rate or fallback to 1 (with warning)
    const rate = exchangeRate || 1;

    if (!exchangeRate) {
      console.warn(`Missing exchange rate for ${currency}, using rate 1.0 as fallback`);
    }

    return amount * rate;
  }

  /**
   * Utility method to safely get exchange rate
   */
  protected getExchangeRate(exchangeRate: any): number {
    if (exchangeRate === null || exchangeRate === undefined) {
      return 1;
    }

    const rate = Number(exchangeRate);
    return isNaN(rate) || rate <= 0 ? 1 : rate;
  }

  /**
   * Utility method to validate and parse filters
   */
  protected validateFilters(filters: DashboardFilters): DashboardFilters {
    try {
      return DashboardFiltersSchema.parse(filters);
    } catch (error) {
      console.warn('Invalid dashboard filters provided, using defaults:', error);
      return {};
    }
  }

  /**
   * Optimize query performance using relationLoadStrategy
   */
  protected getOptimizedQueryConfig(includeRelations: boolean = false): { relationLoadStrategy?: 'query' | 'join' } {
    // Use 'join' strategy for better performance when including relations
    // Use 'query' strategy for simple queries without relations
    return includeRelations ? { relationLoadStrategy: 'join' } : {};
  }

  /**
   * Get database performance metrics
   */
  async getDatabaseMetrics(): Promise<{
    slowQueries: PerformanceLog[];
    averageQueryTime: number;
    totalQueries: number;
    cacheHitRate?: number;
  }> {
    const slowQueries = this.performanceLogs.filter(log => log.duration > 1000);
    const totalQueries = this.performanceLogs.length;
    const averageQueryTime = totalQueries > 0
      ? this.performanceLogs.reduce((sum, log) => sum + log.duration, 0) / totalQueries
      : 0;

    return {
      slowQueries,
      averageQueryTime: Number(averageQueryTime.toFixed(2)),
      totalQueries,
      // cacheHitRate will be implemented in subtask 14.12
    };
  }

  /**
   * Optimize specific query based on filters
   */
  protected optimizeQuery<T extends Record<string, any>>(
    baseWhere: T,
    filters: DashboardFilters
  ): T & { relationLoadStrategy?: 'query' | 'join' } {
    // Add performance optimizations based on filter complexity
    const hasComplexFilters = !!(
      filters.accountIds?.length ||
      filters.categoryIds?.length ||
      filters.familyMemberIds?.length ||
      filters.dateRange ||
      filters.period
    );

    return {
      ...baseWhere,
      ...(hasComplexFilters ? { relationLoadStrategy: 'join' as const } : {})
    };
  }

  /**
   * Execute query with performance monitoring
   */
  protected async executeWithMonitoring<T>(
    operation: string,
    queryFn: () => Promise<T>,
    filters?: DashboardFilters
  ): Promise<T> {
    const startTime = Date.now();

    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;

      this.logPerformance(operation, duration, filters);

      // Log warning for slow queries
      if (duration > 2000) {
        console.warn(`🐌 Slow dashboard query detected: ${operation} took ${duration}ms`);
        console.warn('Consider optimizing this query or adding specific indexes');
      }

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logPerformance(`${operation}_ERROR`, duration, filters);
      throw error;
    }
  }

  /**
   * Log performance metrics
   */
  private logPerformance(operation: string, duration: number, filters?: DashboardFilters): void {
    const log: PerformanceLog = {
      operation,
      duration,
      timestamp: new Date(),
      filters
    };
    
    this.performanceLogs.push(log);
    
    // Keep only last 100 logs to prevent memory issues
    if (this.performanceLogs.length > 100) {
      this.performanceLogs = this.performanceLogs.slice(-100);
    }
    
    // Log slow operations (> 1 second)
    if (duration > 1000) {
      console.warn(`Slow dashboard operation: ${operation} took ${duration}ms`, { filters });
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceLog[] {
    return [...this.performanceLogs];
  }

  /**
   * Clear performance logs
   */
  clearPerformanceLogs(): void {
    this.performanceLogs = [];
  }

  /**
   * Get cache configuration for a specific data type
   */
  getCacheConfig(type: keyof typeof this.CACHE_CONFIG): CacheConfig {
    return this.CACHE_CONFIG[type];
  }
}

export const dashboardService = new DashboardService();
