import { redisCache } from '../lib/redis';
import { DashboardFilters } from '../schemas/dashboard.schemas';
import crypto from 'crypto';

/**
 * Dashboard-specific cache service with intelligent invalidation
 */
export class DashboardCacheService {
  private readonly keyPrefix = 'dashboard:';
  private readonly defaultTTL = 300; // 5 minutes
  
  // Cache TTL configurations (in seconds)
  private readonly cacheTTL = {
    ACCOUNT_BALANCES: 300,     // 5 minutes
    NET_WORTH: 600,            // 10 minutes
    CREDIT_CARD_USAGE: 300,    // 5 minutes
    EXPENSES_BY_CATEGORY: 900, // 15 minutes
    EXPENSES_BY_MEMBER: 900,   // 15 minutes
    BUDGET_COMPARISON: 600,    // 10 minutes
    GOAL_PROGRESS: 1800,       // 30 minutes
    OVERVIEW: 300              // 5 minutes
  };

  /**
   * Generate cache key from filters
   */
  private generateCacheKey(operation: string, filters: DashboardFilters): string {
    // Create a deterministic hash of the filters
    const filtersString = JSON.stringify(filters, Object.keys(filters).sort());
    const hash = crypto.createHash('md5').update(filtersString).digest('hex');
    return `${this.keyPrefix}${operation}:${hash}`;
  }

  /**
   * Generate invalidation pattern for related cache entries
   */
  private getInvalidationPattern(operation?: string): string {
    if (operation) {
      return `${this.keyPrefix}${operation}:*`;
    }
    return `${this.keyPrefix}*`;
  }

  /**
   * Get cached data with fallback
   */
  async get<T>(operation: keyof typeof this.cacheTTL, filters: DashboardFilters): Promise<T | null> {
    if (!redisCache.isAvailable()) {
      return null;
    }

    try {
      const key = this.generateCacheKey(operation, filters);
      const cached = await redisCache.get<{
        data: T;
        timestamp: number;
        filters: DashboardFilters;
      }>(key);

      if (cached) {
        // Check if cache is still valid (additional validation)
        const age = Date.now() - cached.timestamp;
        const maxAge = this.cacheTTL[operation] * 1000;
        
        if (age <= maxAge) {
          console.log(`Cache HIT for ${operation} (age: ${Math.round(age / 1000)}s)`);
          return cached.data;
        } else {
          // Cache expired, remove it
          await redisCache.del(key);
          console.log(`Cache EXPIRED for ${operation} (age: ${Math.round(age / 1000)}s)`);
        }
      }

      console.log(`Cache MISS for ${operation}`);
      return null;
    } catch (error) {
      console.warn(`Dashboard cache get error for ${operation}:`, error);
      return null;
    }
  }

  /**
   * Set cached data with TTL
   */
  async set<T>(
    operation: keyof typeof this.cacheTTL,
    filters: DashboardFilters,
    data: T
  ): Promise<boolean> {
    if (!redisCache.isAvailable()) {
      return false;
    }

    try {
      const key = this.generateCacheKey(operation, filters);
      const ttl = this.cacheTTL[operation];

      const cacheData = {
        data,
        timestamp: Date.now(),
        filters
      };

      const success = await redisCache.set(key, cacheData, ttl);
      
      if (success) {
        console.log(`Cache SET for ${operation} (TTL: ${ttl}s)`);
      } else {
        console.warn(`Cache SET failed for ${operation}`);
      }
      
      return success;
    } catch (error) {
      console.warn(`Dashboard cache set error for ${operation}:`, error);
      return false;
    }
  }

  /**
   * Invalidate cache for specific operation
   */
  async invalidate(operation?: keyof typeof this.cacheTTL): Promise<number> {
    try {
      const pattern = this.getInvalidationPattern(operation);
      const deletedCount = await redisCache.delPattern(pattern);
      
      if (deletedCount > 0) {
        console.log(`Cache INVALIDATED: ${deletedCount} keys for pattern "${pattern}"`);
      }
      
      return deletedCount;
    } catch (error) {
      console.warn(`Dashboard cache invalidation error:`, error);
      return 0;
    }
  }

  /**
   * Invalidate all dashboard cache
   */
  async invalidateAll(): Promise<number> {
    return this.invalidate();
  }

  /**
   * Warm up cache with common queries
   */
  async warmUp(commonFilters: DashboardFilters[] = [{}]): Promise<void> {
    console.log('Dashboard cache warm-up started...');
    
    // This would typically be called after cache invalidation
    // to pre-populate cache with common queries
    for (const filters of commonFilters) {
      // The actual warming would be done by calling the dashboard service methods
      // which would populate the cache as a side effect
      console.log(`Warming cache for filters:`, filters);
    }
    
    console.log('Dashboard cache warm-up completed');
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    isAvailable: boolean;
    hitRate?: number;
    keyCount?: number;
    memoryUsage?: string;
    operations: Record<string, { ttl: number; keyPattern: string }>;
  }> {
    const stats: {
      isAvailable: boolean;
      hitRate?: number;
      keyCount?: number;
      memoryUsage?: string;
      operations: Record<string, { ttl: number; keyPattern: string }>;
    } = {
      isAvailable: redisCache.isAvailable(),
      operations: {} as Record<string, { ttl: number; keyPattern: string }>
    };

    // Add operation info
    for (const [operation, ttl] of Object.entries(this.cacheTTL)) {
      stats.operations[operation] = {
        ttl,
        keyPattern: this.getInvalidationPattern(operation as keyof typeof this.cacheTTL)
      };
    }

    if (redisCache.isAvailable()) {
      try {
        // Get Redis stats
        const redisStats = await redisCache.getStats();
        
        // Parse memory usage from Redis info if available
        if (redisStats.info) {
          const memoryMatch = redisStats.info.match(/used_memory_human:([^\r\n]+)/);
          if (memoryMatch) {
            stats.memoryUsage = memoryMatch[1].trim();
          }
        }

        // Count dashboard keys
        let keyCount = 0;
        try {
          for await (const key of redisCache['client']?.scanIterator({ MATCH: `${this.keyPrefix}*` }) || []) {
            keyCount++;
          }
          stats.keyCount = keyCount;
        } catch (error) {
          console.warn('Could not count cache keys:', error);
        }

      } catch (error) {
        console.warn('Could not get cache stats:', error);
      }
    }

    return stats;
  }

  /**
   * Cache wrapper for dashboard operations
   */
  async withCache<T>(
    operation: keyof typeof this.cacheTTL,
    filters: DashboardFilters,
    dataFetcher: () => Promise<T>
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(operation, filters);
    if (cached !== null) {
      return cached;
    }

    // Cache miss - fetch data
    const startTime = Date.now();
    const data = await dataFetcher();
    const fetchTime = Date.now() - startTime;

    // Cache the result (fire and forget)
    this.set(operation, filters, data).catch(error => {
      console.warn(`Failed to cache ${operation}:`, error);
    });

    console.log(`Data fetched for ${operation} in ${fetchTime}ms`);
    return data;
  }

  /**
   * Batch invalidation for related operations
   */
  async invalidateRelated(changedEntity: 'transaction' | 'account' | 'budget' | 'goal'): Promise<void> {
    const invalidationMap = {
      transaction: [
        'ACCOUNT_BALANCES',
        'NET_WORTH', 
        'EXPENSES_BY_CATEGORY',
        'EXPENSES_BY_MEMBER',
        'BUDGET_COMPARISON',
        'OVERVIEW'
      ],
      account: [
        'ACCOUNT_BALANCES',
        'NET_WORTH',
        'CREDIT_CARD_USAGE',
        'OVERVIEW'
      ],
      budget: [
        'BUDGET_COMPARISON',
        'OVERVIEW'
      ],
      goal: [
        'GOAL_PROGRESS',
        'OVERVIEW'
      ]
    };

    const operationsToInvalidate = invalidationMap[changedEntity];
    
    console.log(`Invalidating cache for ${changedEntity} change:`, operationsToInvalidate);
    
    for (const operation of operationsToInvalidate) {
      await this.invalidate(operation as keyof typeof this.cacheTTL);
    }
  }

  /**
   * Health check for cache service
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    redis: boolean;
    latency?: number;
    error?: string;
  }> {
    try {
      const startTime = Date.now();
      const pingResult = await redisCache.ping();
      const latency = Date.now() - startTime;

      if (pingResult) {
        return {
          status: latency < 100 ? 'healthy' : 'degraded',
          redis: true,
          latency
        };
      } else {
        return {
          status: 'unhealthy',
          redis: false,
          error: 'Redis ping failed'
        };
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        redis: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Export singleton instance
export const dashboardCache = new DashboardCacheService();
