import prisma from '../lib/prisma';
import { Prisma, AccountType } from '@prisma/client';
import {
  CreateAccountRequest,
  UpdateAccountRequest,
  AccountFilters,
  AccountResponse,
  PaginatedAccountsResponse
} from '../schemas/account.schemas';

export class AccountService {
  /**
   * Create a new account with family member associations
   */
  async create(data: CreateAccountRequest): Promise<AccountResponse> {
    // Check if name already exists (case-insensitive)
    const existingAccount = await prisma.account.findFirst({
      where: {
        name: {
          equals: data.name,
          mode: 'insensitive'
        },
        deletedAt: null
      }
    });

    if (existingAccount) {
      throw new Error('Já existe uma conta com este nome');
    }

    let familyMemberIds: string[] = [];

    // Check if familyMemberIds is provided and is an array
    if (data.familyMemberIds && Array.isArray(data.familyMemberIds)) {
      familyMemberIds = data.familyMemberIds;
    }

    // If no family members provided, create or use a default one
    if (familyMemberIds.length === 0) {
      // Check if there's already a default family member
      let defaultMember = await prisma.familyMember.findFirst({
        where: {
          name: 'Usuário Principal',
          deletedAt: null
        }
      });

      // Create default family member if it doesn't exist
      if (!defaultMember) {
        defaultMember = await prisma.familyMember.create({
          data: {
            name: 'Usuário Principal',
            color: '#3B82F6'
          }
        });
      }

      familyMemberIds = [defaultMember.id];
    }

    // Verify family members exist
    const familyMembers = await prisma.familyMember.findMany({
      where: {
        id: { in: familyMemberIds },
        deletedAt: null
      }
    });

    if (familyMembers.length !== familyMemberIds.length) {
      throw new Error('Um ou mais membros da família não foram encontrados');
    }

    // Use transaction for data integrity
    const account = await prisma.$transaction(async (tx) => {
      // Create account
      const newAccount = await tx.account.create({
        data: {
          name: data.name,
          type: data.type,
          currency: data.currency,
          creditLimit: data.creditLimit,
          exchangeRate: data.exchangeRate,
          includeInTotal: data.includeInTotal,
          logoPath: data.logoPath
        }
      });

      // Create family member associations
      await tx.accountMember.createMany({
        data: familyMemberIds.map(familyMemberId => ({
          accountId: newAccount.id,
          familyMemberId
        }))
      });

      return newAccount;
    });

    return this.formatAccount(account, familyMembers);
  }

  /**
   * Get all accounts with filters and pagination
   */
  async findAll(filters: AccountFilters = {}): Promise<PaginatedAccountsResponse> {
    const {
      name,
      type,
      currency,
      includeInTotal,
      familyMemberId,
      includeArchived = false,
      includeDeleted = false,
      page = 1,
      limit = 20
    } = filters;

    // Build where clause
    const where: Prisma.AccountWhereInput = {};

    if (name) {
      where.name = {
        contains: name,
        mode: 'insensitive'
      };
    }

    if (type) {
      where.type = type;
    }

    if (currency) {
      where.currency = currency;
    }

    if (includeInTotal !== undefined) {
      where.includeInTotal = includeInTotal;
    }

    if (familyMemberId) {
      where.members = {
        some: {
          familyMemberId,
          familyMember: { deletedAt: null }
        }
      };
    }

    if (!includeDeleted) {
      where.deletedAt = null;
    }

    if (!includeArchived && !includeDeleted) {
      where.deletedAt = null;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await prisma.account.count({ where });

    // Get accounts with family members
    const accounts = await prisma.account.findMany({
      where,
      include: {
        members: {
          include: {
            familyMember: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          },
          where: {
            familyMember: { deletedAt: null }
          }
        }
      },
      orderBy: [
        { deletedAt: 'asc' }, // Non-archived first
        { name: 'asc' }
      ],
      skip,
      take: limit
    });

    return {
      data: accounts.map(account => this.formatAccount(
        account,
        account.members.map(m => m.familyMember)
      )),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get account by ID
   */
  async findById(id: string, includeArchived = false): Promise<AccountResponse | null> {
    const where: Prisma.AccountWhereInput = { id };
    
    if (!includeArchived) {
      where.deletedAt = null;
    }

    const account = await prisma.account.findFirst({
      where,
      include: {
        members: {
          include: {
            familyMember: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          },
          where: {
            familyMember: { deletedAt: null }
          }
        }
      }
    });

    if (!account) {
      return null;
    }

    return this.formatAccount(
      account,
      account.members.map(m => m.familyMember)
    );
  }

  /**
   * Update account
   */
  async update(id: string, data: UpdateAccountRequest): Promise<AccountResponse> {
    // Check if account exists and is not deleted
    const existingAccount = await prisma.account.findFirst({
      where: { id, deletedAt: null },
      include: { members: true }
    });

    if (!existingAccount) {
      throw new Error('Conta não encontrada');
    }

    // Check name uniqueness if name is being updated
    if (data.name && data.name !== existingAccount.name) {
      const nameExists = await prisma.account.findFirst({
        where: {
          name: {
            equals: data.name,
            mode: 'insensitive'
          },
          deletedAt: null,
          id: { not: id }
        }
      });

      if (nameExists) {
        throw new Error('Já existe uma conta com este nome');
      }
    }

    // Validate credit limit for credit cards
    if (existingAccount.type === AccountType.CREDIT_CARD && data.creditLimit === undefined && !existingAccount.creditLimit) {
      throw new Error('Limite de crédito é obrigatório para cartões de crédito');
    }

    // Validate exchange rate for foreign currencies
    if (existingAccount.currency !== 'BRL' && data.exchangeRate === undefined && !existingAccount.exchangeRate) {
      throw new Error('Taxa de câmbio é obrigatória para moedas estrangeiras');
    }

    // Use transaction for data integrity
    const updatedAccount = await prisma.$transaction(async (tx) => {
      // Update account
      const account = await tx.account.update({
        where: { id },
        data: {
          ...data,
          version: { increment: 1 }
        }
      });

      // Update family member associations if provided
      if (data.familyMemberIds) {
        // Verify family members exist
        const familyMembers = await tx.familyMember.findMany({
          where: {
            id: { in: data.familyMemberIds },
            deletedAt: null
          }
        });

        if (familyMembers.length !== data.familyMemberIds.length) {
          throw new Error('Um ou mais membros da família não foram encontrados');
        }

        // Remove existing associations
        await tx.accountMember.deleteMany({
          where: { accountId: id }
        });

        // Create new associations
        await tx.accountMember.createMany({
          data: data.familyMemberIds.map(familyMemberId => ({
            accountId: id,
            familyMemberId
          }))
        });

        return { account, familyMembers };
      }

      return { account, familyMembers: [] };
    });

    // Get family members if not updated
    let familyMembers: Array<{ id: string; name: string; color: string }> = updatedAccount.familyMembers;
    if (!data.familyMemberIds) {
      const members = await prisma.accountMember.findMany({
        where: { accountId: id },
        include: {
          familyMember: {
            select: {
              id: true,
              name: true,
              color: true
            }
          }
        }
      });
      familyMembers = members.map(m => ({
        id: m.familyMember.id,
        name: m.familyMember.name,
        color: m.familyMember.color
      }));
    }

    return this.formatAccount(updatedAccount.account, familyMembers);
  }

  /**
   * Archive/Unarchive account (soft delete)
   */
  async archive(id: string, archived: boolean): Promise<AccountResponse> {
    const existingAccount = await prisma.account.findUnique({
      where: { id },
      include: {
        members: {
          include: {
            familyMember: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          }
        }
      }
    });

    if (!existingAccount) {
      throw new Error('Conta não encontrada');
    }

    // Check if account is already in the desired state
    const isCurrentlyArchived = existingAccount.deletedAt !== null;
    if (isCurrentlyArchived === archived) {
      const action = archived ? 'arquivada' : 'ativa';
      throw new Error(`Conta já está ${action}`);
    }

    const updatedAccount = await prisma.account.update({
      where: { id },
      data: {
        deletedAt: archived ? new Date() : null,
        version: { increment: 1 }
      }
    });

    return this.formatAccount(
      updatedAccount,
      existingAccount.members.map(m => m.familyMember)
    );
  }

  /**
   * Permanently delete account
   */
  async delete(id: string): Promise<void> {
    const existingAccount = await prisma.account.findUnique({
      where: { id }
    });

    if (!existingAccount) {
      throw new Error('Conta não encontrada');
    }

    // Use transaction to ensure data integrity
    await prisma.$transaction(async (tx) => {
      // Check if account has any transactions that would prevent deletion
      const hasTransactions = await tx.transaction.findFirst({
        where: {
          OR: [
            { accountId: id },
            { destinationAccountId: id }
          ]
        }
      });

      const hasRecurringTransactions = await tx.recurringTransaction.findFirst({
        where: { accountId: id }
      });

      const hasBalanceHistory = await tx.accountBalanceHistory.findFirst({
        where: { accountId: id }
      });

      if (hasTransactions || hasRecurringTransactions || hasBalanceHistory) {
        throw new Error('Não é possível deletar conta que possui transações, transações recorrentes ou histórico de saldo associados');
      }

      // Delete account member associations
      await tx.accountMember.deleteMany({
        where: { accountId: id }
      });

      // Delete the account
      await tx.account.delete({
        where: { id }
      });
    });
  }

  /**
   * Format account for response
   */
  private formatAccount(account: any, familyMembers: any[] = []): AccountResponse {
    const currentBalance = Number(account.currentBalance) || 0;
    const exchangeRate = Number(account.exchangeRate) || 1;
    const balanceInBRL = account.currency === 'BRL' ? currentBalance : currentBalance * exchangeRate;

    const formatted: AccountResponse = {
      id: account.id,
      name: account.name,
      type: account.type,
      currency: account.currency,
      creditLimit: account.creditLimit ? Number(account.creditLimit) : undefined,
      exchangeRate: account.exchangeRate ? Number(account.exchangeRate) : undefined,
      includeInTotal: account.includeInTotal,
      logoPath: account.logoPath,
      archived: account.deletedAt !== null,
      createdAt: account.createdAt.toISOString(),
      updatedAt: account.updatedAt.toISOString(),
      version: account.version,
      currentBalance: currentBalance,
      balanceInBRL: balanceInBRL,
      familyMembers: familyMembers.map(member => ({
        id: member.id,
        name: member.name,
        color: member.color
      }))
    };

    // Calculate available balance for credit cards
    if (account.type === AccountType.CREDIT_CARD && account.creditLimit) {
      const creditLimit = Number(account.creditLimit);
      // For credit cards, available balance is credit limit minus current balance (debt)
      // If currentBalance is negative, it means there's debt, so available = limit + balance
      // If currentBalance is positive, it means there's credit, so available = limit - balance
      formatted.availableBalance = creditLimit - Math.abs(currentBalance);
    }

    return formatted;
  }
}

export const accountService = new AccountService();
