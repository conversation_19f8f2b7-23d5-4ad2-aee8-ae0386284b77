import { Prisma } from '@prisma/client';
import prisma from '../lib/prisma';
import {
  CreateGoalMilestoneRequest,
  UpdateGoalMilestoneRequest,
  MilestoneFilters,
  GoalMilestoneResponse
} from '../schemas/financial-goal.schemas';

export class MilestoneService {
  /**
   * Create a new milestone for a goal
   */
  async create(goalId: string, data: CreateGoalMilestoneRequest): Promise<GoalMilestoneResponse> {
    try {
      // Check if goal exists and is not deleted
      const goal = await prisma.goal.findFirst({
        where: {
          id: goalId,
          deletedAt: null
        }
      });

      if (!goal) {
        throw new Error('Meta financeira não encontrada');
      }

      // Validate that milestone target amount doesn't exceed goal target amount
      const targetAmount = Number(goal.targetAmount);
      if (data.targetAmount > targetAmount) {
        throw new Error('Valor alvo do marco não pode exceder o valor alvo da meta');
      }

      // Create milestone using transaction
      const milestone = await prisma.$transaction(async (tx) => {
        return await tx.goalMilestone.create({
          data: {
            goalId,
            name: data.name,
            targetAmount: new Prisma.Decimal(data.targetAmount),
            targetDate: data.targetDate
          }
        });
      });

      return this.formatMilestoneResponse(milestone);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao criar marco da meta');
    }
  }

  /**
   * Find all milestones for a goal with filters
   */
  async findByGoal(goalId: string, filters: Omit<MilestoneFilters, 'goalId'>): Promise<GoalMilestoneResponse[]> {
    try {
      // Check if goal exists and get current amount
      const goal = await prisma.goal.findFirst({
        where: {
          id: goalId,
          deletedAt: null
        },
        select: {
          id: true,
          currentAmount: true // ✅ Incluir valor atual da meta
        }
      });

      if (!goal) {
        throw new Error('Meta financeira não encontrada');
      }

      const {
        status,
        targetDateFrom,
        targetDateTo,
        sortBy,
        sortOrder
      } = filters;

      // Build where clause
      const where: Prisma.GoalMilestoneWhereInput = {
        goalId,
        ...(targetDateFrom && {
          targetDate: {
            gte: targetDateFrom
          }
        }),
        ...(targetDateTo && {
          targetDate: {
            lte: targetDateTo
          }
        })
      };

      // Add status filter
      if (status !== 'all') {
        const now = new Date();
        switch (status) {
          case 'completed':
            where.isCompleted = true;
            break;
          case 'pending':
            where.isCompleted = false;
            where.targetDate = { gte: now };
            break;
          case 'overdue':
            where.isCompleted = false;
            where.targetDate = { lt: now };
            break;
        }
      }

      // Build orderBy clause
      const orderBy: Prisma.GoalMilestoneOrderByWithRelationInput = {
        [sortBy]: sortOrder
      };

      const milestones = await prisma.goalMilestone.findMany({
        where,
        orderBy
      });

      // Apply automatic completion logic
      const goalCurrentAmount = Number(goal.currentAmount);
      return milestones.map(milestone => this.formatMilestoneResponse(milestone, goalCurrentAmount));
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao buscar marcos da meta');
    }
  }

  /**
   * Find milestone by ID
   */
  async findById(id: string): Promise<GoalMilestoneResponse | null> {
    try {
      const milestone = await prisma.goalMilestone.findUnique({
        where: { id },
        include: {
          goal: {
            select: {
              id: true,
              name: true,
              currentAmount: true, // ✅ Incluir valor atual da meta
              deletedAt: true
            }
          }
        }
      });

      if (!milestone || milestone.goal.deletedAt) {
        return null;
      }

      // Apply automatic completion logic
      const goalCurrentAmount = Number(milestone.goal.currentAmount);
      return this.formatMilestoneResponse(milestone, goalCurrentAmount);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao buscar marco da meta');
    }
  }

  /**
   * Update milestone
   */
  async update(id: string, data: UpdateGoalMilestoneRequest): Promise<GoalMilestoneResponse> {
    try {
      // Check if milestone exists
      const existingMilestone = await prisma.goalMilestone.findUnique({
        where: { id },
        include: {
          goal: {
            select: {
              id: true,
              targetAmount: true,
              currentAmount: true, // ✅ Incluir valor atual da meta
              deletedAt: true
            }
          }
        }
      });

      if (!existingMilestone || existingMilestone.goal.deletedAt) {
        throw new Error('Marco da meta não encontrado');
      }

      // Validate target amount if being updated
      if (data.targetAmount) {
        const goalTargetAmount = Number(existingMilestone.goal.targetAmount);
        if (data.targetAmount > goalTargetAmount) {
          throw new Error('Valor alvo do marco não pode exceder o valor alvo da meta');
        }
      }

      // Update milestone
      const updateData: Prisma.GoalMilestoneUpdateInput = {
        ...(data.name && { name: data.name }),
        ...(data.targetAmount && { targetAmount: new Prisma.Decimal(data.targetAmount) }),
        ...(data.targetDate !== undefined && { targetDate: data.targetDate }),
        ...(data.isCompleted !== undefined && { isCompleted: data.isCompleted })
      };

      const milestone = await prisma.goalMilestone.update({
        where: { id },
        data: updateData
      });

      // Apply automatic completion logic
      const goalCurrentAmount = Number(existingMilestone.goal.currentAmount);
      return this.formatMilestoneResponse(milestone, goalCurrentAmount);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao atualizar marco da meta');
    }
  }

  /**
   * Delete milestone
   */
  async delete(id: string): Promise<void> {
    try {
      const milestone = await prisma.goalMilestone.findUnique({
        where: { id },
        include: {
          goal: {
            select: {
              deletedAt: true
            }
          }
        }
      });

      if (!milestone || milestone.goal.deletedAt) {
        throw new Error('Marco da meta não encontrado');
      }

      await prisma.goalMilestone.delete({
        where: { id }
      });
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao deletar marco da meta');
    }
  }

  /**
   * Mark milestone as completed
   */
  async markCompleted(id: string): Promise<GoalMilestoneResponse> {
    try {
      const milestone = await this.update(id, { isCompleted: true });
      return milestone;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao marcar marco como concluído');
    }
  }

  /**
   * Mark milestone as pending
   */
  async markPending(id: string): Promise<GoalMilestoneResponse> {
    try {
      const milestone = await this.update(id, { isCompleted: false });
      return milestone;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao marcar marco como pendente');
    }
  }

  /**
   * Calculate automatic milestone completion status
   */
  private calculateMilestoneStatus(milestone: any, goalCurrentAmount: number): boolean {
    const milestoneTargetAmount = Number(milestone.targetAmount)
    const milestoneTargetDate = new Date(milestone.targetDate)
    const today = new Date()

    // Marco é concluído automaticamente se:
    // 1. Data atual <= data limite (ainda não venceu)
    // 2. E valor acumulado >= valor do marco
    const isWithinDeadline = today <= milestoneTargetDate
    const hasReachedAmount = goalCurrentAmount >= milestoneTargetAmount

    return isWithinDeadline && hasReachedAmount
  }

  /**
   * Format milestone response
   */
  private formatMilestoneResponse(milestone: any, goalCurrentAmount?: number): GoalMilestoneResponse {
    // Calculate automatic completion if goal current amount is provided
    let isCompleted = milestone.isCompleted; // Default to stored value

    if (goalCurrentAmount !== undefined) {
      isCompleted = this.calculateMilestoneStatus(milestone, goalCurrentAmount);
    }

    return {
      id: milestone.id,
      goalId: milestone.goalId,
      name: milestone.name,
      targetAmount: Number(milestone.targetAmount),
      targetDate: milestone.targetDate.toISOString(),
      isCompleted: isCompleted, // ✅ Agora automático baseado em valor e data
      createdAt: milestone.createdAt.toISOString(),
      updatedAt: milestone.updatedAt.toISOString(),
      version: milestone.version
    };
  }
}

// Export singleton instance
export const milestoneService = new MilestoneService();
