import { Prisma } from '@prisma/client';
import prisma from '../lib/prisma';

export interface GoalProgressHistoryResponse {
  id: string;
  goalId: string;
  goalName: string;
  previousAmount: number;
  newAmount: number;
  amountChanged: number;
  operation: string;
  description?: string;
  createdAt: string;
}

export interface ProgressHistoryFilters {
  goalId?: string;
  operation?: string;
  fromDate?: Date;
  toDate?: Date;
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'amountChanged';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedProgressHistoryResponse {
  data: GoalProgressHistoryResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class GoalProgressHistoryService {
  /**
   * Get all progress history with filters and pagination
   */
  async findAll(filters: ProgressHistoryFilters = {}): Promise<PaginatedProgressHistoryResponse> {
    const {
      goalId,
      operation,
      fromDate,
      toDate,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = filters;

    // Build where clause
    const where: Prisma.GoalProgressHistoryWhereInput = {};

    if (goalId) {
      where.goalId = goalId;
    }

    if (operation) {
      where.operation = operation;
    }

    if (fromDate || toDate) {
      where.createdAt = {};
      if (fromDate) where.createdAt.gte = fromDate;
      if (toDate) where.createdAt.lte = toDate;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build orderBy clause
    const orderBy: Prisma.GoalProgressHistoryOrderByWithRelationInput = {
      [sortBy]: sortOrder
    };

    // Execute queries in parallel
    const [progressHistory, total] = await Promise.all([
      prisma.goalProgressHistory.findMany({
        where,
        include: {
          goal: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.goalProgressHistory.count({ where })
    ]);

    // Format responses
    const formattedHistory: GoalProgressHistoryResponse[] = progressHistory.map(history => ({
      id: history.id,
      goalId: history.goalId,
      goalName: history.goal.name,
      previousAmount: Number(history.previousAmount),
      newAmount: Number(history.newAmount),
      amountChanged: Number(history.amountChanged),
      operation: history.operation,
      description: history.description || undefined,
      createdAt: history.createdAt.toISOString()
    }));

    return {
      data: formattedHistory,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get progress history for a specific goal
   */
  async findByGoal(goalId: string, filters: Omit<ProgressHistoryFilters, 'goalId'> = {}): Promise<PaginatedProgressHistoryResponse> {
    return this.findAll({ ...filters, goalId });
  }

  /**
   * Get recent progress updates
   */
  async getRecent(limit = 10): Promise<GoalProgressHistoryResponse[]> {
    const response = await this.findAll({
      limit,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
    return response.data;
  }

  /**
   * Update progress history entry description
   */
  async updateDescription(id: string, description: string): Promise<GoalProgressHistoryResponse> {
    try {
      const updated = await prisma.goalProgressHistory.update({
        where: { id },
        data: { description },
        include: {
          goal: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      return {
        id: updated.id,
        goalId: updated.goalId,
        goalName: updated.goal.name,
        previousAmount: Number(updated.previousAmount),
        newAmount: Number(updated.newAmount),
        amountChanged: Number(updated.amountChanged),
        operation: updated.operation,
        description: updated.description || undefined,
        createdAt: updated.createdAt.toISOString()
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao atualizar descrição do histórico');
    }
  }

  /**
   * Delete progress history entry
   */
  async delete(id: string): Promise<void> {
    try {
      await prisma.goalProgressHistory.delete({
        where: { id }
      });
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao deletar entrada do histórico');
    }
  }
}

// Export singleton instance
export const goalProgressHistoryService = new GoalProgressHistoryService();
