import { Prisma, TransactionType } from '@prisma/client';
import prisma from '../lib/prisma';
import {
  TransferRequest,
  TransferResponse,
  CurrencyConversionData,
  CreateTransactionRequest,
  UpdateTransactionRequest,
  TransactionResponse,
  PaginatedTransactionsResponse
} from '../schemas/transaction.schemas';
import { transactionService } from './transaction.service';

export class TransferService {
  /**
   * Create a new transfer between accounts with validation and balance updates
   */
  async createTransfer(data: TransferRequest): Promise<TransferResponse> {
    // Validate accounts exist and are different
    await this.validateTransferAccounts(data.accountId, data.destinationAccountId);

    // Validate sufficient balance
    await this.validateSufficientBalance(data.accountId, data.amount);

    // Validate currency conversion if applicable
    if (data.exchangeRate && data.sourceCurrency && data.destinationCurrency) {
      await this.validateCurrencyConversion(data);
    }

    // Use transaction service to create the transfer
    const transfer = await transactionService.create(data as CreateTransactionRequest);

    // Add audit log for transfer
    await this.createTransferAuditLog(transfer.id, 'CREATED', data);

    return transfer as TransferResponse;
  }

  /**
   * Find all transfers with filters
   */
  async findAllTransfers(filters: any): Promise<PaginatedTransactionsResponse> {
    const transferFilters = {
      ...filters,
      type: TransactionType.TRANSFER
    };

    return await transactionService.findAll(transferFilters);
  }

  /**
   * Find transfer by ID
   */
  async findTransferById(id: string, includeDeleted = false): Promise<TransferResponse | null> {
    const transfer = await transactionService.findById(id, includeDeleted);
    
    if (!transfer || transfer.type !== TransactionType.TRANSFER) {
      return null;
    }

    return transfer as TransferResponse;
  }

  /**
   * Update transfer with validation
   */
  async updateTransfer(id: string, data: UpdateTransactionRequest): Promise<TransferResponse> {
    // Get existing transfer
    const existingTransfer = await this.findTransferById(id);
    if (!existingTransfer) {
      throw new Error('Transferência não encontrada');
    }

    // Validate account changes if provided
    if (data.destinationAccountId && data.destinationAccountId !== existingTransfer.destinationAccountId) {
      await this.validateTransferAccounts(existingTransfer.accountId, data.destinationAccountId);
    }

    // Validate balance if amount is being changed
    if (data.amount && data.amount !== existingTransfer.amount) {
      await this.validateSufficientBalance(existingTransfer.accountId, data.amount);
    }

    // Update transfer
    const updatedTransfer = await transactionService.update(id, data);

    // Add audit log
    await this.createTransferAuditLog(id, 'UPDATED', data);

    return updatedTransfer as TransferResponse;
  }

  /**
   * Delete transfer (soft delete)
   */
  async deleteTransfer(id: string): Promise<void> {
    const existingTransfer = await this.findTransferById(id);
    if (!existingTransfer) {
      throw new Error('Transferência não encontrada');
    }

    await transactionService.delete(id);

    // Add audit log
    await this.createTransferAuditLog(id, 'DELETED', {});
  }

  /**
   * Calculate currency conversion
   */
  async calculateCurrencyConversion(data: CurrencyConversionData): Promise<CurrencyConversionData> {
    const { sourceAmount, exchangeRate } = data;
    const destinationAmount = Number((sourceAmount * exchangeRate).toFixed(2));

    return {
      ...data,
      destinationAmount
    };
  }

  /**
   * Validate transfer data before creation
   */
  async validateTransferData(data: any): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Validate accounts
      if (data.accountId && data.destinationAccountId) {
        await this.validateTransferAccounts(data.accountId, data.destinationAccountId);
      }

      // Validate balance
      if (data.accountId && data.amount) {
        await this.validateSufficientBalance(data.accountId, data.amount);
      }

      // Validate currency conversion
      if (data.exchangeRate && data.sourceCurrency && data.destinationCurrency) {
        if (data.sourceCurrency === data.destinationCurrency) {
          warnings.push('Taxa de câmbio desnecessária para transferência na mesma moeda');
        }
      }

    } catch (error) {
      errors.push(error instanceof Error ? error.message : 'Erro de validação');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate that accounts exist and are different
   */
  private async validateTransferAccounts(sourceAccountId: string, destinationAccountId: string): Promise<void> {
    if (sourceAccountId === destinationAccountId) {
      throw new Error('Conta origem e destino devem ser diferentes');
    }

    // Check source account
    const sourceAccount = await prisma.account.findFirst({
      where: { id: sourceAccountId, deletedAt: null }
    });

    if (!sourceAccount) {
      throw new Error('Conta origem não encontrada');
    }

    // Check destination account
    const destinationAccount = await prisma.account.findFirst({
      where: { id: destinationAccountId, deletedAt: null }
    });

    if (!destinationAccount) {
      throw new Error('Conta destino não encontrada');
    }
  }

  /**
   * Validate sufficient balance for transfer
   */
  private async validateSufficientBalance(accountId: string, amount: number): Promise<void> {
    const account = await prisma.account.findFirst({
      where: { id: accountId, deletedAt: null }
    });

    if (!account) {
      throw new Error('Conta não encontrada');
    }

    const currentBalance = Number(account.currentBalance);
    const transferAmount = Number(amount);

    // For credit cards, check against credit limit
    if (account.type === 'CREDIT_CARD') {
      const creditLimit = Number(account.creditLimit || 0);
      const availableCredit = creditLimit + currentBalance; // Balance is negative for credit cards
      
      if (transferAmount > availableCredit) {
        throw new Error('Limite de crédito insuficiente para a transferência');
      }
    } else {
      // For other account types, check positive balance
      if (transferAmount > currentBalance) {
        throw new Error('Saldo insuficiente para a transferência');
      }
    }
  }

  /**
   * Validate currency conversion data
   */
  private async validateCurrencyConversion(data: TransferRequest): Promise<void> {
    if (!data.sourceCurrency || !data.destinationCurrency || !data.exchangeRate) {
      throw new Error('Dados de conversão de moeda incompletos');
    }

    if (data.sourceCurrency === data.destinationCurrency && data.exchangeRate !== 1) {
      throw new Error('Taxa de câmbio deve ser 1 para transferências na mesma moeda');
    }

    if (data.exchangeRate <= 0) {
      throw new Error('Taxa de câmbio deve ser positiva');
    }

    // Validate currency codes (basic validation)
    const validCurrencyPattern = /^[A-Z]{3}$/;
    if (!validCurrencyPattern.test(data.sourceCurrency) || !validCurrencyPattern.test(data.destinationCurrency)) {
      throw new Error('Códigos de moeda inválidos');
    }
  }

  /**
   * Create audit log for transfer operations
   */
  private async createTransferAuditLog(transferId: string, action: string, data: any): Promise<void> {
    // This would typically log to an audit table or external service
    console.log(`Transfer Audit: ${action} - Transfer ID: ${transferId}`, {
      timestamp: new Date().toISOString(),
      action,
      transferId,
      data: JSON.stringify(data)
    });
  }
}

export const transferService = new TransferService();
