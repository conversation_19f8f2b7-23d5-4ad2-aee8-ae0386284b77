import prisma from '../lib/prisma';
import {
  CreateFamilyMemberRequest,
  UpdateFamilyMemberRequest,
  FamilyMemberFilters,
  FamilyMemberResponse,
  PaginatedFamilyMembersResponse
} from '../schemas/family-member.schemas';

export class FamilyMemberService {
  /**
   * Create a new family member
   */
  async create(data: CreateFamilyMemberRequest): Promise<FamilyMemberResponse> {
    // Check if name already exists (case-insensitive)
    const existingMember = await prisma.familyMember.findFirst({
      where: {
        name: {
          equals: data.name,
          mode: 'insensitive'
        },
        deletedAt: null
      }
    });

    if (existingMember) {
      throw new Error('Já existe um membro da família com este nome');
    }

    // Check if color already exists
    const existingColor = await prisma.familyMember.findFirst({
      where: {
        color: data.color,
        deletedAt: null
      }
    });

    if (existingColor) {
      throw new Error('Já existe um membro da família com esta cor');
    }

    const familyMember = await prisma.familyMember.create({
      data: {
        name: data.name,
        color: data.color
      }
    });

    return this.formatFamilyMember(familyMember);
  }

  /**
   * Get all family members with filters and pagination
   */
  async findAll(filters: FamilyMemberFilters = {}): Promise<PaginatedFamilyMembersResponse> {
    const {
      name,
      includeArchived = false,
      includeDeleted = false,
      page = 1,
      limit = 20
    } = filters;

    // Build where clause
    const where: any = {};

    if (name) {
      where.name = {
        contains: name,
        mode: 'insensitive'
      };
    }

    // Handle archived/deleted logic
    if (!includeDeleted && !includeArchived) {
      // Show only active members (deletedAt is null)
      where.deletedAt = null;
    } else if (!includeDeleted && includeArchived) {
      // Show both active and archived members (all records)
      // No filter on deletedAt
    } else if (includeDeleted && !includeArchived) {
      // Show only deleted members (deletedAt is not null)
      where.deletedAt = { not: null };
    }
    // If both includeDeleted and includeArchived are true, show all records

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await prisma.familyMember.count({ where });

    // Get family members
    const familyMembers = await prisma.familyMember.findMany({
      where,
      orderBy: [
        { deletedAt: 'asc' }, // Non-archived first
        { name: 'asc' }
      ],
      skip,
      take: limit
    });

    return {
      data: familyMembers.map(member => this.formatFamilyMember(member)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get family member by ID
   */
  async findById(id: string, includeArchived = false): Promise<FamilyMemberResponse | null> {
    const where: any = { id };
    
    if (!includeArchived) {
      where.deletedAt = null;
    }

    const familyMember = await prisma.familyMember.findFirst({ where });

    if (!familyMember) {
      return null;
    }

    return this.formatFamilyMember(familyMember);
  }

  /**
   * Update family member
   */
  async update(id: string, data: UpdateFamilyMemberRequest): Promise<FamilyMemberResponse> {
    // Check if family member exists and is not deleted
    const existingMember = await prisma.familyMember.findFirst({
      where: { id, deletedAt: null }
    });

    if (!existingMember) {
      throw new Error('Membro da família não encontrado');
    }

    // Check name uniqueness if name is being updated
    if (data.name && data.name !== existingMember.name) {
      const nameExists = await prisma.familyMember.findFirst({
        where: {
          name: {
            equals: data.name,
            mode: 'insensitive'
          },
          deletedAt: null,
          id: { not: id }
        }
      });

      if (nameExists) {
        throw new Error('Já existe um membro da família com este nome');
      }
    }

    // Check color uniqueness if color is being updated
    if (data.color && data.color !== existingMember.color) {
      const colorExists = await prisma.familyMember.findFirst({
        where: {
          color: data.color,
          deletedAt: null,
          id: { not: id }
        }
      });

      if (colorExists) {
        throw new Error('Já existe um membro da família com esta cor');
      }
    }

    const updatedMember = await prisma.familyMember.update({
      where: { id },
      data: {
        ...data,
        version: { increment: 1 }
      }
    });

    return this.formatFamilyMember(updatedMember);
  }

  /**
   * Archive/Unarchive family member (soft delete)
   */
  async archive(id: string, archived: boolean): Promise<FamilyMemberResponse> {
    const existingMember = await prisma.familyMember.findUnique({
      where: { id }
    });

    if (!existingMember) {
      throw new Error('Membro da família não encontrado');
    }

    // Check if member is already in the desired state
    const isCurrentlyArchived = existingMember.deletedAt !== null;
    if (isCurrentlyArchived === archived) {
      const action = archived ? 'arquivado' : 'ativo';
      throw new Error(`Membro da família já está ${action}`);
    }

    const updatedMember = await prisma.familyMember.update({
      where: { id },
      data: {
        deletedAt: archived ? new Date() : null,
        version: { increment: 1 }
      }
    });

    return this.formatFamilyMember(updatedMember);
  }

  /**
   * Get family members statistics
   */
  async getStats(): Promise<{
    totalMembers: number;
    activeMembers: number;
    archivedMembers: number;
  }> {
    const totalMembers = await prisma.familyMember.count();
    const activeMembers = await prisma.familyMember.count({
      where: { deletedAt: null }
    });
    const archivedMembers = await prisma.familyMember.count({
      where: { deletedAt: { not: null } }
    });

    return {
      totalMembers,
      activeMembers,
      archivedMembers
    };
  }

  /**
   * Permanently delete family member
   */
  async delete(id: string): Promise<void> {
    const existingMember = await prisma.familyMember.findUnique({
      where: { id }
    });

    if (!existingMember) {
      throw new Error('Membro da família não encontrado');
    }

    // Use transaction to ensure data integrity
    await prisma.$transaction(async (tx) => {
      // Check if member has any relationships that would prevent deletion
      const hasAccounts = await tx.accountMember.findFirst({
        where: { familyMemberId: id }
      });

      const hasTransactions = await tx.transactionMember.findFirst({
        where: { familyMemberId: id }
      });

      const hasBudgets = await tx.budget.findFirst({
        where: { familyMemberId: id }
      });

      const hasGoals = await tx.goalMember.findFirst({
        where: { familyMemberId: id }
      });

      if (hasAccounts || hasTransactions || hasBudgets || hasGoals) {
        throw new Error('Não é possível deletar membro da família que possui contas, transações, orçamentos ou metas associadas');
      }

      // Delete the family member
      await tx.familyMember.delete({
        where: { id }
      });
    });
  }

  /**
   * Format family member for response
   */
  private formatFamilyMember(member: any): FamilyMemberResponse {
    return {
      id: member.id,
      name: member.name,
      color: member.color,
      archived: member.deletedAt !== null,
      createdAt: member.createdAt.toISOString(),
      updatedAt: member.updatedAt.toISOString(),
      version: member.version
    };
  }
}

export const familyMemberService = new FamilyMemberService();
