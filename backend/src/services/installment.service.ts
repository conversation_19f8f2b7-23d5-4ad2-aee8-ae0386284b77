import prisma from '../lib/prisma';
import { TransactionType } from '@prisma/client';

export interface InstallmentTransactionData {
  description: string;
  totalAmount: number;
  totalInstallments: number;
  firstInstallmentDate: Date;
  accountId: string;
  categoryId?: string;
  type: TransactionType;
}

export interface InstallmentResult {
  totalInstallments: number;
  installmentAmount: number;
  createdTransactions: string[];
}

export interface InstallmentUpdateResult {
  updatedInstallments: number;
  affectedTransactionIds: string[];
}

export interface InstallmentCancellationResult {
  cancelledInstallments: number;
  message: string;
}

export interface InstallmentSummary {
  totalInstallments: number;
  completedInstallments: number;
  remainingInstallments: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  progressPercentage: number;
  nextInstallmentDate?: Date;
}

export interface UpdateInstallmentsData {
  installments: Array<{
    amount: number;
    transactionDate: Date;
    description?: string;
  }>;
  totalAmount: number;
}

export interface UpdateInstallmentsResult {
  parentTransaction: any;
  newInstallments: any[];
  deletedInstallments: string[];
  summary: InstallmentSummary;
}

export class InstallmentService {
  private readonly MIN_INSTALLMENTS = 2;
  private readonly MAX_INSTALLMENTS = 60;
  private readonly MIN_INSTALLMENT_AMOUNT = 1.00;

  /**
   * Create installment transaction with future installments
   */
  async createInstallmentTransaction(data: InstallmentTransactionData): Promise<InstallmentResult> {
    this.validateInstallmentData(data);

    const installmentAmount = this.calculateInstallmentAmount(data.totalAmount, data.totalInstallments);
    const createdTransactions: string[] = [];

    await prisma.$transaction(async (tx) => {
      let parentTransactionId: string | null = null;

      for (let i = 1; i <= data.totalInstallments; i++) {
        const installmentDate = this.calculateInstallmentDate(data.firstInstallmentDate, i - 1);
        const isFirstInstallment = i === 1;
        const isLastInstallment = i === data.totalInstallments;
        
        // Calculate amount (last installment gets remainder)
        let amount = installmentAmount;
        if (isLastInstallment) {
          const totalPreviousAmount = installmentAmount * (data.totalInstallments - 1);
          amount = Math.round((data.totalAmount - totalPreviousAmount) * 100) / 100;
        }

        const transaction: any = await tx.transaction.create({
          data: {
            description: `${data.description} (${i}/${data.totalInstallments})`,
            amount,
            transactionDate: installmentDate,
            type: data.type,
            accountId: data.accountId,
            categoryId: data.categoryId,
            installmentNumber: i,
            totalInstallments: data.totalInstallments,
            parentTransactionId: isFirstInstallment ? null : parentTransactionId,
            isFuture: !isFirstInstallment // First installment is immediate
          }
        });

        createdTransactions.push(transaction.id);

        // Set parent ID for subsequent installments
        if (isFirstInstallment) {
          parentTransactionId = transaction.id;
        }
      }
    });

    return {
      totalInstallments: data.totalInstallments,
      installmentAmount,
      createdTransactions
    };
  }



  /**
   * Cancel remaining future installments
   */
  async cancelRemainingInstallments(transactionId: string): Promise<InstallmentCancellationResult> {
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId }
    });

    if (!transaction || !transaction.installmentNumber) {
      throw new Error('Transaction is not part of an installment series');
    }

    const parentId = transaction.parentTransactionId || transaction.id;

    // Find future installments
    const futureInstallments = await prisma.transaction.findMany({
      where: {
        OR: [
          { parentTransactionId: parentId },
          { parentTransactionId: transactionId }
        ],
        installmentNumber: {
          gt: transaction.installmentNumber
        },
        isFuture: true
      }
    });

    if (futureInstallments.length === 0) {
      return {
        cancelledInstallments: 0,
        message: 'No future installments to cancel'
      };
    }

    const result = await prisma.transaction.deleteMany({
      where: {
        OR: [
          { parentTransactionId: parentId },
          { parentTransactionId: transactionId }
        ],
        installmentNumber: {
          gt: transaction.installmentNumber
        },
        isFuture: true
      }
    });

    return {
      cancelledInstallments: result.count,
      message: `Cancelled ${result.count} future installments`
    };
  }

  /**
   * Get all installments for a parent transaction (including current active ones)
   */
  async getInstallmentsByParent(transactionId: string): Promise<{
    parentTransaction: any;
    allInstallments: any[];
    totalInstallments: number;
  }> {
    // First, get the transaction to determine if it's parent or child
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        parentTransaction: true
      }
    });

    if (!transaction) {
      throw new Error('Transação não encontrada');
    }

    // Determine the parent transaction ID
    const parentId = transaction.parentTransactionId || transactionId;

    // Get all installments for this parent (only active ones, not soft deleted)
    const allTransactions = await prisma.transaction.findMany({
      where: {
        OR: [
          { id: parentId },
          { parentTransactionId: parentId }
        ],
        deletedAt: null // Only get active installments
      },
      include: {
        account: {
          select: {
            id: true,
            name: true,
            type: true,
            currency: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        destinationAccount: {
          select: {
            id: true,
            name: true,
            type: true,
            currency: true
          }
        },
        parentTransaction: {
          select: {
            id: true,
            description: true
          }
        }
      },
      orderBy: [
        { installmentNumber: 'asc' },
        { transactionDate: 'asc' }
      ]
    });

    // Separate parent and child transactions
    const parentTransaction = allTransactions.find(t => !t.parentTransactionId);
    const childInstallments = allTransactions.filter(t => t.parentTransactionId);

    // Combine all installments (parent first, then children)
    const allInstallments = parentTransaction ? [parentTransaction, ...childInstallments] : childInstallments;

    return {
      parentTransaction,
      allInstallments,
      totalInstallments: allInstallments.length
    };
  }

  /**
   * Update installment transaction using delete + create strategy
   */
  async updateInstallmentTransaction(
    parentTransactionId: string,
    data: UpdateInstallmentsData
  ): Promise<UpdateInstallmentsResult> {
    // Validate input data
    this.validateUpdateInstallmentData(data);

    return await prisma.$transaction(async (tx) => {
      // 1. Get parent transaction and all related installments
      const parentTransaction = await tx.transaction.findUnique({
        where: { id: parentTransactionId }
      });

      if (!parentTransaction) {
        throw new Error('Transação pai não encontrada');
      }

      if (!parentTransaction.totalInstallments || parentTransaction.totalInstallments < 2) {
        throw new Error('Transação não é parcelada');
      }

      // Get ALL installments (parent + children) that are not deleted
      const allInstallments = await tx.transaction.findMany({
        where: {
          OR: [
            { id: parentTransactionId },
            { parentTransactionId: parentTransactionId }
          ],
          deletedAt: null
        },
        orderBy: { installmentNumber: 'asc' }
      });

      // 2. Validate total amount matches
      const inputSum = data.installments.reduce((sum, inst) => sum + inst.amount, 0);
      if (Math.abs(inputSum - data.totalAmount) >= 0.01) {
        throw new Error('A soma das parcelas deve ser igual ao valor total');
      }

      // 3. Soft delete ALL existing installments (parent + children)
      const deletedInstallmentIds = allInstallments.map(t => t.id);

      if (deletedInstallmentIds.length > 0) {
        await tx.transaction.updateMany({
          where: { id: { in: deletedInstallmentIds } },
          data: {
            deletedAt: new Date(),
            updatedAt: new Date()
          }
        });

        // Delete related transaction members and tags
        await tx.transactionMember.deleteMany({
          where: { transactionId: { in: deletedInstallmentIds } }
        });

        await tx.transactionTag.deleteMany({
          where: { transactionId: { in: deletedInstallmentIds } }
        });
      }

      // 4. Create new installments (first one becomes the new parent)
      const newInstallments = [];
      let newParentTransaction: any = null;

      for (let i = 0; i < data.installments.length; i++) {
        const installmentData = data.installments[i];
        const isFirstInstallment = i === 0;

        const newInstallment = await tx.transaction.create({
          data: {
            description: installmentData.description || `${parentTransaction.description} - Parcela ${i + 1}/${data.installments.length}`,
            amount: installmentData.amount,
            transactionDate: installmentData.transactionDate,
            type: parentTransaction.type,
            accountId: parentTransaction.accountId,
            categoryId: parentTransaction.categoryId,
            parentTransactionId: isFirstInstallment ? null : newParentTransaction?.id,
            installmentNumber: isFirstInstallment ? undefined : i + 1,
            totalInstallments: data.installments.length,
            exchangeRate: parentTransaction.exchangeRate,
            destinationAccountId: parentTransaction.destinationAccountId,
            isFuture: installmentData.transactionDate > new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });

        if (isFirstInstallment) {
          newParentTransaction = newInstallment;
        }

        newInstallments.push(newInstallment);
      }

      // 5. The first installment is now the parent, no need to update separately
      const updatedParent = newParentTransaction;

      // 6. Generate summary
      const summary = await this.generateInstallmentSummary(
        updatedParent,
        newInstallments
      );

      return {
        parentTransaction: updatedParent,
        newInstallments,
        deletedInstallments: deletedInstallmentIds,
        summary
      };
    });
  }

  /**
   * Get installment summary and progress
   */
  async getInstallmentSummary(parentTransactionId: string): Promise<InstallmentSummary> {
    const installments = await prisma.transaction.findMany({
      where: {
        OR: [
          { id: parentTransactionId },
          { parentTransactionId: parentTransactionId }
        ]
      },
      orderBy: { installmentNumber: 'asc' }
    });

    if (installments.length === 0) {
      throw new Error('No installments found');
    }

    const completedInstallments = installments.filter(i => !i.isFuture);
    const remainingInstallments = installments.filter(i => i.isFuture);
    
    const totalAmount = installments.reduce((sum, i) => sum + Number(i.amount), 0);
    const paidAmount = completedInstallments.reduce((sum, i) => sum + Number(i.amount), 0);
    const remainingAmount = totalAmount - paidAmount;
    
    const progressPercentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;
    
    const nextInstallment = remainingInstallments
      .sort((a, b) => new Date(a.transactionDate).getTime() - new Date(b.transactionDate).getTime())[0];

    return {
      totalInstallments: installments.length,
      completedInstallments: completedInstallments.length,
      remainingInstallments: remainingInstallments.length,
      totalAmount,
      paidAmount,
      remainingAmount,
      progressPercentage: Math.round(progressPercentage * 100) / 100,
      nextInstallmentDate: nextInstallment?.transactionDate
    };
  }

  /**
   * Validate update installment data
   */
  private validateUpdateInstallmentData(data: UpdateInstallmentsData): void {
    if (data.installments.length < this.MIN_INSTALLMENTS) {
      throw new Error(`Mínimo de ${this.MIN_INSTALLMENTS} parcelas obrigatório`);
    }

    if (data.installments.length > this.MAX_INSTALLMENTS) {
      throw new Error(`Máximo de ${this.MAX_INSTALLMENTS} parcelas permitido`);
    }

    if (data.totalAmount <= 0) {
      throw new Error('Valor total deve ser positivo');
    }

    // Validate each installment
    data.installments.forEach((installment, index) => {
      if (installment.amount <= 0) {
        throw new Error(`Valor da parcela ${index + 1} deve ser positivo`);
      }

      if (installment.amount < this.MIN_INSTALLMENT_AMOUNT) {
        throw new Error(`Valor mínimo da parcela é R$ ${this.MIN_INSTALLMENT_AMOUNT.toFixed(2)}`);
      }
    });

    // Validate sum
    const sum = data.installments.reduce((acc, inst) => acc + inst.amount, 0);
    if (Math.abs(sum - data.totalAmount) >= 0.01) {
      throw new Error('A soma das parcelas deve ser igual ao valor total');
    }
  }

  /**
   * Generate installment summary from transactions
   */
  private async generateInstallmentSummary(
    parentTransaction: any,
    installments: any[]
  ): Promise<InstallmentSummary> {
    const completedInstallments = installments.filter(i => !i.isFuture);
    const remainingInstallments = installments.filter(i => i.isFuture);

    const totalAmount = installments.reduce((sum, i) => sum + Number(i.amount), 0);
    const paidAmount = completedInstallments.reduce((sum, i) => sum + Number(i.amount), 0);
    const remainingAmount = totalAmount - paidAmount;

    const progressPercentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

    const nextInstallment = remainingInstallments
      .sort((a, b) => new Date(a.transactionDate).getTime() - new Date(b.transactionDate).getTime())[0];

    return {
      totalInstallments: installments.length,
      completedInstallments: completedInstallments.length,
      remainingInstallments: remainingInstallments.length,
      totalAmount,
      paidAmount,
      remainingAmount,
      progressPercentage: Math.round(progressPercentage * 100) / 100,
      nextInstallmentDate: nextInstallment?.transactionDate
    };
  }

  /**
   * Validate installment data
   */
  private validateInstallmentData(data: InstallmentTransactionData): void {
    if (data.totalInstallments < this.MIN_INSTALLMENTS) {
      throw new Error(`Minimum ${this.MIN_INSTALLMENTS} installments required`);
    }

    if (data.totalInstallments > this.MAX_INSTALLMENTS) {
      throw new Error(`Maximum ${this.MAX_INSTALLMENTS} installments allowed`);
    }

    const installmentAmount = this.calculateInstallmentAmount(data.totalAmount, data.totalInstallments);
    if (installmentAmount < this.MIN_INSTALLMENT_AMOUNT) {
      throw new Error(`Minimum installment amount is ${this.MIN_INSTALLMENT_AMOUNT}`);
    }

    if (data.totalAmount <= 0) {
      throw new Error('Total amount must be positive');
    }
  }

  /**
   * Calculate installment amount
   */
  private calculateInstallmentAmount(totalAmount: number, totalInstallments: number): number {
    return Math.round((totalAmount / totalInstallments) * 100) / 100;
  }

  /**
   * Calculate installment date
   */
  private calculateInstallmentDate(firstDate: Date, monthsToAdd: number): Date {
    const date = new Date(firstDate);
    date.setMonth(date.getMonth() + monthsToAdd);
    return date;
  }
}
