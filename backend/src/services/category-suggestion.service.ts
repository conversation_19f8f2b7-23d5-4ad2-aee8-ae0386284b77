import { TransactionType } from '@prisma/client';
import { historicalAnalysisService, CategoryFrequency, TransactionPattern, ValueRangePattern } from './historical-analysis.service';
import { 
  combinedSimilarity, 
  findMostSimilar, 
  extractKeywords,
  normalizeText 
} from '../utils/text-similarity.utils';

/**
 * Interface for category suggestion request
 */
export interface CategorySuggestionRequest {
  description: string;
  amount?: number;
  type?: TransactionType;
  userId?: string;
}

/**
 * Interface for category suggestion response
 */
export interface CategorySuggestion {
  categoryId: string;
  categoryName: string;
  confidence: number;
  reason: string;
  matchType: 'description' | 'amount' | 'frequency' | 'keyword' | 'combined';
}

/**
 * Configuration for suggestion algorithms
 */
interface SuggestionConfig {
  descriptionWeight: number;
  amountWeight: number;
  frequencyWeight: number;
  keywordWeight: number;
  minConfidence: number;
  maxSuggestions: number;
}

/**
 * Service for generating intelligent category suggestions based on historical data
 */
export class CategorySuggestionService {
  private readonly defaultConfig: SuggestionConfig = {
    descriptionWeight: 0.4,
    amountWeight: 0.2,
    frequencyWeight: 0.2,
    keywordWeight: 0.2,
    minConfidence: 0.3,
    maxSuggestions: 5
  };

  /**
   * Get category suggestions for a new transaction
   */
  async getSuggestions(
    request: CategorySuggestionRequest,
    config?: Partial<SuggestionConfig>
  ): Promise<CategorySuggestion[]> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    try {
      // Get historical data
      const [frequencies, patterns, valueRanges] = await Promise.all([
        historicalAnalysisService.getCategoryFrequencies(request.userId),
        historicalAnalysisService.getTransactionPatterns(request.userId),
        historicalAnalysisService.getValueRangePatterns(request.userId)
      ]);

      if (frequencies.length === 0) {
        return this.getFallbackSuggestions();
      }

      // Generate suggestions using different algorithms
      const suggestions = new Map<string, CategorySuggestion>();

      // 1. Description similarity suggestions
      const descriptionSuggestions = await this.getSuggestionsByDescription(
        request.description,
        patterns,
        finalConfig.descriptionWeight
      );
      this.mergeSuggestions(suggestions, descriptionSuggestions);

      // 2. Amount range suggestions
      if (request.amount) {
        const amountSuggestions = this.getSuggestionsByAmount(
          request.amount,
          valueRanges,
          finalConfig.amountWeight
        );
        this.mergeSuggestions(suggestions, amountSuggestions);
      }

      // 3. Frequency-based suggestions
      const frequencySuggestions = this.getSuggestionsByFrequency(
        frequencies,
        finalConfig.frequencyWeight
      );
      this.mergeSuggestions(suggestions, frequencySuggestions);

      // 4. Keyword-based suggestions
      const keywordSuggestions = await this.getSuggestionsByKeywords(
        request.description,
        request.userId,
        finalConfig.keywordWeight
      );
      this.mergeSuggestions(suggestions, keywordSuggestions);

      // Convert to array and sort by confidence
      const result = Array.from(suggestions.values())
        .filter(s => s.confidence >= finalConfig.minConfidence)
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, finalConfig.maxSuggestions);

      return result;
    } catch (error) {
      console.error('Error generating category suggestions:', error);
      return this.getFallbackSuggestions();
    }
  }

  /**
   * Get suggestions based on description similarity
   */
  private async getSuggestionsByDescription(
    description: string,
    patterns: TransactionPattern[],
    weight: number
  ): Promise<CategorySuggestion[]> {
    if (patterns.length === 0) return [];

    const descriptions = patterns.map(p => p.description);
    const mostSimilar = findMostSimilar(description, descriptions);

    if (!mostSimilar || mostSimilar.similarity < 0.3) return [];

    const pattern = patterns[mostSimilar.index];
    const confidence = mostSimilar.similarity * weight;

    return [{
      categoryId: pattern.categoryId,
      categoryName: pattern.categoryName,
      confidence,
      reason: `Descrição similar a "${pattern.description}" (${Math.round(mostSimilar.similarity * 100)}% de similaridade)`,
      matchType: 'description'
    }];
  }

  /**
   * Get suggestions based on amount ranges
   */
  private getSuggestionsByAmount(
    amount: number,
    valueRanges: ValueRangePattern[],
    weight: number
  ): CategorySuggestion[] {
    const suggestions: CategorySuggestion[] = [];

    for (const range of valueRanges) {
      if (amount >= range.minAmount && amount <= range.maxAmount) {
        // Calculate confidence based on how close the amount is to the average
        const avgDiff = Math.abs(amount - range.averageAmount);
        const rangeSize = range.maxAmount - range.minAmount;
        const proximity = rangeSize > 0 ? 1 - (avgDiff / rangeSize) : 1;
        const confidence = proximity * weight;

        suggestions.push({
          categoryId: range.categoryId,
          categoryName: range.categoryName,
          confidence,
          reason: `Valor R$ ${amount.toFixed(2)} está na faixa típica desta categoria (R$ ${range.minAmount.toFixed(2)} - R$ ${range.maxAmount.toFixed(2)})`,
          matchType: 'amount'
        });
      }
    }

    return suggestions.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Get suggestions based on category frequency
   */
  private getSuggestionsByFrequency(
    frequencies: CategoryFrequency[],
    weight: number
  ): CategorySuggestion[] {
    const totalTransactions = frequencies.reduce((sum, f) => sum + f.frequency, 0);
    
    return frequencies.slice(0, 3).map(freq => {
      const relativeFrequency = freq.frequency / totalTransactions;
      const confidence = relativeFrequency * weight;

      return {
        categoryId: freq.categoryId,
        categoryName: freq.categoryName,
        confidence,
        reason: `Categoria mais utilizada (${freq.frequency} transações, ${Math.round(relativeFrequency * 100)}% do total)`,
        matchType: 'frequency'
      };
    });
  }

  /**
   * Get suggestions based on keyword matching
   */
  private async getSuggestionsByKeywords(
    description: string,
    userId?: string,
    weight: number = 0.2
  ): Promise<CategorySuggestion[]> {
    try {
      const categoryIds = await historicalAnalysisService.findCategoriesByKeywords(description, userId);
      
      if (categoryIds.length === 0) return [];

      const frequencies = await historicalAnalysisService.getCategoryFrequencies(userId);
      const frequencyMap = new Map(frequencies.map(f => [f.categoryId, f]));

      const keywords = extractKeywords(description, 3);
      
      return categoryIds.map(categoryId => {
        const freq = frequencyMap.get(categoryId);
        if (!freq) return null;

        // Calculate confidence based on keyword relevance and category frequency
        const keywordRelevance = keywords.length > 0 ? 0.8 : 0.5;
        const frequencyBonus = Math.min(freq.frequency / 10, 0.5); // Max 0.5 bonus
        const confidence = (keywordRelevance + frequencyBonus) * weight;

        return {
          categoryId,
          categoryName: freq.categoryName,
          confidence,
          reason: `Palavras-chave "${keywords.join(', ')}" encontradas em transações desta categoria`,
          matchType: 'keyword' as const
        };
      }).filter(Boolean) as CategorySuggestion[];
    } catch (error) {
      console.error('Error getting keyword suggestions:', error);
      return [];
    }
  }

  /**
   * Merge suggestions, combining confidence scores for the same category
   */
  private mergeSuggestions(
    existing: Map<string, CategorySuggestion>,
    newSuggestions: CategorySuggestion[]
  ): void {
    for (const suggestion of newSuggestions) {
      const existingSuggestion = existing.get(suggestion.categoryId);
      
      if (existingSuggestion) {
        // Combine confidence scores and reasons
        existingSuggestion.confidence = Math.min(
          existingSuggestion.confidence + suggestion.confidence,
          1.0 // Cap at 100%
        );
        existingSuggestion.reason += ` | ${suggestion.reason}`;
        existingSuggestion.matchType = 'combined';
      } else {
        existing.set(suggestion.categoryId, { ...suggestion });
      }
    }
  }

  /**
   * Get fallback suggestions when no historical data is available
   */
  private getFallbackSuggestions(): CategorySuggestion[] {
    // These would typically come from a default category set
    return [
      {
        categoryId: 'default-food',
        categoryName: 'Alimentação',
        confidence: 0.5,
        reason: 'Categoria padrão sugerida',
        matchType: 'frequency'
      },
      {
        categoryId: 'default-transport',
        categoryName: 'Transporte',
        confidence: 0.4,
        reason: 'Categoria padrão sugerida',
        matchType: 'frequency'
      },
      {
        categoryId: 'default-utilities',
        categoryName: 'Utilidades',
        confidence: 0.3,
        reason: 'Categoria padrão sugerida',
        matchType: 'frequency'
      }
    ];
  }

  /**
   * Get suggestions with custom weights for specific scenarios
   */
  async getSuggestionsForExpense(request: CategorySuggestionRequest): Promise<CategorySuggestion[]> {
    return this.getSuggestions(request, {
      descriptionWeight: 0.5, // Higher weight for description in expenses
      amountWeight: 0.3,
      frequencyWeight: 0.1,
      keywordWeight: 0.1
    });
  }

  /**
   * Get suggestions with custom weights for income transactions
   */
  async getSuggestionsForIncome(request: CategorySuggestionRequest): Promise<CategorySuggestion[]> {
    return this.getSuggestions(request, {
      descriptionWeight: 0.6, // Even higher weight for description in income
      amountWeight: 0.2,
      frequencyWeight: 0.1,
      keywordWeight: 0.1
    });
  }
}

// Export singleton instance
export const categorySuggestionService = new CategorySuggestionService();
