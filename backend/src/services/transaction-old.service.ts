import prisma from '../lib/prisma';
import { Prisma, TransactionType } from '@prisma/client';
import {
  CreateTransactionRequest,
  UpdateTransactionRequest,
  TransactionFilters,
  TransactionResponse,
  PaginatedTransactionsResponse
} from '../schemas/transaction.schemas';

export class TransactionService {
  /**
   * Create a new transaction with balance updates
   */
  async create(data: CreateTransactionRequest): Promise<TransactionResponse> {
    // Check if this is an installment transaction
    if (data.totalInstallments && data.totalInstallments > 1) {
      return this.createInstallmentTransaction(data);
    }

    // Regular single transaction
    return this.createSingleTransaction(data);
  }

  private async createSingleTransaction(data: CreateTransactionRequest): Promise<TransactionResponse> {
    // Verify account exists
    const account = await prisma.account.findFirst({
      where: { id: data.accountId, deletedAt: null }
    });

    if (!account) {
      throw new Error('Conta não encontrada');
    }

    // Verify destination account for transfers
    if (data.type === TransactionType.TRANSFER && data.destinationAccountId) {
      const destinationAccount = await prisma.account.findFirst({
        where: { id: data.destinationAccountId, deletedAt: null }
      });

      if (!destinationAccount) {
        throw new Error('Conta destino não encontrada');
      }
    }

    // Verify category exists if provided
    if (data.categoryId) {
      const category = await prisma.category.findFirst({
        where: { id: data.categoryId, deletedAt: null }
      });

      if (!category) {
        throw new Error('Categoria não encontrada');
      }
    }

    // Verify family members exist
    const familyMembers = await prisma.familyMember.findMany({
      where: {
        id: { in: data.familyMemberIds },
        deletedAt: null
      }
    });

    if (familyMembers.length !== data.familyMemberIds.length) {
      throw new Error('Um ou mais membros da família não foram encontrados');
    }

    // Verify tags exist if provided
    if (data.tagIds && data.tagIds.length > 0) {
      const tags = await prisma.tag.findMany({
        where: {
          id: { in: data.tagIds },
          deletedAt: null
        }
      });

      if (tags.length !== data.tagIds.length) {
        throw new Error('Uma ou mais tags não foram encontradas');
      }
    }

    // Use transaction for data integrity
    const transaction = await prisma.$transaction(async (tx) => {
      // Create transaction
      const newTransaction = await tx.transaction.create({
        data: {
          description: data.description,
          amount: data.amount,
          transactionDate: data.transactionDate,
          type: data.type,
          accountId: data.accountId,
          categoryId: data.categoryId,
          destinationAccountId: data.destinationAccountId,
          exchangeRate: data.exchangeRate,
          totalInstallments: data.totalInstallments,
          parentTransactionId: data.parentTransactionId,
          isFuture: data.isFuture
        },
        include: {
          account: true,
          category: true,
          destinationAccount: true,
          parentTransaction: true
        }
      });

      // Create family member associations
      await tx.transactionMember.createMany({
        data: data.familyMemberIds.map(familyMemberId => ({
          transactionId: newTransaction.id,
          familyMemberId
        }))
      });

      // Create tag associations if provided
      if (data.tagIds && data.tagIds.length > 0) {
        await tx.transactionTag.createMany({
          data: data.tagIds.map(tagId => ({
            transactionId: newTransaction.id,
            tagId
          }))
        });
      }

      // Update account balance if not a future transaction
      if (!data.isFuture) {
        await this.updateAccountBalance(tx, data.accountId, data.type, data.amount);

        // Update destination account balance for transfers
        if (data.type === TransactionType.TRANSFER && data.destinationAccountId) {
          await this.updateAccountBalance(tx, data.destinationAccountId, TransactionType.INCOME, data.amount);
        }
      }

      return newTransaction;
    });

    return this.formatTransaction(transaction, familyMembers, data.tagIds ? await prisma.tag.findMany({
      where: { id: { in: data.tagIds } }
    }) : []);
  }

  private async createInstallmentTransaction(data: CreateTransactionRequest): Promise<TransactionResponse> {
    const totalInstallments = data.totalInstallments!;
    const installmentAmount = Number((data.amount / totalInstallments).toFixed(2));

    // Verify account exists
    const account = await prisma.account.findFirst({
      where: { id: data.accountId, deletedAt: null }
    });

    if (!account) {
      throw new Error('Conta não encontrada');
    }

    // Verify destination account for transfers
    if (data.type === TransactionType.TRANSFER && data.destinationAccountId) {
      const destinationAccount = await prisma.account.findFirst({
        where: { id: data.destinationAccountId, deletedAt: null }
      });

      if (!destinationAccount) {
        throw new Error('Conta destino não encontrada');
      }
    }

    // Verify category exists if provided
    if (data.categoryId) {
      const category = await prisma.category.findFirst({
        where: { id: data.categoryId, deletedAt: null }
      });

      if (!category) {
        throw new Error('Categoria não encontrada');
      }
    }

    // Verify family members exist
    const familyMembers = await prisma.familyMember.findMany({
      where: {
        id: { in: data.familyMemberIds },
        deletedAt: null
      }
    });

    if (familyMembers.length !== data.familyMemberIds.length) {
      throw new Error('Um ou mais membros da família não foram encontrados');
    }

    // Verify tags exist if provided
    if (data.tagIds && data.tagIds.length > 0) {
      const tags = await prisma.tag.findMany({
        where: {
          id: { in: data.tagIds },
          deletedAt: null
        }
      });

      if (tags.length !== data.tagIds.length) {
        throw new Error('Uma ou mais tags não foram encontradas');
      }
    }

    // Create all installments in a database transaction
    const result = await prisma.$transaction(async (tx) => {
      let parentTransaction: any = null;

      for (let i = 1; i <= totalInstallments; i++) {
        // Calculate installment date (add months to the original date)
        const installmentDate = new Date(data.transactionDate);
        installmentDate.setMonth(installmentDate.getMonth() + (i - 1));

        // For the first installment, create the parent transaction
        // For subsequent installments, create child transactions
        const isFirstInstallment = i === 1;

        const transaction = await tx.transaction.create({
          data: {
            description: `${data.description} (${i}/${totalInstallments})`,
            amount: installmentAmount,
            transactionDate: installmentDate,
            type: data.type,
            accountId: data.accountId,
            categoryId: data.categoryId,
            destinationAccountId: data.destinationAccountId,
            exchangeRate: data.exchangeRate,
            installmentNumber: i,
            totalInstallments: totalInstallments,
            parentTransactionId: isFirstInstallment ? undefined : parentTransaction?.id,
            isFuture: i > 1, // First installment is immediate, others are future
          },
          include: {
            account: true,
            category: true,
            destinationAccount: true,
            parentTransaction: true
          }
        });

        if (isFirstInstallment) {
          parentTransaction = transaction;
        }

        // Create family member associations for each installment
        await tx.transactionMember.createMany({
          data: data.familyMemberIds.map(familyMemberId => ({
            transactionId: transaction.id,
            familyMemberId
          }))
        });

        // Create tag associations for each installment
        if (data.tagIds && data.tagIds.length > 0) {
          await tx.transactionTag.createMany({
            data: data.tagIds.map(tagId => ({
              transactionId: transaction.id,
              tagId
            }))
          });
        }

        // Update account balance only for the first installment (immediate)
        if (isFirstInstallment) {
          await this.updateAccountBalance(tx, data.accountId, data.type, installmentAmount);

          // Update destination account balance for transfers
          if (data.type === TransactionType.TRANSFER && data.destinationAccountId) {
            await this.updateAccountBalance(tx, data.destinationAccountId, TransactionType.INCOME, installmentAmount);
          }
        }
      }

      return parentTransaction;
    });

    // Return the parent transaction with related data
    return this.formatTransaction(result, familyMembers, data.tagIds ? await prisma.tag.findMany({
      where: { id: { in: data.tagIds } }
    }) : []);
  }

  private async updateInstallmentTransaction(id: string, data: UpdateTransactionRequest): Promise<TransactionResponse> {
    // Find the transaction being edited and all related installments
    const editedTransaction = await prisma.transaction.findFirst({
      where: { id, deletedAt: null },
      include: {
        members: true,
        tags: true,
        parentTransaction: {
          include: {
            installments: {
              where: { deletedAt: null },
              orderBy: { installmentNumber: 'asc' }
            }
          }
        },
        installments: {
          where: { deletedAt: null },
          orderBy: { installmentNumber: 'asc' }
        }
      }
    });

    if (!editedTransaction) {
      throw new Error('Transação não encontrada');
    }

    // Get all installments (whether this is parent or child)
    let allInstallments: any[];
    let parentTransaction: any;

    if (editedTransaction.parentTransactionId) {
      // This is a child installment
      parentTransaction = editedTransaction.parentTransaction;
      allInstallments = [parentTransaction, ...parentTransaction.installments];
    } else {
      // This is the parent transaction
      parentTransaction = editedTransaction;
      allInstallments = [parentTransaction, ...editedTransaction.installments];
    }

    // Check if we have edited installments data from frontend
    if ((data as any).editedInstallments) {
      return this.updateAllInstallments(allInstallments, data);
    }

    // Calculate current total value and find the installment being edited
    let currentTotal = 0;
    let editedInstallmentIndex = -1;

    allInstallments.forEach((installment, index) => {
      currentTotal += installment.amount;
      if (installment.id === id) {
        editedInstallmentIndex = index;
      }
    });

    // Apply changes to the edited installment
    const updatedInstallments = allInstallments.map((installment, index) => {
      if (index === editedInstallmentIndex) {
        return {
          ...installment,
          description: data.description || installment.description,
          amount: data.amount || installment.amount,
          transactionDate: data.transactionDate || installment.transactionDate,
          categoryId: data.categoryId !== undefined ? data.categoryId : installment.categoryId,
          destinationAccountId: data.destinationAccountId !== undefined ? data.destinationAccountId : installment.destinationAccountId,
          exchangeRate: data.exchangeRate !== undefined ? data.exchangeRate : installment.exchangeRate,
        };
      }
      return installment;
    });

    // Recalculate values for future installments
    const editedInstallment = updatedInstallments[editedInstallmentIndex];
    const editedDate = new Date(editedInstallment.transactionDate);

    // Find installments that come after the edited one
    const futureInstallments = updatedInstallments.filter((installment, index) => {
      const installmentDate = new Date(installment.transactionDate);
      return installmentDate > editedDate && index !== editedInstallmentIndex;
    });

    // Calculate remaining value for future installments
    const pastAndCurrentTotal = updatedInstallments
      .filter((installment, index) => {
        const installmentDate = new Date(installment.transactionDate);
        return installmentDate <= editedDate;
      })
      .reduce((sum, installment) => sum + installment.amount, 0);

    const newTotal = currentTotal + (editedInstallment.amount - allInstallments[editedInstallmentIndex].amount);
    const remainingValue = newTotal - pastAndCurrentTotal;

    // Redistribute remaining value among future installments
    if (futureInstallments.length > 0 && remainingValue > 0) {
      const valuePerFutureInstallment = Number((remainingValue / futureInstallments.length).toFixed(2));

      updatedInstallments.forEach((installment, index) => {
        const installmentDate = new Date(installment.transactionDate);
        if (installmentDate > editedDate && index !== editedInstallmentIndex) {
          installment.amount = valuePerFutureInstallment;
        }
      });
    }

    // Delete and recreate all installments in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Soft delete all existing installments
      const installmentIds = allInstallments.map(i => i.id);
      await tx.transaction.updateMany({
        where: { id: { in: installmentIds } },
        data: { deletedAt: new Date() }
      });

      // Delete related records
      await tx.transactionMember.deleteMany({
        where: { transactionId: { in: installmentIds } }
      });
      await tx.transactionTag.deleteMany({
        where: { transactionId: { in: installmentIds } }
      });

      // Recreate all installments
      let newParentTransaction: any = null;

      for (let i = 0; i < updatedInstallments.length; i++) {
        const installment = updatedInstallments[i];
        const isFirst = i === 0;

        const newTransaction = await tx.transaction.create({
          data: {
            description: installment.description,
            amount: installment.amount,
            transactionDate: installment.transactionDate,
            type: installment.type,
            accountId: installment.accountId,
            categoryId: installment.categoryId,
            destinationAccountId: installment.destinationAccountId,
            exchangeRate: installment.exchangeRate,
            installmentNumber: isFirst ? undefined : i,
            totalInstallments: allInstallments.length,
            parentTransactionId: isFirst ? undefined : newParentTransaction?.id,
            isFuture: !isFirst,
          },
          include: {
            account: true,
            category: true,
            destinationAccount: true,
            parentTransaction: true
          }
        });

        if (isFirst) {
          newParentTransaction = newTransaction;
        }

        // Recreate family member associations
        if (data.familyMemberIds && data.familyMemberIds.length > 0) {
          await tx.transactionMember.createMany({
            data: data.familyMemberIds.map(familyMemberId => ({
              transactionId: newTransaction.id,
              familyMemberId
            }))
          });
        }

        // Recreate tag associations
        if (data.tagIds && data.tagIds.length > 0) {
          await tx.transactionTag.createMany({
            data: data.tagIds.map(tagId => ({
              transactionId: newTransaction.id,
              tagId
            }))
          });
        }
      }

      return newParentTransaction;
    });

    // Return the updated transaction
    const familyMembers = await prisma.familyMember.findMany({
      where: { id: { in: data.familyMemberIds || [] } }
    });

    const tags = data.tagIds ? await prisma.tag.findMany({
      where: { id: { in: data.tagIds } }
    }) : [];

    return this.formatTransaction(result, familyMembers, tags);
  }

  private async updateAllInstallments(allInstallments: any[], data: UpdateTransactionRequest): Promise<TransactionResponse> {
    const editedInstallments = (data as any).editedInstallments;
    const newTotalInstallments = data.totalInstallments || allInstallments.length;

    // Validate installments data
    if (!editedInstallments || editedInstallments.length === 0) {
      throw new Error('Dados das parcelas não fornecidos');
    }

    // Delete and recreate all installments in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Soft delete all existing installments
      const installmentIds = allInstallments.map(i => i.id);
      await tx.transaction.updateMany({
        where: { id: { in: installmentIds } },
        data: { deletedAt: new Date() }
      });

      // Delete related records
      await tx.transactionMember.deleteMany({
        where: { transactionId: { in: installmentIds } }
      });
      await tx.transactionTag.deleteMany({
        where: { transactionId: { in: installmentIds } }
      });

      // Recreate all installments with edited data
      let newParentTransaction: any = null;

      for (let i = 0; i < editedInstallments.length; i++) {
        const editedData = editedInstallments[i];
        const originalInstallment = allInstallments[i] || allInstallments[0]; // Use first installment as template for new ones
        const isFirst = i === 0;

        // Generate description for the installment
        const baseDescription = data.description || originalInstallment.description.replace(/\s*\(\d+\/\d+\)$/, '');
        const installmentDescription = `${baseDescription} (${i + 1}/${editedInstallments.length})`;

        const newTransaction = await tx.transaction.create({
          data: {
            description: installmentDescription,
            amount: editedData.amount,
            transactionDate: editedData.transactionDate,
            type: originalInstallment.type,
            accountId: originalInstallment.accountId,
            categoryId: data.categoryId !== undefined ? data.categoryId : originalInstallment.categoryId,
            destinationAccountId: data.destinationAccountId !== undefined ? data.destinationAccountId : originalInstallment.destinationAccountId,
            exchangeRate: data.exchangeRate !== undefined ? data.exchangeRate : originalInstallment.exchangeRate,
            installmentNumber: isFirst ? undefined : i,
            totalInstallments: editedInstallments.length,
            parentTransactionId: isFirst ? undefined : newParentTransaction?.id,
            isFuture: !isFirst,
          },
          include: {
            account: true,
            category: true,
            destinationAccount: true,
            parentTransaction: true
          }
        });

        if (isFirst) {
          newParentTransaction = newTransaction;
        }

        // Recreate family member associations
        if (data.familyMemberIds && data.familyMemberIds.length > 0) {
          await tx.transactionMember.createMany({
            data: data.familyMemberIds.map(familyMemberId => ({
              transactionId: newTransaction.id,
              familyMemberId
            }))
          });
        }

        // Recreate tag associations
        if (data.tagIds && data.tagIds.length > 0) {
          await tx.transactionTag.createMany({
            data: data.tagIds.map(tagId => ({
              transactionId: newTransaction.id,
              tagId
            }))
          });
        }
      }

      return newParentTransaction;
    });

    // Return the updated transaction
    const familyMembers = await prisma.familyMember.findMany({
      where: { id: { in: data.familyMemberIds || [] } }
    });

    const tags = data.tagIds ? await prisma.tag.findMany({
      where: { id: { in: data.tagIds } }
    }) : [];

    return this.formatTransaction(result, familyMembers, tags);
  }

  /**
   * Get all transactions with filters and pagination
   */
  async findAll(filters: TransactionFilters = {}): Promise<PaginatedTransactionsResponse> {
    const {
      description,
      type,
      accountId,
      categoryId,
      familyMemberId,
      tagId,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      isFuture,
      includeDeleted = false,
      page = 1,
      limit = 20,
      sortBy = 'transactionDate',
      sortOrder = 'desc'
    } = filters;

    // Build where clause
    const where: Prisma.TransactionWhereInput = {};

    if (description) {
      where.description = {
        contains: description,
        mode: 'insensitive'
      };
    }

    if (type) {
      where.type = type;
    }

    if (accountId) {
      where.OR = [
        { accountId },
        { destinationAccountId: accountId }
      ];
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (familyMemberId) {
      where.members = {
        some: {
          familyMemberId,
          familyMember: { deletedAt: null }
        }
      };
    }

    if (tagId) {
      where.tags = {
        some: {
          tagId,
          tag: { deletedAt: null }
        }
      };
    }

    if (startDate || endDate) {
      where.transactionDate = {};
      if (startDate) where.transactionDate.gte = startDate;
      if (endDate) where.transactionDate.lte = endDate;
    }

    if (minAmount || maxAmount) {
      where.amount = {};
      if (minAmount) where.amount.gte = minAmount;
      if (maxAmount) where.amount.lte = maxAmount;
    }

    if (isFuture !== undefined) {
      where.isFuture = isFuture;
    }

    if (!includeDeleted) {
      where.deletedAt = null;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await prisma.transaction.count({ where });

    // Get transactions with related data
    const transactions = await prisma.transaction.findMany({
      where,
      include: {
        account: {
          select: {
            id: true,
            name: true,
            type: true,
            currency: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        destinationAccount: {
          select: {
            id: true,
            name: true,
            type: true,
            currency: true
          }
        },
        parentTransaction: {
          select: {
            id: true,
            description: true
          }
        },
        installments: {
          select: {
            id: true,
            description: true,
            amount: true,
            transactionDate: true,
            installmentNumber: true
          },
          orderBy: { installmentNumber: 'asc' }
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          },
          where: {
            tag: { deletedAt: null }
          }
        },
        members: {
          include: {
            familyMember: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          },
          where: {
            familyMember: { deletedAt: null }
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    });

    // Calculate summary
    const summary = await this.calculateSummary(where);

    return {
      data: transactions.map(transaction => this.formatTransaction(
        transaction,
        transaction.members.map(m => m.familyMember),
        transaction.tags.map(t => t.tag)
      )),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      summary
    };
  }

  /**
   * Get transaction by ID
   */
  async findById(id: string, includeDeleted = false): Promise<TransactionResponse | null> {
    const where: Prisma.TransactionWhereInput = { id };
    
    if (!includeDeleted) {
      where.deletedAt = null;
    }

    const transaction = await prisma.transaction.findFirst({
      where,
      include: {
        account: {
          select: {
            id: true,
            name: true,
            type: true,
            currency: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        destinationAccount: {
          select: {
            id: true,
            name: true,
            type: true,
            currency: true
          }
        },
        parentTransaction: {
          select: {
            id: true,
            description: true
          }
        },
        installments: {
          select: {
            id: true,
            description: true,
            amount: true,
            transactionDate: true,
            installmentNumber: true
          },
          orderBy: { installmentNumber: 'asc' }
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          },
          where: {
            tag: { deletedAt: null }
          }
        },
        members: {
          include: {
            familyMember: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          },
          where: {
            familyMember: { deletedAt: null }
          }
        }
      }
    });

    if (!transaction) {
      return null;
    }

    return this.formatTransaction(
      transaction,
      transaction.members.map(m => m.familyMember),
      transaction.tags.map(t => t.tag)
    );
  }

  /**
   * Update transaction
   */
  async update(id: string, data: UpdateTransactionRequest): Promise<TransactionResponse> {
    // Check if transaction exists and is not deleted
    const existingTransaction = await prisma.transaction.findFirst({
      where: { id, deletedAt: null },
      include: {
        members: true,
        tags: true,
        installments: true,
        parentTransaction: true
      }
    });

    if (!existingTransaction) {
      throw new Error('Transação não encontrada');
    }

    // Check if this is an installment transaction (parent or child)
    const isInstallmentTransaction = existingTransaction.totalInstallments && existingTransaction.totalInstallments > 1;
    const isChildInstallment = existingTransaction.parentTransactionId;

    if (isInstallmentTransaction || isChildInstallment) {
      return this.updateInstallmentTransaction(id, data);
    }

    // Verify category exists if provided
    if (data.categoryId) {
      const category = await prisma.category.findFirst({
        where: { id: data.categoryId, deletedAt: null }
      });

      if (!category) {
        throw new Error('Categoria não encontrada');
      }
    }

    // Verify family members exist if provided
    if (data.familyMemberIds) {
      const familyMembers = await prisma.familyMember.findMany({
        where: {
          id: { in: data.familyMemberIds },
          deletedAt: null
        }
      });

      if (familyMembers.length !== data.familyMemberIds.length) {
        throw new Error('Um ou mais membros da família não foram encontrados');
      }
    }

    // Verify tags exist if provided
    if (data.tagIds && data.tagIds.length > 0) {
      const tags = await prisma.tag.findMany({
        where: {
          id: { in: data.tagIds },
          deletedAt: null
        }
      });

      if (tags.length !== data.tagIds.length) {
        throw new Error('Uma ou mais tags não foram encontradas');
      }
    }

    // Use transaction for data integrity
    const updatedTransaction = await prisma.$transaction(async (tx) => {
      // Calculate balance adjustments if amount changed and not future transaction
      const oldAmount = Number(existingTransaction.amount);
      const newAmount = data.amount || oldAmount;
      const amountChanged = data.amount && oldAmount !== newAmount;

      if (amountChanged && !existingTransaction.isFuture) {
        // Reverse old transaction effect
        await this.reverseAccountBalance(tx, existingTransaction.accountId, existingTransaction.type, oldAmount);

        if (existingTransaction.type === TransactionType.TRANSFER && existingTransaction.destinationAccountId) {
          await this.reverseAccountBalance(tx, existingTransaction.destinationAccountId, TransactionType.INCOME, oldAmount);
        }

        // Apply new transaction effect
        await this.updateAccountBalance(tx, existingTransaction.accountId, existingTransaction.type, newAmount);

        if (existingTransaction.type === TransactionType.TRANSFER && existingTransaction.destinationAccountId) {
          await this.updateAccountBalance(tx, existingTransaction.destinationAccountId, TransactionType.INCOME, newAmount);
        }
      }

      // Update transaction - only include valid Prisma fields
      const updateData: any = {
        version: { increment: 1 }
      };

      // Add only valid transaction fields (exclude accountId, type, familyMemberIds, tagIds)
      if (data.description !== undefined) updateData.description = data.description;
      if (data.amount !== undefined) updateData.amount = data.amount;
      if (data.transactionDate !== undefined) updateData.transactionDate = data.transactionDate;
      if (data.categoryId !== undefined) updateData.categoryId = data.categoryId;
      if (data.destinationAccountId !== undefined) updateData.destinationAccountId = data.destinationAccountId;
      if (data.exchangeRate !== undefined) updateData.exchangeRate = data.exchangeRate;
      if (data.isFuture !== undefined) updateData.isFuture = data.isFuture;

      const transaction = await tx.transaction.update({
        where: { id },
        data: updateData,
        include: {
          account: true,
          category: true,
          destinationAccount: true,
          parentTransaction: true,
          installments: true
        }
      });

      // Update family member associations if provided
      if (data.familyMemberIds) {
        await tx.transactionMember.deleteMany({
          where: { transactionId: id }
        });

        await tx.transactionMember.createMany({
          data: data.familyMemberIds.map(familyMemberId => ({
            transactionId: id,
            familyMemberId
          }))
        });
      }

      // Update tag associations if provided
      if (data.tagIds !== undefined) {
        await tx.transactionTag.deleteMany({
          where: { transactionId: id }
        });

        if (data.tagIds.length > 0) {
          await tx.transactionTag.createMany({
            data: data.tagIds.map(tagId => ({
              transactionId: id,
              tagId
            }))
          });
        }
      }

      return transaction;
    });

    // Get updated family members and tags
    const familyMembers = data.familyMemberIds ?
      await prisma.familyMember.findMany({
        where: { id: { in: data.familyMemberIds } }
      }) :
      await prisma.transactionMember.findMany({
        where: { transactionId: id },
        include: { familyMember: true }
      }).then(members => members.map(m => m.familyMember));

    const tags = data.tagIds !== undefined ?
      (data.tagIds.length > 0 ? await prisma.tag.findMany({
        where: { id: { in: data.tagIds } }
      }) : []) :
      await prisma.transactionTag.findMany({
        where: { transactionId: id },
        include: { tag: true }
      }).then(tags => tags.map(t => t.tag));

    return this.formatTransaction(updatedTransaction, familyMembers, tags);
  }

  /**
   * Soft delete transaction
   */
  async delete(id: string): Promise<void> {
    const existingTransaction = await prisma.transaction.findFirst({
      where: { id, deletedAt: null }
    });

    if (!existingTransaction) {
      throw new Error('Transação não encontrada');
    }

    // Use transaction for data integrity
    await prisma.$transaction(async (tx) => {
      // Reverse balance effects if not future transaction
      if (!existingTransaction.isFuture) {
        await this.reverseAccountBalance(tx, existingTransaction.accountId, existingTransaction.type, Number(existingTransaction.amount));

        if (existingTransaction.type === TransactionType.TRANSFER && existingTransaction.destinationAccountId) {
          await this.reverseAccountBalance(tx, existingTransaction.destinationAccountId, TransactionType.INCOME, Number(existingTransaction.amount));
        }
      }

      // Soft delete the transaction
      await tx.transaction.update({
        where: { id },
        data: {
          deletedAt: new Date(),
          version: { increment: 1 }
        }
      });
    });
  }

  /**
   * Update account balance based on transaction type
   */
  private async updateAccountBalance(
    tx: Prisma.TransactionClient,
    accountId: string,
    type: TransactionType,
    amount: number
  ): Promise<void> {
    const balanceChange = this.calculateBalanceChange(type, amount);

    // Update the account's current balance
    await tx.account.update({
      where: { id: accountId },
      data: {
        currentBalance: { increment: balanceChange },
        updatedAt: new Date()
      }
    });

    // Create balance history record for tracking
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get the updated account to record the new balance
    const updatedAccount = await tx.account.findUnique({
      where: { id: accountId },
      select: { currentBalance: true }
    });

    if (updatedAccount) {
      // Upsert balance history for today
      await tx.accountBalanceHistory.upsert({
        where: {
          accountId_balanceDate: {
            accountId,
            balanceDate: today
          }
        },
        update: {
          balance: updatedAccount.currentBalance
        },
        create: {
          accountId,
          balance: updatedAccount.currentBalance,
          balanceDate: today
        }
      });
    }
  }

  /**
   * Reverse account balance changes
   */
  private async reverseAccountBalance(
    tx: Prisma.TransactionClient,
    accountId: string,
    type: TransactionType,
    amount: number
  ): Promise<void> {
    const balanceChange = -this.calculateBalanceChange(type, amount);

    // Update the account's current balance (reverse the change)
    await tx.account.update({
      where: { id: accountId },
      data: {
        currentBalance: { increment: balanceChange },
        updatedAt: new Date()
      }
    });

    // Update balance history for today
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get the updated account to record the new balance
    const updatedAccount = await tx.account.findUnique({
      where: { id: accountId },
      select: { currentBalance: true }
    });

    if (updatedAccount) {
      // Upsert balance history for today
      await tx.accountBalanceHistory.upsert({
        where: {
          accountId_balanceDate: {
            accountId,
            balanceDate: today
          }
        },
        update: {
          balance: updatedAccount.currentBalance
        },
        create: {
          accountId,
          balance: updatedAccount.currentBalance,
          balanceDate: today
        }
      });
    }
  }

  /**
   * Calculate balance change based on transaction type
   */
  private calculateBalanceChange(type: TransactionType, amount: number): number {
    switch (type) {
      case TransactionType.INCOME:
        return amount;
      case TransactionType.EXPENSE:
        return -amount;
      case TransactionType.TRANSFER:
        return -amount; // For source account
      default:
        return 0;
    }
  }

  /**
   * Calculate transaction summary
   */
  private async calculateSummary(where: Prisma.TransactionWhereInput) {
    const summary = await prisma.transaction.aggregate({
      where,
      _sum: {
        amount: true
      },
      _count: {
        _all: true
      }
    });

    // Get totals by type
    const incomeTotal = await prisma.transaction.aggregate({
      where: { ...where, type: TransactionType.INCOME },
      _sum: { amount: true }
    });

    const expenseTotal = await prisma.transaction.aggregate({
      where: { ...where, type: TransactionType.EXPENSE },
      _sum: { amount: true }
    });

    const transferTotal = await prisma.transaction.aggregate({
      where: { ...where, type: TransactionType.TRANSFER },
      _sum: { amount: true }
    });

    const totalIncome = Number(incomeTotal._sum.amount || 0);
    const totalExpenses = Number(expenseTotal._sum.amount || 0);
    const totalTransfers = Number(transferTotal._sum.amount || 0);

    return {
      totalIncome,
      totalExpense: totalExpenses,
      totalTransfer: totalTransfers,
      netAmount: totalIncome - totalExpenses,
      transactionCount: summary._count._all || 0
    };
  }

  /**
   * Format transaction for response
   */
  private formatTransaction(
    transaction: any,
    familyMembers: any[] = [],
    tags: any[] = []
  ): TransactionResponse {
    return {
      id: transaction.id,
      description: transaction.description,
      amount: Number(transaction.amount),
      transactionDate: transaction.transactionDate.toISOString().split('T')[0],
      type: transaction.type,
      accountId: transaction.accountId,
      categoryId: transaction.categoryId,
      parentTransactionId: transaction.parentTransactionId,
      installmentNumber: transaction.installmentNumber,
      totalInstallments: transaction.totalInstallments,
      exchangeRate: transaction.exchangeRate ? Number(transaction.exchangeRate) : undefined,
      destinationAccountId: transaction.destinationAccountId,
      isFuture: transaction.isFuture,
      createdAt: transaction.createdAt.toISOString(),
      updatedAt: transaction.updatedAt.toISOString(),
      version: transaction.version,
      account: transaction.account ? {
        id: transaction.account.id,
        name: transaction.account.name,
        type: transaction.account.type,
        currency: transaction.account.currency
      } : undefined,
      category: transaction.category ? {
        id: transaction.category.id,
        name: transaction.category.name,
        color: transaction.category.color
      } : undefined,
      destinationAccount: transaction.destinationAccount ? {
        id: transaction.destinationAccount.id,
        name: transaction.destinationAccount.name,
        type: transaction.destinationAccount.type,
        currency: transaction.destinationAccount.currency
      } : undefined,
      parentTransaction: transaction.parentTransaction ? {
        id: transaction.parentTransaction.id,
        description: transaction.parentTransaction.description
      } : undefined,
      installments: transaction.installments ? transaction.installments.map((inst: any) => ({
        id: inst.id,
        description: inst.description,
        amount: Number(inst.amount),
        transactionDate: inst.transactionDate.toISOString().split('T')[0],
        installmentNumber: inst.installmentNumber
      })) : undefined,
      tags: tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        color: tag.color
      })),
      familyMembers: familyMembers.map(member => ({
        id: member.id,
        name: member.name,
        color: member.color
      }))
    };
  }
}

export const transactionService = new TransactionService();
