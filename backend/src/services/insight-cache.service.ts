import { redisCache } from '../lib/redis';
import { InsightResponse, InsightGenerationResponse, InsightAnalyticsResponse } from '../schemas/insight.schemas';

/**
 * Cache service specifically for insights with intelligent invalidation
 */
export class InsightCacheService {
  private readonly CACHE_PREFIX = 'insights:';
  private readonly GENERATION_PREFIX = 'insights:generation:';
  private readonly ANALYTICS_PREFIX = 'insights:analytics:';
  private readonly USER_INSIGHTS_PREFIX = 'insights:user:';
  
  // Cache TTL configurations (in seconds)
  private readonly TTL = {
    INSIGHT: 60 * 60 * 24, // 24 hours for individual insights
    GENERATION: 60 * 30, // 30 minutes for generation results
    ANALYTICS: 60 * 60 * 2, // 2 hours for analytics
    USER_INSIGHTS: 60 * 60, // 1 hour for user insight lists
    PATTERN_ANALYSIS: 60 * 60 * 6, // 6 hours for pattern analysis
    ANOMALY_DETECTION: 60 * 15 // 15 minutes for anomaly detection (more frequent updates)
  };

  /**
   * Cache individual insight
   */
  async cacheInsight(insight: InsightResponse): Promise<void> {
    const key = this.getInsightKey(insight.id);
    await redisCache.set(key, insight, this.TTL.INSIGHT);
  }

  /**
   * Get cached insight
   */
  async getCachedInsight(insightId: string): Promise<InsightResponse | null> {
    const key = this.getInsightKey(insightId);
    return await redisCache.get<InsightResponse>(key);
  }

  /**
   * Cache insight generation results
   */
  async cacheGenerationResult(
    userId: string,
    request: any,
    result: InsightGenerationResponse
  ): Promise<void> {
    const key = this.getGenerationKey(userId, request);
    await redisCache.set(key, result, this.TTL.GENERATION);
  }

  /**
   * Get cached generation result
   */
  async getCachedGenerationResult(
    userId: string,
    request: any
  ): Promise<InsightGenerationResponse | null> {
    const key = this.getGenerationKey(userId, request);
    return await redisCache.get<InsightGenerationResponse>(key);
  }

  /**
   * Cache user insights list
   */
  async cacheUserInsights(
    userId: string,
    filters: any,
    insights: InsightResponse[]
  ): Promise<void> {
    const key = this.getUserInsightsKey(userId, filters);
    await redisCache.set(key, insights, this.TTL.USER_INSIGHTS);
  }

  /**
   * Get cached user insights
   */
  async getCachedUserInsights(
    userId: string,
    filters: any
  ): Promise<InsightResponse[] | null> {
    const key = this.getUserInsightsKey(userId, filters);
    return await redisCache.get<InsightResponse[]>(key);
  }

  /**
   * Cache analytics data
   */
  async cacheAnalytics(
    userId: string,
    analytics: InsightAnalyticsResponse
  ): Promise<void> {
    const key = this.getAnalyticsKey(userId);
    await redisCache.set(key, analytics, this.TTL.ANALYTICS);
  }

  /**
   * Get cached analytics
   */
  async getCachedAnalytics(userId: string): Promise<InsightAnalyticsResponse | null> {
    const key = this.getAnalyticsKey(userId);
    return await redisCache.get<InsightAnalyticsResponse>(key);
  }

  /**
   * Cache pattern analysis results
   */
  async cachePatternAnalysis(
    userId: string,
    analysisType: string,
    data: any
  ): Promise<void> {
    const key = this.getPatternAnalysisKey(userId, analysisType);
    await redisCache.set(key, data, this.TTL.PATTERN_ANALYSIS);
  }

  /**
   * Get cached pattern analysis
   */
  async getCachedPatternAnalysis(
    userId: string,
    analysisType: string
  ): Promise<any | null> {
    const key = this.getPatternAnalysisKey(userId, analysisType);
    return await redisCache.get(key);
  }

  /**
   * Cache anomaly detection results
   */
  async cacheAnomalyDetection(
    userId: string,
    anomalies: any[]
  ): Promise<void> {
    const key = this.getAnomalyDetectionKey(userId);
    await redisCache.set(key, anomalies, this.TTL.ANOMALY_DETECTION);
  }

  /**
   * Get cached anomaly detection results
   */
  async getCachedAnomalyDetection(userId: string): Promise<any[] | null> {
    const key = this.getAnomalyDetectionKey(userId);
    return await redisCache.get<any[]>(key);
  }

  /**
   * Invalidate all insight caches for a user
   */
  async invalidateUserInsights(userId: string): Promise<void> {
    const patterns = [
      `${this.USER_INSIGHTS_PREFIX}${userId}:*`,
      `${this.GENERATION_PREFIX}${userId}:*`,
      `${this.ANALYTICS_PREFIX}${userId}`,
      `${this.getPatternAnalysisKey(userId, '*')}`,
      `${this.getAnomalyDetectionKey(userId)}`
    ];

    for (const pattern of patterns) {
      await redisCache.delPattern(pattern);
    }
  }

  /**
   * Invalidate specific insight cache
   */
  async invalidateInsight(insightId: string): Promise<void> {
    const key = this.getInsightKey(insightId);
    await redisCache.del(key);
  }

  /**
   * Invalidate caches when transactions change
   */
  async invalidateOnTransactionChange(userId: string, categoryId?: string): Promise<void> {
    // Invalidate generation and pattern analysis caches
    await redisCache.delPattern(`${this.GENERATION_PREFIX}${userId}:*`);
    await redisCache.delPattern(`${this.getPatternAnalysisKey(userId, '*')}`);
    await redisCache.del(this.getAnomalyDetectionKey(userId));
    
    // Invalidate user insights that might be affected
    if (categoryId) {
      await redisCache.delPattern(`${this.USER_INSIGHTS_PREFIX}${userId}:*category*${categoryId}*`);
    } else {
      await redisCache.delPattern(`${this.USER_INSIGHTS_PREFIX}${userId}:*`);
    }
  }

  /**
   * Invalidate caches when budgets change
   */
  async invalidateOnBudgetChange(userId: string, categoryId?: string): Promise<void> {
    // Invalidate budget-related insights
    await redisCache.delPattern(`${this.GENERATION_PREFIX}${userId}:*budget*`);
    
    if (categoryId) {
      await redisCache.delPattern(`${this.USER_INSIGHTS_PREFIX}${userId}:*category*${categoryId}*`);
    }
  }

  /**
   * Invalidate caches when goals change
   */
  async invalidateOnGoalChange(userId: string, goalId?: string): Promise<void> {
    // Invalidate goal-related insights
    await redisCache.delPattern(`${this.GENERATION_PREFIX}${userId}:*goal*`);
    
    if (goalId) {
      await redisCache.delPattern(`${this.USER_INSIGHTS_PREFIX}${userId}:*goal*${goalId}*`);
    }
  }

  /**
   * Get cache statistics for insights
   */
  async getCacheStats(userId: string): Promise<{
    totalKeys: number;
    insightKeys: number;
    generationKeys: number;
    analyticsKeys: number;
    patternKeys: number;
    anomalyKeys: number;
  }> {
    const patterns = [
      `${this.CACHE_PREFIX}*`,
      `${this.USER_INSIGHTS_PREFIX}${userId}:*`,
      `${this.GENERATION_PREFIX}${userId}:*`,
      `${this.ANALYTICS_PREFIX}${userId}`,
      `${this.getPatternAnalysisKey(userId, '*')}`,
      `${this.getAnomalyDetectionKey(userId)}`
    ];

    const stats = {
      totalKeys: 0,
      insightKeys: 0,
      generationKeys: 0,
      analyticsKeys: 0,
      patternKeys: 0,
      anomalyKeys: 0
    };

    // Note: This is a simplified version. In production, you might want to use Redis SCAN
    // to count keys more efficiently
    return stats;
  }

  /**
   * Warm up cache with frequently accessed data
   */
  async warmUpCache(userId: string): Promise<void> {
    // This method can be called to pre-populate cache with commonly accessed insights
    // Implementation would depend on usage patterns
  }

  /**
   * Generate cache keys
   */
  private getInsightKey(insightId: string): string {
    return `${this.CACHE_PREFIX}${insightId}`;
  }

  private getGenerationKey(userId: string, request: any): string {
    const requestHash = this.hashRequest(request);
    return `${this.GENERATION_PREFIX}${userId}:${requestHash}`;
  }

  private getUserInsightsKey(userId: string, filters: any): string {
    const filtersHash = this.hashRequest(filters);
    return `${this.USER_INSIGHTS_PREFIX}${userId}:${filtersHash}`;
  }

  private getAnalyticsKey(userId: string): string {
    return `${this.ANALYTICS_PREFIX}${userId}`;
  }

  private getPatternAnalysisKey(userId: string, analysisType: string): string {
    return `insights:patterns:${userId}:${analysisType}`;
  }

  private getAnomalyDetectionKey(userId: string): string {
    return `insights:anomalies:${userId}`;
  }

  /**
   * Create a simple hash for request objects
   */
  private hashRequest(request: any): string {
    const str = JSON.stringify(request, Object.keys(request).sort());
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

// Export singleton instance
export const insightCacheService = new InsightCacheService();
