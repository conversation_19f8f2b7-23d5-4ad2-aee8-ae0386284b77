import { Prisma } from '@prisma/client';
import prisma from '../lib/prisma';
import {
  CreateInsightRequest,
  UpdateInsightRequest,
  InsightFilters,
  InsightResponse,
  PaginatedInsightsResponse,
  GenerateInsightsRequest,
  InsightGenerationResponse,
  BulkInsightActionRequest,
  InsightAnalyticsResponse
} from '../schemas/insight.schemas';
import {
  TransactionData,
  calculateCategorySpending,
  calculateMonthlyTrends,
  calculateTrendDirection,
  detectSpendingAnomalies,
  comparePeriods
} from '../utils/financial-analysis.utils';
import {
  calculateStatisticalSummary,
  detectSeasonalPatterns,
  generateForecast
} from '../utils/statistical-analysis.utils';
import { insightCacheService } from './insight-cache.service';

export class InsightService {
  /**
   * Create a new insight manually
   */
  async create(data: CreateInsightRequest): Promise<InsightResponse> {
    const insight = await prisma.insight.create({
      data: {
        type: data.type,
        priority: data.priority,
        title: data.title,
        description: data.description,
        data: data.data,
        categoryId: data.categoryId,
        accountId: data.accountId,
        goalId: data.goalId,
        periodStart: data.periodStart ? new Date(data.periodStart) : null,
        periodEnd: data.periodEnd ? new Date(data.periodEnd) : null,
        recommendations: data.recommendations,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
        relevanceScore: data.relevanceScore
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        account: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        goal: {
          select: {
            id: true,
            name: true,
            targetAmount: true,
            currentAmount: true
          }
        }
      }
    });

    return this.formatInsight(insight);
  }

  /**
   * Get all insights with filters and pagination
   */
  async findAll(filters: InsightFilters = {}): Promise<PaginatedInsightsResponse> {
    const {
      type,
      priority,
      status,
      categoryId,
      accountId,
      goalId,
      fromDate,
      toDate,
      includeExpired = false,
      minRelevanceScore,
      page = 1,
      limit = 20
    } = filters;

    // Build where clause
    const where: Prisma.InsightWhereInput = {};

    if (type) {
      where.type = type as any;
    }
    if (priority) where.priority = priority;
    if (status) where.status = status;
    if (categoryId) where.categoryId = categoryId;
    if (accountId) where.accountId = accountId;
    if (goalId) where.goalId = goalId;

    if (fromDate || toDate) {
      where.createdAt = {};
      if (fromDate) where.createdAt.gte = new Date(fromDate);
      if (toDate) where.createdAt.lte = new Date(toDate);
    }

    if (!includeExpired) {
      where.OR = [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ];
    }

    if (minRelevanceScore !== undefined) {
      where.relevanceScore = { gte: minRelevanceScore };
    }

    where.deletedAt = null;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await prisma.insight.count({ where });

    // Get insights with relationships
    const insights = await prisma.insight.findMany({
      where,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        account: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        goal: {
          select: {
            id: true,
            name: true,
            targetAmount: true,
            currentAmount: true
          }
        }
      },
      orderBy: [
        { priority: 'desc' },
        { relevanceScore: 'desc' },
        { createdAt: 'desc' }
      ],
      skip,
      take: limit
    });

    // Calculate summary statistics
    const allInsights = await prisma.insight.findMany({
      where: { deletedAt: null },
      select: { type: true, priority: true, status: true }
    });

    const summary = {
      totalByType: this.groupByField(allInsights, 'type'),
      totalByPriority: this.groupByField(allInsights, 'priority'),
      totalByStatus: this.groupByField(allInsights, 'status')
    };

    return {
      data: insights.map(insight => this.formatInsight(insight)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      summary
    };
  }

  /**
   * Get insight by ID with caching
   */
  async findById(id: string): Promise<InsightResponse | null> {
    // Try to get from cache first
    const cachedInsight = await insightCacheService.getCachedInsight(id);
    if (cachedInsight) {
      return cachedInsight;
    }

    const insight = await prisma.insight.findFirst({
      where: { id, deletedAt: null },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        account: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        goal: {
          select: {
            id: true,
            name: true,
            targetAmount: true,
            currentAmount: true
          }
        }
      }
    });

    if (!insight) {
      return null;
    }

    const formattedInsight = this.formatInsight(insight);

    // Cache the result
    await insightCacheService.cacheInsight(formattedInsight);

    return formattedInsight;
  }

  /**
   * Update insight
   */
  async update(id: string, data: UpdateInsightRequest): Promise<InsightResponse> {
    const existingInsight = await prisma.insight.findFirst({
      where: { id, deletedAt: null }
    });

    if (!existingInsight) {
      throw new Error('Insight não encontrado');
    }

    const updatedInsight = await prisma.insight.update({
      where: { id },
      data: {
        status: data.status,
        actionTaken: data.actionTaken,
        relevanceScore: data.relevanceScore,
        version: { increment: 1 }
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        account: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        goal: {
          select: {
            id: true,
            name: true,
            targetAmount: true,
            currentAmount: true
          }
        }
      }
    });

    const formattedInsight = this.formatInsight(updatedInsight);

    // Invalidate cache for this insight
    await insightCacheService.invalidateInsight(id);

    // Cache the updated insight
    await insightCacheService.cacheInsight(formattedInsight);

    return formattedInsight;
  }

  /**
   * Delete insight (soft delete)
   */
  async delete(id: string): Promise<void> {
    const existingInsight = await prisma.insight.findFirst({
      where: { id, deletedAt: null }
    });

    if (!existingInsight) {
      throw new Error('Insight não encontrado');
    }

    await prisma.insight.update({
      where: { id },
      data: {
        deletedAt: new Date(),
        version: { increment: 1 }
      }
    });

    // Invalidate cache for this insight
    await insightCacheService.invalidateInsight(id);
  }

  /**
   * Bulk actions on insights
   */
  async bulkAction(data: BulkInsightActionRequest): Promise<{ updated: number }> {
    const { insightIds, action } = data;

    let updateData: Partial<Prisma.InsightUpdateInput> = {
      version: { increment: 1 }
    };

    switch (action) {
      case 'MARK_VIEWED':
        updateData.status = 'VIEWED';
        break;
      case 'DISMISS':
        updateData.status = 'DISMISSED';
        break;
      case 'MARK_ACTED_UPON':
        updateData.status = 'ACTED_UPON';
        updateData.actionTaken = true;
        break;
    }

    const result = await prisma.insight.updateMany({
      where: {
        id: { in: insightIds },
        deletedAt: null
      },
      data: updateData
    });

    return { updated: result.count };
  }

  /**
   * Format insight for response
   */
  private formatInsight(insight: any): InsightResponse {
    return {
      id: insight.id,
      type: insight.type,
      priority: insight.priority,
      status: insight.status,
      title: insight.title,
      description: insight.description,
      data: insight.data,
      categoryId: insight.categoryId,
      accountId: insight.accountId,
      goalId: insight.goalId,
      periodStart: insight.periodStart?.toISOString(),
      periodEnd: insight.periodEnd?.toISOString(),
      recommendations: insight.recommendations,
      actionTaken: insight.actionTaken,
      expiresAt: insight.expiresAt?.toISOString(),
      relevanceScore: insight.relevanceScore ? Number(insight.relevanceScore) : undefined,
      createdAt: insight.createdAt.toISOString(),
      updatedAt: insight.updatedAt.toISOString(),
      version: insight.version,
      category: insight.category,
      account: insight.account,
      goal: insight.goal ? {
        ...insight.goal,
        targetAmount: Number(insight.goal.targetAmount),
        currentAmount: Number(insight.goal.currentAmount)
      } : undefined
    };
  }

  /**
   * Generate insights automatically based on transaction data with caching
   */
  async generateInsights(
    request: GenerateInsightsRequest = { forceRegenerate: false },
    userId: string = 'default'
  ): Promise<InsightGenerationResponse> {
    const startTime = Date.now();
    const {
      types,
      forceRegenerate = false,
      periodStart,
      periodEnd,
      categoryIds,
      accountIds,
      goalIds
    } = request;

    // Check cache first (unless force regenerate)
    if (!forceRegenerate) {
      const cachedResult = await insightCacheService.getCachedGenerationResult(userId, request);
      if (cachedResult) {
        return {
          ...cachedResult,
          processingTime: Date.now() - startTime
        };
      }
    }

    // Get transaction data for analysis
    const transactions = await this.getTransactionData({
      periodStart,
      periodEnd,
      categoryIds,
      accountIds
    });

    if (transactions.length < 10) {
      const emptyResult = {
        generated: 0,
        updated: 0,
        expired: 0,
        insights: [],
        processingTime: Date.now() - startTime
      };

      // Cache empty result for short time to avoid repeated processing
      await insightCacheService.cacheGenerationResult(userId, request, emptyResult);
      return emptyResult;
    }

    const generatedInsights: InsightResponse[] = [];
    let generated = 0;
    let updated = 0;

    // Generate different types of insights
    const insightTypes = types || [
      'SPENDING_PATTERN',
      'BUDGET_ALERT',
      'ANOMALY_DETECTION',
      'TREND_ANALYSIS',
      'CATEGORY_ANALYSIS'
    ];

    for (const type of insightTypes) {
      try {
        const insights = await this.generateInsightsByType(type, transactions, forceRegenerate);
        generatedInsights.push(...insights);
        generated += insights.length;
      } catch (error) {
        console.error(`Error generating insights for type ${type}:`, error);
      }
    }

    // Expire old insights
    const expired = await this.expireOldInsights();

    const processingTime = Date.now() - startTime;

    const result = {
      generated,
      updated,
      expired,
      insights: generatedInsights,
      processingTime
    };

    // Cache the result
    await insightCacheService.cacheGenerationResult(userId, request, result);

    return result;
  }

  /**
   * Generate insights by specific type
   */
  private async generateInsightsByType(
    type: string,
    transactions: TransactionData[],
    forceRegenerate: boolean
  ): Promise<InsightResponse[]> {
    const insights: InsightResponse[] = [];

    switch (type) {
      case 'SPENDING_PATTERN':
        insights.push(...await this.generateSpendingPatternInsights(transactions, forceRegenerate));
        break;
      case 'ANOMALY_DETECTION':
        insights.push(...await this.generateAnomalyInsights(transactions, forceRegenerate));
        break;
      case 'TREND_ANALYSIS':
        insights.push(...await this.generateTrendInsights(transactions, forceRegenerate));
        break;
      case 'CATEGORY_ANALYSIS':
        insights.push(...await this.generateCategoryInsights(transactions, forceRegenerate));
        break;
      case 'BUDGET_ALERT':
        insights.push(...await this.generateBudgetAlerts(forceRegenerate));
        break;
    }

    return insights;
  }

  /**
   * Generate spending pattern insights
   */
  private async generateSpendingPatternInsights(
    transactions: TransactionData[],
    forceRegenerate: boolean
  ): Promise<InsightResponse[]> {
    const insights: InsightResponse[] = [];

    // Analyze category spending patterns
    const categorySpending = calculateCategorySpending(transactions);
    const topCategories = categorySpending.slice(0, 3);

    for (const category of topCategories) {
      if (category.percentage > 30) { // High spending category
        const existingInsight = await this.findExistingInsight(
          'SPENDING_PATTERN',
          category.categoryId,
          forceRegenerate
        );

        if (!existingInsight) {
          const insight = await this.create({
            type: 'SPENDING_PATTERN',
            priority: category.percentage > 50 ? 'HIGH' : 'MEDIUM',
            title: `Alto gasto em ${category.categoryName}`,
            description: `Você gastou ${category.percentage.toFixed(1)}% do seu orçamento em ${category.categoryName} (R$ ${category.totalAmount.toFixed(2)})`,
            data: {
              categorySpending: category,
              analysisType: 'high_category_spending'
            },
            categoryId: category.categoryId,
            relevanceScore: Math.min(10, category.percentage / 10),
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
            recommendations: {
              actions: [
                'Revise seus gastos nesta categoria',
                'Considere estabelecer um limite mensal',
                'Procure alternativas mais econômicas'
              ]
            }
          });
          insights.push(insight);
        }
      }
    }

    return insights;
  }

  /**
   * Generate anomaly detection insights
   */
  private async generateAnomalyInsights(
    transactions: TransactionData[],
    forceRegenerate: boolean
  ): Promise<InsightResponse[]> {
    const insights: InsightResponse[] = [];

    const anomalies = detectSpendingAnomalies(transactions, 90, 2.0);
    const highSeverityAnomalies = anomalies.filter(a => a.severity === 'HIGH').slice(0, 3);

    for (const anomaly of highSeverityAnomalies) {
      const existingInsight = await this.findExistingInsight(
        'ANOMALY_DETECTION',
        anomaly.transaction.id,
        forceRegenerate
      );

      if (!existingInsight) {
        const insight = await this.create({
          type: 'ANOMALY_DETECTION',
          priority: anomaly.severity === 'HIGH' ? 'CRITICAL' : 'HIGH',
          title: `Gasto incomum detectado`,
          description: `${anomaly.reason}: R$ ${anomaly.transaction.amount.toFixed(2)} em ${anomaly.transaction.description}`,
          data: {
            anomaly,
            analysisType: 'spending_anomaly'
          },
          categoryId: anomaly.transaction.categoryId,
          accountId: anomaly.transaction.accountId,
          relevanceScore: Math.min(10, anomaly.anomalyScore),
          expiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days
          recommendations: {
            actions: [
              'Verifique se esta transação está correta',
              'Considere se este gasto era necessário',
              'Monitore gastos futuros nesta categoria'
            ]
          }
        });
        insights.push(insight);
      }
    }

    return insights;
  }

  /**
   * Generate trend analysis insights
   */
  private async generateTrendInsights(
    transactions: TransactionData[],
    forceRegenerate: boolean
  ): Promise<InsightResponse[]> {
    const insights: InsightResponse[] = [];

    const monthlyTrends = calculateMonthlyTrends(transactions, 6);
    if (monthlyTrends.length >= 3) {
      const trendAnalysis = calculateTrendDirection(monthlyTrends);

      if (trendAnalysis.strength > 0.6 && Math.abs(trendAnalysis.changePercentage) > 15) {
        const existingInsight = await this.findExistingInsight(
          'TREND_ANALYSIS',
          'monthly_spending_trend',
          forceRegenerate
        );

        if (!existingInsight) {
          const isIncreasing = trendAnalysis.direction === 'INCREASING';
          const insight = await this.create({
            type: 'TREND_ANALYSIS',
            priority: Math.abs(trendAnalysis.changePercentage) > 30 ? 'HIGH' : 'MEDIUM',
            title: `Tendência de gastos ${isIncreasing ? 'crescente' : 'decrescente'}`,
            description: `Seus gastos ${isIncreasing ? 'aumentaram' : 'diminuíram'} ${Math.abs(trendAnalysis.changePercentage).toFixed(1)}% nos últimos meses`,
            data: {
              trendAnalysis,
              monthlyTrends,
              analysisType: 'spending_trend'
            },
            relevanceScore: Math.min(10, trendAnalysis.strength * 10),
            expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days
            recommendations: {
              actions: isIncreasing ? [
                'Revise seu orçamento mensal',
                'Identifique categorias com maior aumento',
                'Considere reduzir gastos desnecessários'
              ] : [
                'Parabéns pela redução de gastos!',
                'Mantenha este padrão positivo',
                'Considere investir a economia'
              ]
            }
          });
          insights.push(insight);
        }
      }
    }

    return insights;
  }

  /**
   * Generate category analysis insights
   */
  private async generateCategoryInsights(
    transactions: TransactionData[],
    forceRegenerate: boolean
  ): Promise<InsightResponse[]> {
    const insights: InsightResponse[] = [];

    // Compare current month with previous month
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const previousMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    const currentTransactions = transactions.filter(t => t.date >= currentMonthStart);
    const previousTransactions = transactions.filter(t =>
      t.date >= previousMonthStart && t.date <= previousMonthEnd
    );

    if (currentTransactions.length > 0 && previousTransactions.length > 0) {
      const comparison = comparePeriods(currentTransactions, previousTransactions);
      const significantChanges = comparison.categoryChanges
        .filter(c => Math.abs(c.changePercentage) > 50 && Math.abs(c.change) > 100)
        .slice(0, 2);

      for (const change of significantChanges) {
        const existingInsight = await this.findExistingInsight(
          'CATEGORY_ANALYSIS',
          change.categoryId,
          forceRegenerate
        );

        if (!existingInsight) {
          const isIncrease = change.change > 0;
          const insight = await this.create({
            type: 'CATEGORY_ANALYSIS',
            priority: Math.abs(change.changePercentage) > 100 ? 'HIGH' : 'MEDIUM',
            title: `${isIncrease ? 'Aumento' : 'Redução'} significativo em ${change.categoryName}`,
            description: `Gastos em ${change.categoryName} ${isIncrease ? 'aumentaram' : 'diminuíram'} ${Math.abs(change.changePercentage).toFixed(1)}% este mês`,
            data: {
              categoryChange: change,
              analysisType: 'category_comparison'
            },
            categoryId: change.categoryId,
            relevanceScore: Math.min(10, Math.abs(change.changePercentage) / 20),
            expiresAt: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days
            recommendations: {
              actions: isIncrease ? [
                'Analise o que causou este aumento',
                'Verifique se há gastos desnecessários',
                'Considere ajustar o orçamento desta categoria'
              ] : [
                'Ótima redução de gastos!',
                'Mantenha este controle',
                'Considere realocar a economia para outras prioridades'
              ]
            }
          });
          insights.push(insight);
        }
      }
    }

    return insights;
  }

  /**
   * Generate budget alert insights
   */
  private async generateBudgetAlerts(forceRegenerate: boolean): Promise<InsightResponse[]> {
    const insights: InsightResponse[] = [];

    // Get current month budgets and spending
    const now = new Date();
    const currentMonth = now.getMonth() + 1;
    const currentYear = now.getFullYear();

    const budgets = await prisma.budget.findMany({
      where: {
        month: currentMonth,
        year: currentYear,
        deletedAt: null
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        }
      }
    });

    for (const budget of budgets) {
      // Calculate actual spending for this category this month
      const monthStart = new Date(currentYear, currentMonth - 1, 1);
      const monthEnd = new Date(currentYear, currentMonth, 0);

      const actualSpending = await prisma.transaction.aggregate({
        where: {
          categoryId: budget.categoryId,
          transactionDate: {
            gte: monthStart,
            lte: monthEnd
          },
          type: 'EXPENSE',
          deletedAt: null
        },
        _sum: {
          amount: true
        }
      });

      const spent = Number(actualSpending._sum.amount || 0);
      const budgetAmount = Number(budget.plannedAmount);
      const usagePercentage = budgetAmount > 0 ? (spent / budgetAmount) * 100 : 0;

      if (usagePercentage > 80) { // Alert when over 80% of budget is used
        const existingInsight = await this.findExistingInsight(
          'BUDGET_ALERT',
          budget.categoryId,
          forceRegenerate
        );

        if (!existingInsight) {
          const insight = await this.create({
            type: 'BUDGET_ALERT',
            priority: usagePercentage > 100 ? 'CRITICAL' : 'HIGH',
            title: `Orçamento ${usagePercentage > 100 ? 'excedido' : 'quase esgotado'} em ${budget.category.name}`,
            description: `Você já gastou ${usagePercentage.toFixed(1)}% do orçamento de ${budget.category.name} (R$ ${spent.toFixed(2)} de R$ ${budgetAmount.toFixed(2)})`,
            data: {
              budgetId: budget.id,
              budgetAmount,
              actualSpending: spent,
              usagePercentage,
              analysisType: 'budget_usage'
            },
            categoryId: budget.categoryId,
            relevanceScore: Math.min(10, usagePercentage / 10),
            expiresAt: monthEnd.toISOString(),
            recommendations: {
              actions: usagePercentage > 100 ? [
                'Revise seus gastos nesta categoria',
                'Considere ajustar o orçamento para o próximo mês',
                'Procure formas de reduzir gastos'
              ] : [
                'Monitore seus gastos restantes',
                'Evite gastos desnecessários',
                'Considere realocar orçamento de outras categorias'
              ]
            }
          });
          insights.push(insight);
        }
      }
    }

    return insights;
  }

  /**
   * Get transaction data for analysis
   */
  private async getTransactionData(filters: {
    periodStart?: string;
    periodEnd?: string;
    categoryIds?: string[];
    accountIds?: string[];
  }): Promise<TransactionData[]> {
    const where: Prisma.TransactionWhereInput = {
      deletedAt: null
    };

    if (filters.periodStart || filters.periodEnd) {
      where.transactionDate = {};
      if (filters.periodStart) where.transactionDate.gte = new Date(filters.periodStart);
      if (filters.periodEnd) where.transactionDate.lte = new Date(filters.periodEnd);
    } else {
      // Default to last 6 months
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      where.transactionDate = { gte: sixMonthsAgo };
    }

    if (filters.categoryIds?.length) {
      where.categoryId = { in: filters.categoryIds };
    }

    if (filters.accountIds?.length) {
      where.accountId = { in: filters.accountIds };
    }

    const transactions = await prisma.transaction.findMany({
      where,
      include: {
        category: {
          select: {
            name: true
          }
        },
        account: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        transactionDate: 'desc'
      }
    });

    return transactions.map(t => ({
      id: t.id,
      amount: Number(t.amount),
      date: t.transactionDate,
      categoryId: t.categoryId || undefined,
      categoryName: t.category?.name,
      accountId: t.accountId,
      accountName: t.account.name,
      type: t.type as 'EXPENSE' | 'INCOME' | 'TRANSFER',
      description: t.description
    }));
  }

  /**
   * Find existing insight to avoid duplicates
   */
  private async findExistingInsight(
    type: string,
    relatedId: string,
    forceRegenerate: boolean
  ): Promise<any> {
    if (forceRegenerate) {
      return null;
    }

    // Look for recent insights of the same type
    const recentDate = new Date();
    recentDate.setDate(recentDate.getDate() - 7); // Last 7 days

    const existing = await prisma.insight.findFirst({
      where: {
        type: type as any,
        OR: [
          { categoryId: relatedId },
          { accountId: relatedId },
          { goalId: relatedId },
          { data: { path: ['analysisType'], equals: relatedId } }
        ],
        createdAt: { gte: recentDate },
        deletedAt: null
      }
    });

    return existing;
  }

  /**
   * Expire old insights
   */
  private async expireOldInsights(): Promise<number> {
    const now = new Date();

    const result = await prisma.insight.updateMany({
      where: {
        expiresAt: { lt: now },
        status: { not: 'DISMISSED' },
        deletedAt: null
      },
      data: {
        status: 'DISMISSED',
        version: { increment: 1 }
      }
    });

    return result.count;
  }

  /**
   * Get analytics about insights with caching
   */
  async getAnalytics(userId: string = 'default'): Promise<InsightAnalyticsResponse> {
    // Try to get from cache first
    const cachedAnalytics = await insightCacheService.getCachedAnalytics(userId);
    if (cachedAnalytics) {
      return cachedAnalytics;
    }
    const insights = await prisma.insight.findMany({
      where: { deletedAt: null },
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    const totalInsights = insights.length;
    const newInsights = insights.filter(i => i.status === 'NEW').length;
    const actionableInsights = insights.filter(i =>
      i.status === 'NEW' || i.status === 'VIEWED'
    ).length;

    const relevanceScores = insights
      .filter(i => i.relevanceScore !== null)
      .map(i => Number(i.relevanceScore));

    const averageRelevanceScore = relevanceScores.length > 0
      ? relevanceScores.reduce((sum, score) => sum + score, 0) / relevanceScores.length
      : 0;

    // Top categories by insight count
    const categoryInsightCount = new Map<string, { name: string; count: number }>();
    insights.forEach(insight => {
      if (insight.categoryId && insight.category) {
        const existing = categoryInsightCount.get(insight.categoryId);
        if (existing) {
          existing.count++;
        } else {
          categoryInsightCount.set(insight.categoryId, {
            name: insight.category.name,
            count: 1
          });
        }
      }
    });

    const topCategories = Array.from(categoryInsightCount.entries())
      .map(([categoryId, data]) => ({
        categoryId,
        categoryName: data.name,
        insightCount: data.count
      }))
      .sort((a, b) => b.insightCount - a.insightCount)
      .slice(0, 5);

    const analytics = {
      totalInsights,
      newInsights,
      actionableInsights,
      averageRelevanceScore,
      topCategories,
      typeDistribution: this.groupByField(insights, 'type'),
      priorityDistribution: this.groupByField(insights, 'priority'),
      statusDistribution: this.groupByField(insights, 'status')
    };

    // Cache the analytics
    await insightCacheService.cacheAnalytics(userId, analytics);

    return analytics;
  }

  /**
   * Helper function to group by field
   */
  private groupByField(items: any[], field: string): Record<string, number> {
    return items.reduce((acc, item) => {
      const key = item[field];
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});
  }
}
