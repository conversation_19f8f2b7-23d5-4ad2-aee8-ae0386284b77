import { Job<PERSON>ontext, JobResult, JobType, scheduler } from '../config/scheduler.config';
import { InsightService } from './insight.service';
import { insightCacheService } from './insight-cache.service';
import prisma from '../lib/prisma';

/**
 * Job service for automatic insight generation
 * Runs scheduled tasks to generate insights for all users
 */
export class InsightJobService {
  private insightService: InsightService;

  constructor() {
    this.insightService = new InsightService();
  }

  /**
   * Initialize and register job functions
   */
  initialize(): void {
    // Register the insight generation job function
    scheduler.registerJobFunction(JobType.INSIGHTS_GENERATION, this.generateInsights.bind(this));

    console.log('[InsightJobService] Job functions registered');
  }

  /**
   * Main job function for generating insights
   */
  async generateInsights(context: JobContext): Promise<JobResult> {
    const startTime = Date.now();
    let totalGenerated = 0;
    let totalUpdated = 0;
    let totalExpired = 0;
    let processedUsers = 0;
    let errors: string[] = [];

    try {
      console.log(`[InsightJob] Starting insight generation job - ${context.jobId}`);

      // Get all active users (for now, we'll use a simple approach)
      // In a real multi-user system, you'd get actual user IDs
      const users = await this.getActiveUsers();

      if (users.length === 0) {
        return {
          success: true,
          message: 'No active users found for insight generation',
          data: { totalGenerated: 0, totalUpdated: 0, totalExpired: 0, processedUsers: 0 },
          duration: Date.now() - startTime
        };
      }

      console.log(`[InsightJob] Processing insights for ${users.length} users`);

      // Process insights for each user
      for (const user of users) {
        try {
          const userStartTime = Date.now();
          
          // Generate insights for this user
          const result = await this.generateInsightsForUser(user.id, context);
          
          totalGenerated += result.generated;
          totalUpdated += result.updated;
          totalExpired += result.expired;
          processedUsers++;

          const userDuration = Date.now() - userStartTime;
          console.log(`[InsightJob] User ${user.id}: ${result.generated} generated, ${result.updated} updated, ${result.expired} expired (${userDuration}ms)`);

        } catch (error) {
          const errorMsg = `Failed to process user ${user.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMsg);
          console.error(`[InsightJob] ${errorMsg}`, error);
        }
      }

      // Clean up expired insights globally
      try {
        const expiredCount = await this.cleanupExpiredInsights();
        totalExpired += expiredCount;
        console.log(`[InsightJob] Cleaned up ${expiredCount} expired insights`);
      } catch (error) {
        const errorMsg = `Failed to cleanup expired insights: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        console.error(`[InsightJob] ${errorMsg}`, error);
      }

      const duration = Date.now() - startTime;
      const successRate = processedUsers / users.length;

      // Determine if job was successful
      const success = successRate >= 0.8; // Consider successful if 80% of users processed

      const message = success
        ? `Generated ${totalGenerated} insights for ${processedUsers}/${users.length} users in ${duration}ms`
        : `Partially completed: ${processedUsers}/${users.length} users processed with ${errors.length} errors`;

      return {
        success,
        message,
        data: {
          totalGenerated,
          totalUpdated,
          totalExpired,
          processedUsers,
          totalUsers: users.length,
          successRate,
          errors: errors.slice(0, 5) // Limit error details
        },
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      console.error('[InsightJob] Job failed with error:', error);
      
      return {
        success: false,
        message: `Insight generation job failed: ${errorMessage}`,
        error: error instanceof Error ? error : new Error(errorMessage),
        data: { totalGenerated, totalUpdated, totalExpired, processedUsers, errors },
        duration
      };
    }
  }

  /**
   * Generate insights for a specific user
   */
  private async generateInsightsForUser(userId: string, _context: JobContext): Promise<{
    generated: number;
    updated: number;
    expired: number;
  }> {
    // Check if user has sufficient data for analysis
    const hasData = await this.checkUserDataSufficiency(userId);
    if (!hasData) {
      console.log(`[InsightJob] User ${userId} has insufficient data for insight generation`);
      return { generated: 0, updated: 0, expired: 0 };
    }

    // Generate insights with different types
    const insightTypes = [
      'SPENDING_PATTERN',
      'BUDGET_ALERT',
      'ANOMALY_DETECTION',
      'TREND_ANALYSIS',
      'CATEGORY_ANALYSIS'
    ] as const;

    const generateRequest = {
      types: [...insightTypes],
      forceRegenerate: false, // Don't force regenerate in scheduled jobs
      // Use last 3 months for analysis
      periodStart: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
      periodEnd: new Date().toISOString()
    };

    const result = await this.insightService.generateInsights(generateRequest, userId);

    // Invalidate old cache entries for this user
    await insightCacheService.invalidateUserInsights(userId);

    return {
      generated: result.generated,
      updated: result.updated,
      expired: result.expired
    };
  }

  /**
   * Check if user has sufficient data for insight generation
   */
  private async checkUserDataSufficiency(userId: string): Promise<boolean> {
    try {
      // Check if user has transactions in the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const transactionCount = await prisma.transaction.count({
        where: {
          transactionDate: {
            gte: thirtyDaysAgo
          },
          deletedAt: null
        }
      });

      // Require at least 5 transactions for meaningful insights
      return transactionCount >= 5;

    } catch (error) {
      console.error(`[InsightJob] Error checking data sufficiency for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Get active users for insight generation
   * For now, returns a default user since we're in single-user mode
   */
  private async getActiveUsers(): Promise<Array<{ id: string }>> {
    try {
      // In single-user mode, return default user
      // In multi-user mode, this would query actual users
      return [{ id: 'default' }];
      
      // Future multi-user implementation:
      // const users = await prisma.user.findMany({
      //   where: {
      //     isActive: true,
      //     deletedAt: null
      //   },
      //   select: {
      //     id: true
      //   }
      // });
      // return users;

    } catch (error) {
      console.error('[InsightJob] Error getting active users:', error);
      return [];
    }
  }

  /**
   * Clean up expired insights
   */
  private async cleanupExpiredInsights(): Promise<number> {
    try {
      const now = new Date();
      
      // Soft delete expired insights that haven't been dismissed
      const result = await prisma.insight.updateMany({
        where: {
          expiresAt: {
            lt: now
          },
          status: {
            not: 'DISMISSED'
          },
          deletedAt: null
        },
        data: {
          status: 'DISMISSED',
          deletedAt: now,
          version: {
            increment: 1
          }
        }
      });

      return result.count;

    } catch (error) {
      console.error('[InsightJob] Error cleaning up expired insights:', error);
      throw error;
    }
  }

  /**
   * Generate insights on demand (for manual triggers)
   */
  async generateInsightsOnDemand(userId: string = 'default'): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      const context: JobContext = {
        jobId: `manual_${Date.now()}`,
        name: 'Manual Insight Generation',
        type: 'insights_generation' as any,
        startTime: new Date(),
        attempt: 1,
        maxAttempts: 1
      };

      const result = await this.generateInsightsForUser(userId, context);

      return {
        success: true,
        message: `Generated ${result.generated} insights for user ${userId}`,
        data: result
      };

    } catch (error) {
      console.error('[InsightJob] Manual insight generation failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get job statistics
   */
  async getJobStatistics(): Promise<{
    totalInsights: number;
    insightsToday: number;
    insightsThisWeek: number;
    averageGenerationTime: number;
    lastJobRun?: Date;
  }> {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const [totalInsights, insightsToday, insightsThisWeek] = await Promise.all([
        prisma.insight.count({
          where: { deletedAt: null }
        }),
        prisma.insight.count({
          where: {
            createdAt: { gte: today },
            deletedAt: null
          }
        }),
        prisma.insight.count({
          where: {
            createdAt: { gte: weekAgo },
            deletedAt: null
          }
        })
      ]);

      return {
        totalInsights,
        insightsToday,
        insightsThisWeek,
        averageGenerationTime: 0, // This would be calculated from job logs in production
        lastJobRun: undefined // This would come from job execution logs
      };

    } catch (error) {
      console.error('[InsightJob] Error getting job statistics:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const insightJobService = new InsightJobService();
