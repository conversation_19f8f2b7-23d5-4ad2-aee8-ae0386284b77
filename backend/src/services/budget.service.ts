import { Prisma } from '@prisma/client';
import prisma from '../lib/prisma';
import {
  CreateBudgetRequest,
  UpdateBudgetRequest,
  BudgetFilters,
  BudgetProgressFilters,
  BudgetResponse,
  PaginatedBudgetsResponse,
  BudgetProgress,
  BudgetSummary,
  BudgetReport
} from '../schemas/budget.schemas';

export class BudgetService {
  /**
   * Create a new budget
   */
  async create(data: CreateBudgetRequest): Promise<BudgetResponse> {
    try {
      // Check if budget already exists for the same category, member, month, and year
      const existingBudget = await prisma.budget.findFirst({
        where: {
          categoryId: data.categoryId,
          familyMemberId: data.familyMemberId || null,
          month: data.month,
          year: data.year,
          deletedAt: null
        }
      });

      if (existingBudget) {
        throw new Error('Já existe um orçamento para esta categoria, membro e período');
      }

      // Validate that category exists and is not archived
      const category = await prisma.category.findFirst({
        where: {
          id: data.categoryId,
          deletedAt: null
        },
        include: {
          parent: true
        }
      });

      if (!category) {
        throw new Error('Categoria não encontrada ou foi arquivada');
      }

      // Validate family member if provided
      if (data.familyMemberId) {
        const familyMember = await prisma.familyMember.findFirst({
          where: {
            id: data.familyMemberId,
            deletedAt: null
          }
        });

        if (!familyMember) {
          throw new Error('Membro da família não encontrado ou foi arquivado');
        }
      }

      // Create budget using transaction for data integrity
      const budget = await prisma.$transaction(async (tx) => {
        return await tx.budget.create({
          data: {
            plannedAmount: new Prisma.Decimal(data.plannedAmount),
            month: data.month,
            year: data.year,
            categoryId: data.categoryId,
            familyMemberId: data.familyMemberId || null
          },
          include: {
            category: {
              include: {
                parent: true
              }
            },
            familyMember: true
          }
        });
      });

      return this.formatBudgetResponse(budget);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao criar orçamento');
    }
  }

  /**
   * Find all budgets with filters and pagination
   */
  async findAll(filters: BudgetFilters): Promise<PaginatedBudgetsResponse> {
    try {
      const {
        categoryId,
        familyMemberId,
        month,
        year,
        page,
        limit,
        includeProgress,
        sortBy,
        sortOrder
      } = filters;

      // Build where clause
      const where: Prisma.BudgetWhereInput = {
        deletedAt: null,
        ...(categoryId && { categoryId }),
        ...(familyMemberId && { familyMemberId }),
        ...(month && { month }),
        ...(year && { year })
      };

      // Build orderBy clause
      const orderBy: Prisma.BudgetOrderByWithRelationInput = {};
      switch (sortBy) {
        case 'category':
          orderBy.category = { name: sortOrder };
          break;
        case 'familyMember':
          orderBy.familyMember = { name: sortOrder };
          break;
        default:
          orderBy[sortBy] = sortOrder;
      }

      // Execute queries in parallel
      const [budgets, total] = await Promise.all([
        prisma.budget.findMany({
          where,
          include: {
            category: {
              include: {
                parent: true
              }
            },
            familyMember: true
          },
          orderBy,
          skip: (page - 1) * limit,
          take: limit
        }),
        prisma.budget.count({ where })
      ]);

      // Format responses
      const formattedBudgets: BudgetResponse[] = [];
      for (const budget of budgets) {
        const formattedBudget = this.formatBudgetResponse(budget);
        
        // Add progress if requested
        if (includeProgress) {
          formattedBudget.progress = await this.calculateBudgetProgress(
            budget.id,
            budget.categoryId,
            budget.familyMemberId,
            budget.month,
            budget.year,
            Number(budget.plannedAmount)
          );
        }
        
        formattedBudgets.push(formattedBudget);
      }

      return {
        data: formattedBudgets,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao buscar orçamentos');
    }
  }

  /**
   * Find budget by ID
   */
  async findById(id: string, includeProgress: boolean = false): Promise<BudgetResponse | null> {
    try {
      const budget = await prisma.budget.findFirst({
        where: {
          id,
          deletedAt: null
        },
        include: {
          category: {
            include: {
              parent: true
            }
          },
          familyMember: true
        }
      });

      if (!budget) {
        return null;
      }

      const formattedBudget = this.formatBudgetResponse(budget);

      if (includeProgress) {
        formattedBudget.progress = await this.calculateBudgetProgress(
          budget.id,
          budget.categoryId,
          budget.familyMemberId,
          budget.month,
          budget.year,
          Number(budget.plannedAmount)
        );
      }

      return formattedBudget;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao buscar orçamento');
    }
  }

  /**
   * Update budget
   */
  async update(id: string, data: UpdateBudgetRequest): Promise<BudgetResponse> {
    try {
      // Check if budget exists
      const existingBudget = await prisma.budget.findFirst({
        where: {
          id,
          deletedAt: null
        }
      });

      if (!existingBudget) {
        throw new Error('Orçamento não encontrado');
      }

      // Check for conflicts if category, member, month, or year are being updated
      if (data.categoryId || data.familyMemberId !== undefined || data.month || data.year) {
        const conflictWhere: Prisma.BudgetWhereInput = {
          id: { not: id },
          categoryId: data.categoryId || existingBudget.categoryId,
          familyMemberId: data.familyMemberId !== undefined ? data.familyMemberId : existingBudget.familyMemberId,
          month: data.month || existingBudget.month,
          year: data.year || existingBudget.year,
          deletedAt: null
        };

        const conflictingBudget = await prisma.budget.findFirst({
          where: conflictWhere
        });

        if (conflictingBudget) {
          throw new Error('Já existe um orçamento para esta categoria, membro e período');
        }
      }

      // Validate category if being updated
      if (data.categoryId) {
        const category = await prisma.category.findFirst({
          where: {
            id: data.categoryId,
            deletedAt: null
          }
        });

        if (!category) {
          throw new Error('Categoria não encontrada ou foi arquivada');
        }
      }

      // Validate family member if being updated
      if (data.familyMemberId) {
        const familyMember = await prisma.familyMember.findFirst({
          where: {
            id: data.familyMemberId,
            deletedAt: null
          }
        });

        if (!familyMember) {
          throw new Error('Membro da família não encontrado ou foi arquivado');
        }
      }

      // Update budget
      const updateData: Prisma.BudgetUpdateInput = {
        ...(data.plannedAmount && { plannedAmount: new Prisma.Decimal(data.plannedAmount) }),
        ...(data.month && { month: data.month }),
        ...(data.year && { year: data.year }),
        ...(data.categoryId && { categoryId: data.categoryId }),
        ...(data.familyMemberId !== undefined && { familyMemberId: data.familyMemberId })
      };

      const budget = await prisma.budget.update({
        where: { id },
        data: updateData,
        include: {
          category: {
            include: {
              parent: true
            }
          },
          familyMember: true
        }
      });

      return this.formatBudgetResponse(budget);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao atualizar orçamento');
    }
  }

  /**
   * Delete budget (soft delete)
   */
  async delete(id: string): Promise<void> {
    try {
      const budget = await prisma.budget.findFirst({
        where: {
          id,
          deletedAt: null
        }
      });

      if (!budget) {
        throw new Error('Orçamento não encontrado');
      }

      await prisma.budget.update({
        where: { id },
        data: {
          deletedAt: new Date()
        }
      });
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao deletar orçamento');
    }
  }

  /**
   * Format budget response
   */
  private formatBudgetResponse(budget: any): BudgetResponse {
    return {
      id: budget.id,
      plannedAmount: Number(budget.plannedAmount),
      month: budget.month,
      year: budget.year,
      categoryId: budget.categoryId,
      familyMemberId: budget.familyMemberId,
      createdAt: budget.createdAt.toISOString(),
      updatedAt: budget.updatedAt.toISOString(),
      version: budget.version,
      category: {
        id: budget.category.id,
        name: budget.category.name,
        color: budget.category.color,
        ...(budget.category.parent && {
          parent: {
            id: budget.category.parent.id,
            name: budget.category.parent.name,
            color: budget.category.parent.color
          }
        })
      },
      ...(budget.familyMember && {
        familyMember: {
          id: budget.familyMember.id,
          name: budget.familyMember.name,
          color: budget.familyMember.color
        }
      })
    };
  }

  /**
   * Calculate budget progress with actual spending data
   */
  private async calculateBudgetProgress(
    budgetId: string,
    categoryId: string,
    familyMemberId: string | null,
    month: number,
    year: number,
    plannedAmount: number
  ): Promise<BudgetProgress> {
    try {
      // Calculate date range for the budget period
      const startDate = new Date(year, month - 1, 1); // First day of month
      const endDate = new Date(year, month, 0); // Last day of month
      const currentDate = new Date();

      // Build where clause for transactions
      const transactionWhere: Prisma.TransactionWhereInput = {
        transactionDate: {
          gte: startDate,
          lte: endDate
        },
        categoryId: categoryId,
        ...(familyMemberId && {
          members: {
            some: {
              familyMemberId: familyMemberId
            }
          }
        })
      };

      // Get actual spending data using aggregation
      const [transactionAggregation, transactionCount] = await Promise.all([
        prisma.transaction.aggregate({
          where: transactionWhere,
          _sum: {
            totalAmount: true
          },
          _avg: {
            totalAmount: true
          }
        }),
        prisma.transaction.count({
          where: transactionWhere
        })
      ]);

      const actualAmount = Number(transactionAggregation._sum.totalAmount || 0);
      const averageTransactionAmount = Number(transactionAggregation._avg.totalAmount || 0);
      const remainingAmount = plannedAmount - actualAmount;
      const percentageUsed = plannedAmount > 0 ? (actualAmount / plannedAmount) * 100 : 0;
      const percentageRemaining = Math.max(0, 100 - percentageUsed);
      const isOverBudget = actualAmount > plannedAmount;
      const overBudgetAmount = isOverBudget ? actualAmount - plannedAmount : undefined;

      // Calculate time-based metrics
      const daysInPeriod = endDate.getDate();
      const currentDay = currentDate.getDate();
      const daysRemaining = Math.max(0, daysInPeriod - currentDay);
      const dailyBudgetRemaining = daysRemaining > 0 ? remainingAmount / daysRemaining : 0;

      // Calculate projected total based on current spending rate
      let projectedTotal: number | undefined;
      if (currentDay > 0 && currentDate.getMonth() === month - 1 && currentDate.getFullYear() === year) {
        const dailySpendingRate = actualAmount / currentDay;
        projectedTotal = dailySpendingRate * daysInPeriod;
      }

      // Determine status
      let status: BudgetProgress['status'];
      if (isOverBudget) {
        status = 'over_budget';
      } else if (percentageUsed >= 90) {
        status = 'warning';
      } else if (projectedTotal && projectedTotal > plannedAmount * 1.1) {
        status = 'warning';
      } else if (percentageUsed >= 70) {
        status = 'on_track';
      } else {
        status = 'under_budget';
      }

      return {
        actualAmount,
        remainingAmount,
        percentageUsed: Math.round(percentageUsed * 100) / 100, // Round to 2 decimal places
        percentageRemaining: Math.round(percentageRemaining * 100) / 100,
        isOverBudget,
        overBudgetAmount,
        transactionCount,
        averageTransactionAmount: Math.round(averageTransactionAmount * 100) / 100,
        daysInPeriod,
        daysRemaining,
        dailyBudgetRemaining: Math.round(dailyBudgetRemaining * 100) / 100,
        projectedTotal: projectedTotal ? Math.round(projectedTotal * 100) / 100 : undefined,
        status
      };
    } catch (error) {
      console.error('Error calculating budget progress:', error);
      // Return default values on error
      return {
        actualAmount: 0,
        remainingAmount: plannedAmount,
        percentageUsed: 0,
        percentageRemaining: 100,
        isOverBudget: false,
        transactionCount: 0,
        averageTransactionAmount: 0,
        daysInPeriod: new Date(year, month, 0).getDate(),
        daysRemaining: new Date(year, month, 0).getDate(),
        dailyBudgetRemaining: plannedAmount / new Date(year, month, 0).getDate(),
        status: 'under_budget'
      };
    }
  }

  /**
   * Get budget summary for a period
   */
  async getBudgetSummary(filters: BudgetProgressFilters): Promise<BudgetSummary> {
    try {
      const { month, year, categoryId, familyMemberId } = filters;

      // Build where clause
      const where: Prisma.BudgetWhereInput = {
        deletedAt: null,
        ...(month && { month }),
        ...(year && { year }),
        ...(categoryId && { categoryId }),
        ...(familyMemberId && { familyMemberId })
      };

      // Get all budgets for the period
      const budgets = await prisma.budget.findMany({
        where,
        include: {
          category: true,
          familyMember: true
        }
      });

      let totalPlanned = 0;
      let totalActual = 0;
      let overBudgetCount = 0;
      let underBudgetCount = 0;
      let onTrackCount = 0;
      let warningCount = 0;

      // Calculate progress for each budget
      for (const budget of budgets) {
        const progress = await this.calculateBudgetProgress(
          budget.id,
          budget.categoryId,
          budget.familyMemberId,
          budget.month,
          budget.year,
          Number(budget.plannedAmount)
        );

        totalPlanned += Number(budget.plannedAmount);
        totalActual += progress.actualAmount;

        // Count by status
        switch (progress.status) {
          case 'over_budget':
            overBudgetCount++;
            break;
          case 'warning':
            warningCount++;
            break;
          case 'on_track':
            onTrackCount++;
            break;
          case 'under_budget':
            underBudgetCount++;
            break;
        }
      }

      const totalRemaining = totalPlanned - totalActual;
      const overallPercentageUsed = totalPlanned > 0 ? (totalActual / totalPlanned) * 100 : 0;

      return {
        totalPlanned: Math.round(totalPlanned * 100) / 100,
        totalActual: Math.round(totalActual * 100) / 100,
        totalRemaining: Math.round(totalRemaining * 100) / 100,
        overallPercentageUsed: Math.round(overallPercentageUsed * 100) / 100,
        budgetCount: budgets.length,
        overBudgetCount,
        underBudgetCount,
        onTrackCount,
        warningCount
      };
    } catch (error) {
      console.error('Error calculating budget summary:', error);
      throw new Error('Erro interno do servidor ao calcular resumo de orçamentos');
    }
  }

  /**
   * Get budget report with detailed breakdown
   */
  async getBudgetReport(filters: BudgetProgressFilters): Promise<BudgetReport> {
    try {
      const { month, year, categoryId, familyMemberId, includeSubcategories } = filters;
      const reportMonth = month || new Date().getMonth() + 1;
      const reportYear = year || new Date().getFullYear();

      // Get summary
      const summary = await this.getBudgetSummary({
        ...filters,
        month: reportMonth,
        year: reportYear
      });

      // Get budgets with progress
      const budgetFilters: BudgetFilters = {
        month: reportMonth,
        year: reportYear,
        categoryId,
        familyMemberId,
        page: 1,
        limit: 100,
        includeProgress: true,
        sortBy: 'category',
        sortOrder: 'asc'
      };

      const budgetsResult = await this.findAll(budgetFilters);

      // Group by category for breakdown
      const categoryBreakdown = new Map<string, {
        categoryId: string;
        categoryName: string;
        totalPlanned: number;
        totalActual: number;
        percentageUsed: number;
        status: BudgetProgress['status'];
      }>();

      // Group by member for breakdown
      const memberBreakdown = new Map<string, {
        familyMemberId: string;
        familyMemberName: string;
        totalPlanned: number;
        totalActual: number;
        percentageUsed: number;
        budgetCount: number;
      }>();

      for (const budget of budgetsResult.data) {
        if (!budget.progress) continue;

        // Category breakdown
        const categoryKey = budget.categoryId;
        const existingCategory = categoryBreakdown.get(categoryKey);

        if (existingCategory) {
          existingCategory.totalPlanned += budget.plannedAmount;
          existingCategory.totalActual += budget.progress.actualAmount;
          existingCategory.percentageUsed = existingCategory.totalPlanned > 0
            ? (existingCategory.totalActual / existingCategory.totalPlanned) * 100
            : 0;
        } else {
          categoryBreakdown.set(categoryKey, {
            categoryId: budget.categoryId,
            categoryName: budget.category.name,
            totalPlanned: budget.plannedAmount,
            totalActual: budget.progress.actualAmount,
            percentageUsed: budget.progress.percentageUsed,
            status: budget.progress.status
          });
        }

        // Member breakdown (if family member exists)
        if (budget.familyMember) {
          const memberKey = budget.familyMember.id;
          const existingMember = memberBreakdown.get(memberKey);

          if (existingMember) {
            existingMember.totalPlanned += budget.plannedAmount;
            existingMember.totalActual += budget.progress.actualAmount;
            existingMember.percentageUsed = existingMember.totalPlanned > 0
              ? (existingMember.totalActual / existingMember.totalPlanned) * 100
              : 0;
            existingMember.budgetCount++;
          } else {
            memberBreakdown.set(memberKey, {
              familyMemberId: budget.familyMember.id,
              familyMemberName: budget.familyMember.name,
              totalPlanned: budget.plannedAmount,
              totalActual: budget.progress.actualAmount,
              percentageUsed: budget.progress.percentageUsed,
              budgetCount: 1
            });
          }
        }
      }

      return {
        period: {
          month: reportMonth,
          year: reportYear
        },
        summary,
        budgets: budgetsResult.data,
        categoryBreakdown: Array.from(categoryBreakdown.values()),
        memberBreakdown: Array.from(memberBreakdown.values())
      };
    } catch (error) {
      console.error('Error generating budget report:', error);
      throw new Error('Erro interno do servidor ao gerar relatório de orçamentos');
    }
  }
}

export const budgetService = new BudgetService();
