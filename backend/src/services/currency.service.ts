import axios from 'axios';

export interface CurrencyConversionResult {
  originalAmount: number;
  convertedAmount: number;
  fromCurrency: string;
  toCurrency: string;
  exchangeRate: number;
  isManualRate: boolean;
  timestamp: Date;
}

export class CurrencyService {
  private readonly supportedCurrencies = [
    'BRL', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF'
  ];

  private readonly apiUrl = 'https://api.exchangerate-api.com/v4/latest';

  /**
   * Convert amount between currencies
   */
  async convertAmount(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
    manualRate?: number
  ): Promise<CurrencyConversionResult> {
    // Validate inputs
    this.validateAmount(amount);
    this.validateCurrency(fromCurrency);
    this.validateCurrency(toCurrency);

    if (manualRate !== undefined) {
      this.validateExchangeRate(manualRate);
    }

    // Same currency conversion
    if (fromCurrency === toCurrency) {
      return {
        originalAmount: amount,
        convertedAmount: amount,
        fromCurrency,
        toCurrency,
        exchangeRate: 1,
        isManualRate: false,
        timestamp: new Date()
      };
    }

    // Use manual rate if provided
    if (manualRate !== undefined) {
      return {
        originalAmount: amount,
        convertedAmount: amount * manualRate,
        fromCurrency,
        toCurrency,
        exchangeRate: manualRate,
        isManualRate: true,
        timestamp: new Date()
      };
    }

    // Fetch current rate from API
    const exchangeRate = await this.getCurrentRate(fromCurrency, toCurrency);
    
    return {
      originalAmount: amount,
      convertedAmount: amount * exchangeRate,
      fromCurrency,
      toCurrency,
      exchangeRate,
      isManualRate: false,
      timestamp: new Date()
    };
  }

  /**
   * Get current exchange rate from API
   */
  async getCurrentRate(fromCurrency: string, toCurrency: string): Promise<number> {
    if (fromCurrency === toCurrency) {
      return 1;
    }

    try {
      const response = await axios.get(`${this.apiUrl}/${fromCurrency}`);
      const rates = response.data.rates;

      if (!rates[toCurrency]) {
        throw new Error(`Exchange rate not available for ${toCurrency}`);
      }

      return rates[toCurrency];
    } catch (error) {
      if (error instanceof Error && error.message.includes('Exchange rate not available')) {
        throw error;
      }
      throw new Error('Failed to fetch exchange rate');
    }
  }

  /**
   * Get list of supported currencies
   */
  getSupportedCurrencies(): string[] {
    return [...this.supportedCurrencies];
  }

  /**
   * Format currency amount with locale-specific formatting
   */
  formatCurrency(amount: number, currency: string): string {
    const localeMap: Record<string, string> = {
      BRL: 'pt-BR',
      USD: 'en-US',
      EUR: 'de-DE',
      GBP: 'en-GB',
      JPY: 'ja-JP',
      CAD: 'en-CA',
      AUD: 'en-AU',
      CHF: 'de-CH'
    };

    const locale = localeMap[currency] || 'en-US';

    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(amount);
  }

  /**
   * Calculate conversion fee
   */
  calculateConversionFee(
    amount: number, 
    feePercentage: number, 
    minimumFee?: number
  ): number {
    const calculatedFee = amount * (feePercentage / 100);
    
    if (minimumFee && calculatedFee < minimumFee) {
      return minimumFee;
    }

    return calculatedFee;
  }

  /**
   * Validate amount is positive
   */
  private validateAmount(amount: number): void {
    if (amount <= 0) {
      throw new Error('Amount must be positive');
    }
  }

  /**
   * Validate currency code
   */
  private validateCurrency(currency: string): void {
    if (!this.supportedCurrencies.includes(currency)) {
      throw new Error(`Invalid currency code: ${currency}`);
    }
  }

  /**
   * Validate exchange rate is positive
   */
  private validateExchangeRate(rate: number): void {
    if (rate <= 0) {
      throw new Error('Exchange rate must be positive');
    }
  }
}
