import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

// Import routes
import authRoutes from './routes/auth.routes';
import familyMemberRoutes from './routes/family-member.routes';
import accountRoutes from './routes/account.routes';
import categoryRoutes from './routes/category.routes';
import categorySuggestionRoutes from './routes/category-suggestion.routes';
import tagRoutes from './routes/tag.routes';
import transactionRoutes from './routes/transaction.routes';
import transferRoutes from './routes/transfer.routes';
import budgetRoutes from './routes/budget.routes';
import financialGoalRoutes from './routes/financial-goal.routes';
import milestoneRoutes from './routes/milestone.routes';
import goalProgressHistoryRoutes from './routes/goal-progress-history.routes';
import cacheRoutes from './routes/cache.routes';
import futureTransactionRoutes from './routes/future-transaction.routes';
import dashboardRoutes from './routes/dashboard.routes';
import insightRoutes from './routes/insight.routes';

// Import scheduler and job services
import { scheduler } from './config/scheduler.config';
import { recurringTransactionJobService } from './services/recurring-transaction-job.service';
import { futureTransactionJobService } from './services/future-transaction-job.service';
import { insightJobService } from './services/insight-job.service';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;
const API_PREFIX = process.env.API_PREFIX || '/api/v1';

// Security middleware
app.use(helmet());

// CORS configuration
const corsOrigin = process.env.FRONTEND_URL || 'http://localhost:3000';
console.log(`🔗 CORS configured for origin: ${corsOrigin}`);

app.use(cors({
  origin: corsOrigin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: {
      message: 'Muitas tentativas. Tente novamente em alguns minutos.',
      code: 'RATE_LIMIT_EXCEEDED'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined'));
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Personal Finance Manager API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Debug endpoint for configuration
app.get('/debug/config', (req, res) => {
  res.status(200).json({
    success: true,
    config: {
      port: PORT,
      environment: process.env.NODE_ENV || 'development',
      corsOrigin: process.env.FRONTEND_URL || 'http://localhost:3000',
      apiPrefix: API_PREFIX,
      timestamp: new Date().toISOString()
    }
  });
});

// API routes
app.use(`${API_PREFIX}/auth`, authRoutes);
app.use(`${API_PREFIX}/family-members`, familyMemberRoutes);
app.use(`${API_PREFIX}/accounts`, accountRoutes);
app.use(`${API_PREFIX}/categories`, categoryRoutes);
app.use(`${API_PREFIX}/category-suggestions`, categorySuggestionRoutes);
app.use(`${API_PREFIX}/tags`, tagRoutes);
app.use(`${API_PREFIX}/transactions`, transactionRoutes);
app.use(`${API_PREFIX}/transfers`, transferRoutes);
app.use(`${API_PREFIX}/budgets`, budgetRoutes);
app.use(`${API_PREFIX}/goals`, financialGoalRoutes);
app.use(`${API_PREFIX}/milestones`, milestoneRoutes);
app.use(`${API_PREFIX}/progress-history`, goalProgressHistoryRoutes);
app.use(`${API_PREFIX}/cache`, cacheRoutes);
app.use(`${API_PREFIX}/future-transactions`, futureTransactionRoutes);
app.use(`${API_PREFIX}/dashboard`, dashboardRoutes);
app.use(`${API_PREFIX}/insights`, insightRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      message: 'Endpoint não encontrado',
      code: 'NOT_FOUND'
    }
  });
});

// Global error handler
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', error);

  // Handle Prisma errors
  if (error.code === 'P2002') {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Dados duplicados',
        code: 'DUPLICATE_DATA'
      }
    });
  }

  // Handle validation errors
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: {
        message: error.message,
        code: 'VALIDATION_ERROR'
      }
    });
  }

  // Default error response
  return res.status(500).json({
    success: false,
    error: {
      message: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    }
  });
});

// Start server
if (process.env.NODE_ENV !== 'test') {
  app.listen(PORT, () => {
    console.log(`🚀 Personal Finance Manager API running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 Health check: http://localhost:${PORT}/health`);
    console.log(`📡 API base URL: http://localhost:${PORT}${API_PREFIX}`);

    // Initialize scheduler after server starts
    console.log('⏰ Initializing job scheduler...');
    recurringTransactionJobService.initialize();
    futureTransactionJobService.initialize();
    insightJobService.initialize();
    scheduler.initialize();
    console.log('✅ Job scheduler initialized successfully');
  });

  // Graceful shutdown handlers
  const gracefulShutdown = async (signal: string) => {
    console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

    try {
      // Shutdown scheduler
      console.log('⏰ Shutting down job scheduler...');
      await scheduler.gracefulShutdown();
      console.log('✅ Job scheduler shutdown complete');

      console.log('👋 Graceful shutdown completed');
      process.exit(0);

    } catch (error) {
      console.error('❌ Error during graceful shutdown:', error);
      process.exit(1);
    }
  };

  // Register shutdown handlers
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    gracefulShutdown('UNCAUGHT_EXCEPTION');
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown('UNHANDLED_REJECTION');
  });
}

export default app;
