import { Router } from 'express';
import { tagController } from '../controllers/tag.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireActiveUser);

/**
 * @route   POST /api/v1/tags
 * @desc    Create a new tag
 * @access  Private
 * @body    {
 *   name: string,
 *   color: string
 * }
 */
router.post('/', tagController.create.bind(tagController));

/**
 * @route   GET /api/v1/tags/stats
 * @desc    Get tag statistics
 * @access  Private
 */
router.get('/stats', tagController.getStats.bind(tagController));

/**
 * @route   GET /api/v1/tags
 * @desc    Get all tags with filters and pagination
 * @access  Private
 * @query   name - Filter by name (partial match, case-insensitive)
 * @query   includeArchived - Include archived tags (default: false)
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20, max: 100)
 */
router.get('/', tagController.findAll.bind(tagController));

/**
 * @route   GET /api/v1/tags/:id
 * @desc    Get tag by ID
 * @access  Private
 * @query   includeArchived - Include archived tag (default: false)
 */
router.get('/:id', tagController.findById.bind(tagController));

/**
 * @route   PUT /api/v1/tags/:id
 * @desc    Update tag
 * @access  Private
 * @body    {
 *   name?: string,
 *   color?: string
 * }
 */
router.put('/:id', tagController.update.bind(tagController));

/**
 * @route   PATCH /api/v1/tags/:id/archive
 * @desc    Archive or unarchive tag
 * @access  Private
 * @body    { "archived": boolean }
 */
router.patch('/:id/archive', tagController.archive.bind(tagController));

/**
 * @route   DELETE /api/v1/tags/:id
 * @desc    Permanently delete tag
 * @access  Private
 * @note    Tag must not have any associated transactions
 */
router.delete('/:id', tagController.delete.bind(tagController));

export default router;
