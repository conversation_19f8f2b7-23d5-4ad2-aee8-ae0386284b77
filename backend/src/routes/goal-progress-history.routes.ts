import { Router } from 'express';
import { goalProgressHistoryController } from '../controllers/goal-progress-history.controller';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * @route GET /api/v1/goals/progress-history
 * @desc Get all progress history with filters and pagination
 * @access Private
 * @query {string} [goalId] - Filter by goal ID
 * @query {string} [operation] - Filter by operation type (add, subtract, set)
 * @query {string} [fromDate] - Filter from date (ISO string)
 * @query {string} [toDate] - Filter to date (ISO string)
 * @query {number} [page=1] - Page number
 * @query {number} [limit=20] - Items per page
 * @query {string} [sortBy=createdAt] - Sort field (createdAt, amountChanged)
 * @query {string} [sortOrder=desc] - Sort order (asc, desc)
 */
router.get('/', goalProgressHistoryController.findAll.bind(goalProgressHistoryController));

/**
 * @route GET /api/v1/goals/progress-history/recent
 * @desc Get recent progress updates
 * @access Private
 * @query {number} [limit=10] - Number of recent updates to return
 */
router.get('/recent', goalProgressHistoryController.getRecent.bind(goalProgressHistoryController));

/**
 * @route PUT /api/v1/goals/progress-history/:id
 * @desc Update progress history entry description
 * @access Private
 * @param {string} id - Progress history entry ID
 * @body {string} description - New description
 */
router.put('/:id', goalProgressHistoryController.updateDescription.bind(goalProgressHistoryController));

/**
 * @route DELETE /api/v1/goals/progress-history/:id
 * @desc Delete progress history entry
 * @access Private
 * @param {string} id - Progress history entry ID
 */
router.delete('/:id', goalProgressHistoryController.delete.bind(goalProgressHistoryController));

export default router;
