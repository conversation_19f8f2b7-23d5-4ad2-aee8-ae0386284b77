import { Router } from 'express';
import { insight<PERSON><PERSON>roller } from '../controllers/insight.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireActiveUser);

/**
 * @route   POST /api/v1/insights
 * @desc    Create a new insight manually
 * @access  Private
 * @body    {
 *   type: InsightType,
 *   priority: InsightPriority,
 *   title: string,
 *   description: string,
 *   data?: object,
 *   categoryId?: string,
 *   accountId?: string,
 *   goalId?: string,
 *   periodStart?: string,
 *   periodEnd?: string,
 *   recommendations?: object,
 *   expiresAt?: string,
 *   relevanceScore?: number
 * }
 */
router.post('/', insightController.create.bind(insightController));

/**
 * @route   GET /api/v1/insights
 * @desc    Get all insights with filters and pagination
 * @access  Private
 * @query   type - Filter by insight type
 * @query   priority - Filter by priority (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)
 * @query   status - Filter by status (NEW, VIEWED, DISMISSED, ACTED_UPON)
 * @query   categoryId - Filter by category ID
 * @query   accountId - Filter by account ID
 * @query   goalId - Filter by goal ID
 * @query   fromDate - Filter insights created from this date
 * @query   toDate - Filter insights created until this date
 * @query   includeExpired - Include expired insights (default: false)
 * @query   minRelevanceScore - Minimum relevance score filter
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20, max: 100)
 */
router.get('/', insightController.findAll.bind(insightController));

/**
 * @route   POST /api/v1/insights/generate
 * @desc    Generate insights automatically based on user data
 * @access  Private
 * @body    {
 *   types?: InsightType[],
 *   forceRegenerate?: boolean,
 *   periodStart?: string,
 *   periodEnd?: string,
 *   categoryIds?: string[],
 *   accountIds?: string[],
 *   goalIds?: string[]
 * }
 */
router.post('/generate', insightController.generateInsights.bind(insightController));

/**
 * @route   POST /api/v1/insights/bulk-action
 * @desc    Perform bulk actions on multiple insights
 * @access  Private
 * @body    {
 *   insightIds: string[],
 *   action: 'MARK_VIEWED' | 'DISMISS' | 'MARK_ACTED_UPON'
 * }
 */
router.post('/bulk-action', insightController.bulkAction.bind(insightController));

/**
 * @route   GET /api/v1/insights/analytics
 * @desc    Get insight analytics and statistics
 * @access  Private
 */
router.get('/analytics', insightController.getAnalytics.bind(insightController));

/**
 * @route   GET /api/v1/insights/:id
 * @desc    Get insight by ID
 * @access  Private
 */
router.get('/:id', insightController.findById.bind(insightController));

/**
 * @route   PUT /api/v1/insights/:id
 * @desc    Update insight
 * @access  Private
 * @body    {
 *   status?: InsightStatus,
 *   actionTaken?: boolean,
 *   relevanceScore?: number
 * }
 */
router.put('/:id', insightController.update.bind(insightController));

/**
 * @route   DELETE /api/v1/insights/:id
 * @desc    Delete insight (soft delete)
 * @access  Private
 */
router.delete('/:id', insightController.delete.bind(insightController));

export default router;
