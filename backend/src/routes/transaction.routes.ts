import { Router } from 'express';
import { transactionController } from '../controllers/transaction.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireActiveUser);

/**
 * @route   POST /api/v1/transactions
 * @desc    Create a new transaction
 * @access  Private
 * @body    {
 *   description: string,
 *   amount: number,
 *   transactionDate: string | Date,
 *   type: TransactionType,
 *   accountId: string,
 *   categoryId?: string,
 *   destinationAccountId?: string,
 *   exchangeRate?: number,
 *   installmentNumber?: number,
 *   totalInstallments?: number,
 *   parentTransactionId?: string,
 *   isFuture?: boolean,
 *   tagIds?: string[],
 *   familyMemberIds: string[]
 * }
 */
router.post('/', transactionController.create.bind(transactionController));

/**
 * @route   GET /api/v1/transactions
 * @desc    Get all transactions with filters and pagination
 * @access  Private
 * @query   description - Filter by description (partial match, case-insensitive)
 * @query   type - Filter by transaction type (INCOME, EXPENSE, TRANSFER)
 * @query   accountId - Filter by account ID (includes both source and destination)
 * @query   categoryId - Filter by category ID
 * @query   familyMemberId - Filter by family member ID
 * @query   tagId - Filter by tag ID
 * @query   startDate - Filter by start date (ISO string)
 * @query   endDate - Filter by end date (ISO string)
 * @query   minAmount - Filter by minimum amount
 * @query   maxAmount - Filter by maximum amount
 * @query   isFuture - Filter by future transactions (true/false)
 * @query   includeDeleted - Include deleted transactions (default: false)
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20, max: 100)
 * @query   sortBy - Sort field (transactionDate, amount, description, createdAt)
 * @query   sortOrder - Sort order (asc, desc)
 */
router.get('/', transactionController.findAll.bind(transactionController));

/**
 * @route   GET /api/v1/transactions/:id
 * @desc    Get transaction by ID
 * @access  Private
 * @query   includeDeleted - Include deleted transaction (default: false)
 */
router.get('/:id', transactionController.findById.bind(transactionController));

/**
 * @route   PUT /api/v1/transactions/:id
 * @desc    Update transaction
 * @access  Private
 * @body    {
 *   description?: string,
 *   amount?: number,
 *   transactionDate?: string | Date,
 *   categoryId?: string,
 *   destinationAccountId?: string,
 *   exchangeRate?: number,
 *   isFuture?: boolean,
 *   tagIds?: string[],
 *   familyMemberIds?: string[]
 * }
 */
router.put('/:id', transactionController.update.bind(transactionController));

/**
 * @route   GET /api/v1/transactions/:id/installments
 * @desc    Get all installments of a transaction (included in main transaction response)
 * @access  Private
 * @note    Installments are now included in the main transaction response
 */
// router.get('/:id/installments', transactionController.getInstallments.bind(transactionController));

/**
 * @route   PUT /api/v1/transactions/:id/installments
 * @desc    Update installments via main transaction update endpoint
 * @access  Private
 * @note    Use PUT /api/v1/transactions/:id with installments array in body
 */
// router.put('/:id/installments', transactionController.updateInstallments.bind(transactionController));

/**
 * @route   PATCH /api/v1/transactions/:id/installments/:installmentNumber
 * @desc    Update installment status (paid/unpaid)
 * @access  Private
 * @body    {
 *   isPaid: boolean,
 *   paidAt?: string
 * }
 */
router.patch('/:id/installments/:installmentNumber', transactionController.updateInstallmentStatus.bind(transactionController));

/**
 * @route   DELETE /api/v1/transactions/:id
 * @desc    Delete transaction (soft delete)
 * @access  Private
 * @note    This will reverse any balance changes and soft delete the transaction
 */
router.delete('/:id', transactionController.delete.bind(transactionController));

export default router;
