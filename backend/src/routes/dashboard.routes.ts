import { Router } from 'express';
import { DashboardController } from '../controllers/dashboard.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { warmDashboardCache } from '../middleware/cache-invalidation.middleware';
import { dashboardMiddleware } from '../middleware/dashboard-validation.middleware';

const router = Router();
const dashboardController = new DashboardController();

// Apply authentication middleware to all dashboard routes
router.use(authenticateToken);

// Apply dashboard-specific middleware (validation, caching, logging, rate limiting)
router.use(dashboardMiddleware);

// Apply cache warming middleware for GET requests
router.use(warmDashboardCache);

/**
 * @route GET /api/dashboard/overview
 * @desc Get complete dashboard overview with all aggregated data
 * @access Private
 * @query {string} [accountIds] - Comma-separated account IDs to filter
 * @query {string} [categoryIds] - Comma-separated category IDs to filter
 * @query {string} [familyMemberIds] - Comma-separated family member IDs to filter
 * @query {string} [currencies] - Comma-separated currencies to filter
 * @query {string} [startDate] - Start date for date range filter (ISO string)
 * @query {string} [endDate] - End date for date range filter (ISO string)
 * @query {number} [month] - Month for period filter (1-12)
 * @query {number} [year] - Year for period filter
 * @query {string} [tagIds] - Comma-separated tag IDs to filter
 * @query {string} [budgetIds] - Comma-separated budget IDs to filter
 * @returns {Object} Complete dashboard overview data
 */
router.get('/overview', dashboardController.getOverview.bind(dashboardController));

/**
 * @route GET /api/dashboard/account-balances
 * @desc Get account balances aggregation
 * @access Private
 * @query {string} [accountIds] - Filter by specific accounts
 * @query {string} [currencies] - Filter by currencies
 * @returns {Object} Account balances aggregation data
 */
router.get('/account-balances', dashboardController.getAccountBalances.bind(dashboardController));

/**
 * @route GET /api/dashboard/net-worth
 * @desc Get net worth calculation and history
 * @access Private
 * @query {string} [accountIds] - Filter by specific accounts
 * @query {string} [currencies] - Filter by currencies
 * @query {string} [startDate] - Start date for history
 * @query {string} [endDate] - End date for history
 * @returns {Object} Net worth data with historical tracking
 */
router.get('/net-worth', dashboardController.getNetWorth.bind(dashboardController));

/**
 * @route GET /api/dashboard/credit-card-usage
 * @desc Get credit card usage analysis
 * @access Private
 * @query {string} [accountIds] - Filter by specific credit card accounts
 * @query {string} [currencies] - Filter by currencies
 * @returns {Object} Credit card usage analysis
 */
router.get('/credit-card-usage', dashboardController.getCreditCardUsage.bind(dashboardController));

/**
 * @route GET /api/dashboard/expenses-by-category
 * @desc Get expenses breakdown by category
 * @access Private
 * @query {string} [categoryIds] - Filter by specific categories
 * @query {string} [accountIds] - Filter by specific accounts
 * @query {string} [familyMemberIds] - Filter by family members
 * @query {string} [startDate] - Start date for analysis
 * @query {string} [endDate] - End date for analysis
 * @query {number} [month] - Month for period filter
 * @query {number} [year] - Year for period filter
 * @returns {Object} Expenses breakdown by category
 */
router.get('/expenses-by-category', dashboardController.getExpensesByCategory.bind(dashboardController));

/**
 * @route GET /api/dashboard/expenses-by-member
 * @desc Get expenses breakdown by family member
 * @access Private
 * @query {string} [familyMemberIds] - Filter by specific family members
 * @query {string} [categoryIds] - Filter by categories
 * @query {string} [accountIds] - Filter by accounts
 * @query {string} [startDate] - Start date for analysis
 * @query {string} [endDate] - End date for analysis
 * @query {number} [month] - Month for period filter
 * @query {number} [year] - Year for period filter
 * @returns {Object} Expenses breakdown by family member
 */
router.get('/expenses-by-member', dashboardController.getExpensesByMember.bind(dashboardController));

/**
 * @route GET /api/dashboard/budget-comparison
 * @desc Get budget vs actual expenses comparison
 * @access Private
 * @query {string} [categoryIds] - Filter by specific categories
 * @query {string} [familyMemberIds] - Filter by family members
 * @query {number} [month] - Month for comparison (default: current month)
 * @query {number} [year] - Year for comparison (default: current year)
 * @query {string} [budgetIds] - Filter by specific budgets
 * @returns {Object} Budget vs actual comparison data
 */
router.get('/budget-comparison', dashboardController.getBudgetComparison.bind(dashboardController));

/**
 * @route GET /api/dashboard/goal-progress
 * @desc Get financial goals progress
 * @access Private
 * @query {string} [familyMemberIds] - Filter by family members
 * @query {string} [startDate] - Start date for progress calculation
 * @query {string} [endDate] - End date for progress calculation
 * @returns {Object} Financial goals progress data
 */
router.get('/goal-progress', dashboardController.getGoalProgress.bind(dashboardController));

/**
 * @route GET /api/dashboard/performance-metrics
 * @desc Get dashboard performance metrics (for monitoring)
 * @access Private
 * @returns {Object} Performance metrics and statistics
 */
router.get('/performance-metrics', dashboardController.getPerformanceMetrics.bind(dashboardController));

export default router;
