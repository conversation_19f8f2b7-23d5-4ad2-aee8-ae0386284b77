import { Router } from 'express';
import { budgetController } from '../controllers/budget.controller';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

/**
 * @route   POST /api/v1/budgets
 * @desc    Create a new budget
 * @access  Private
 * @body    {
 *   plannedAmount: number,
 *   month: number,
 *   year: number,
 *   categoryId: string,
 *   familyMemberId?: string
 * }
 * @returns {
 *   success: boolean,
 *   data: BudgetResponse,
 *   message: string
 * }
 */
router.post('/', authenticateToken, budgetController.create.bind(budgetController));

/**
 * @route   GET /api/v1/budgets
 * @desc    Get all budgets with filters and pagination
 * @access  Private
 * @query   {
 *   categoryId?: string,
 *   familyMemberId?: string,
 *   month?: number,
 *   year?: number,
 *   page?: number,
 *   limit?: number,
 *   includeProgress?: boolean,
 *   sortBy?: 'plannedAmount' | 'month' | 'year' | 'createdAt' | 'category' | 'familyMember',
 *   sortOrder?: 'asc' | 'desc'
 * }
 * @returns {
 *   success: boolean,
 *   data: BudgetResponse[],
 *   pagination: PaginationInfo,
 *   message: string
 * }
 */
router.get('/', authenticateToken, budgetController.findAll.bind(budgetController));

/**
 * @route   GET /api/v1/budgets/report
 * @desc    Get budget summary and report
 * @access  Private
 * @query   {
 *   categoryId?: string,
 *   familyMemberId?: string,
 *   month?: number,
 *   year?: number,
 *   startMonth?: number,
 *   startYear?: number,
 *   endMonth?: number,
 *   endYear?: number,
 *   includeSubcategories?: boolean,
 *   alertThreshold?: number
 * }
 * @returns {
 *   success: boolean,
 *   data: BudgetReport,
 *   message: string
 * }
 */
router.get('/report', authenticateToken, budgetController.getBudgetReport.bind(budgetController));

/**
 * @route   GET /api/v1/budgets/:id
 * @desc    Get budget by ID
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @query   {
 *   includeProgress?: boolean
 * }
 * @returns {
 *   success: boolean,
 *   data: BudgetResponse,
 *   message: string
 * }
 */
router.get('/:id', authenticateToken, budgetController.findById.bind(budgetController));

/**
 * @route   GET /api/v1/budgets/:id/progress
 * @desc    Get budget progress for a specific budget
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @returns {
 *   success: boolean,
 *   data: {
 *     budgetId: string,
 *     progress: BudgetProgress
 *   },
 *   message: string
 * }
 */
router.get('/:id/progress', authenticateToken, budgetController.getBudgetProgress.bind(budgetController));

/**
 * @route   PUT /api/v1/budgets/:id
 * @desc    Update budget
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @body    {
 *   plannedAmount?: number,
 *   month?: number,
 *   year?: number,
 *   categoryId?: string,
 *   familyMemberId?: string
 * }
 * @returns {
 *   success: boolean,
 *   data: BudgetResponse,
 *   message: string
 * }
 */
router.put('/:id', authenticateToken, budgetController.update.bind(budgetController));

/**
 * @route   DELETE /api/v1/budgets/:id
 * @desc    Delete budget (soft delete)
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @returns {
 *   success: boolean,
 *   message: string
 * }
 */
router.delete('/:id', authenticateToken, budgetController.delete.bind(budgetController));

export default router;
