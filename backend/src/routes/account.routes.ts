import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { accountController } from '../controllers/account.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireActiveUser);

// Configure multer for SVG logo uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(process.cwd(), 'public', 'icons', 'brands');
    
    // Ensure directory exists
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const filename = `account-${req.params.id}-${uniqueSuffix}.svg`;
    cb(null, filename);
  }
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Only allow SVG files
  if (file.mimetype === 'image/svg+xml') {
    cb(null, true);
  } else {
    cb(new Error('Apenas arquivos SVG são permitidos'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 1024 * 1024, // 1MB limit
    files: 1
  }
});

/**
 * @route   POST /api/v1/accounts
 * @desc    Create a new account
 * @access  Private
 * @body    {
 *   name: string,
 *   type: AccountType,
 *   currency?: string,
 *   creditLimit?: number,
 *   exchangeRate?: number,
 *   includeInTotal?: boolean,
 *   logoPath?: string,
 *   familyMemberIds: string[]
 * }
 */
router.post('/', accountController.create.bind(accountController));

/**
 * @route   GET /api/v1/accounts
 * @desc    Get all accounts with filters and pagination
 * @access  Private
 * @query   name - Filter by name (partial match, case-insensitive)
 * @query   type - Filter by account type
 * @query   currency - Filter by currency
 * @query   includeInTotal - Filter by include in total flag
 * @query   familyMemberId - Filter by family member ID
 * @query   includeArchived - Include archived accounts (default: false)
 * @query   includeDeleted - Include deleted accounts (default: false)
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20, max: 100)
 */
router.get('/', accountController.findAll.bind(accountController));

/**
 * @route   GET /api/v1/accounts/:id
 * @desc    Get account by ID
 * @access  Private
 * @query   includeArchived - Include archived account (default: false)
 */
router.get('/:id', accountController.findById.bind(accountController));

/**
 * @route   PUT /api/v1/accounts/:id
 * @desc    Update account
 * @access  Private
 * @body    {
 *   name?: string,
 *   creditLimit?: number,
 *   exchangeRate?: number,
 *   includeInTotal?: boolean,
 *   logoPath?: string,
 *   familyMemberIds?: string[]
 * }
 */
router.put('/:id', accountController.update.bind(accountController));

/**
 * @route   PATCH /api/v1/accounts/:id/archive
 * @desc    Archive or unarchive account
 * @access  Private
 * @body    { "archived": boolean }
 */
router.patch('/:id/archive', accountController.archive.bind(accountController));

/**
 * @route   POST /api/v1/accounts/:id/logo
 * @desc    Upload SVG logo for account
 * @access  Private
 * @body    FormData with 'logo' file field (SVG only, max 1MB)
 */
router.post('/:id/logo', (req, res, next) => {
  upload.single('logo')(req, res, (err: any) => {
    if (err) {
      return res.status(400).json({
        success: false,
        error: {
          message: err.message,
          code: 'FILE_UPLOAD_ERROR'
        }
      });
    }
    return next();
  });
}, accountController.uploadLogo.bind(accountController));

/**
 * @route   DELETE /api/v1/accounts/:id
 * @desc    Permanently delete account
 * @access  Private
 * @note    This will fail if the account has associated transactions, recurring transactions, or balance history
 */
router.delete('/:id', accountController.delete.bind(accountController));

export default router;
