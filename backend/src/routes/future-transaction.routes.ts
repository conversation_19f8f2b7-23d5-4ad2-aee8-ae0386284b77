import { Router } from 'express';
import { FutureTransactionController } from '../controllers/future-transaction.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();
const futureTransactionController = new FutureTransactionController();

// Rate limiting for future transaction endpoints
const futureTransactionLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: {
      message: 'Muitas tentativas. Tente novamente em alguns minutos.',
      code: 'RATE_LIMIT_EXCEEDED'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Admin rate limiting for processing endpoints
const adminLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // limit each IP to 10 requests per hour for admin operations
  message: {
    success: false,
    error: {
      message: 'Limite de operações administrativas excedido.',
      code: 'ADMIN_RATE_LIMIT_EXCEEDED'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @route GET /api/v1/future-transactions
 * @desc Get all future transactions with optional filters
 * @access Private
 * @query {string} [accountId] - Filter by account ID
 * @query {string} [categoryId] - Filter by category ID
 * @query {string} [familyMemberId] - Filter by family member ID
 * @query {string} [startDate] - Filter by start date (ISO string)
 * @query {string} [endDate] - Filter by end date (ISO string)
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=20] - Number of items per page
 * @query {string} [sortBy=transactionDate] - Field to sort by
 * @query {string} [sortOrder=asc] - Sort order (asc/desc)
 */
router.get('/', futureTransactionLimiter, futureTransactionController.getFutureTransactions.bind(futureTransactionController));

/**
 * @route GET /api/v1/future-transactions/projected-balance
 * @desc Get projected balance based on future transactions
 * @access Private
 * @query {string[]} [accountIds] - Array of account IDs to include
 * @query {string[]} [currencies] - Array of currencies to include
 * @query {string} [projectionDate] - Date to project balance to (ISO string)
 * @query {boolean} [includeRecurring=false] - Include recurring transactions in projection
 */
router.get('/projected-balance', futureTransactionLimiter, futureTransactionController.getProjectedBalance.bind(futureTransactionController));

/**
 * @route GET /api/v1/future-transactions/stats
 * @desc Get statistics about pending future transactions
 * @access Private
 */
router.get('/stats', futureTransactionLimiter, futureTransactionController.getFutureTransactionStats.bind(futureTransactionController));

/**
 * @route GET /api/v1/future-transactions/due-today
 * @desc Get future transactions due today
 * @access Private
 */
router.get('/due-today', futureTransactionLimiter, futureTransactionController.getFutureTransactionsDueToday.bind(futureTransactionController));

/**
 * @route POST /api/v1/future-transactions/process
 * @desc Manually trigger processing of future transactions (admin endpoint)
 * @access Private
 * @body {string} [targetDate] - Target date for processing (ISO string, defaults to today)
 */
router.post('/process', adminLimiter, futureTransactionController.triggerFutureTransactionProcessing.bind(futureTransactionController));

/**
 * @route PUT /api/v1/future-transactions/:id
 * @desc Update a future transaction
 * @access Private
 * @param {string} id - Transaction ID
 * @body {object} updateData - Transaction update data
 */
router.put('/:id', futureTransactionLimiter, futureTransactionController.updateFutureTransaction.bind(futureTransactionController));

/**
 * @route DELETE /api/v1/future-transactions/:id
 * @desc Cancel a future transaction
 * @access Private
 * @param {string} id - Transaction ID
 */
router.delete('/:id', futureTransactionLimiter, futureTransactionController.cancelFutureTransaction.bind(futureTransactionController));

export default router;
