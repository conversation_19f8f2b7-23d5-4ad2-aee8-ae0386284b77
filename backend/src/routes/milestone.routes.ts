import { Router } from 'express';
import { milestoneController } from '../controllers/milestone.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireActiveUser);

/**
 * @route   GET /api/v1/milestones/:id
 * @desc    Get milestone by ID
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @returns {
 *   success: boolean,
 *   data: GoalMilestoneResponse,
 *   message: string
 * }
 */
router.get('/:id', milestoneController.findById.bind(milestoneController));

/**
 * @route   PUT /api/v1/milestones/:id
 * @desc    Update milestone
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @body    {
 *   name?: string,
 *   targetAmount?: number,
 *   targetDate?: string,
 *   isCompleted?: boolean
 * }
 * @returns {
 *   success: boolean,
 *   data: GoalMilestoneResponse,
 *   message: string
 * }
 */
router.put('/:id', milestoneController.update.bind(milestoneController));

/**
 * @route   POST /api/v1/milestones/:id/complete
 * @desc    Mark milestone as completed
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @returns {
 *   success: boolean,
 *   data: GoalMilestoneResponse,
 *   message: string
 * }
 */
router.post('/:id/complete', milestoneController.markCompleted.bind(milestoneController));

/**
 * @route   POST /api/v1/milestones/:id/pending
 * @desc    Mark milestone as pending
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @returns {
 *   success: boolean,
 *   data: GoalMilestoneResponse,
 *   message: string
 * }
 */
router.post('/:id/pending', milestoneController.markPending.bind(milestoneController));

/**
 * @route   DELETE /api/v1/milestones/:id
 * @desc    Delete milestone
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @returns {
 *   success: boolean,
 *   message: string
 * }
 */
router.delete('/:id', milestoneController.delete.bind(milestoneController));

export default router;
