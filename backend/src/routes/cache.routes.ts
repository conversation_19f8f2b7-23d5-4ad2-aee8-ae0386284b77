import { Router } from 'express';
import { dashboardCache } from '../services/dashboard-cache.service';
import { redisCache } from '../lib/redis';

const router = Router();

/**
 * GET /api/cache/health
 * Get cache health status
 */
router.get('/health', async (req, res) => {
  try {
    const health = await dashboardCache.healthCheck();
    
    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'degraded' ? 200 : 503;
    
    res.status(statusCode).json({
      success: true,
      data: health
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to check cache health',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/cache/stats
 * Get cache statistics
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await dashboardCache.getStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get cache statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/cache/invalidate
 * Invalidate cache entries
 */
router.post('/invalidate', async (req, res) => {
  try {
    const { operation, entity } = req.body;
    
    let deletedCount = 0;
    
    if (entity) {
      // Invalidate by entity type (transaction, account, etc.)
      if (!['transaction', 'account', 'budget', 'goal'].includes(entity)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid entity type. Must be one of: transaction, account, budget, goal'
        });
      }
      
      await dashboardCache.invalidateRelated(entity);
      deletedCount = 1; // Placeholder since invalidateRelated doesn't return count
    } else if (operation) {
      // Invalidate specific operation
      const validOperations = [
        'ACCOUNT_BALANCES',
        'NET_WORTH',
        'CREDIT_CARD_USAGE',
        'EXPENSES_BY_CATEGORY',
        'EXPENSES_BY_MEMBER',
        'BUDGET_COMPARISON',
        'GOAL_PROGRESS',
        'OVERVIEW'
      ];
      
      if (!validOperations.includes(operation)) {
        return res.status(400).json({
          success: false,
          error: `Invalid operation. Must be one of: ${validOperations.join(', ')}`
        });
      }
      
      deletedCount = await dashboardCache.invalidate(operation);
    } else {
      // Invalidate all dashboard cache
      deletedCount = await dashboardCache.invalidateAll();
    }
    
    return res.json({
      success: true,
      data: {
        deletedCount,
        message: `Cache invalidated successfully`
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Failed to invalidate cache',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/cache/warm-up
 * Warm up cache with common queries
 */
router.post('/warm-up', async (req, res) => {
  try {
    const { filters = [{}] } = req.body;
    
    // Validate filters array
    if (!Array.isArray(filters)) {
      return res.status(400).json({
        success: false,
        error: 'Filters must be an array'
      });
    }
    
    await dashboardCache.warmUp(filters);
    
    return res.json({
      success: true,
      data: {
        message: 'Cache warm-up initiated',
        filtersCount: filters.length
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Failed to warm up cache',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/cache/reconnect
 * Force Redis reconnection
 */
router.post('/reconnect', async (req, res) => {
  try {
    await redisCache.reconnect();
    
    res.json({
      success: true,
      data: {
        message: 'Redis reconnection initiated'
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to reconnect to Redis',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/cache/ping
 * Ping Redis server
 */
router.get('/ping', async (req, res) => {
  try {
    const startTime = Date.now();
    const isAlive = await redisCache.ping();
    const latency = Date.now() - startTime;
    
    if (isAlive) {
      res.json({
        success: true,
        data: {
          status: 'PONG',
          latency: `${latency}ms`,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.status(503).json({
        success: false,
        error: 'Redis ping failed',
        data: {
          status: 'DOWN',
          latency: `${latency}ms`,
          timestamp: new Date().toISOString()
        }
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to ping Redis',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/cache/info
 * Get comprehensive cache information
 */
router.get('/info', async (req, res) => {
  try {
    const [health, stats, redisStats] = await Promise.all([
      dashboardCache.healthCheck(),
      dashboardCache.getStats(),
      redisCache.getStats()
    ]);
    
    res.json({
      success: true,
      data: {
        health,
        dashboard: stats,
        redis: redisStats,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get cache information',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
