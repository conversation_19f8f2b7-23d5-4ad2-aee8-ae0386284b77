import { Router } from 'express';
import { transferController } from '../controllers/transfer.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';
import {
  validateTransferRules,
  validateCurrencyConversion,
  auditTransferOperation
} from '../middleware/transfer.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireActiveUser);

// Apply audit middleware to all routes
router.use(auditTransferOperation);

/**
 * @route   POST /api/v1/transfers
 * @desc    Create a new transfer between accounts
 * @access  Private
 * @body    {
 *   description: string,
 *   amount: number,
 *   transactionDate: string | Date,
 *   accountId: string,
 *   destinationAccountId: string,
 *   exchangeRate?: number,
 *   sourceCurrency?: string,
 *   destinationCurrency?: string,
 *   sourceAmount?: number,
 *   destinationAmount?: number,
 *   transferReference?: string,
 *   isFuture?: boolean,
 *   tagIds?: string[],
 *   familyMemberIds: string[]
 * }
 */
router.post('/',
  validateTransferRules,
  validateCurrencyConversion,
  transferController.create.bind(transferController)
);

/**
 * @route   GET /api/v1/transfers
 * @desc    Get all transfers with optional filters
 * @access  Private
 * @query   description - Filter by description (partial match)
 * @query   accountId - Filter by source account ID
 * @query   destinationAccountId - Filter by destination account ID
 * @query   familyMemberId - Filter by family member ID
 * @query   tagId - Filter by tag ID
 * @query   startDate - Filter by start date (ISO string)
 * @query   endDate - Filter by end date (ISO string)
 * @query   minAmount - Filter by minimum amount
 * @query   maxAmount - Filter by maximum amount
 * @query   isFuture - Filter by future transfers (true/false)
 * @query   sourceCurrency - Filter by source currency
 * @query   destinationCurrency - Filter by destination currency
 * @query   transferReference - Filter by transfer reference
 * @query   includeDeleted - Include deleted transfers (default: false)
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20, max: 100)
 * @query   sortBy - Sort field (transactionDate, amount, description, createdAt)
 * @query   sortOrder - Sort order (asc, desc)
 */
router.get('/', transferController.findAll.bind(transferController));

/**
 * @route   GET /api/v1/transfers/:id
 * @desc    Get transfer by ID
 * @access  Private
 * @query   includeDeleted - Include deleted transfer (default: false)
 */
router.get('/:id', transferController.findById.bind(transferController));

/**
 * @route   PUT /api/v1/transfers/:id
 * @desc    Update transfer
 * @access  Private
 * @body    {
 *   description?: string,
 *   amount?: number,
 *   transactionDate?: string | Date,
 *   destinationAccountId?: string,
 *   exchangeRate?: number,
 *   sourceCurrency?: string,
 *   destinationCurrency?: string,
 *   sourceAmount?: number,
 *   destinationAmount?: number,
 *   transferReference?: string,
 *   isFuture?: boolean,
 *   tagIds?: string[],
 *   familyMemberIds?: string[]
 * }
 */
router.put('/:id',
  validateCurrencyConversion,
  transferController.update.bind(transferController)
);

/**
 * @route   DELETE /api/v1/transfers/:id
 * @desc    Delete transfer (soft delete)
 * @access  Private
 * @note    This will reverse any balance changes and soft delete the transfer
 */
router.delete('/:id', transferController.delete.bind(transferController));

/**
 * @route   POST /api/v1/transfers/calculate-conversion
 * @desc    Calculate currency conversion for transfer preview
 * @access  Private
 * @body    {
 *   sourceAmount: number,
 *   sourceCurrency: string,
 *   destinationCurrency: string,
 *   exchangeRate: number
 * }
 */
router.post('/calculate-conversion',
  validateCurrencyConversion,
  transferController.calculateConversion.bind(transferController)
);

/**
 * @route   POST /api/v1/transfers/validate
 * @desc    Validate transfer data before creation
 * @access  Private
 * @body    {
 *   accountId: string,
 *   destinationAccountId: string,
 *   amount: number,
 *   exchangeRate?: number,
 *   sourceCurrency?: string,
 *   destinationCurrency?: string
 * }
 */
router.post('/validate', transferController.validateTransfer.bind(transferController));

export default router;
