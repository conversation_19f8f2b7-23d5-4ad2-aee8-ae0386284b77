import { Router } from 'express';
import { financialGoalController } from '../controllers/financial-goal.controller';
import { milestoneController } from '../controllers/milestone.controller';
import { goalProgressHistoryController } from '../controllers/goal-progress-history.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireActiveUser);

/**
 * @route   POST /api/v1/goals
 * @desc    Create a new financial goal
 * @access  Private
 * @body    {
 *   name: string,
 *   targetAmount: number,
 *   currentAmount?: number,
 *   targetDate?: string,
 *   familyMemberIds: string[]
 * }
 * @returns {
 *   success: boolean,
 *   data: FinancialGoalResponse,
 *   message: string
 * }
 */
router.post('/', financialGoalController.create.bind(financialGoalController));

/**
 * @route   GET /api/v1/goals
 * @desc    Get all financial goals with filters and pagination
 * @access  Private
 * @query   {
 *   familyMemberId?: string,
 *   status?: 'active' | 'completed' | 'overdue' | 'all',
 *   minTargetAmount?: number,
 *   maxTargetAmount?: number,
 *   targetDateFrom?: string,
 *   targetDateTo?: string,
 *   page?: number,
 *   limit?: number,
 *   sortBy?: 'name' | 'targetAmount' | 'currentAmount' | 'targetDate' | 'createdAt' | 'progress',
 *   sortOrder?: 'asc' | 'desc',
 *   includeCompleted?: boolean,
 *   includeMilestones?: boolean
 * }
 * @returns {
 *   success: boolean,
 *   data: FinancialGoalResponse[],
 *   pagination: PaginationInfo,
 *   message: string
 * }
 */
router.get('/', financialGoalController.findAll.bind(financialGoalController));

/**
 * @route   GET /api/v1/goals/summary
 * @desc    Get goals summary statistics
 * @access  Private
 * @query   {
 *   familyMemberId?: string
 * }
 * @returns {
 *   success: boolean,
 *   data: GoalSummary,
 *   message: string
 * }
 */
router.get('/summary', financialGoalController.getSummary.bind(financialGoalController));

/**
 * @route   GET /api/v1/goals/:id
 * @desc    Get financial goal by ID
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @query   {
 *   includeMilestones?: boolean
 * }
 * @returns {
 *   success: boolean,
 *   data: FinancialGoalResponse,
 *   message: string
 * }
 */
router.get('/:id', financialGoalController.findById.bind(financialGoalController));

/**
 * @route   POST /api/v1/goals/:id/progress
 * @desc    Update goal progress
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @body    {
 *   amount: number,
 *   operation?: 'add' | 'subtract' | 'set',
 *   description?: string
 * }
 * @returns {
 *   success: boolean,
 *   data: GoalProgressUpdateResponse,
 *   message: string
 * }
 */
router.post('/:id/progress', financialGoalController.updateProgress.bind(financialGoalController));

/**
 * @route   GET /api/v1/goals/:goalId/progress-history
 * @desc    Get progress history for a specific goal
 * @access  Private
 * @params  {
 *   goalId: string
 * }
 * @query   {
 *   operation?: string,
 *   fromDate?: string,
 *   toDate?: string,
 *   page?: number,
 *   limit?: number,
 *   sortBy?: 'createdAt' | 'amountChanged',
 *   sortOrder?: 'asc' | 'desc'
 * }
 * @returns {
 *   success: boolean,
 *   data: GoalProgressHistoryResponse[],
 *   pagination: PaginationInfo,
 *   message: string
 * }
 */
router.get('/:goalId/progress-history', goalProgressHistoryController.findByGoal.bind(goalProgressHistoryController));

/**
 * @route   GET /api/v1/goals/:goalId/milestones
 * @desc    Get all milestones for a goal with filters
 * @access  Private
 * @params  {
 *   goalId: string
 * }
 * @query   {
 *   status?: 'pending' | 'completed' | 'overdue' | 'all',
 *   targetDateFrom?: string,
 *   targetDateTo?: string,
 *   sortBy?: 'name' | 'targetAmount' | 'targetDate' | 'createdAt',
 *   sortOrder?: 'asc' | 'desc'
 * }
 * @returns {
 *   success: boolean,
 *   data: GoalMilestoneResponse[],
 *   message: string
 * }
 */
router.get('/:goalId/milestones', milestoneController.findByGoal.bind(milestoneController));

/**
 * @route   POST /api/v1/goals/:goalId/milestones
 * @desc    Create a new milestone for a goal
 * @access  Private
 * @params  {
 *   goalId: string
 * }
 * @body    {
 *   name: string,
 *   targetAmount: number,
 *   targetDate: string
 * }
 * @returns {
 *   success: boolean,
 *   data: GoalMilestoneResponse,
 *   message: string
 * }
 */
router.post('/:goalId/milestones', milestoneController.create.bind(milestoneController));

/**
 * @route   PUT /api/v1/goals/:id
 * @desc    Update financial goal
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @body    {
 *   name?: string,
 *   targetAmount?: number,
 *   targetDate?: string,
 *   familyMemberIds?: string[]
 * }
 * @returns {
 *   success: boolean,
 *   data: FinancialGoalResponse,
 *   message: string
 * }
 */
router.put('/:id', financialGoalController.update.bind(financialGoalController));

/**
 * @route   DELETE /api/v1/goals/:id
 * @desc    Delete financial goal (soft delete)
 * @access  Private
 * @params  {
 *   id: string
 * }
 * @returns {
 *   success: boolean,
 *   message: string
 * }
 */
router.delete('/:id', financialGoalController.delete.bind(financialGoalController));

export default router;
