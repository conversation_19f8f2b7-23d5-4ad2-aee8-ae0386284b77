import { Router } from 'express';
import { authController } from '../controllers/auth.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';

const router = Router();

/**
 * @route   POST /api/v1/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', authController.register.bind(authController));

/**
 * @route   POST /api/v1/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login', authController.login.bind(authController));

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user (client-side token removal)
 * @access  Public
 */
router.post('/logout', authController.logout.bind(authController));

/**
 * @route   POST /api/v1/auth/verify
 * @desc    Verify token validity
 * @access  Public
 */
router.post('/verify', authController.verifyToken.bind(authController));

/**
 * @route   GET /api/v1/auth/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile', 
  authenticateToken, 
  requireActiveUser, 
  authController.getProfile.bind(authController)
);

/**
 * @route   PUT /api/v1/auth/password
 * @desc    Update user password
 * @access  Private
 */
router.put('/password', 
  authenticateToken, 
  requireActiveUser, 
  authController.updatePassword.bind(authController)
);

export default router;
