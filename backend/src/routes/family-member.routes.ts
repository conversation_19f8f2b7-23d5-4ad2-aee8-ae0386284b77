import { Router } from 'express';
import { familyMemberController } from '../controllers/family-member.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireActiveUser);

/**
 * @route   POST /api/v1/family-members
 * @desc    Create a new family member
 * @access  Private
 */
router.post('/', familyMemberController.create.bind(familyMemberController));

/**
 * @route   GET /api/v1/family-members
 * @desc    Get all family members with filters and pagination
 * @access  Private
 * @query   name - Filter by name (partial match, case-insensitive)
 * @query   includeArchived - Include archived members (default: false)
 * @query   includeDeleted - Include deleted members (default: false)
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20, max: 100)
 */
router.get('/', familyMemberController.findAll.bind(familyMemberController));

/**
 * @route   GET /api/v1/family-members/stats
 * @desc    Get family members statistics
 * @access  Private
 */
router.get('/stats', familyMemberController.getStats.bind(familyMemberController));

/**
 * @route   GET /api/v1/family-members/:id
 * @desc    Get family member by ID
 * @access  Private
 * @query   includeArchived - Include archived member (default: false)
 */
router.get('/:id', familyMemberController.findById.bind(familyMemberController));

/**
 * @route   PUT /api/v1/family-members/:id
 * @desc    Update family member
 * @access  Private
 */
router.put('/:id', familyMemberController.update.bind(familyMemberController));

/**
 * @route   PATCH /api/v1/family-members/:id/archive
 * @desc    Archive or unarchive family member
 * @access  Private
 * @body    { "archived": boolean }
 */
router.patch('/:id/archive', familyMemberController.archive.bind(familyMemberController));

/**
 * @route   DELETE /api/v1/family-members/:id
 * @desc    Permanently delete family member
 * @access  Private
 * @note    This will fail if the member has associated accounts, transactions, budgets, or goals
 */
router.delete('/:id', familyMemberController.delete.bind(familyMemberController));

export default router;
