import { Router } from 'express';
import { categoryController } from '../controllers/category.controller';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);
router.use(requireActiveUser);

/**
 * @route   POST /api/v1/categories
 * @desc    Create a new category
 * @access  Private
 * @body    {
 *   name: string,
 *   color?: string,
 *   parentId?: string
 * }
 */
router.post('/', categoryController.create.bind(categoryController));

/**
 * @route   GET /api/v1/categories
 * @desc    Get all categories with filters and pagination
 * @access  Private
 * @query   name - Filter by name (partial match, case-insensitive)
 * @query   parentId - Filter by parent category ID
 * @query   includeChildren - Include children in response (default: false)
 * @query   onlyParents - Show only parent categories (default: false)
 * @query   onlyChildren - Show only subcategories (default: false)
 * @query   includeArchived - Include archived categories (default: false)
 * @query   includeDeleted - Include deleted categories (default: false)
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20, max: 100)
 */
router.get('/', categoryController.findAll.bind(categoryController));

/**
 * @route   GET /api/v1/categories/stats
 * @desc    Get categories statistics
 * @access  Private
 */
router.get('/stats', categoryController.getStats.bind(categoryController));

/**
 * @route   GET /api/v1/categories/selection
 * @desc    Get categories for selection dropdown
 * @access  Private
 * @query   onlyActive - Only active categories (default: false)
 * @query   excludeId - Exclude specific category ID
 */
router.get('/selection', categoryController.getForSelection.bind(categoryController));

/**
 * @route   GET /api/v1/categories/tree
 * @desc    Get category tree for hierarchical display
 * @access  Private
 * @query   includeArchived - Include archived categories (default: false)
 */
router.get('/tree', categoryController.getCategoryTree.bind(categoryController));

/**
 * @route   GET /api/v1/categories/:id
 * @desc    Get category by ID
 * @access  Private
 * @query   includeArchived - Include archived category (default: false)
 */
router.get('/:id', categoryController.findById.bind(categoryController));

/**
 * @route   PUT /api/v1/categories/:id
 * @desc    Update category
 * @access  Private
 * @body    {
 *   name?: string,
 *   color?: string,
 *   parentId?: string | null
 * }
 */
router.put('/:id', categoryController.update.bind(categoryController));

/**
 * @route   PATCH /api/v1/categories/:id/archive
 * @desc    Archive or unarchive category
 * @access  Private
 * @body    { "archived": boolean }
 */
router.patch('/:id/archive', categoryController.archive.bind(categoryController));

/**
 * @route   DELETE /api/v1/categories/:id
 * @desc    Permanently delete category
 * @access  Private
 * @note    This will also delete all subcategories if it's a parent category
 * @note    Category must not have any associated transactions, budgets, or recurring transactions
 */
router.delete('/:id', categoryController.delete.bind(categoryController));

export default router;
