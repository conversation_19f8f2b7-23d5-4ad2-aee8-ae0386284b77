import { Router } from 'express';
import { categorySuggestionController } from '../controllers/category-suggestion.controller';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

/**
 * Category Suggestion Routes
 * All routes require authentication
 */

/**
 * @route POST /api/v1/category-suggestions
 * @desc Get category suggestions for a single transaction
 * @access Private
 * @body {
 *   description: string,
 *   amount?: number,
 *   type?: TransactionType,
 *   userId?: string,
 *   maxSuggestions?: number,
 *   minConfidence?: number
 * }
 */
router.post(
  '/',
  authenticateToken,
  categorySuggestionController.getSuggestions.bind(categorySuggestionController)
);

/**
 * @route POST /api/v1/category-suggestions/expense
 * @desc Get category suggestions optimized for expense transactions
 * @access Private
 * @body {
 *   description: string,
 *   amount?: number,
 *   type?: TransactionType,
 *   userId?: string,
 *   maxSuggestions?: number,
 *   minConfidence?: number
 * }
 */
router.post(
  '/expense',
  authenticateToken,
  categorySuggestionController.getSuggestionsForExpense.bind(categorySuggestionController)
);

/**
 * @route POST /api/v1/category-suggestions/income
 * @desc Get category suggestions optimized for income transactions
 * @access Private
 * @body {
 *   description: string,
 *   amount?: number,
 *   type?: TransactionType,
 *   userId?: string,
 *   maxSuggestions?: number,
 *   minConfidence?: number
 * }
 */
router.post(
  '/income',
  authenticateToken,
  categorySuggestionController.getSuggestionsForIncome.bind(categorySuggestionController)
);

/**
 * @route POST /api/v1/category-suggestions/bulk
 * @desc Get category suggestions for multiple transactions in bulk
 * @access Private
 * @body {
 *   transactions: Array<{
 *     id?: string,
 *     description: string,
 *     amount?: number,
 *     type?: TransactionType
 *   }>,
 *   userId?: string,
 *   maxSuggestionsPerTransaction?: number,
 *   minConfidence?: number
 * }
 */
router.post(
  '/bulk',
  authenticateToken,
  categorySuggestionController.getBulkSuggestions.bind(categorySuggestionController)
);

/**
 * @route POST /api/v1/category-suggestions/custom
 * @desc Get category suggestions with custom algorithm configuration
 * @access Private
 * @body {
 *   transaction: {
 *     description: string,
 *     amount?: number,
 *     type?: TransactionType,
 *     userId?: string,
 *     maxSuggestions?: number,
 *     minConfidence?: number
 *   },
 *   config: {
 *     descriptionWeight?: number,
 *     amountWeight?: number,
 *     frequencyWeight?: number,
 *     keywordWeight?: number,
 *     minConfidence?: number,
 *     maxSuggestions?: number
 *   }
 * }
 */
router.post(
  '/custom',
  authenticateToken,
  categorySuggestionController.getSuggestionsWithConfig.bind(categorySuggestionController)
);

/**
 * @route POST /api/v1/category-suggestions/feedback
 * @desc Submit feedback about suggestion quality for future improvements
 * @access Private
 * @body {
 *   transactionId: string,
 *   suggestedCategoryId: string,
 *   actualCategoryId: string,
 *   wasAccepted: boolean,
 *   confidence: number,
 *   matchType: 'description' | 'amount' | 'frequency' | 'keyword' | 'combined',
 *   userId?: string,
 *   feedback?: string,
 *   timestamp?: Date
 * }
 */
router.post(
  '/feedback',
  authenticateToken,
  categorySuggestionController.submitFeedback.bind(categorySuggestionController)
);

export default router;
