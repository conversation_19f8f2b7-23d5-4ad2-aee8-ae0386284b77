/**
 * Statistical analysis utilities for financial insights
 * Provides advanced statistical functions for pattern detection and forecasting
 */

export interface StatisticalSummary {
  mean: number;
  median: number;
  mode: number[];
  standardDeviation: number;
  variance: number;
  min: number;
  max: number;
  range: number;
  quartiles: {
    q1: number;
    q2: number;
    q3: number;
  };
  outliers: number[];
}

export interface SeasonalPattern {
  period: 'WEEKLY' | 'MONTHLY' | 'QUARTERLY';
  strength: number; // 0-1, where 1 is strongest seasonality
  peaks: number[]; // Indices of peak periods
  troughs: number[]; // Indices of trough periods
  averageByPeriod: number[];
}

export interface ForecastResult {
  predictions: number[];
  confidence: number; // 0-1
  trend: 'INCREASING' | 'DECREASING' | 'STABLE';
  seasonality: SeasonalPattern | null;
}

/**
 * Calculate comprehensive statistical summary for a dataset
 */
export function calculateStatisticalSummary(data: number[]): StatisticalSummary {
  if (data.length === 0) {
    throw new Error('Dataset cannot be empty');
  }

  const sortedData = [...data].sort((a, b) => a - b);
  const n = data.length;

  // Basic statistics
  const mean = data.reduce((sum, val) => sum + val, 0) / n;
  const median = n % 2 === 0 
    ? (sortedData[n / 2 - 1] + sortedData[n / 2]) / 2
    : sortedData[Math.floor(n / 2)];

  // Mode calculation
  const frequency = new Map<number, number>();
  data.forEach(val => {
    frequency.set(val, (frequency.get(val) || 0) + 1);
  });
  const maxFreq = Math.max(...Array.from(frequency.values()));
  const mode = Array.from(frequency.entries())
    .filter(([_, freq]) => freq === maxFreq)
    .map(([val, _]) => val);

  // Variance and standard deviation
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / n;
  const standardDeviation = Math.sqrt(variance);

  // Range
  const min = sortedData[0];
  const max = sortedData[n - 1];
  const range = max - min;

  // Quartiles
  const q1Index = Math.floor(n * 0.25);
  const q2Index = Math.floor(n * 0.5);
  const q3Index = Math.floor(n * 0.75);
  
  const quartiles = {
    q1: sortedData[q1Index],
    q2: sortedData[q2Index],
    q3: sortedData[q3Index]
  };

  // Outliers using IQR method
  const iqr = quartiles.q3 - quartiles.q1;
  const lowerBound = quartiles.q1 - 1.5 * iqr;
  const upperBound = quartiles.q3 + 1.5 * iqr;
  const outliers = data.filter(val => val < lowerBound || val > upperBound);

  return {
    mean,
    median,
    mode,
    standardDeviation,
    variance,
    min,
    max,
    range,
    quartiles,
    outliers
  };
}

/**
 * Detect seasonal patterns in time series data
 */
export function detectSeasonalPatterns(
  data: number[],
  period: 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
): SeasonalPattern | null {
  if (data.length < 12) {
    return null; // Not enough data for seasonal analysis
  }

  let periodLength: number;
  switch (period) {
    case 'WEEKLY':
      periodLength = 7;
      break;
    case 'MONTHLY':
      periodLength = 30;
      break;
    case 'QUARTERLY':
      periodLength = 90;
      break;
  }

  if (data.length < periodLength * 2) {
    return null; // Need at least 2 full periods
  }

  // Calculate average for each period position
  const periodAverages: number[] = new Array(periodLength).fill(0);
  const periodCounts: number[] = new Array(periodLength).fill(0);

  data.forEach((value, index) => {
    const periodIndex = index % periodLength;
    periodAverages[periodIndex] += value;
    periodCounts[periodIndex]++;
  });

  // Normalize averages
  for (let i = 0; i < periodLength; i++) {
    if (periodCounts[i] > 0) {
      periodAverages[i] /= periodCounts[i];
    }
  }

  // Calculate seasonal strength using coefficient of variation
  const overallMean = periodAverages.reduce((sum, val) => sum + val, 0) / periodLength;
  const seasonalVariance = periodAverages.reduce((sum, val) => 
    sum + Math.pow(val - overallMean, 2), 0
  ) / periodLength;
  const seasonalStdDev = Math.sqrt(seasonalVariance);
  const coefficientOfVariation = overallMean > 0 ? seasonalStdDev / overallMean : 0;

  // Determine strength (0-1 scale)
  const strength = Math.min(1, coefficientOfVariation);

  // Find peaks and troughs
  const peaks: number[] = [];
  const troughs: number[] = [];

  for (let i = 0; i < periodLength; i++) {
    const prev = periodAverages[(i - 1 + periodLength) % periodLength];
    const curr = periodAverages[i];
    const next = periodAverages[(i + 1) % periodLength];

    if (curr > prev && curr > next) {
      peaks.push(i);
    } else if (curr < prev && curr < next) {
      troughs.push(i);
    }
  }

  return {
    period,
    strength,
    peaks,
    troughs,
    averageByPeriod: periodAverages
  };
}

/**
 * Simple moving average calculation
 */
export function calculateMovingAverage(data: number[], windowSize: number): number[] {
  if (windowSize <= 0 || windowSize > data.length) {
    throw new Error('Invalid window size');
  }

  const result: number[] = [];
  
  for (let i = windowSize - 1; i < data.length; i++) {
    const window = data.slice(i - windowSize + 1, i + 1);
    const average = window.reduce((sum, val) => sum + val, 0) / windowSize;
    result.push(average);
  }

  return result;
}

/**
 * Exponential moving average calculation
 */
export function calculateExponentialMovingAverage(
  data: number[], 
  alpha: number = 0.3
): number[] {
  if (alpha <= 0 || alpha > 1) {
    throw new Error('Alpha must be between 0 and 1');
  }

  const result: number[] = [];
  let ema = data[0]; // Initialize with first value

  result.push(ema);

  for (let i = 1; i < data.length; i++) {
    ema = alpha * data[i] + (1 - alpha) * ema;
    result.push(ema);
  }

  return result;
}

/**
 * Calculate correlation coefficient between two datasets
 */
export function calculateCorrelation(x: number[], y: number[]): number {
  if (x.length !== y.length || x.length === 0) {
    throw new Error('Datasets must have the same non-zero length');
  }

  const n = x.length;
  const meanX = x.reduce((sum, val) => sum + val, 0) / n;
  const meanY = y.reduce((sum, val) => sum + val, 0) / n;

  let numerator = 0;
  let sumXSquared = 0;
  let sumYSquared = 0;

  for (let i = 0; i < n; i++) {
    const deltaX = x[i] - meanX;
    const deltaY = y[i] - meanY;
    
    numerator += deltaX * deltaY;
    sumXSquared += deltaX * deltaX;
    sumYSquared += deltaY * deltaY;
  }

  const denominator = Math.sqrt(sumXSquared * sumYSquared);
  
  return denominator === 0 ? 0 : numerator / denominator;
}

/**
 * Simple linear regression for trend analysis
 */
export function calculateLinearRegression(x: number[], y: number[]): {
  slope: number;
  intercept: number;
  rSquared: number;
  predictions: number[];
} {
  if (x.length !== y.length || x.length < 2) {
    throw new Error('Datasets must have the same length and at least 2 points');
  }

  const n = x.length;
  const sumX = x.reduce((sum, val) => sum + val, 0);
  const sumY = y.reduce((sum, val) => sum + val, 0);
  const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
  const sumXSquared = x.reduce((sum, val) => sum + val * val, 0);

  const slope = (n * sumXY - sumX * sumY) / (n * sumXSquared - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;

  // Calculate R-squared
  const meanY = sumY / n;
  const predictions = x.map(val => slope * val + intercept);
  
  const ssTotal = y.reduce((sum, val) => sum + Math.pow(val - meanY, 2), 0);
  const ssResidual = y.reduce((sum, val, i) => 
    sum + Math.pow(val - predictions[i], 2), 0
  );
  
  const rSquared = ssTotal > 0 ? 1 - (ssResidual / ssTotal) : 0;

  return {
    slope,
    intercept,
    rSquared,
    predictions
  };
}

/**
 * Simple forecasting using trend and seasonality
 */
export function generateForecast(
  data: number[],
  periodsAhead: number,
  includeSeasonality: boolean = true
): ForecastResult {
  if (data.length < 3) {
    throw new Error('Need at least 3 data points for forecasting');
  }

  // Create time index
  const timeIndex = data.map((_, i) => i);
  
  // Calculate trend using linear regression
  const regression = calculateLinearRegression(timeIndex, data);
  
  // Determine trend direction
  let trend: 'INCREASING' | 'DECREASING' | 'STABLE';
  if (Math.abs(regression.slope) < 0.01) {
    trend = 'STABLE';
  } else if (regression.slope > 0) {
    trend = 'INCREASING';
  } else {
    trend = 'DECREASING';
  }

  // Detect seasonality
  let seasonality: SeasonalPattern | null = null;
  if (includeSeasonality && data.length >= 12) {
    seasonality = detectSeasonalPatterns(data, 'MONTHLY');
  }

  // Generate predictions
  const predictions: number[] = [];
  const lastIndex = data.length - 1;

  for (let i = 1; i <= periodsAhead; i++) {
    const futureIndex = lastIndex + i;
    let prediction = regression.slope * futureIndex + regression.intercept;

    // Apply seasonal adjustment if available
    if (seasonality && seasonality.strength > 0.1) {
      const seasonalIndex = futureIndex % seasonality.averageByPeriod.length;
      const seasonalFactor = seasonality.averageByPeriod[seasonalIndex];
      const overallMean = seasonality.averageByPeriod.reduce((sum, val) => sum + val, 0) / 
                         seasonality.averageByPeriod.length;
      
      if (overallMean > 0) {
        prediction *= (seasonalFactor / overallMean);
      }
    }

    predictions.push(Math.max(0, prediction)); // Ensure non-negative predictions
  }

  // Calculate confidence based on R-squared and data consistency
  const confidence = Math.max(0.1, Math.min(0.9, regression.rSquared));

  return {
    predictions,
    confidence,
    trend,
    seasonality
  };
}
