/**
 * Financial analysis utilities for insight generation
 * Provides algorithms for analyzing spending patterns, trends, and anomalies
 */

export interface TransactionData {
  id: string;
  amount: number;
  date: Date;
  categoryId?: string;
  categoryName?: string;
  accountId: string;
  accountName: string;
  type: 'EXPENSE' | 'INCOME' | 'TRANSFER';
  description: string;
}

export interface CategorySpending {
  categoryId: string;
  categoryName: string;
  totalAmount: number;
  transactionCount: number;
  averageAmount: number;
  percentage: number;
}

export interface TrendData {
  period: string;
  amount: number;
  transactionCount: number;
  date: Date;
}

export interface AnomalyResult {
  transaction: TransactionData;
  anomalyScore: number;
  reason: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH';
}

/**
 * Calculate spending by category for a given period
 */
export function calculateCategorySpending(
  transactions: TransactionData[],
  includeIncome: boolean = false
): CategorySpending[] {
  const categoryMap = new Map<string, {
    totalAmount: number;
    transactionCount: number;
    categoryName: string;
  }>();

  // Filter transactions by type
  const filteredTransactions = transactions.filter(t => 
    includeIncome ? true : t.type !== 'INCOME'
  );

  // Calculate totals by category
  filteredTransactions.forEach(transaction => {
    const categoryId = transaction.categoryId || 'uncategorized';
    const categoryName = transaction.categoryName || 'Sem Categoria';
    
    if (!categoryMap.has(categoryId)) {
      categoryMap.set(categoryId, {
        totalAmount: 0,
        transactionCount: 0,
        categoryName
      });
    }

    const category = categoryMap.get(categoryId)!;
    category.totalAmount += Math.abs(transaction.amount);
    category.transactionCount += 1;
  });

  // Calculate total for percentage calculation
  const totalAmount = Array.from(categoryMap.values())
    .reduce((sum, cat) => sum + cat.totalAmount, 0);

  // Convert to CategorySpending array
  return Array.from(categoryMap.entries()).map(([categoryId, data]) => ({
    categoryId,
    categoryName: data.categoryName,
    totalAmount: data.totalAmount,
    transactionCount: data.transactionCount,
    averageAmount: data.totalAmount / data.transactionCount,
    percentage: totalAmount > 0 ? (data.totalAmount / totalAmount) * 100 : 0
  })).sort((a, b) => b.totalAmount - a.totalAmount);
}

/**
 * Calculate monthly spending trends
 */
export function calculateMonthlyTrends(
  transactions: TransactionData[],
  months: number = 12
): TrendData[] {
  const monthMap = new Map<string, { amount: number; count: number; date: Date }>();
  
  // Get date range
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(endDate.getMonth() - months);

  // Filter transactions within date range
  const filteredTransactions = transactions.filter(t => 
    t.date >= startDate && t.date <= endDate && t.type !== 'INCOME'
  );

  // Group by month
  filteredTransactions.forEach(transaction => {
    const monthKey = `${transaction.date.getFullYear()}-${String(transaction.date.getMonth() + 1).padStart(2, '0')}`;
    
    if (!monthMap.has(monthKey)) {
      monthMap.set(monthKey, {
        amount: 0,
        count: 0,
        date: new Date(transaction.date.getFullYear(), transaction.date.getMonth(), 1)
      });
    }

    const month = monthMap.get(monthKey)!;
    month.amount += Math.abs(transaction.amount);
    month.count += 1;
  });

  // Convert to TrendData array and sort by date
  return Array.from(monthMap.entries()).map(([period, data]) => ({
    period,
    amount: data.amount,
    transactionCount: data.count,
    date: data.date
  })).sort((a, b) => a.date.getTime() - b.date.getTime());
}

/**
 * Calculate trend direction and strength
 */
export function calculateTrendDirection(trendData: TrendData[]): {
  direction: 'INCREASING' | 'DECREASING' | 'STABLE';
  strength: number; // 0-1, where 1 is strongest trend
  changePercentage: number;
} {
  if (trendData.length < 2) {
    return { direction: 'STABLE', strength: 0, changePercentage: 0 };
  }

  // Simple linear regression to determine trend
  const n = trendData.length;
  const sumX = trendData.reduce((sum, _, index) => sum + index, 0);
  const sumY = trendData.reduce((sum, data) => sum + data.amount, 0);
  const sumXY = trendData.reduce((sum, data, index) => sum + (index * data.amount), 0);
  const sumXX = trendData.reduce((sum, _, index) => sum + (index * index), 0);

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  
  // Calculate R-squared for trend strength
  const meanY = sumY / n;
  const ssTotal = trendData.reduce((sum, data) => sum + Math.pow(data.amount - meanY, 2), 0);
  const ssResidual = trendData.reduce((sum, data, index) => {
    const predicted = meanY + slope * (index - (sumX / n));
    return sum + Math.pow(data.amount - predicted, 2);
  }, 0);
  
  const rSquared = ssTotal > 0 ? 1 - (ssResidual / ssTotal) : 0;
  
  // Calculate percentage change
  const firstAmount = trendData[0].amount;
  const lastAmount = trendData[trendData.length - 1].amount;
  const changePercentage = firstAmount > 0 ? ((lastAmount - firstAmount) / firstAmount) * 100 : 0;

  // Determine direction
  let direction: 'INCREASING' | 'DECREASING' | 'STABLE';
  if (Math.abs(slope) < 0.1) {
    direction = 'STABLE';
  } else if (slope > 0) {
    direction = 'INCREASING';
  } else {
    direction = 'DECREASING';
  }

  return {
    direction,
    strength: Math.max(0, Math.min(1, rSquared)),
    changePercentage
  };
}

/**
 * Detect spending anomalies using statistical analysis
 */
export function detectSpendingAnomalies(
  transactions: TransactionData[],
  lookbackDays: number = 90,
  sensitivityThreshold: number = 2.0
): AnomalyResult[] {
  const anomalies: AnomalyResult[] = [];
  
  // Filter recent transactions (expenses only)
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - lookbackDays);
  
  const recentTransactions = transactions.filter(t => 
    t.date >= cutoffDate && t.type === 'EXPENSE'
  );

  if (recentTransactions.length < 10) {
    return anomalies; // Not enough data for analysis
  }

  // Calculate statistics by category
  const categoryStats = new Map<string, {
    amounts: number[];
    mean: number;
    stdDev: number;
  }>();

  // Group transactions by category
  recentTransactions.forEach(transaction => {
    const categoryId = transaction.categoryId || 'uncategorized';
    
    if (!categoryStats.has(categoryId)) {
      categoryStats.set(categoryId, { amounts: [], mean: 0, stdDev: 0 });
    }
    
    categoryStats.get(categoryId)!.amounts.push(Math.abs(transaction.amount));
  });

  // Calculate mean and standard deviation for each category
  categoryStats.forEach((stats, categoryId) => {
    if (stats.amounts.length < 3) return; // Skip categories with too few transactions
    
    stats.mean = stats.amounts.reduce((sum, amount) => sum + amount, 0) / stats.amounts.length;
    
    const variance = stats.amounts.reduce((sum, amount) => 
      sum + Math.pow(amount - stats.mean, 2), 0
    ) / stats.amounts.length;
    
    stats.stdDev = Math.sqrt(variance);
  });

  // Check each transaction for anomalies
  recentTransactions.forEach(transaction => {
    const categoryId = transaction.categoryId || 'uncategorized';
    const stats = categoryStats.get(categoryId);
    
    if (!stats || stats.stdDev === 0) return;
    
    const amount = Math.abs(transaction.amount);
    const zScore = (amount - stats.mean) / stats.stdDev;
    
    if (Math.abs(zScore) >= sensitivityThreshold) {
      let severity: 'LOW' | 'MEDIUM' | 'HIGH';
      let reason: string;
      
      if (Math.abs(zScore) >= 3.0) {
        severity = 'HIGH';
        reason = `Gasto extremamente incomum (${Math.abs(zScore).toFixed(1)}x acima do normal)`;
      } else if (Math.abs(zScore) >= 2.5) {
        severity = 'MEDIUM';
        reason = `Gasto significativamente acima do padrão (${Math.abs(zScore).toFixed(1)}x acima do normal)`;
      } else {
        severity = 'LOW';
        reason = `Gasto acima do padrão usual (${Math.abs(zScore).toFixed(1)}x acima do normal)`;
      }
      
      anomalies.push({
        transaction,
        anomalyScore: Math.abs(zScore),
        reason,
        severity
      });
    }
  });

  return anomalies.sort((a, b) => b.anomalyScore - a.anomalyScore);
}

/**
 * Compare current period with previous period
 */
export function comparePeriods(
  currentTransactions: TransactionData[],
  previousTransactions: TransactionData[]
): {
  totalChange: number;
  totalChangePercentage: number;
  categoryChanges: Array<{
    categoryId: string;
    categoryName: string;
    currentAmount: number;
    previousAmount: number;
    change: number;
    changePercentage: number;
  }>;
} {
  const currentSpending = calculateCategorySpending(currentTransactions);
  const previousSpending = calculateCategorySpending(previousTransactions);
  
  const currentTotal = currentSpending.reduce((sum, cat) => sum + cat.totalAmount, 0);
  const previousTotal = previousSpending.reduce((sum, cat) => sum + cat.totalAmount, 0);
  
  const totalChange = currentTotal - previousTotal;
  const totalChangePercentage = previousTotal > 0 ? (totalChange / previousTotal) * 100 : 0;
  
  // Create map for easy lookup
  const previousMap = new Map(previousSpending.map(cat => [cat.categoryId, cat]));
  const currentMap = new Map(currentSpending.map(cat => [cat.categoryId, cat]));
  
  // Get all unique categories
  const allCategories = new Set([
    ...currentSpending.map(cat => cat.categoryId),
    ...previousSpending.map(cat => cat.categoryId)
  ]);
  
  const categoryChanges = Array.from(allCategories).map(categoryId => {
    const current = currentMap.get(categoryId);
    const previous = previousMap.get(categoryId);
    
    const currentAmount = current?.totalAmount || 0;
    const previousAmount = previous?.totalAmount || 0;
    const change = currentAmount - previousAmount;
    const changePercentage = previousAmount > 0 ? (change / previousAmount) * 100 : 0;
    
    return {
      categoryId,
      categoryName: current?.categoryName || previous?.categoryName || 'Categoria Desconhecida',
      currentAmount,
      previousAmount,
      change,
      changePercentage
    };
  }).sort((a, b) => Math.abs(b.change) - Math.abs(a.change));
  
  return {
    totalChange,
    totalChangePercentage,
    categoryChanges
  };
}
