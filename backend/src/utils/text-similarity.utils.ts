/**
 * Text similarity utilities for category suggestion system
 * Provides various algorithms to calculate similarity between strings
 */

/**
 * Normalize text for comparison by removing accents, converting to lowercase,
 * and removing extra whitespace
 */
export function normalizeText(text: string): string {
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove accents
    .replace(/[^\w\s]/g, ' ') // Replace punctuation with spaces
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim();
}

/**
 * Tokenize text into words, filtering out common stop words
 */
export function tokenizeText(text: string): string[] {
  const stopWords = new Set([
    'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
    'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
    'to', 'was', 'will', 'with', 'o', 'a', 'os', 'as', 'um', 'uma',
    'de', 'do', 'da', 'dos', 'das', 'em', 'no', 'na', 'nos', 'nas',
    'para', 'por', 'com', 'sem', 'sobre', 'entre', 'até', 'desde'
  ]);

  const normalized = normalizeText(text);
  return normalized
    .split(' ')
    .filter(word => word.length > 2 && !stopWords.has(word));
}

/**
 * Calculate Levenshtein distance between two strings
 * Returns the minimum number of single-character edits required to transform one string into another
 */
export function levenshteinDistance(str1: string, str2: string): number {
  const matrix: number[][] = [];
  const len1 = str1.length;
  const len2 = str2.length;

  // Initialize matrix
  for (let i = 0; i <= len1; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= len2; j++) {
    matrix[0][j] = j;
  }

  // Fill matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,     // deletion
        matrix[i][j - 1] + 1,     // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }

  return matrix[len1][len2];
}

/**
 * Calculate normalized Levenshtein similarity (0-1 scale, where 1 is identical)
 */
export function levenshteinSimilarity(str1: string, str2: string): number {
  const normalized1 = normalizeText(str1);
  const normalized2 = normalizeText(str2);
  
  if (normalized1 === normalized2) return 1;
  if (normalized1.length === 0 && normalized2.length === 0) return 1;
  if (normalized1.length === 0 || normalized2.length === 0) return 0;

  const distance = levenshteinDistance(normalized1, normalized2);
  const maxLength = Math.max(normalized1.length, normalized2.length);
  
  return 1 - (distance / maxLength);
}

/**
 * Calculate Jaccard similarity coefficient for two sets of tokens
 * Returns the size of intersection divided by size of union
 */
export function jaccardSimilarity(tokens1: string[], tokens2: string[]): number {
  const set1 = new Set(tokens1);
  const set2 = new Set(tokens2);
  
  const intersection = new Set([...set1].filter(x => set2.has(x)));
  const union = new Set([...set1, ...set2]);
  
  if (union.size === 0) return 1; // Both empty sets are identical
  
  return intersection.size / union.size;
}

/**
 * Calculate cosine similarity between two text strings using TF-IDF vectors
 */
export function cosineSimilarity(text1: string, text2: string): number {
  const tokens1 = tokenizeText(text1);
  const tokens2 = tokenizeText(text2);
  
  if (tokens1.length === 0 && tokens2.length === 0) return 1;
  if (tokens1.length === 0 || tokens2.length === 0) return 0;
  
  // Create vocabulary
  const vocabulary = new Set([...tokens1, ...tokens2]);
  const vocabArray = Array.from(vocabulary);
  
  // Calculate term frequencies
  const tf1 = calculateTermFrequency(tokens1, vocabArray);
  const tf2 = calculateTermFrequency(tokens2, vocabArray);
  
  // Calculate cosine similarity
  let dotProduct = 0;
  let magnitude1 = 0;
  let magnitude2 = 0;
  
  for (let i = 0; i < vocabArray.length; i++) {
    dotProduct += tf1[i] * tf2[i];
    magnitude1 += tf1[i] * tf1[i];
    magnitude2 += tf2[i] * tf2[i];
  }
  
  const magnitude = Math.sqrt(magnitude1) * Math.sqrt(magnitude2);
  
  return magnitude === 0 ? 0 : dotProduct / magnitude;
}

/**
 * Calculate term frequency vector for given tokens and vocabulary
 */
function calculateTermFrequency(tokens: string[], vocabulary: string[]): number[] {
  const tf: number[] = new Array(vocabulary.length).fill(0);
  
  tokens.forEach(token => {
    const index = vocabulary.indexOf(token);
    if (index !== -1) {
      tf[index]++;
    }
  });
  
  // Normalize by document length
  const docLength = tokens.length;
  return tf.map(freq => docLength > 0 ? freq / docLength : 0);
}

/**
 * Calculate combined similarity score using multiple algorithms
 * Returns weighted average of different similarity measures
 */
export function combinedSimilarity(
  text1: string, 
  text2: string,
  weights: {
    levenshtein?: number;
    jaccard?: number;
    cosine?: number;
  } = {}
): number {
  const defaultWeights = {
    levenshtein: 0.4,
    jaccard: 0.3,
    cosine: 0.3
  };
  
  const finalWeights = { ...defaultWeights, ...weights };
  
  const tokens1 = tokenizeText(text1);
  const tokens2 = tokenizeText(text2);
  
  const levenshteinScore = levenshteinSimilarity(text1, text2);
  const jaccardScore = jaccardSimilarity(tokens1, tokens2);
  const cosineScore = cosineSimilarity(text1, text2);
  
  return (
    levenshteinScore * finalWeights.levenshtein +
    jaccardScore * finalWeights.jaccard +
    cosineScore * finalWeights.cosine
  );
}

/**
 * Find the most similar text from a list of candidates
 */
export function findMostSimilar(
  target: string,
  candidates: string[],
  algorithm: 'levenshtein' | 'jaccard' | 'cosine' | 'combined' = 'combined'
): { text: string; similarity: number; index: number } | null {
  if (candidates.length === 0) return null;
  
  let bestMatch = { text: candidates[0], similarity: 0, index: 0 };
  
  candidates.forEach((candidate, index) => {
    let similarity: number;
    
    switch (algorithm) {
      case 'levenshtein':
        similarity = levenshteinSimilarity(target, candidate);
        break;
      case 'jaccard':
        const tokens1 = tokenizeText(target);
        const tokens2 = tokenizeText(candidate);
        similarity = jaccardSimilarity(tokens1, tokens2);
        break;
      case 'cosine':
        similarity = cosineSimilarity(target, candidate);
        break;
      case 'combined':
      default:
        similarity = combinedSimilarity(target, candidate);
        break;
    }
    
    if (similarity > bestMatch.similarity) {
      bestMatch = { text: candidate, similarity, index };
    }
  });
  
  return bestMatch;
}

/**
 * Extract keywords from text based on frequency and length
 */
export function extractKeywords(text: string, maxKeywords: number = 5): string[] {
  const tokens = tokenizeText(text);
  const frequency: { [key: string]: number } = {};
  
  // Count frequency
  tokens.forEach(token => {
    frequency[token] = (frequency[token] || 0) + 1;
  });
  
  // Sort by frequency and length (prefer longer words)
  return Object.entries(frequency)
    .sort(([a, freqA], [b, freqB]) => {
      if (freqA !== freqB) return freqB - freqA;
      return b.length - a.length;
    })
    .slice(0, maxKeywords)
    .map(([word]) => word);
}
