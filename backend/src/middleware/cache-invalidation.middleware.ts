import { Request, Response, NextFunction } from 'express';
import { dashboardCache } from '../services/dashboard-cache.service';

/**
 * Middleware to automatically invalidate cache when data is modified
 */

interface CacheInvalidationOptions {
  entity: 'transaction' | 'account' | 'budget' | 'goal';
  operations?: string[]; // Specific operations to invalidate
  condition?: (req: Request, res: Response) => boolean; // Conditional invalidation
}

/**
 * Create cache invalidation middleware
 */
export function createCacheInvalidationMiddleware(options: CacheInvalidationOptions) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Store original res.json to intercept successful responses
    const originalJson = res.json;
    
    res.json = function(body: any) {
      // Only invalidate cache on successful operations (2xx status codes)
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Check if condition is met (if provided)
        if (options.condition && !options.condition(req, res)) {
          return originalJson.call(this, body);
        }
        
        // Perform cache invalidation asynchronously (don't block response)
        setImmediate(async () => {
          try {
            if (options.operations) {
              // Invalidate specific operations
              for (const operation of options.operations) {
                await dashboardCache.invalidate(operation as any);
              }
            } else {
              // Invalidate related caches based on entity
              await dashboardCache.invalidateRelated(options.entity);
            }
            
            console.log(`Cache invalidated for ${options.entity} modification`);
          } catch (error) {
            console.warn(`Failed to invalidate cache for ${options.entity}:`, error);
          }
        });
      }
      
      return originalJson.call(this, body);
    };
    
    next();
  };
}

/**
 * Predefined middleware for common entities
 */

// Transaction cache invalidation
export const invalidateTransactionCache = createCacheInvalidationMiddleware({
  entity: 'transaction',
  condition: (req, res) => {
    // Only invalidate for POST, PUT, PATCH, DELETE operations
    return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method);
  }
});

// Account cache invalidation
export const invalidateAccountCache = createCacheInvalidationMiddleware({
  entity: 'account',
  condition: (req, res) => {
    return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method);
  }
});

// Budget cache invalidation
export const invalidateBudgetCache = createCacheInvalidationMiddleware({
  entity: 'budget',
  condition: (req, res) => {
    return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method);
  }
});

// Goal cache invalidation
export const invalidateGoalCache = createCacheInvalidationMiddleware({
  entity: 'goal',
  condition: (req, res) => {
    return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method);
  }
});

/**
 * Middleware for bulk operations that affect multiple entities
 */
export const invalidateAllDashboardCache = async (req: Request, res: Response, next: NextFunction) => {
  const originalJson = res.json;
  
  res.json = function(body: any) {
    if (res.statusCode >= 200 && res.statusCode < 300) {
      setImmediate(async () => {
        try {
          await dashboardCache.invalidateAll();
          console.log('All dashboard cache invalidated due to bulk operation');
        } catch (error) {
          console.warn('Failed to invalidate all dashboard cache:', error);
        }
      });
    }
    
    return originalJson.call(this, body);
  };
  
  next();
};

/**
 * Smart cache invalidation based on request path
 */
export const smartCacheInvalidation = async (req: Request, res: Response, next: NextFunction) => {
  // Skip for GET requests
  if (req.method === 'GET') {
    return next();
  }
  
  const originalJson = res.json;
  
  res.json = function(body: any) {
    if (res.statusCode >= 200 && res.statusCode < 300) {
      setImmediate(async () => {
        try {
          const path = req.path.toLowerCase();
          
          if (path.includes('/transaction')) {
            await dashboardCache.invalidateRelated('transaction');
          } else if (path.includes('/account')) {
            await dashboardCache.invalidateRelated('account');
          } else if (path.includes('/budget')) {
            await dashboardCache.invalidateRelated('budget');
          } else if (path.includes('/goal')) {
            await dashboardCache.invalidateRelated('goal');
          } else if (path.includes('/transfer')) {
            // Transfers affect both transactions and accounts
            await dashboardCache.invalidateRelated('transaction');
            await dashboardCache.invalidateRelated('account');
          }
          
          console.log(`Smart cache invalidation triggered for path: ${path}`);
        } catch (error) {
          console.warn('Smart cache invalidation failed:', error);
        }
      });
    }
    
    return originalJson.call(this, body);
  };
  
  next();
};

/**
 * Cache warming middleware for dashboard routes
 */
export const warmDashboardCache = async (req: Request, res: Response, next: NextFunction) => {
  // Only warm cache for GET requests to dashboard endpoints
  if (req.method === 'GET' && req.path.includes('/dashboard')) {
    setImmediate(async () => {
      try {
        // Extract filters from query parameters
        const filters = {
          accountIds: req.query.accountIds ? 
            (Array.isArray(req.query.accountIds) ? req.query.accountIds : [req.query.accountIds]) : 
            undefined,
          categoryIds: req.query.categoryIds ? 
            (Array.isArray(req.query.categoryIds) ? req.query.categoryIds : [req.query.categoryIds]) : 
            undefined,
          familyMemberIds: req.query.familyMemberIds ? 
            (Array.isArray(req.query.familyMemberIds) ? req.query.familyMemberIds : [req.query.familyMemberIds]) : 
            undefined,
          dateRange: req.query.startDate && req.query.endDate ? {
            startDate: req.query.startDate as string,
            endDate: req.query.endDate as string
          } : undefined
        };
        
        // Remove undefined values
        const cleanFilters = Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== undefined)
        );
        
        await dashboardCache.warmUp([cleanFilters]);
        console.log('Dashboard cache warmed for filters:', cleanFilters);
      } catch (error) {
        console.warn('Failed to warm dashboard cache:', error);
      }
    });
  }
  
  next();
};
