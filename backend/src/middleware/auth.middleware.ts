import { Request, Response, NextFunction } from 'express';
import { authService } from '../services/auth.service';

// Temporary inline type until we fix the shared module import
interface AuthUser {
  id: string;
  email: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: AuthUser;
    }
  }
}

/**
 * Middleware to authenticate JWT tokens
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        success: false,
        error: {
          message: 'Token de acesso requerido',
          code: 'MISSING_TOKEN'
        }
      });
      return;
    }

    // Verify token and get user
    const user = await authService.verifyToken(token);
    req.user = user;
    
    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      error: {
        message: 'Token inválido ou expirado',
        code: 'INVALID_TOKEN'
      }
    });
  }
};

/**
 * Middleware to check if user is active
 */
export const requireActiveUser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: {
        message: 'Usuário não autenticado',
        code: 'NOT_AUTHENTICATED'
      }
    });
    return;
  }

  if (!req.user.isActive) {
    res.status(403).json({
      success: false,
      error: {
        message: 'Conta desativada',
        code: 'ACCOUNT_DEACTIVATED'
      }
    });
    return;
  }

  next();
};

/**
 * Optional authentication middleware - doesn't fail if no token
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const user = await authService.verifyToken(token);
      req.user = user;
    }
    
    next();
  } catch (error) {
    // Continue without user if token is invalid
    next();
  }
};
