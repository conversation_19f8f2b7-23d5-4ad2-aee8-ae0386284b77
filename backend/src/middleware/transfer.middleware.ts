import { Request, Response, NextFunction } from 'express';
import prisma from '../lib/prisma';

/**
 * Middleware to validate transfer-specific business rules
 */
export const validateTransferRules = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { accountId, destinationAccountId, amount, type } = req.body;

    // Only apply to transfer transactions
    if (type !== 'TRANSFER') {
      next();
      return;
    }

    // Validate required fields for transfers
    if (!destinationAccountId) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Conta destino é obrigatória para transferências',
          code: 'MISSING_DESTINATION_ACCOUNT'
        }
      });
      return;
    }

    // Validate accounts are different
    if (accountId === destinationAccountId) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Conta origem e destino devem ser diferentes',
          code: 'SAME_ACCOUNT_TRANSFER'
        }
      });
      return;
    }

    // Validate accounts exist
    const [sourceAccount, destinationAccount] = await Promise.all([
      prisma.account.findFirst({
        where: { id: accountId, deletedAt: null }
      }),
      prisma.account.findFirst({
        where: { id: destinationAccountId, deletedAt: null }
      })
    ]);

    if (!sourceAccount) {
      res.status(404).json({
        success: false,
        error: {
          message: 'Conta origem não encontrada',
          code: 'SOURCE_ACCOUNT_NOT_FOUND'
        }
      });
      return;
    }

    if (!destinationAccount) {
      res.status(404).json({
        success: false,
        error: {
          message: 'Conta destino não encontrada',
          code: 'DESTINATION_ACCOUNT_NOT_FOUND'
        }
      });
      return;
    }

    // Validate sufficient balance (only for non-future transactions)
    if (!req.body.isFuture) {
      const currentBalance = Number(sourceAccount.currentBalance);
      const transferAmount = Number(amount);

      if (sourceAccount.type === 'CREDIT_CARD') {
        const creditLimit = Number(sourceAccount.creditLimit || 0);
        const availableCredit = creditLimit + currentBalance;
        
        if (transferAmount > availableCredit) {
          res.status(400).json({
            success: false,
            error: {
              message: 'Limite de crédito insuficiente para a transferência',
              code: 'INSUFFICIENT_CREDIT_LIMIT'
            }
          });
          return;
        }
      } else {
        if (transferAmount > currentBalance) {
          res.status(400).json({
            success: false,
            error: {
              message: 'Saldo insuficiente para a transferência',
              code: 'INSUFFICIENT_BALANCE'
            }
          });
          return;
        }
      }
    }

    // Add account information to request for later use
    req.body._sourceAccount = sourceAccount;
    req.body._destinationAccount = destinationAccount;

    next();
  } catch (error) {
    console.error('Transfer validation middleware error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
};

/**
 * Middleware to validate currency conversion data
 */
export const validateCurrencyConversion = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const { 
      exchangeRate, 
      sourceCurrency, 
      destinationCurrency, 
      sourceAmount, 
      destinationAmount 
    } = req.body;

    // If exchange rate is provided, validate currency data
    if (exchangeRate) {
      if (!sourceCurrency || !destinationCurrency) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Moedas de origem e destino são obrigatórias quando há taxa de câmbio',
            code: 'MISSING_CURRENCY_DATA'
          }
        });
        return;
      }

      // Validate currency codes
      const currencyPattern = /^[A-Z]{3}$/;
      if (!currencyPattern.test(sourceCurrency) || !currencyPattern.test(destinationCurrency)) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Códigos de moeda inválidos (devem ter 3 letras maiúsculas)',
            code: 'INVALID_CURRENCY_CODE'
          }
        });
        return;
      }

      // Validate exchange rate
      if (exchangeRate <= 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Taxa de câmbio deve ser positiva',
            code: 'INVALID_EXCHANGE_RATE'
          }
        });
        return;
      }

      // If same currency, exchange rate should be 1
      if (sourceCurrency === destinationCurrency && exchangeRate !== 1) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Taxa de câmbio deve ser 1 para transferências na mesma moeda',
            code: 'INVALID_SAME_CURRENCY_RATE'
          }
        });
        return;
      }

      // Validate amounts if provided
      if (sourceAmount && destinationAmount) {
        const calculatedDestination = Number((sourceAmount * exchangeRate).toFixed(2));
        const providedDestination = Number(destinationAmount);
        
        // Allow small rounding differences (0.01)
        if (Math.abs(calculatedDestination - providedDestination) > 0.01) {
          res.status(400).json({
            success: false,
            error: {
              message: 'Valor de destino não corresponde ao cálculo da taxa de câmbio',
              code: 'CURRENCY_CALCULATION_MISMATCH'
            }
          });
          return;
        }
      }
    }

    next();
  } catch (error) {
    console.error('Currency conversion validation error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
};

/**
 * Middleware to log transfer operations for audit
 */
export const auditTransferOperation = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Store original res.json to intercept response
  const originalJson = res.json;
  
  res.json = function(body: any) {
    // Log successful operations
    if (body.success && req.method !== 'GET') {
      console.log('Transfer Operation Audit:', {
        timestamp: new Date().toISOString(),
        method: req.method,
        path: req.path,
        userId: req.user?.id,
        transferId: body.data?.id || req.params.id,
        operation: getOperationType(req.method),
        sourceAccount: req.body.accountId,
        destinationAccount: req.body.destinationAccountId,
        amount: req.body.amount,
        currency: req.body.sourceCurrency || 'BRL'
      });
    }
    
    return originalJson.call(this, body);
  };

  next();
};

/**
 * Helper function to determine operation type
 */
function getOperationType(method: string): string {
  switch (method) {
    case 'POST': return 'CREATE';
    case 'PUT': return 'UPDATE';
    case 'DELETE': return 'DELETE';
    default: return 'UNKNOWN';
  }
}
