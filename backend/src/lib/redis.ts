import { createClient, RedisClientType } from 'redis';

/**
 * Redis Cache Manager with robust error handling and fallbacks
 * Ensures the application continues to work even when Redis is unavailable
 */
export class RedisCacheManager {
  private client: RedisClientType | null = null;
  private isConnected = false;
  private connectionAttempts = 0;
  private readonly maxConnectionAttempts = 3;
  private readonly reconnectDelay = 5000; // 5 seconds
  private isConnecting = false;

  constructor(private readonly redisUrl?: string) {
    this.initializeClient();
  }

  /**
   * Initialize Redis client with error handling
   */
  private async initializeClient(): Promise<void> {
    if (this.isConnecting) return;
    this.isConnecting = true;

    try {
      // Create Redis client with configuration
      this.client = createClient({
        url: this.redisUrl || process.env.REDIS_URL || 'redis://localhost:6379',
        socket: {
          reconnectStrategy: (retries) => {
            if (retries >= this.maxConnectionAttempts) {
              console.warn('Redis: Max reconnection attempts reached, giving up');
              return false;
            }

            // Exponential backoff with jitter
            const jitter = Math.floor(Math.random() * 1000);
            const delay = Math.min(Math.pow(2, retries) * 1000, 10000);
            console.log(`Redis: Reconnecting in ${delay + jitter}ms (attempt ${retries + 1})`);
            return delay + jitter;
          },
          connectTimeout: 10000 // 10 seconds
        }
      });

      // Set up event listeners
      this.client.on('error', (err) => {
        console.error('Redis Client Error:', err);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('Redis: Connected successfully');
        this.isConnected = true;
        this.connectionAttempts = 0;
      });

      this.client.on('disconnect', () => {
        console.warn('Redis: Disconnected');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        console.log('Redis: Attempting to reconnect...');
      });

      // Attempt to connect
      await this.client.connect();
      
    } catch (error) {
      console.error('Redis: Failed to initialize client:', error);
      this.connectionAttempts++;
      this.isConnected = false;
      this.client = null;
      
      // Retry connection after delay if under max attempts
      if (this.connectionAttempts < this.maxConnectionAttempts) {
        setTimeout(() => {
          this.isConnecting = false;
          this.initializeClient();
        }, this.reconnectDelay);
      } else {
        console.warn('Redis: Max connection attempts reached, cache will be disabled');
      }
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * Check if Redis is available
   */
  public isAvailable(): boolean {
    return this.isConnected && this.client !== null;
  }

  /**
   * Get data from cache with fallback
   */
  async get<T>(key: string): Promise<T | null> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const data = await this.client!.get(key);
      if (data && typeof data === 'string') {
        return JSON.parse(data) as T;
      }
      return null;
    } catch (error) {
      console.warn(`Redis: Failed to get key "${key}":`, error);
      return null;
    }
  }

  /**
   * Set data in cache with TTL
   */
  async set(key: string, value: any, ttlSeconds?: number): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const serializedValue = JSON.stringify(value);
      
      if (ttlSeconds) {
        await this.client!.setEx(key, ttlSeconds, serializedValue);
      } else {
        await this.client!.set(key, serializedValue);
      }
      
      return true;
    } catch (error) {
      console.warn(`Redis: Failed to set key "${key}":`, error);
      return false;
    }
  }

  /**
   * Delete data from cache
   */
  async del(key: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client!.del(key);
      return true;
    } catch (error) {
      console.warn(`Redis: Failed to delete key "${key}":`, error);
      return false;
    }
  }

  /**
   * Delete multiple keys matching a pattern
   */
  async delPattern(pattern: string): Promise<number> {
    if (!this.isAvailable()) {
      return 0;
    }

    try {
      const keys = [];
      for await (const key of this.client!.scanIterator({ MATCH: pattern })) {
        keys.push(key);
      }
      
      if (keys.length > 0) {
        return await this.client!.del(keys as any);
      }
      
      return 0;
    } catch (error) {
      console.warn(`Redis: Failed to delete pattern "${pattern}":`, error);
      return 0;
    }
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const result = await this.client!.exists(key);
      return result === 1;
    } catch (error) {
      console.warn(`Redis: Failed to check existence of key "${key}":`, error);
      return false;
    }
  }

  /**
   * Set TTL for existing key
   */
  async expire(key: string, ttlSeconds: number): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const result = await this.client!.expire(key, ttlSeconds);
      return result === 1;
    } catch (error) {
      console.warn(`Redis: Failed to set TTL for key "${key}":`, error);
      return false;
    }
  }

  /**
   * Get TTL for key
   */
  async ttl(key: string): Promise<number> {
    if (!this.isAvailable()) {
      return -1;
    }

    try {
      return await this.client!.ttl(key);
    } catch (error) {
      console.warn(`Redis: Failed to get TTL for key "${key}":`, error);
      return -1;
    }
  }

  /**
   * Ping Redis server
   */
  async ping(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const result = await this.client!.ping();
      return result === 'PONG';
    } catch (error) {
      console.warn('Redis: Ping failed:', error);
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    isConnected: boolean;
    connectionAttempts: number;
    info?: any;
  }> {
    const stats = {
      isConnected: this.isConnected,
      connectionAttempts: this.connectionAttempts,
      info: undefined as any
    };

    if (this.isAvailable()) {
      try {
        stats.info = await this.client!.info();
      } catch (error) {
        console.warn('Redis: Failed to get info:', error);
      }
    }

    return stats;
  }

  /**
   * Gracefully disconnect from Redis
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      try {
        await this.client.disconnect();
        console.log('Redis: Disconnected gracefully');
      } catch (error) {
        console.error('Redis: Error during disconnect:', error);
      } finally {
        this.client = null;
        this.isConnected = false;
      }
    }
  }

  /**
   * Force reconnection
   */
  async reconnect(): Promise<void> {
    if (this.client) {
      await this.disconnect();
    }
    this.connectionAttempts = 0;
    await this.initializeClient();
  }
}

// Create singleton instance
export const redisCache = new RedisCacheManager();

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down Redis connection...');
  await redisCache.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down Redis connection...');
  await redisCache.disconnect();
  process.exit(0);
});
