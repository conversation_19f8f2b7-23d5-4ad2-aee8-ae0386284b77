import { Request, Response } from 'express';
import { ZodError } from 'zod';
import { categoryService } from '../services/category.service';
import {
  CreateCategorySchema,
  UpdateCategorySchema,
  CategoryFiltersSchema,
  ArchiveCategorySchema
} from '../schemas/category.schemas';

export class CategoryController {
  /**
   * Create a new category
   * @route POST /api/v1/categories
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = CreateCategorySchema.parse(req.body);

      // Create category
      const category = await categoryService.create(validatedData);

      res.status(201).json({
        success: true,
        data: category,
        message: 'Categoria criada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all categories with filters and pagination
   * @route GET /api/v1/categories
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const validatedFilters = CategoryFiltersSchema.parse(req.query);

      // Get categories
      const result = await categoryService.findAll(validatedFilters);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        message: 'Categorias obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get category by ID
   * @route GET /api/v1/categories/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const includeArchived = req.query.includeArchived === 'true';

      const category = await categoryService.findById(id, includeArchived);

      if (!category) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Categoria não encontrada',
            code: 'CATEGORY_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: category,
        message: 'Categoria obtida com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get categories statistics
   * @route GET /api/v1/categories/stats
   */
  async getStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await categoryService.getStats();

      res.status(200).json({
        success: true,
        data: stats,
        message: 'Estatísticas das categorias obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get categories for selection dropdown
   * @route GET /api/v1/categories/selection
   */
  async getForSelection(req: Request, res: Response): Promise<void> {
    try {
      const onlyActive = req.query.onlyActive === 'true';
      const excludeId = req.query.excludeId as string;

      const categories = await categoryService.getForSelection(onlyActive, excludeId);

      res.status(200).json({
        success: true,
        data: categories,
        message: 'Categorias para seleção obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get category tree for hierarchical display
   * @route GET /api/v1/categories/tree
   */
  async getCategoryTree(req: Request, res: Response): Promise<void> {
    try {
      const includeArchived = req.query.includeArchived === 'true';

      const tree = await categoryService.getCategoryTree(includeArchived);

      res.status(200).json({
        success: true,
        data: tree,
        message: 'Árvore de categorias obtida com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update category
   * @route PUT /api/v1/categories/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      // Validate request body
      const validatedData = UpdateCategorySchema.parse(req.body);

      // Update category
      const category = await categoryService.update(id, validatedData);

      res.status(200).json({
        success: true,
        data: category,
        message: 'Categoria atualizada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Archive or unarchive category
   * @route PATCH /api/v1/categories/:id/archive
   */
  async archive(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      // Validate request body
      const validatedData = ArchiveCategorySchema.parse(req.body);

      // Archive/unarchive category
      const category = await categoryService.archive(id, validatedData.archived);

      const action = validatedData.archived ? 'arquivada' : 'reativada';
      res.status(200).json({
        success: true,
        data: category,
        message: `Categoria ${action} com sucesso`
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete category permanently
   * @route DELETE /api/v1/categories/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      await categoryService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Categoria deletada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    console.error('CategoryController Error:', error);

    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('não encontrada') || error.message.includes('não encontrado')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'NOT_FOUND'
          }
        });
        return;
      }

      if (error.message.includes('já existe') || error.message.includes('já está')) {
        res.status(409).json({
          success: false,
          error: {
            message: error.message,
            code: 'CONFLICT'
          }
        });
        return;
      }

      if (error.message.includes('hierarquia') || error.message.includes('circular') || error.message.includes('níveis')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'HIERARCHY_ERROR'
          }
        });
        return;
      }

      if (error.message.includes('possui') || error.message.includes('associadas')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'HAS_DEPENDENCIES'
          }
        });
        return;
      }

      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'BAD_REQUEST'
        }
      });
      return;
    }

    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
}

export const categoryController = new CategoryController();
