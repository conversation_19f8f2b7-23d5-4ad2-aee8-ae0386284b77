import { Request, Response } from 'express';
import { z } from 'zod';
import { Prisma } from '@prisma/client';
import { transactionService } from '../services/transaction.service';
import {
  CreateTransactionSchema,
  UpdateTransactionSchema,
  TransactionFiltersSchema,
  updateInstallmentsSchema
} from '../schemas/transaction.schemas';
import { InstallmentService } from '../services/installment.service';

export class TransactionController {
  private installmentService = new InstallmentService();
  /**
   * Create a new transaction
   * @route POST /api/v1/transactions
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const result = CreateTransactionSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Create transaction
      const transaction = await transactionService.create(result.data);

      res.status(201).json({
        success: true,
        data: transaction,
        message: 'Transação criada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all transactions with filters and pagination
   * @route GET /api/v1/transactions
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const result = TransactionFiltersSchema.safeParse(req.query);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Parâmetros de consulta inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Get transactions
      const transactions = await transactionService.findAll(result.data);

      res.status(200).json({
        success: true,
        data: transactions.data,
        pagination: transactions.pagination,
        summary: transactions.summary,
        message: 'Transações obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get transaction by ID
   * @route GET /api/v1/transactions/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const includeDeleted = req.query.includeDeleted === 'true';

      // Get transaction
      const transaction = await transactionService.findById(id, includeDeleted);

      if (!transaction) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Transação não encontrada',
            code: 'TRANSACTION_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'Transação obtida com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update transaction
   * @route PUT /api/v1/transactions/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate request body
      const result = UpdateTransactionSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Check if there's data to update
      if (Object.keys(result.data).length === 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum dado fornecido para atualização',
            code: 'NO_UPDATE_DATA'
          }
        });
        return;
      }

      // Update transaction
      const transaction = await transactionService.update(id, result.data);

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'Transação atualizada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete transaction (soft delete)
   * @route DELETE /api/v1/transactions/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Delete transaction
      await transactionService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Transação deletada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get installments of a transaction
   * @route GET /api/v1/transactions/:id/installments
   */
  async getInstallments(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Get installments using the service
      const installments = await this.installmentService.getInstallmentsByParent(id);

      res.status(200).json({
        success: true,
        data: installments,
        message: 'Parcelas recuperadas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update installments of a transaction
   * @route PUT /api/v1/transactions/:id/installments
   */
  async updateInstallments(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate request body
      const result = updateInstallmentsSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Convert string dates to Date objects
      const updateData = {
        ...result.data,
        installments: result.data.installments.map(inst => ({
          ...inst,
          transactionDate: new Date(inst.transactionDate)
        }))
      };

      // Update installments
      const updateResult = await this.installmentService.updateInstallmentTransaction(id, updateData);

      res.status(200).json({
        success: true,
        data: {
          parentTransaction: updateResult.parentTransaction,
          installments: updateResult.newInstallments,
          summary: updateResult.summary,
          deletedInstallments: updateResult.deletedInstallments
        },
        message: 'Parcelas atualizadas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          res.status(409).json({
            success: false,
            error: {
              message: 'Violação de restrição única',
              code: 'UNIQUE_CONSTRAINT_VIOLATION',
              details: error.meta
            }
          });
          return;
        case 'P2003':
          res.status(400).json({
            success: false,
            error: {
              message: 'Violação de chave estrangeira',
              code: 'FOREIGN_KEY_CONSTRAINT_VIOLATION',
              details: error.meta
            }
          });
          return;
        case 'P2025':
          res.status(404).json({
            success: false,
            error: {
              message: 'Registro não encontrado',
              code: 'RECORD_NOT_FOUND'
            }
          });
          return;
      }
    }

    // Handle application errors
    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('não encontrado') || error.message.includes('não encontrada')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'TRANSACTION_NOT_FOUND'
          }
        });
        return;
      }

      if (error.message.includes('já existe') || error.message.includes('já está')) {
        res.status(409).json({
          success: false,
          error: {
            message: error.message,
            code: 'CONFLICT'
          }
        });
        return;
      }

      if (error.message.includes('não é possível deletar')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'DELETE_CONSTRAINT_VIOLATION'
          }
        });
        return;
      }

      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'TRANSACTION_ERROR'
        }
      });
      return;
    }

    // Generic server error
    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      }
    });
  }
}

export const transactionController = new TransactionController();
