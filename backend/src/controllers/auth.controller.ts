import { Request, Response } from 'express';
import { authService } from '../services/auth.service';
import { z } from 'zod';

// Temporary inline schemas until we fix the shared module import
const RegisterSchema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(6, '<PERSON>ha deve ter pelo menos 6 caracteres'),
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
});

const LoginSchema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(1, 'Senha é obrigatória'),
});

export class AuthController {
  /**
   * Register a new user
   */
  async register(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = RegisterSchema.parse(req.body);

      // Register user
      const result = await authService.register(validatedData);

      res.status(201).json({
        success: true,
        data: result,
        message: 'Usuário registrado com sucesso'
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            field: error.errors[0]?.path[0]
          }
        });
        return;
      }

      if (error instanceof Error) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'REGISTRATION_ERROR'
          }
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Erro interno do servidor',
          code: 'INTERNAL_ERROR'
        }
      });
    }
  }

  /**
   * Login user
   */
  async login(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = LoginSchema.parse(req.body);

      // Login user
      const result = await authService.login(validatedData);

      res.status(200).json({
        success: true,
        data: result,
        message: 'Login realizado com sucesso'
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            field: error.errors[0]?.path[0]
          }
        });
        return;
      }

      if (error instanceof Error) {
        res.status(401).json({
          success: false,
          error: {
            message: error.message,
            code: 'LOGIN_ERROR'
          }
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Erro interno do servidor',
          code: 'INTERNAL_ERROR'
        }
      });
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            message: 'Usuário não autenticado',
            code: 'NOT_AUTHENTICATED'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: req.user,
        message: 'Perfil obtido com sucesso'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Erro interno do servidor',
          code: 'INTERNAL_ERROR'
        }
      });
    }
  }

  /**
   * Update user password
   */
  async updatePassword(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            message: 'Usuário não autenticado',
            code: 'NOT_AUTHENTICATED'
          }
        });
        return;
      }

      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Senha atual e nova senha são obrigatórias',
            code: 'MISSING_PASSWORDS'
          }
        });
        return;
      }

      if (newPassword.length < 6) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nova senha deve ter pelo menos 6 caracteres',
            code: 'WEAK_PASSWORD'
          }
        });
        return;
      }

      await authService.updatePassword(req.user.id, currentPassword, newPassword);

      res.status(200).json({
        success: true,
        message: 'Senha atualizada com sucesso'
      });
    } catch (error) {
      if (error instanceof Error) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'PASSWORD_UPDATE_ERROR'
          }
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Erro interno do servidor',
          code: 'INTERNAL_ERROR'
        }
      });
    }
  }

  /**
   * Logout user (client-side token removal)
   */
  async logout(req: Request, res: Response): Promise<void> {
    res.status(200).json({
      success: true,
      message: 'Logout realizado com sucesso'
    });
  }

  /**
   * Verify token validity
   */
  async verifyToken(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.body;

      if (!token) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Token é obrigatório',
            code: 'MISSING_TOKEN'
          }
        });
        return;
      }

      const user = await authService.verifyToken(token);

      res.status(200).json({
        success: true,
        data: { user, valid: true },
        message: 'Token válido'
      });
    } catch (error) {
      res.status(401).json({
        success: false,
        data: { valid: false },
        error: {
          message: 'Token inválido',
          code: 'INVALID_TOKEN'
        }
      });
    }
  }
}

export const authController = new AuthController();
