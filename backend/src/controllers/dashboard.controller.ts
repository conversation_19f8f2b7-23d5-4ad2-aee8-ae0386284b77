import { Request, Response } from 'express';
import { DashboardService } from '../services/dashboard.service';
import { DashboardFiltersSchema } from '../schemas/dashboard.schemas';
import { z } from 'zod';

/**
 * Dashboard Controller
 * Handles all dashboard-related API endpoints with aggregated financial data
 */
export class DashboardController {
  private dashboardService: DashboardService;

  constructor() {
    this.dashboardService = new DashboardService();
  }

  /**
   * GET /api/dashboard/overview
   * Get complete dashboard overview with all aggregated data
   */
  async getOverview(req: Request, res: Response): Promise<void> {
    try {
      const filters = DashboardFiltersSchema.parse(req.query);
      
      const overview = await this.dashboardService.getOverview(filters);
      
      res.json({
        success: true,
        data: overview
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid filters provided',
          details: error.errors
        });
      } else {
        console.error('Dashboard overview error:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch dashboard overview',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * GET /api/dashboard/account-balances
   * Get account balances aggregation
   */
  async getAccountBalances(req: Request, res: Response): Promise<void> {
    try {
      const filters = DashboardFiltersSchema.parse(req.query);
      
      const balances = await this.dashboardService.getAccountBalances(filters);
      
      res.json({
        success: true,
        data: balances
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid filters provided',
          details: error.errors
        });
      } else {
        console.error('Account balances error:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch account balances',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * GET /api/dashboard/net-worth
   * Get net worth calculation and history
   */
  async getNetWorth(req: Request, res: Response): Promise<void> {
    try {
      const filters = DashboardFiltersSchema.parse(req.query);
      
      const netWorth = await this.dashboardService.getNetWorth(filters);
      
      res.json({
        success: true,
        data: netWorth
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid filters provided',
          details: error.errors
        });
      } else {
        console.error('Net worth error:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch net worth data',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * GET /api/dashboard/credit-card-usage
   * Get credit card usage analysis
   */
  async getCreditCardUsage(req: Request, res: Response): Promise<void> {
    try {
      const filters = DashboardFiltersSchema.parse(req.query);
      
      const creditCardUsage = await this.dashboardService.getCreditCardUsage(filters);
      
      res.json({
        success: true,
        data: creditCardUsage
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid filters provided',
          details: error.errors
        });
      } else {
        console.error('Credit card usage error:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch credit card usage data',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * GET /api/dashboard/expenses-by-category
   * Get expenses breakdown by category
   */
  async getExpensesByCategory(req: Request, res: Response): Promise<void> {
    try {
      const filters = DashboardFiltersSchema.parse(req.query);
      
      const expenses = await this.dashboardService.getExpensesByCategory(filters);
      
      res.json({
        success: true,
        data: expenses
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid filters provided',
          details: error.errors
        });
      } else {
        console.error('Expenses by category error:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch expenses by category',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * GET /api/dashboard/expenses-by-member
   * Get expenses breakdown by family member
   */
  async getExpensesByMember(req: Request, res: Response): Promise<void> {
    try {
      const filters = DashboardFiltersSchema.parse(req.query);
      
      const expenses = await this.dashboardService.getExpensesByMember(filters);
      
      res.json({
        success: true,
        data: expenses
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid filters provided',
          details: error.errors
        });
      } else {
        console.error('Expenses by member error:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch expenses by member',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * GET /api/dashboard/budget-comparison
   * Get budget vs actual expenses comparison
   */
  async getBudgetComparison(req: Request, res: Response): Promise<void> {
    try {
      const filters = DashboardFiltersSchema.parse(req.query);
      
      const comparison = await this.dashboardService.getBudgetComparison(filters);
      
      res.json({
        success: true,
        data: comparison
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid filters provided',
          details: error.errors
        });
      } else {
        console.error('Budget comparison error:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch budget comparison',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * GET /api/dashboard/goal-progress
   * Get financial goals progress
   */
  async getGoalProgress(req: Request, res: Response): Promise<void> {
    try {
      const filters = DashboardFiltersSchema.parse(req.query);
      
      const progress = await this.dashboardService.getGoalProgress(filters);
      
      res.json({
        success: true,
        data: progress
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid filters provided',
          details: error.errors
        });
      } else {
        console.error('Goal progress error:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch goal progress',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * GET /api/dashboard/performance-metrics
   * Get dashboard performance metrics
   */
  async getPerformanceMetrics(req: Request, res: Response): Promise<void> {
    try {
      const metrics = await this.dashboardService.getDatabaseMetrics();
      
      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      console.error('Performance metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch performance metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}
