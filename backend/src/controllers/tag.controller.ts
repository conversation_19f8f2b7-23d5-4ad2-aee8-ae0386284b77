import { Request, Response } from 'express';
import { ZodError } from 'zod';
import { tagService } from '../services/tag.service';
import {
  CreateTagSchema,
  UpdateTagSchema,
  TagFiltersSchema,
  ArchiveTagSchema
} from '../schemas/tag.schemas';

export class TagController {
  /**
   * Create a new tag
   * @route POST /api/v1/tags
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = CreateTagSchema.parse(req.body);

      // Create tag
      const tag = await tagService.create(validatedData);

      res.status(201).json({
        success: true,
        data: tag,
        message: 'Tag criada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all tags with filters and pagination
   * @route GET /api/v1/tags
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const validatedFilters = TagFiltersSchema.parse(req.query);

      // Get tags
      const result = await tagService.findAll(validatedFilters);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        message: 'Tags obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get tag by ID
   * @route GET /api/v1/tags/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const includeArchived = req.query.includeArchived === 'true';

      const tag = await tagService.findById(id, includeArchived);

      if (!tag) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Tag não encontrada',
            code: 'TAG_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: tag,
        message: 'Tag obtida com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update tag
   * @route PUT /api/v1/tags/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      // Validate request body
      const validatedData = UpdateTagSchema.parse(req.body);

      // Update tag
      const tag = await tagService.update(id, validatedData);

      res.status(200).json({
        success: true,
        data: tag,
        message: 'Tag atualizada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Archive or unarchive tag
   * @route PATCH /api/v1/tags/:id/archive
   */
  async archive(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      // Validate request body
      const validatedData = ArchiveTagSchema.parse(req.body);

      // Archive/unarchive tag
      const tag = await tagService.archive(id, validatedData.archived);

      const action = validatedData.archived ? 'arquivada' : 'reativada';
      res.status(200).json({
        success: true,
        data: tag,
        message: `Tag ${action} com sucesso`
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete tag permanently
   * @route DELETE /api/v1/tags/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      await tagService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Tag deletada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get tag statistics
   * @route GET /api/v1/tags/stats
   */
  async getStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await tagService.getStats();

      res.status(200).json({
        success: true,
        data: stats,
        message: 'Estatísticas obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    console.error('TagController Error:', error);

    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('não encontrada') || error.message.includes('não encontrado')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'NOT_FOUND'
          }
        });
        return;
      }

      if (error.message.includes('já existe') || error.message.includes('já está')) {
        res.status(409).json({
          success: false,
          error: {
            message: error.message,
            code: 'CONFLICT'
          }
        });
        return;
      }

      if (error.message.includes('possui') || error.message.includes('associadas')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'HAS_DEPENDENCIES'
          }
        });
        return;
      }

      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'BAD_REQUEST'
        }
      });
      return;
    }

    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
}

export const tagController = new TagController();
