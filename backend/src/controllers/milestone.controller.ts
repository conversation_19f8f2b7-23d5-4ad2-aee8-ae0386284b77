import { Request, Response } from 'express';
import { z } from 'zod';
import { milestoneService } from '../services/milestone.service';
import {
  CreateGoalMilestoneSchema,
  UpdateGoalMilestoneSchema,
  MilestoneFiltersSchema
} from '../schemas/financial-goal.schemas';

export class MilestoneController {
  /**
   * Create a new milestone for a goal
   * @route POST /api/v1/goals/:goalId/milestones
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      const { goalId } = req.params;

      // Validate goal ID format
      if (!goalId || typeof goalId !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID da meta financeira é obrigatório',
            code: 'INVALID_GOAL_ID'
          }
        });
        return;
      }

      // Validate request body
      const validatedData = CreateGoalMilestoneSchema.parse(req.body);

      // Create milestone
      const milestone = await milestoneService.create(goalId, validatedData);

      res.status(201).json({
        success: true,
        data: milestone,
        message: '<PERSON> da meta criado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all milestones for a goal with filters
   * @route GET /api/v1/goals/:goalId/milestones
   */
  async findByGoal(req: Request, res: Response): Promise<void> {
    try {
      const { goalId } = req.params;

      // Validate goal ID format
      if (!goalId || typeof goalId !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID da meta financeira é obrigatório',
            code: 'INVALID_GOAL_ID'
          }
        });
        return;
      }

      // Validate query parameters
      const validatedFilters = MilestoneFiltersSchema.parse({
        goalId,
        ...req.query,
        // Convert date strings to Date objects
        ...(req.query.targetDateFrom && { targetDateFrom: new Date(req.query.targetDateFrom as string) }),
        ...(req.query.targetDateTo && { targetDateTo: new Date(req.query.targetDateTo as string) })
      });

      // Remove goalId from filters since it's already in the URL
      const { goalId: _, ...filters } = validatedFilters;

      // Get milestones
      const milestones = await milestoneService.findByGoal(goalId, filters);

      res.status(200).json({
        success: true,
        data: milestones,
        message: 'Marcos da meta recuperados com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get milestone by ID
   * @route GET /api/v1/milestones/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do marco é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Get milestone
      const milestone = await milestoneService.findById(id);

      if (!milestone) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Marco da meta não encontrado',
            code: 'MILESTONE_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: milestone,
        message: 'Marco da meta recuperado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update milestone
   * @route PUT /api/v1/milestones/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do marco é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Validate request body
      const validatedData = UpdateGoalMilestoneSchema.parse(req.body);

      // Check if there's data to update
      if (Object.keys(validatedData).length === 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum dado fornecido para atualização',
            code: 'NO_UPDATE_DATA'
          }
        });
        return;
      }

      // Update milestone
      const milestone = await milestoneService.update(id, validatedData);

      res.status(200).json({
        success: true,
        data: milestone,
        message: 'Marco da meta atualizado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete milestone
   * @route DELETE /api/v1/milestones/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do marco é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Delete milestone
      await milestoneService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Marco da meta deletado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Mark milestone as completed
   * @route POST /api/v1/milestones/:id/complete
   */
  async markCompleted(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do marco é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Mark as completed
      const milestone = await milestoneService.markCompleted(id);

      res.status(200).json({
        success: true,
        data: milestone,
        message: 'Marco marcado como concluído com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Mark milestone as pending
   * @route POST /api/v1/milestones/:id/pending
   */
  async markPending(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do marco é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Mark as pending
      const milestone = await milestoneService.markPending(id);

      res.status(200).json({
        success: true,
        data: milestone,
        message: 'Marco marcado como pendente com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    if (error instanceof Error) {
      // Check for specific business logic errors
      if (error.message.includes('não encontrado') || error.message.includes('não encontrada')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'NOT_FOUND'
          }
        });
        return;
      }

      if (error.message.includes('exceder') || error.message.includes('maior')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'INVALID_AMOUNT'
          }
        });
        return;
      }

      // Generic business logic error
      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'BUSINESS_LOGIC_ERROR'
        }
      });
      return;
    }

    // Generic server error
    console.error('Unexpected error in MilestoneController:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
}

export const milestoneController = new MilestoneController();
