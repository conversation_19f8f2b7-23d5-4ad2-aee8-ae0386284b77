import { Request, Response } from 'express';
import { z } from 'zod';
import { Prisma } from '@prisma/client';
import { transferService } from '../services/transfer.service';
import {
  CreateTransactionSchema,
  UpdateTransactionSchema,
  TransferRequest
} from '../schemas/transaction.schemas';

export class Transfer<PERSON>ontroller {
  /**
   * Create a new transfer between accounts
   * @route POST /api/v1/transfers
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body for transfer
      const result = CreateTransactionSchema.safeParse({
        ...req.body,
        type: 'TRANSFER'
      });
      
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Additional transfer-specific validations
      if (!result.data.destinationAccountId) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Conta destino é obrigatória para transferências',
            code: 'MISSING_DESTINATION_ACCOUNT'
          }
        });
        return;
      }

      if (result.data.accountId === result.data.destinationAccountId) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Conta origem e destino devem ser diferentes',
            code: 'SAME_ACCOUNT_TRANSFER'
          }
        });
        return;
      }

      // Create transfer
      const transfer = await transferService.createTransfer(result.data as TransferRequest);

      res.status(201).json({
        success: true,
        data: transfer,
        message: 'Transferência criada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all transfers with filters
   * @route GET /api/v1/transfers
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      const filters = {
        ...req.query,
        type: 'TRANSFER' // Force type to be TRANSFER
      };

      const transfers = await transferService.findAllTransfers(filters);

      res.status(200).json({
        success: true,
        data: transfers.data,
        pagination: transfers.pagination,
        summary: transfers.summary,
        message: 'Transferências obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get transfer by ID
   * @route GET /api/v1/transfers/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const includeDeleted = req.query.includeDeleted === 'true';

      const transfer = await transferService.findTransferById(id, includeDeleted);

      if (!transfer) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Transferência não encontrada',
            code: 'TRANSFER_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: transfer,
        message: 'Transferência obtida com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update transfer
   * @route PUT /api/v1/transfers/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate request body
      const result = UpdateTransactionSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Check if there's data to update
      if (Object.keys(result.data).length === 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum dado fornecido para atualização',
            code: 'NO_UPDATE_DATA'
          }
        });
        return;
      }

      // Update transfer
      const transfer = await transferService.updateTransfer(id, result.data);

      res.status(200).json({
        success: true,
        data: transfer,
        message: 'Transferência atualizada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete transfer (soft delete)
   * @route DELETE /api/v1/transfers/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      await transferService.deleteTransfer(id);

      res.status(200).json({
        success: true,
        message: 'Transferência excluída com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Calculate currency conversion for transfer preview
   * @route POST /api/v1/transfers/calculate-conversion
   */
  async calculateConversion(req: Request, res: Response): Promise<void> {
    try {
      const { sourceAmount, sourceCurrency, destinationCurrency, exchangeRate } = req.body;

      if (!sourceAmount || !sourceCurrency || !destinationCurrency || !exchangeRate) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados obrigatórios: sourceAmount, sourceCurrency, destinationCurrency, exchangeRate',
            code: 'MISSING_CONVERSION_DATA'
          }
        });
        return;
      }

      const conversion = await transferService.calculateCurrencyConversion({
        sourceAmount,
        sourceCurrency,
        destinationCurrency,
        exchangeRate,
        destinationAmount: 0, // Will be calculated by the service
        conversionDate: new Date()
      });

      res.status(200).json({
        success: true,
        data: conversion,
        message: 'Conversão calculada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Validate transfer before creation
   * @route POST /api/v1/transfers/validate
   */
  async validateTransfer(req: Request, res: Response): Promise<void> {
    try {
      const validation = await transferService.validateTransferData(req.body);

      res.status(200).json({
        success: true,
        data: validation,
        message: 'Validação concluída'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors consistently
   */
  private handleError(error: any, res: Response): void {
    console.error('Transfer Controller Error:', error);

    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        res.status(409).json({
          success: false,
          error: {
            message: 'Violação de restrição única',
            code: 'UNIQUE_CONSTRAINT_VIOLATION'
          }
        });
        return;
      }

      if (error.code === 'P2025') {
        res.status(404).json({
          success: false,
          error: {
            message: 'Transferência não encontrada',
            code: 'TRANSFER_NOT_FOUND'
          }
        });
        return;
      }
    }

    // Generic error
    res.status(500).json({
      success: false,
      error: {
        message: error.message || 'Erro interno do servidor',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
}

export const transferController = new TransferController();
