import { Request, Response } from 'express';
import { ZodError } from 'zod';
import { InsightService } from '../services/insight.service';
import {
  CreateInsightSchema,
  UpdateInsightSchema,
  InsightFiltersSchema,
  BulkInsightActionSchema,
  GenerateInsightsSchema
} from '../schemas/insight.schemas';

export class InsightController {
  private insightService: InsightService;

  constructor() {
    this.insightService = new InsightService();
  }

  /**
   * Create a new insight manually
   * @route POST /api/v1/insights
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = CreateInsightSchema.parse(req.body);

      // Create insight
      const insight = await this.insightService.create(validatedData);

      res.status(201).json({
        success: true,
        data: insight,
        message: 'Insight criado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all insights with filters and pagination
   * @route GET /api/v1/insights
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const validatedFilters = InsightFiltersSchema.parse(req.query);

      // Get insights
      const result = await this.insightService.findAll(validatedFilters);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        summary: result.summary,
        message: 'Insights obtidos com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get insight by ID
   * @route GET /api/v1/insights/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const insight = await this.insightService.findById(id);

      if (!insight) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Insight não encontrado',
            code: 'INSIGHT_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: insight,
        message: 'Insight obtido com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update insight
   * @route PUT /api/v1/insights/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      // Validate request body
      const validatedData = UpdateInsightSchema.parse(req.body);

      // Update insight
      const insight = await this.insightService.update(id, validatedData);

      res.status(200).json({
        success: true,
        data: insight,
        message: 'Insight atualizado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete insight
   * @route DELETE /api/v1/insights/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      await this.insightService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Insight deletado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Generate insights automatically
   * @route POST /api/v1/insights/generate
   */
  async generateInsights(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = GenerateInsightsSchema.parse(req.body);

      // TODO: Get user ID from authentication middleware
      const userId = 'default'; // This should come from req.user.id

      // Generate insights
      const result = await this.insightService.generateInsights(validatedData, userId);

      res.status(200).json({
        success: true,
        data: result,
        message: `${result.generated} insights gerados com sucesso`
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Bulk actions on insights
   * @route POST /api/v1/insights/bulk-action
   */
  async bulkAction(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = BulkInsightActionSchema.parse(req.body);

      // Perform bulk action
      const result = await this.insightService.bulkAction(validatedData);

      res.status(200).json({
        success: true,
        data: result,
        message: `${result.updated} insights atualizados com sucesso`
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get insight analytics
   * @route GET /api/v1/insights/analytics
   */
  async getAnalytics(req: Request, res: Response): Promise<void> {
    try {
      // TODO: Get user ID from authentication middleware
      const userId = 'default'; // This should come from req.user.id

      const analytics = await this.insightService.getAnalytics(userId);

      res.status(200).json({
        success: true,
        data: analytics,
        message: 'Analytics de insights obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    console.error('InsightController Error:', error);

    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('não encontrado') || error.message.includes('não encontrada')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'NOT_FOUND'
          }
        });
        return;
      }

      if (error.message.includes('já existe') || error.message.includes('duplicado')) {
        res.status(409).json({
          success: false,
          error: {
            message: error.message,
            code: 'CONFLICT'
          }
        });
        return;
      }

      if (error.message.includes('dados insuficientes') || error.message.includes('análise')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'INSUFFICIENT_DATA'
          }
        });
        return;
      }

      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'BAD_REQUEST'
        }
      });
      return;
    }

    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
}

export const insightController = new InsightController();
