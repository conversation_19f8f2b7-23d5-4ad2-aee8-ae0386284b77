import { Request, Response } from 'express';
import { TransactionService } from '../services/transaction.service';
import { DashboardService } from '../services/dashboard.service';
import { futureTransactionJobService } from '../services/future-transaction-job.service';
import { z } from 'zod';

/**
 * Controller for managing future transactions
 */
export class FutureTransactionController {
  private transactionService: TransactionService;
  private dashboardService: DashboardService;

  constructor() {
    this.transactionService = new TransactionService();
    this.dashboardService = new DashboardService();
  }

  /**
   * Get all future transactions
   */
  async getFutureTransactions(req: Request, res: Response): Promise<void> {
    try {
      const { 
        accountId, 
        categoryId, 
        familyMemberId,
        startDate,
        endDate,
        page = 1,
        limit = 20,
        sortBy = 'transactionDate',
        sortOrder = 'asc'
      } = req.query;

      const filters = {
        isFuture: true,
        accountId: accountId as string,
        categoryId: categoryId as string,
        familyMemberId: familyMemberId as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: (sortBy as string) as 'transactionDate' | 'description' | 'amount' | 'createdAt',
        sortOrder: sortOrder as 'asc' | 'desc'
      };

      const result = await this.transactionService.findAll(filters);

      res.status(200).json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('Error fetching future transactions:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch future transactions'
      });
    }
  }

  /**
   * Get projected balance for specific accounts and date range
   */
  async getProjectedBalance(req: Request, res: Response): Promise<void> {
    try {
      const schema = z.object({
        accountIds: z.array(z.string()).optional(),
        currencies: z.array(z.string()).optional(),
        projectionDate: z.string().optional(),
        includeRecurring: z.boolean().optional().default(false)
      });

      const { accountIds, currencies, projectionDate, includeRecurring } = schema.parse(req.query);

      // Build filters for dashboard service
      const filters: any = {};
      
      if (accountIds) {
        filters.accountIds = accountIds;
      }
      
      if (currencies) {
        filters.currencies = currencies;
      }

      if (projectionDate) {
        // Set date range from now to projection date
        filters.dateRange = {
          startDate: new Date().toISOString(),
          endDate: new Date(projectionDate).toISOString()
        };
      }

      // Get current and projected balances
      const balanceData = await this.dashboardService.getAccountBalances(filters);

      res.status(200).json({
        success: true,
        data: {
          currentBalance: balanceData.totalBalance,
          currentBalanceInBRL: balanceData.totalBalanceInBRL,
          projectedBalance: balanceData.projectedBalance,
          projectedBalanceInBRL: balanceData.projectedBalanceInBRL,
          projectionDate: projectionDate || 'indefinite',
          includeRecurring,
          balanceByType: balanceData.balanceByType,
          balanceByCurrency: balanceData.balanceByCurrency
        }
      });

    } catch (error) {
      console.error('Error calculating projected balance:', error);
      
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid query parameters',
          details: error.errors
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: 'Failed to calculate projected balance'
      });
    }
  }

  /**
   * Cancel a future transaction
   */
  async cancelFutureTransaction(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Verify it's a future transaction
      const transaction = await this.transactionService.findById(id);
      
      if (!transaction) {
        res.status(404).json({
          success: false,
          error: 'Transaction not found'
        });
        return;
      }

      if (!transaction.isFuture) {
        res.status(400).json({
          success: false,
          error: 'Cannot cancel a transaction that has already been processed'
        });
        return;
      }

      // Delete the future transaction
      await this.transactionService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Future transaction cancelled successfully'
      });

    } catch (error) {
      console.error('Error cancelling future transaction:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to cancel future transaction'
      });
    }
  }

  /**
   * Get statistics about future transactions
   */
  async getFutureTransactionStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await futureTransactionJobService.getPendingFutureTransactionsStats();

      res.status(200).json({
        success: true,
        data: stats
      });

    } catch (error) {
      console.error('Error fetching future transaction stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch future transaction statistics'
      });
    }
  }

  /**
   * Manually trigger processing of future transactions (admin endpoint)
   */
  async triggerFutureTransactionProcessing(req: Request, res: Response): Promise<void> {
    try {
      const schema = z.object({
        targetDate: z.string().optional()
      });

      const { targetDate } = schema.parse(req.body);
      
      const executionDate = targetDate ? new Date(targetDate) : new Date();
      const stats = await futureTransactionJobService.execute(executionDate);

      res.status(200).json({
        success: true,
        message: 'Future transaction processing completed',
        data: stats
      });

    } catch (error) {
      console.error('Error triggering future transaction processing:', error);
      
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid request body',
          details: error.errors
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: 'Failed to trigger future transaction processing'
      });
    }
  }

  /**
   * Get future transactions due today
   */
  async getFutureTransactionsDueToday(req: Request, res: Response): Promise<void> {
    try {
      const today = new Date();
      const startOfDay = new Date(today);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(today);
      endOfDay.setHours(23, 59, 59, 999);

      const filters = {
        isFuture: true,
        startDate: startOfDay,
        endDate: endOfDay,
        sortBy: 'transactionDate' as const,
        sortOrder: 'asc' as const
      };

      const result = await this.transactionService.findAll(filters);

      res.status(200).json({
        success: true,
        data: {
          ...result,
          dueDate: today.toISOString().split('T')[0]
        }
      });

    } catch (error) {
      console.error('Error fetching future transactions due today:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch future transactions due today'
      });
    }
  }

  /**
   * Update a future transaction
   */
  async updateFutureTransaction(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Verify it's a future transaction
      const existingTransaction = await this.transactionService.findById(id);
      
      if (!existingTransaction) {
        res.status(404).json({
          success: false,
          error: 'Transaction not found'
        });
        return;
      }

      if (!existingTransaction.isFuture) {
        res.status(400).json({
          success: false,
          error: 'Cannot update a transaction that has already been processed'
        });
        return;
      }

      // Ensure the updated transaction remains a future transaction
      const updateData = {
        ...req.body,
        isFuture: true
      };

      const updatedTransaction = await this.transactionService.update(id, updateData);

      res.status(200).json({
        success: true,
        data: updatedTransaction
      });

    } catch (error) {
      console.error('Error updating future transaction:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update future transaction'
      });
    }
  }
}
