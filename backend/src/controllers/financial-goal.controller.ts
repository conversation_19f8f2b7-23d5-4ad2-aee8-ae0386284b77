import { Request, Response } from 'express';
import { z } from 'zod';
import { financialGoalService } from '../services/financial-goal.service';
import {
  CreateFinancialGoalSchema,
  UpdateFinancialGoalSchema,
  UpdateGoalProgressSchema,
  FinancialGoalFiltersSchema
} from '../schemas/financial-goal.schemas';

export class FinancialGoalController {
  /**
   * Create a new financial goal
   * @route POST /api/v1/goals
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = CreateFinancialGoalSchema.parse(req.body);

      // Create goal
      const goal = await financialGoalService.create(validatedData);

      res.status(201).json({
        success: true,
        data: goal,
        message: 'Meta financeira criada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all financial goals with filters and pagination
   * @route GET /api/v1/goals
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const validatedFilters = FinancialGoalFiltersSchema.parse({
        ...req.query,
        // Convert string numbers to actual numbers
        ...(req.query.minTargetAmount && { minTargetAmount: parseFloat(req.query.minTargetAmount as string) }),
        ...(req.query.maxTargetAmount && { maxTargetAmount: parseFloat(req.query.maxTargetAmount as string) }),
        ...(req.query.page && { page: parseInt(req.query.page as string) }),
        ...(req.query.limit && { limit: parseInt(req.query.limit as string) }),
        ...(req.query.includeCompleted && { includeCompleted: req.query.includeCompleted === 'true' }),
        ...(req.query.includeMilestones && { includeMilestones: req.query.includeMilestones === 'true' }),
        // Convert date strings to Date objects
        ...(req.query.targetDateFrom && { targetDateFrom: new Date(req.query.targetDateFrom as string) }),
        ...(req.query.targetDateTo && { targetDateTo: new Date(req.query.targetDateTo as string) })
      });

      // Get goals
      const result = await financialGoalService.findAll(validatedFilters);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        message: 'Metas financeiras recuperadas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get financial goal by ID
   * @route GET /api/v1/goals/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const includeMilestones = req.query.includeMilestones !== 'false'; // Default to true

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID da meta financeira é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Get goal
      const goal = await financialGoalService.findById(id, includeMilestones);

      if (!goal) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Meta financeira não encontrada',
            code: 'GOAL_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: goal,
        message: 'Meta financeira recuperada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update financial goal
   * @route PUT /api/v1/goals/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID da meta financeira é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Validate request body
      const validatedData = UpdateFinancialGoalSchema.parse(req.body);

      // Check if there's data to update
      if (Object.keys(validatedData).length === 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum dado fornecido para atualização',
            code: 'NO_UPDATE_DATA'
          }
        });
        return;
      }

      // Update goal
      const goal = await financialGoalService.update(id, validatedData);

      res.status(200).json({
        success: true,
        data: goal,
        message: 'Meta financeira atualizada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update goal progress
   * @route POST /api/v1/goals/:id/progress
   */
  async updateProgress(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID da meta financeira é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Validate request body
      const validatedData = UpdateGoalProgressSchema.parse(req.body);

      // Update progress
      const result = await financialGoalService.updateProgress(id, validatedData);

      res.status(200).json({
        success: true,
        data: result,
        message: result.message
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete financial goal
   * @route DELETE /api/v1/goals/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID da meta financeira é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      // Delete goal
      await financialGoalService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Meta financeira deletada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get goals summary
   * @route GET /api/v1/goals/summary
   */
  async getSummary(req: Request, res: Response): Promise<void> {
    try {
      const familyMemberId = req.query.familyMemberId as string | undefined;

      // Get summary
      const summary = await financialGoalService.getSummary(familyMemberId);

      res.status(200).json({
        success: true,
        data: summary,
        message: 'Resumo das metas financeiras recuperado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    if (error instanceof Error) {
      // Check for specific business logic errors
      if (error.message.includes('não encontrado') || error.message.includes('não encontrada')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'NOT_FOUND'
          }
        });
        return;
      }

      if (error.message.includes('já existe') || error.message.includes('Já existe')) {
        res.status(409).json({
          success: false,
          error: {
            message: error.message,
            code: 'CONFLICT'
          }
        });
        return;
      }

      if (error.message.includes('arquivado') || error.message.includes('arquivada')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'ARCHIVED_RESOURCE'
          }
        });
        return;
      }

      if (error.message.includes('exceder') || error.message.includes('maior')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'INVALID_AMOUNT'
          }
        });
        return;
      }

      // Generic business logic error
      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'BUSINESS_LOGIC_ERROR'
        }
      });
      return;
    }

    // Generic server error
    console.error('Unexpected error in FinancialGoalController:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
}

export const financialGoalController = new FinancialGoalController();
