import { Request, Response } from 'express';
import { z } from 'zod';
import { goalProgressHistoryService } from '../services/goal-progress-history.service';

// Validation schemas
const ProgressHistoryFiltersSchema = z.object({
  goalId: z.string().cuid('ID da meta inválido').optional(),
  operation: z.enum(['add', 'subtract', 'set']).optional(),
  fromDate: z.string().datetime('Data inicial inválida').optional(),
  toDate: z.string().datetime('Data final inválida').optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.enum(['createdAt', 'amountChanged']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

const UpdateDescriptionSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória').max(500, 'Descrição muito longa')
});

export class GoalProgressHistoryController {
  /**
   * Get all progress history with filters
   * @route GET /api/v1/goals/progress-history
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Parse and validate query parameters
      const queryParams = {
        ...req.query,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 20,
        fromDate: req.query.fromDate ? new Date(req.query.fromDate as string) : undefined,
        toDate: req.query.toDate ? new Date(req.query.toDate as string) : undefined
      };

      const validatedFilters = ProgressHistoryFiltersSchema.parse(queryParams);

      const result = await goalProgressHistoryService.findAll(validatedFilters);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        message: 'Histórico de progresso recuperado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get progress history for a specific goal
   * @route GET /api/v1/goals/:goalId/progress-history
   */
  async findByGoal(req: Request, res: Response): Promise<void> {
    try {
      const { goalId } = req.params;

      if (!goalId || typeof goalId !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID da meta é obrigatório',
            code: 'INVALID_GOAL_ID'
          }
        });
        return;
      }

      // Parse query parameters
      const queryParams = {
        ...req.query,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 20,
        fromDate: req.query.fromDate ? new Date(req.query.fromDate as string) : undefined,
        toDate: req.query.toDate ? new Date(req.query.toDate as string) : undefined
      };

      const validatedFilters = ProgressHistoryFiltersSchema.omit({ goalId: true }).parse(queryParams);

      const result = await goalProgressHistoryService.findByGoal(goalId, validatedFilters);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        message: 'Histórico de progresso da meta recuperado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get recent progress updates
   * @route GET /api/v1/goals/progress-history/recent
   */
  async getRecent(req: Request, res: Response): Promise<void> {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      
      const result = await goalProgressHistoryService.getRecent(limit);

      res.status(200).json({
        success: true,
        data: result,
        message: 'Atualizações recentes de progresso recuperadas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update progress history entry description
   * @route PUT /api/v1/goals/progress-history/:id
   */
  async updateDescription(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do histórico é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      const validatedData = UpdateDescriptionSchema.parse(req.body);

      const result = await goalProgressHistoryService.updateDescription(id, validatedData.description);

      res.status(200).json({
        success: true,
        data: result,
        message: 'Descrição do histórico atualizada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete progress history entry
   * @route DELETE /api/v1/goals/progress-history/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      if (!id || typeof id !== 'string') {
        res.status(400).json({
          success: false,
          error: {
            message: 'ID do histórico é obrigatório',
            code: 'INVALID_ID'
          }
        });
        return;
      }

      await goalProgressHistoryService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Entrada do histórico deletada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors consistently
   */
  private handleError(error: any, res: Response): void {
    console.error('Goal Progress History Controller Error:', error);

    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          details: error.errors,
          code: 'VALIDATION_ERROR'
        }
      });
      return;
    }

    if (error instanceof Error) {
      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'BUSINESS_ERROR'
        }
      });
      return;
    }

    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      }
    });
  }
}

// Export singleton instance
export const goalProgressHistoryController = new GoalProgressHistoryController();
