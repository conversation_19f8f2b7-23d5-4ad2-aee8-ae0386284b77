import { Request, Response } from 'express';
import { categorySuggestionService } from '../services/category-suggestion.service';
import {
  CategorySuggestionRequestSchema,
  BulkCategorySuggestionRequestSchema,
  SuggestionConfigSchema,
  SuggestionFeedbackSchema,
  CategorySuggestionRequest,
  BulkCategorySuggestionRequest,
  SuggestionConfig,
  SuggestionFeedback
} from '../schemas/category-suggestion.schemas';

/**
 * Controller for category suggestion endpoints
 */
export class CategorySuggestionController {
  /**
   * Get category suggestions for a single transaction
   * @route POST /api/v1/category-suggestions
   */
  async getSuggestions(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Validate request body
      const result = CategorySuggestionRequestSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      const request: CategorySuggestionRequest = result.data;
      
      // Get suggestions
      const suggestions = await categorySuggestionService.getSuggestions(request);
      
      const processingTime = Date.now() - startTime;

      res.status(200).json({
        success: true,
        data: {
          suggestions,
          metadata: {
            totalSuggestions: suggestions.length,
            hasHistoricalData: suggestions.length > 0 && suggestions[0].matchType !== 'frequency',
            processingTimeMs: processingTime
          }
        },
        message: 'Sugestões de categoria obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get category suggestions for expense transactions with optimized weights
   * @route POST /api/v1/category-suggestions/expense
   */
  async getSuggestionsForExpense(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    
    try {
      const result = CategorySuggestionRequestSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      const request: CategorySuggestionRequest = result.data;
      const suggestions = await categorySuggestionService.getSuggestionsForExpense(request);
      
      const processingTime = Date.now() - startTime;

      res.status(200).json({
        success: true,
        data: {
          suggestions,
          metadata: {
            totalSuggestions: suggestions.length,
            hasHistoricalData: suggestions.length > 0,
            processingTimeMs: processingTime,
            algorithmWeights: {
              description: 0.5,
              amount: 0.3,
              frequency: 0.1,
              keyword: 0.1
            }
          }
        },
        message: 'Sugestões de categoria para despesa obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get category suggestions for income transactions with optimized weights
   * @route POST /api/v1/category-suggestions/income
   */
  async getSuggestionsForIncome(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    
    try {
      const result = CategorySuggestionRequestSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      const request: CategorySuggestionRequest = result.data;
      const suggestions = await categorySuggestionService.getSuggestionsForIncome(request);
      
      const processingTime = Date.now() - startTime;

      res.status(200).json({
        success: true,
        data: {
          suggestions,
          metadata: {
            totalSuggestions: suggestions.length,
            hasHistoricalData: suggestions.length > 0,
            processingTimeMs: processingTime,
            algorithmWeights: {
              description: 0.6,
              amount: 0.2,
              frequency: 0.1,
              keyword: 0.1
            }
          }
        },
        message: 'Sugestões de categoria para receita obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get category suggestions for multiple transactions in bulk
   * @route POST /api/v1/category-suggestions/bulk
   */
  async getBulkSuggestions(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    
    try {
      const result = BulkCategorySuggestionRequestSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      const request: BulkCategorySuggestionRequest = result.data;
      const results = [];
      let successCount = 0;
      let failCount = 0;
      let totalConfidence = 0;
      let confidenceCount = 0;

      // Process each transaction
      for (const transaction of request.transactions) {
        try {
          const suggestionRequest: CategorySuggestionRequest = {
            description: transaction.description,
            amount: transaction.amount,
            type: transaction.type,
            userId: request.userId,
            maxSuggestions: request.maxSuggestionsPerTransaction,
            minConfidence: request.minConfidence
          };

          const suggestions = await categorySuggestionService.getSuggestions(suggestionRequest);
          
          results.push({
            transactionId: transaction.id,
            transactionDescription: transaction.description,
            suggestions
          });

          successCount++;
          
          // Calculate average confidence
          if (suggestions.length > 0) {
            const avgConfidence = suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length;
            totalConfidence += avgConfidence;
            confidenceCount++;
          }
        } catch (error) {
          results.push({
            transactionId: transaction.id,
            transactionDescription: transaction.description,
            suggestions: [],
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          });
          failCount++;
        }
      }

      const processingTime = Date.now() - startTime;
      const averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : undefined;

      res.status(200).json({
        success: true,
        data: {
          results,
          metadata: {
            totalTransactions: request.transactions.length,
            successfulSuggestions: successCount,
            failedSuggestions: failCount,
            averageConfidence,
            processingTimeMs: processingTime
          }
        },
        message: 'Sugestões em lote processadas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get category suggestions with custom configuration
   * @route POST /api/v1/category-suggestions/custom
   */
  async getSuggestionsWithConfig(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Validate request body structure
      const requestBody = req.body;
      if (!requestBody.transaction || !requestBody.config) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Campos "transaction" e "config" são obrigatórios',
            code: 'MISSING_FIELDS'
          }
        });
        return;
      }

      // Validate transaction data
      const transactionResult = CategorySuggestionRequestSchema.safeParse(requestBody.transaction);
      if (!transactionResult.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados da transação inválidos',
            code: 'VALIDATION_ERROR',
            details: transactionResult.error.errors.map(err => ({
              field: `transaction.${err.path.join('.')}`,
              message: err.message
            }))
          }
        });
        return;
      }

      // Validate config data
      const configResult = SuggestionConfigSchema.safeParse(requestBody.config);
      if (!configResult.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Configuração inválida',
            code: 'VALIDATION_ERROR',
            details: configResult.error.errors.map(err => ({
              field: `config.${err.path.join('.')}`,
              message: err.message
            }))
          }
        });
        return;
      }

      const request: CategorySuggestionRequest = transactionResult.data;
      const config: SuggestionConfig = configResult.data;
      
      const suggestions = await categorySuggestionService.getSuggestions(request, config);
      
      const processingTime = Date.now() - startTime;

      res.status(200).json({
        success: true,
        data: {
          suggestions,
          metadata: {
            totalSuggestions: suggestions.length,
            hasHistoricalData: suggestions.length > 0,
            processingTimeMs: processingTime,
            algorithmWeights: {
              description: config.descriptionWeight,
              amount: config.amountWeight,
              frequency: config.frequencyWeight,
              keyword: config.keywordWeight
            }
          }
        },
        message: 'Sugestões com configuração customizada obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Submit feedback about suggestion quality (for future improvements)
   * @route POST /api/v1/category-suggestions/feedback
   */
  async submitFeedback(req: Request, res: Response): Promise<void> {
    try {
      const result = SuggestionFeedbackSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados de feedback inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      const feedback: SuggestionFeedback = result.data;
      
      // TODO: Store feedback in database for future ML improvements
      // For now, just log it
      console.log('Suggestion feedback received:', {
        transactionId: feedback.transactionId,
        wasAccepted: feedback.wasAccepted,
        confidence: feedback.confidence,
        matchType: feedback.matchType,
        timestamp: feedback.timestamp
      });

      res.status(200).json({
        success: true,
        message: 'Feedback registrado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors consistently
   */
  private handleError(error: unknown, res: Response): void {
    console.error('Category suggestion error:', error);
    
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Erro interno do servidor',
          code: 'INTERNAL_SERVER_ERROR',
          details: process.env.NODE_ENV === 'development' ? error.message : undefined
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: {
          message: 'Erro desconhecido',
          code: 'UNKNOWN_ERROR'
        }
      });
    }
  }
}

// Export singleton instance
export const categorySuggestionController = new CategorySuggestionController();
