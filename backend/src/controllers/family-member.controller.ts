import { Request, Response } from 'express';
import { z } from 'zod';
import { familyMemberService } from '../services/family-member.service';
import {
  CreateFamilyMemberSchema,
  UpdateFamilyMemberSchema,
  FamilyMemberFiltersSchema,
  ArchiveFamilyMemberSchema
} from '../schemas/family-member.schemas';

export class FamilyMemberController {
  /**
   * Create a new family member
   * @route POST /api/v1/family-members
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const validatedData = CreateFamilyMemberSchema.parse(req.body);

      // Create family member
      const familyMember = await familyMemberService.create(validatedData);

      res.status(201).json({
        success: true,
        data: familyMember,
        message: 'Membro da família criado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all family members with filters and pagination
   * @route GET /api/v1/family-members
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const validatedFilters = FamilyMemberFiltersSchema.parse(req.query);

      // Get family members
      const result = await familyMemberService.findAll(validatedFilters);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        message: 'Membros da família obtidos com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get family member by ID
   * @route GET /api/v1/family-members/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const includeArchived = req.query.includeArchived === 'true';

      // Get family member
      const familyMember = await familyMemberService.findById(id, includeArchived);

      if (!familyMember) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Membro da família não encontrado',
            code: 'FAMILY_MEMBER_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: familyMember,
        message: 'Membro da família obtido com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update family member
   * @route PUT /api/v1/family-members/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate request body
      const validatedData = UpdateFamilyMemberSchema.parse(req.body);

      // Check if there's data to update
      if (Object.keys(validatedData).length === 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum dado fornecido para atualização',
            code: 'NO_UPDATE_DATA'
          }
        });
        return;
      }

      // Update family member
      const familyMember = await familyMemberService.update(id, validatedData);

      res.status(200).json({
        success: true,
        data: familyMember,
        message: 'Membro da família atualizado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Archive/Unarchive family member
   * @route PATCH /api/v1/family-members/:id/archive
   */
  async archive(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate request body
      const validatedData = ArchiveFamilyMemberSchema.parse(req.body);

      // Archive/Unarchive family member
      const familyMember = await familyMemberService.archive(id, validatedData.archived);

      const action = validatedData.archived ? 'arquivado' : 'reativado';
      res.status(200).json({
        success: true,
        data: familyMember,
        message: `Membro da família ${action} com sucesso`
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get family members statistics
   * @route GET /api/v1/family-members/stats
   */
  async getStats(req: Request, res: Response): Promise<void> {
    try {
      // Get statistics
      const stats = await familyMemberService.getStats();

      res.status(200).json({
        success: true,
        data: stats,
        message: 'Estatísticas dos membros da família obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Permanently delete family member
   * @route DELETE /api/v1/family-members/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Delete family member
      await familyMemberService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Membro da família deletado permanentemente com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('não encontrado')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'FAMILY_MEMBER_NOT_FOUND'
          }
        });
        return;
      }

      if (error.message.includes('já existe') || error.message.includes('já está')) {
        res.status(409).json({
          success: false,
          error: {
            message: error.message,
            code: 'CONFLICT'
          }
        });
        return;
      }

      if (error.message.includes('não é possível deletar')) {
        res.status(400).json({
          success: false,
          error: {
            message: error.message,
            code: 'DELETE_CONSTRAINT_VIOLATION'
          }
        });
        return;
      }

      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'FAMILY_MEMBER_ERROR'
        }
      });
      return;
    }

    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      }
    });
  }
}

export const familyMemberController = new FamilyMemberController();
