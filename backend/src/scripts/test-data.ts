#!/usr/bin/env ts-node

/**
 * Test Data Management CLI
 * Command-line interface for managing test data
 */

import { Command } from 'commander';
import { TestDataManager } from '../tests/utils/test-data-manager';
import { PrismaClient } from '@prisma/client';

const program = new Command();
const prisma = new PrismaClient();
const testDataManager = new TestDataManager(prisma);

program
  .name('test-data')
  .description('Test Data Management CLI')
  .version('1.0.0');

// Create command
program
  .command('create')
  .description('Create test data')
  .option('-u, --users <number>', 'Number of users to create', '5')
  .option('-a, --accounts <number>', 'Number of accounts per user', '3')
  .option('-t, --transactions <number>', 'Number of transactions per account', '20')
  .option('--no-clean', 'Do not clean existing data')
  .option('-f, --fixtures', 'Use fixtures instead of random data')
  .option('-s, --scenario <name>', 'Use specific test scenario')
  .action(async (options) => {
    try {
      console.log('🌱 Creating test data...');
      
      const result = await testDataManager.createTestData({
        users: parseInt(options.users),
        accountsPerUser: parseInt(options.accounts),
        transactionsPerAccount: parseInt(options.transactions),
        clean: options.clean,
        useFixtures: options.fixtures,
        scenario: options.scenario,
      });

      console.log('\n📊 Test Data Created:');
      console.log(`Users: ${result.stats.totalUsers}`);
      console.log(`Accounts: ${result.stats.totalAccounts}`);
      console.log(`Categories: ${result.stats.totalCategories}`);
      console.log(`Transactions: ${result.stats.totalTransactions}`);
      console.log(`Execution Time: ${result.stats.executionTime}ms`);

    } catch (error) {
      console.error('❌ Error creating test data:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// Clean command
program
  .command('clean')
  .description('Clean all test data')
  .action(async () => {
    try {
      console.log('🧹 Cleaning test data...');
      await testDataManager.cleanAllTestData();
      console.log('✅ Test data cleaned successfully');
    } catch (error) {
      console.error('❌ Error cleaning test data:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// Reset command
program
  .command('reset')
  .description('Reset database to clean state')
  .action(async () => {
    try {
      console.log('🔄 Resetting database...');
      await testDataManager.resetDatabase();
      console.log('✅ Database reset successfully');
    } catch (error) {
      console.error('❌ Error resetting database:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// Stats command
program
  .command('stats')
  .description('Show database statistics')
  .action(async () => {
    try {
      const stats = await testDataManager.getStats();
      
      console.log('\n📊 Database Statistics:');
      console.log(`Users: ${stats.users}`);
      console.log(`Accounts: ${stats.accounts}`);
      console.log(`Categories: ${stats.categories}`);
      console.log(`Transactions: ${stats.transactions}`);
      
    } catch (error) {
      console.error('❌ Error getting stats:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// Scenarios command
program
  .command('scenarios')
  .description('List available test scenarios')
  .action(async () => {
    try {
      const scenarios = testDataManager.getAvailableScenarios();
      
      console.log('\n🎭 Available Test Scenarios:');
      scenarios.forEach(scenario => {
        console.log(`  - ${scenario}`);
      });
      
    } catch (error) {
      console.error('❌ Error listing scenarios:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// Fixtures command
program
  .command('fixtures')
  .description('List available fixtures')
  .action(async () => {
    try {
      const fixtures = testDataManager.getAvailableFixtures();
      
      console.log('\n📋 Available Fixtures:');
      console.log('\nUsers:');
      fixtures.users.forEach(fixture => console.log(`  - ${fixture}`));
      
      console.log('\nAccounts:');
      fixtures.accounts.forEach(fixture => console.log(`  - ${fixture}`));
      
      console.log('\nCategories:');
      fixtures.categories.forEach(fixture => console.log(`  - ${fixture}`));
      
      console.log('\nTransactions:');
      fixtures.transactions.forEach(fixture => console.log(`  - ${fixture}`));
      
      console.log('\nScenarios:');
      fixtures.scenarios.forEach(fixture => console.log(`  - ${fixture}`));
      
    } catch (error) {
      console.error('❌ Error listing fixtures:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// Minimal command
program
  .command('minimal')
  .description('Create minimal test dataset for quick tests')
  .action(async () => {
    try {
      console.log('⚡ Creating minimal test data...');
      
      const result = await testDataManager.createMinimalTestData();
      
      console.log('\n📊 Minimal Test Data Created:');
      console.log(`Users: ${result.stats.totalUsers}`);
      console.log(`Accounts: ${result.stats.totalAccounts}`);
      console.log(`Categories: ${result.stats.totalCategories}`);
      console.log(`Transactions: ${result.stats.totalTransactions}`);
      
    } catch (error) {
      console.error('❌ Error creating minimal test data:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// Comprehensive command
program
  .command('comprehensive')
  .description('Create comprehensive test dataset')
  .action(async () => {
    try {
      console.log('🏗️ Creating comprehensive test data...');
      
      const result = await testDataManager.createComprehensiveTestData();
      
      console.log('\n📊 Comprehensive Test Data Created:');
      console.log(`Users: ${result.stats.totalUsers}`);
      console.log(`Accounts: ${result.stats.totalAccounts}`);
      console.log(`Categories: ${result.stats.totalCategories}`);
      console.log(`Transactions: ${result.stats.totalTransactions}`);
      
    } catch (error) {
      console.error('❌ Error creating comprehensive test data:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// User command
program
  .command('user <userId>')
  .description('Create test data for specific user')
  .action(async (userId) => {
    try {
      console.log(`👤 Creating test data for user: ${userId}`);
      
      const result = await testDataManager.createUserTestData(userId);
      
      console.log('\n📊 User Test Data Created:');
      console.log(`Accounts: ${result.accounts.length}`);
      console.log(`Categories: ${result.categories.length}`);
      console.log(`Transactions: ${result.transactions.length}`);
      
    } catch (error) {
      console.error('❌ Error creating user test data:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// Handle cleanup on exit
process.on('SIGINT', async () => {
  console.log('\n🔌 Disconnecting from database...');
  await testDataManager.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🔌 Disconnecting from database...');
  await testDataManager.disconnect();
  process.exit(0);
});

// Parse command line arguments
program.parse(process.argv);

// If no command provided, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
