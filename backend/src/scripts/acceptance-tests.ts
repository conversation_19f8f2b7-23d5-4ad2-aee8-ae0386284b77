#!/usr/bin/env ts-node

/**
 * Testes de Aceitação para Funcionalidades Avançadas
 * Valida integração entre todos os serviços implementados
 */

import { PrismaClient } from '@prisma/client';
import { TransactionService } from '../services/transaction.service';
import { InstallmentService } from '../services/installment.service';
import { CurrencyService } from '../services/currency.service';
import { FutureTransactionJobService } from '../services/future-transaction-job.service';

const prisma = new PrismaClient();

interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  duration: number;
}

class AcceptanceTests {
  private transactionService = new TransactionService();
  private installmentService = new InstallmentService();
  private currencyService = new CurrencyService();
  private jobService = new FutureTransactionJobService();
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🧪 Iniciando Testes de Aceitação...\n');

    const tests = [
      { name: 'Conversão de Moedas', fn: this.testCurrencyConversion },
      { name: 'Transferência Simples', fn: this.testSimpleTransfer },
      { name: 'Transferência com Conversão', fn: this.testTransferWithConversion },
      { name: 'Transação Parcelada', fn: this.testInstallmentTransaction },
      { name: 'Atualização de Parcelas', fn: this.testInstallmentUpdate },
      { name: 'Transações Futuras', fn: this.testFutureTransactions },
      { name: 'Processamento em Lote', fn: this.testBatchProcessing },
      { name: 'Validações de Negócio', fn: this.testBusinessValidations },
      { name: 'Integração Completa', fn: this.testFullIntegration }
    ];

    for (const test of tests) {
      await this.runTest(test.name, test.fn.bind(this));
    }

    this.printResults();
  }

  private async runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Executando: ${testName}`);
      await testFn();
      
      const duration = Date.now() - startTime;
      this.results.push({ testName, passed: true, duration });
      console.log(`✅ ${testName} - ${duration}ms\n`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({ 
        testName, 
        passed: false, 
        error: error instanceof Error ? error.message : String(error),
        duration 
      });
      console.log(`❌ ${testName} - ${error}\n`);
    }
  }

  private async testCurrencyConversion(): Promise<void> {
    // Teste 1: Conversão com taxa manual
    const result1 = await this.currencyService.convertAmount({
      amount: 1000,
      fromCurrency: 'USD',
      toCurrency: 'BRL',
      manualRate: 5.25
    });

    if (result1.convertedAmount !== 5250) {
      throw new Error(`Conversão manual incorreta: esperado 5250, recebido ${result1.convertedAmount}`);
    }

    // Teste 2: Conversão mesma moeda
    const result2 = await this.currencyService.convertAmount({
      amount: 1000,
      fromCurrency: 'BRL',
      toCurrency: 'BRL'
    });

    if (result2.exchangeRate !== 1.0) {
      throw new Error(`Taxa para mesma moeda deve ser 1.0, recebido ${result2.exchangeRate}`);
    }

    // Teste 3: Formatação de moeda
    const formatted = this.currencyService.formatCurrency(1234.56, 'BRL');
    if (!formatted.includes('1.234,56')) {
      throw new Error(`Formatação BRL incorreta: ${formatted}`);
    }
  }

  private async testSimpleTransfer(): Promise<void> {
    // Buscar contas de teste
    const accounts = await prisma.account.findMany({
      where: { currency: 'BRL' },
      take: 2
    });

    if (accounts.length < 2) {
      throw new Error('Necessário pelo menos 2 contas BRL para teste');
    }

    const [sourceAccount, destAccount] = accounts;
    const initialSourceBalance = sourceAccount.currentBalance;
    const initialDestBalance = destAccount.currentBalance;

    // Criar transferência
    const transfer = await this.transactionService.create({
      description: 'Teste transferência simples',
      amount: 100.00,
      type: 'TRANSFER',
      accountId: sourceAccount.id,
      destinationAccountId: destAccount.id,
      familyMemberIds: [],
      isFuture: false
    });

    // Verificar se a transferência foi criada
    if (!transfer.id) {
      throw new Error('Transferência não foi criada');
    }

    // Verificar saldos atualizados
    const updatedSource = await prisma.account.findUnique({
      where: { id: sourceAccount.id }
    });
    const updatedDest = await prisma.account.findUnique({
      where: { id: destAccount.id }
    });

    if (!updatedSource || !updatedDest) {
      throw new Error('Contas não encontradas após transferência');
    }

    const expectedSourceBalance = initialSourceBalance - 100;
    const expectedDestBalance = initialDestBalance + 100;

    if (Math.abs(updatedSource.currentBalance - expectedSourceBalance) > 0.01) {
      throw new Error(`Saldo origem incorreto: esperado ${expectedSourceBalance}, atual ${updatedSource.currentBalance}`);
    }

    if (Math.abs(updatedDest.currentBalance - expectedDestBalance) > 0.01) {
      throw new Error(`Saldo destino incorreto: esperado ${expectedDestBalance}, atual ${updatedDest.currentBalance}`);
    }
  }

  private async testTransferWithConversion(): Promise<void> {
    // Buscar contas USD e BRL
    const usdAccount = await prisma.account.findFirst({
      where: { currency: 'USD' }
    });
    const brlAccount = await prisma.account.findFirst({
      where: { currency: 'BRL' }
    });

    if (!usdAccount || !brlAccount) {
      throw new Error('Necessário contas USD e BRL para teste');
    }

    // Criar transferência com conversão
    const transfer = await this.transactionService.create({
      description: 'Teste conversão USD→BRL',
      amount: 100.00,
      type: 'TRANSFER',
      accountId: usdAccount.id,
      destinationAccountId: brlAccount.id,
      exchangeRate: 5.25,
      sourceCurrency: 'USD',
      destinationCurrency: 'BRL',
      sourceAmount: 100.00,
      destinationAmount: 525.00,
      familyMemberIds: [],
      isFuture: false
    });

    // Verificar campos de conversão
    if (transfer.exchangeRate !== 5.25) {
      throw new Error(`Taxa de câmbio incorreta: ${transfer.exchangeRate}`);
    }

    if (transfer.sourceAmount !== 100.00) {
      throw new Error(`Valor origem incorreto: ${transfer.sourceAmount}`);
    }

    if (transfer.destinationAmount !== 525.00) {
      throw new Error(`Valor destino incorreto: ${transfer.destinationAmount}`);
    }
  }

  private async testInstallmentTransaction(): Promise<void> {
    // Buscar conta de crédito
    const creditAccount = await prisma.account.findFirst({
      where: { type: 'CREDIT_CARD' }
    });

    if (!creditAccount) {
      throw new Error('Necessário conta de crédito para teste');
    }

    // Criar transação parcelada
    const result = await this.installmentService.createInstallmentTransaction({
      description: 'Teste parcelamento 6x',
      totalAmount: 600.00,
      installments: 6,
      accountId: creditAccount.id,
      familyMemberIds: []
    });

    // Verificar resultado
    if (!result.parentTransaction) {
      throw new Error('Transação principal não criada');
    }

    if (result.installmentTransactions.length !== 6) {
      throw new Error(`Esperado 6 parcelas, criadas ${result.installmentTransactions.length}`);
    }

    // Verificar valores das parcelas
    const totalParcelas = result.installmentTransactions.reduce(
      (sum, installment) => sum + installment.amount, 0
    );

    if (Math.abs(totalParcelas - 600.00) > 0.01) {
      throw new Error(`Soma das parcelas incorreta: ${totalParcelas}`);
    }

    // Verificar resumo
    const summary = await this.installmentService.getInstallmentSummary(
      result.parentTransaction.id
    );

    if (summary.totalInstallments !== 6) {
      throw new Error(`Total de parcelas incorreto no resumo: ${summary.totalInstallments}`);
    }
  }

  private async testInstallmentUpdate(): Promise<void> {
    // Buscar transação parcelada existente
    const parentTransaction = await prisma.transaction.findFirst({
      where: { 
        totalInstallments: { gt: 0 },
        parentTransactionId: null
      }
    });

    if (!parentTransaction) {
      throw new Error('Nenhuma transação parcelada encontrada para teste');
    }

    // Atualizar parcelas futuras
    const updateResult = await this.installmentService.updateFutureInstallments(
      parentTransaction.id,
      { newAmount: 150.00 }
    );

    if (updateResult.updatedCount === 0) {
      throw new Error('Nenhuma parcela foi atualizada');
    }

    // Verificar se as parcelas foram atualizadas
    const updatedInstallments = await prisma.transaction.findMany({
      where: {
        parentTransactionId: parentTransaction.id,
        isFuture: true
      }
    });

    const hasUpdatedAmount = updatedInstallments.some(
      installment => installment.amount === 150.00
    );

    if (!hasUpdatedAmount) {
      throw new Error('Parcelas não foram atualizadas com novo valor');
    }
  }

  private async testFutureTransactions(): Promise<void> {
    // Buscar conta para teste
    const account = await prisma.account.findFirst({
      where: { currency: 'BRL' }
    });

    if (!account) {
      throw new Error('Nenhuma conta BRL encontrada');
    }

    // Criar transação futura
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() - 1); // 1 dia no passado para teste

    const futureTransaction = await this.transactionService.create({
      description: 'Teste transação futura',
      amount: 50.00,
      type: 'EXPENSE',
      accountId: account.id,
      transactionDate: futureDate,
      familyMemberIds: [],
      isFuture: true
    });

    if (!futureTransaction.isFuture) {
      throw new Error('Transação não marcada como futura');
    }

    // Verificar que o saldo não foi alterado
    const accountAfter = await prisma.account.findUnique({
      where: { id: account.id }
    });

    if (!accountAfter || accountAfter.currentBalance !== account.currentBalance) {
      throw new Error('Saldo foi alterado para transação futura');
    }
  }

  private async testBatchProcessing(): Promise<void> {
    // Executar job de processamento
    const result = await this.jobService.execute();

    if (typeof result.processed !== 'number') {
      throw new Error('Resultado do job inválido');
    }

    if (typeof result.failed !== 'number') {
      throw new Error('Contagem de falhas inválida');
    }

    if (typeof result.skipped !== 'number') {
      throw new Error('Contagem de ignorados inválida');
    }

    console.log(`   📊 Processadas: ${result.processed}, Falhas: ${result.failed}, Ignoradas: ${result.skipped}`);
  }

  private async testBusinessValidations(): Promise<void> {
    // Buscar conta para teste
    const account = await prisma.account.findFirst();
    if (!account) {
      throw new Error('Nenhuma conta encontrada');
    }

    // Teste 1: Transferência para mesma conta (deve falhar)
    try {
      await this.transactionService.create({
        description: 'Transferência inválida',
        amount: 100.00,
        type: 'TRANSFER',
        accountId: account.id,
        destinationAccountId: account.id,
        familyMemberIds: [],
        isFuture: false
      });
      throw new Error('Transferência para mesma conta deveria falhar');
    } catch (error) {
      if (!error.message.includes('same account')) {
        throw new Error(`Erro inesperado: ${error.message}`);
      }
    }

    // Teste 2: Valor negativo (deve falhar)
    try {
      await this.currencyService.convertAmount({
        amount: -100,
        fromCurrency: 'USD',
        toCurrency: 'BRL'
      });
      throw new Error('Conversão com valor negativo deveria falhar');
    } catch (error) {
      if (!error.message.includes('positive')) {
        throw new Error(`Erro inesperado: ${error.message}`);
      }
    }
  }

  private async testFullIntegration(): Promise<void> {
    // Teste completo: criar parcelamento com conversão de moeda
    const usdAccount = await prisma.account.findFirst({
      where: { currency: 'USD' }
    });
    const creditCard = await prisma.account.findFirst({
      where: { type: 'CREDIT_CARD' }
    });

    if (!usdAccount || !creditCard) {
      throw new Error('Contas necessárias não encontradas');
    }

    // 1. Converter moeda
    const conversion = await this.currencyService.convertAmount({
      amount: 500,
      fromCurrency: 'USD',
      toCurrency: 'BRL',
      manualRate: 5.20
    });

    // 2. Criar parcelamento com valor convertido
    const installmentResult = await this.installmentService.createInstallmentTransaction({
      description: 'Compra internacional parcelada',
      totalAmount: conversion.convertedAmount,
      installments: 10,
      accountId: creditCard.id,
      familyMemberIds: []
    });

    // 3. Verificar integração
    if (installmentResult.installmentTransactions.length !== 10) {
      throw new Error('Parcelamento não criado corretamente');
    }

    const totalParcelas = installmentResult.installmentTransactions.reduce(
      (sum, installment) => sum + installment.amount, 0
    );

    if (Math.abs(totalParcelas - conversion.convertedAmount) > 0.01) {
      throw new Error('Valor total das parcelas não confere com conversão');
    }

    console.log(`   💱 Conversão: $${conversion.originalAmount} → R$${conversion.convertedAmount}`);
    console.log(`   💳 Parcelado em ${installmentResult.installmentTransactions.length}x`);
  }

  private printResults(): void {
    console.log('\n📊 RESULTADOS DOS TESTES DE ACEITAÇÃO\n');
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => r.passed === false).length;
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`✅ Passou: ${passed}`);
    console.log(`❌ Falhou: ${failed}`);
    console.log(`⏱️  Tempo total: ${totalTime}ms`);
    console.log(`📈 Taxa de sucesso: ${((passed / this.results.length) * 100).toFixed(1)}%\n`);

    if (failed > 0) {
      console.log('❌ TESTES QUE FALHARAM:\n');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`   • ${r.testName}: ${r.error}`);
        });
      console.log('');
    }

    if (passed === this.results.length) {
      console.log('🎉 TODOS OS TESTES PASSARAM! Sistema pronto para produção.\n');
    } else {
      console.log('⚠️  Alguns testes falharam. Revisar antes de produção.\n');
    }
  }
}

// Executar testes se chamado diretamente
if (require.main === module) {
  const tests = new AcceptanceTests();
  tests.runAllTests()
    .then(() => {
      console.log('✅ Testes de aceitação concluídos');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erro nos testes:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

export { AcceptanceTests };
