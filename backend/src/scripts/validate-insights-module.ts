#!/usr/bin/env tsx

/**
 * Script de validação do módulo de insights
 * Verifica se todos os componentes estão funcionando corretamente
 */

import { InsightService } from '../services/insight.service';
import { insightJobService } from '../services/insight-job.service';
import { insightCacheService } from '../services/insight-cache.service';
import { redisCache } from '../lib/redis';
import prisma from '../lib/prisma';
import {
  calculateCategorySpending,
  calculateMonthlyTrends,
  detectSpendingAnomalies
} from '../utils/financial-analysis.utils';
import {
  calculateStatisticalSummary,
  generateForecast
} from '../utils/statistical-analysis.utils';

interface ValidationResult {
  component: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

class InsightsModuleValidator {
  private results: ValidationResult[] = [];

  /**
   * Executa todas as validações
   */
  async validateAll(): Promise<void> {
    console.log('🔍 Iniciando validação do módulo de insights...\n');

    await this.validateDatabase();
    await this.validateCache();
    await this.validateUtilities();
    await this.validateServices();
    await this.validateJobService();
    await this.validateSchemas();

    this.printResults();
  }

  /**
   * Valida conexão com banco de dados
   */
  private async validateDatabase(): Promise<void> {
    try {
      await prisma.$connect();
      
      // Verifica se a tabela insights existe
      const insightCount = await prisma.insight.count();
      
      this.addResult({
        component: 'Database Connection',
        status: 'PASS',
        message: 'Conexão com banco de dados estabelecida',
        details: { insightCount }
      });

      // Verifica se o modelo Insight está funcionando
      const testInsight = {
        type: 'SPENDING_PATTERN' as const,
        priority: 'LOW' as const,
        title: 'Teste de validação',
        description: 'Insight criado para validação do módulo'
      };

      const created = await prisma.insight.create({
        data: testInsight
      });

      await prisma.insight.delete({
        where: { id: created.id }
      });

      this.addResult({
        component: 'Insight Model',
        status: 'PASS',
        message: 'Modelo Insight funcionando corretamente'
      });

    } catch (error) {
      this.addResult({
        component: 'Database',
        status: 'FAIL',
        message: `Erro na validação do banco: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      });
    }
  }

  /**
   * Valida sistema de cache
   */
  private async validateCache(): Promise<void> {
    try {
      const isAvailable = redisCache.isAvailable();
      
      if (!isAvailable) {
        this.addResult({
          component: 'Redis Cache',
          status: 'WARNING',
          message: 'Redis não está disponível - cache desabilitado'
        });
        return;
      }

      // Testa operações básicas de cache
      const testKey = 'insights:validation:test';
      const testData = { test: 'validation', timestamp: Date.now() };

      await redisCache.set(testKey, testData, 60);
      const retrieved = await redisCache.get(testKey);
      await redisCache.del(testKey);

      if (JSON.stringify(retrieved) === JSON.stringify(testData)) {
        this.addResult({
          component: 'Redis Cache',
          status: 'PASS',
          message: 'Sistema de cache funcionando corretamente'
        });
      } else {
        this.addResult({
          component: 'Redis Cache',
          status: 'FAIL',
          message: 'Erro na recuperação de dados do cache'
        });
      }

      // Testa cache service específico
      const mockInsight = {
        id: 'test-insight',
        type: 'SPENDING_PATTERN',
        priority: 'MEDIUM',
        status: 'NEW',
        title: 'Test',
        description: 'Test',
        actionTaken: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: 1
      };

      await insightCacheService.cacheInsight(mockInsight as any);
      const cachedInsight = await insightCacheService.getCachedInsight('test-insight');

      if (cachedInsight?.id === 'test-insight') {
        this.addResult({
          component: 'Insight Cache Service',
          status: 'PASS',
          message: 'InsightCacheService funcionando corretamente'
        });
      } else {
        this.addResult({
          component: 'Insight Cache Service',
          status: 'FAIL',
          message: 'Erro no InsightCacheService'
        });
      }

    } catch (error) {
      this.addResult({
        component: 'Cache System',
        status: 'FAIL',
        message: `Erro na validação do cache: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      });
    }
  }

  /**
   * Valida utilitários de análise
   */
  private async validateUtilities(): Promise<void> {
    try {
      // Testa análise financeira
      const sampleTransactions = [
        {
          id: '1',
          amount: 100,
          date: new Date('2024-01-15'),
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE' as const,
          description: 'Supermercado'
        },
        {
          id: '2',
          amount: 200,
          date: new Date('2024-01-20'),
          categoryId: 'cat2',
          categoryName: 'Transporte',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE' as const,
          description: 'Combustível'
        }
      ];

      const categorySpending = calculateCategorySpending(sampleTransactions);
      const monthlyTrends = calculateMonthlyTrends(sampleTransactions);
      const anomalies = detectSpendingAnomalies(sampleTransactions);

      this.addResult({
        component: 'Financial Analysis Utils',
        status: 'PASS',
        message: 'Utilitários de análise financeira funcionando',
        details: {
          categoriesAnalyzed: categorySpending.length,
          trendsCalculated: monthlyTrends.length,
          anomaliesDetected: anomalies.length
        }
      });

      // Testa análise estatística
      const sampleData = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      const statistics = calculateStatisticalSummary(sampleData);
      const forecast = generateForecast(sampleData, 3);

      this.addResult({
        component: 'Statistical Analysis Utils',
        status: 'PASS',
        message: 'Utilitários de análise estatística funcionando',
        details: {
          mean: statistics.mean,
          forecastPoints: forecast.predictions.length,
          trend: forecast.trend
        }
      });

    } catch (error) {
      this.addResult({
        component: 'Analysis Utils',
        status: 'FAIL',
        message: `Erro na validação dos utilitários: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      });
    }
  }

  /**
   * Valida serviços principais
   */
  private async validateServices(): Promise<void> {
    try {
      const insightService = new InsightService();

      // Testa criação de insight
      const testInsight = await insightService.create({
        type: 'SPENDING_PATTERN',
        priority: 'LOW',
        title: 'Insight de validação',
        description: 'Criado durante validação do módulo',
        relevanceScore: 5.0
      });

      // Testa busca por ID
      const foundInsight = await insightService.findById(testInsight.id);

      // Testa atualização
      const updatedInsight = await insightService.update(testInsight.id, {
        status: 'VIEWED'
      });

      // Testa analytics
      const analytics = await insightService.getAnalytics();

      // Limpa dados de teste
      await insightService.delete(testInsight.id);

      this.addResult({
        component: 'Insight Service',
        status: 'PASS',
        message: 'InsightService funcionando corretamente',
        details: {
          created: !!testInsight.id,
          found: !!foundInsight,
          updated: updatedInsight.status === 'VIEWED',
          analytics: !!analytics.totalInsights
        }
      });

    } catch (error) {
      this.addResult({
        component: 'Insight Service',
        status: 'FAIL',
        message: `Erro na validação do InsightService: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      });
    }
  }

  /**
   * Valida job service
   */
  private async validateJobService(): Promise<void> {
    try {
      // Testa geração manual de insights
      const result = await insightJobService.generateInsightsOnDemand('validation-user');

      // Testa estatísticas do job
      const stats = await insightJobService.getJobStatistics();

      this.addResult({
        component: 'Insight Job Service',
        status: 'PASS',
        message: 'InsightJobService funcionando corretamente',
        details: {
          manualGeneration: result.success,
          statistics: !!stats.totalInsights
        }
      });

    } catch (error) {
      this.addResult({
        component: 'Insight Job Service',
        status: 'FAIL',
        message: `Erro na validação do InsightJobService: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      });
    }
  }

  /**
   * Valida schemas Zod
   */
  private async validateSchemas(): Promise<void> {
    try {
      const { CreateInsightSchema, UpdateInsightSchema, InsightFiltersSchema } = 
        await import('../schemas/insight.schemas');

      // Testa schema de criação
      const validCreateData = {
        type: 'SPENDING_PATTERN',
        priority: 'HIGH',
        title: 'Teste de schema',
        description: 'Validação do schema de criação'
      };

      const parsedCreate = CreateInsightSchema.parse(validCreateData);

      // Testa schema de atualização
      const validUpdateData = {
        status: 'VIEWED',
        actionTaken: true
      };

      const parsedUpdate = UpdateInsightSchema.parse(validUpdateData);

      // Testa schema de filtros
      const validFilters = {
        type: 'SPENDING_PATTERN',
        priority: 'HIGH',
        page: '1',
        limit: '20'
      };

      const parsedFilters = InsightFiltersSchema.parse(validFilters);

      this.addResult({
        component: 'Zod Schemas',
        status: 'PASS',
        message: 'Schemas de validação funcionando corretamente',
        details: {
          createSchema: !!parsedCreate,
          updateSchema: !!parsedUpdate,
          filtersSchema: !!parsedFilters
        }
      });

    } catch (error) {
      this.addResult({
        component: 'Zod Schemas',
        status: 'FAIL',
        message: `Erro na validação dos schemas: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      });
    }
  }

  /**
   * Adiciona resultado de validação
   */
  private addResult(result: ValidationResult): void {
    this.results.push(result);
  }

  /**
   * Imprime resultados da validação
   */
  private printResults(): void {
    console.log('\n📊 Resultados da Validação:\n');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;

    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : 
                   result.status === 'FAIL' ? '❌' : '⚠️';
      
      console.log(`${icon} ${result.component}: ${result.message}`);
      
      if (result.details) {
        console.log(`   Detalhes: ${JSON.stringify(result.details, null, 2)}`);
      }
    });

    console.log(`\n📈 Resumo: ${passed} passou, ${failed} falhou, ${warnings} avisos`);
    
    if (failed === 0) {
      console.log('\n🎉 Módulo de insights validado com sucesso!');
    } else {
      console.log('\n⚠️  Alguns componentes falharam na validação. Verifique os erros acima.');
      process.exit(1);
    }
  }
}

// Executa validação se chamado diretamente
if (require.main === module) {
  const validator = new InsightsModuleValidator();
  validator.validateAll()
    .catch(error => {
      console.error('❌ Erro durante validação:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

export { InsightsModuleValidator };
