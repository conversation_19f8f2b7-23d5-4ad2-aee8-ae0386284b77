import { familyMemberService } from '../services/family-member.service';

async function testFamilyMemberModule() {
  console.log('🧪 Testing Family Member Module...\n');

  try {
    // Test 1: Create family members
    console.log('1. Testing family member creation...');
    
    const member1 = await familyMemberService.create({
      name: '<PERSON>',
      color: '#3B82F6'
    });
    console.log('✅ Created member 1:', member1.name);

    const member2 = await familyMemberService.create({
      name: '<PERSON>',
      color: '#EF4444'
    });
    console.log('✅ Created member 2:', member2.name);

    // Test 2: Try to create duplicate name (should fail)
    console.log('\n2. Testing duplicate name validation...');
    try {
      await familyMemberService.create({
        name: '<PERSON>',
        color: '#10B981'
      });
      console.log('❌ Should have failed for duplicate name');
    } catch (error) {
      console.log('✅ Correctly rejected duplicate name:', (error as Error).message);
    }

    // Test 3: Try to create duplicate color (should fail)
    console.log('\n3. Testing duplicate color validation...');
    try {
      await familyMemberService.create({
        name: '<PERSON>',
        color: '#3B82F6'
      });
      console.log('❌ Should have failed for duplicate color');
    } catch (error) {
      console.log('✅ Correctly rejected duplicate color:', (error as Error).message);
    }

    // Test 4: List all family members
    console.log('\n4. Testing family member listing...');
    const allMembers = await familyMemberService.findAll();
    console.log('✅ Found', allMembers.data.length, 'family members');
    console.log('   Pagination:', allMembers.pagination);

    // Test 5: Get family member by ID
    console.log('\n5. Testing get family member by ID...');
    const foundMember = await familyMemberService.findById(member1.id);
    console.log('✅ Found member by ID:', foundMember?.name);

    // Test 6: Update family member
    console.log('\n6. Testing family member update...');
    const updatedMember = await familyMemberService.update(member1.id, {
      name: 'João Silva Santos'
    });
    console.log('✅ Updated member name:', updatedMember.name);
    console.log('   Version incremented:', updatedMember.version);

    // Test 7: Archive family member
    console.log('\n7. Testing family member archiving...');
    const archivedMember = await familyMemberService.archive(member2.id, true);
    console.log('✅ Archived member:', archivedMember.name, '- Archived:', archivedMember.archived);

    // Test 8: List without archived
    console.log('\n8. Testing listing without archived members...');
    const activeMembers = await familyMemberService.findAll({ includeArchived: false });
    console.log('✅ Active members count:', activeMembers.data.length);

    // Test 9: List with archived
    console.log('\n9. Testing listing with archived members...');
    const allMembersIncArchived = await familyMemberService.findAll({ includeArchived: true });
    console.log('✅ All members count (including archived):', allMembersIncArchived.data.length);

    // Test 10: Unarchive family member
    console.log('\n10. Testing family member unarchiving...');
    const unarchivedMember = await familyMemberService.archive(member2.id, false);
    console.log('✅ Unarchived member:', unarchivedMember.name, '- Archived:', unarchivedMember.archived);

    // Test 11: Search by name
    console.log('\n11. Testing search by name...');
    const searchResults = await familyMemberService.findAll({ name: 'João' });
    console.log('✅ Search results for "João":', searchResults.data.length, 'members');

    // Test 12: Pagination
    console.log('\n12. Testing pagination...');
    const paginatedResults = await familyMemberService.findAll({ page: 1, limit: 1 });
    console.log('✅ Paginated results (page 1, limit 1):', paginatedResults.data.length, 'members');
    console.log('   Total pages:', paginatedResults.pagination.totalPages);

    console.log('\n🎉 All Family Member Module tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testFamilyMemberModule()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

export { testFamilyMemberModule };
