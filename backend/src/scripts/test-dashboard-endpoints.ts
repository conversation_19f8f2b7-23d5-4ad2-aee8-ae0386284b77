import { DashboardController } from '../controllers/dashboard.controller';

/**
 * Simple script to test dashboard endpoints functionality
 */
async function testDashboardEndpoints() {
  console.log('🧪 Testing Dashboard Endpoints...\n');

  const controller = new DashboardController();

  // Mock request and response objects
  const mockReq = {
    query: {}
  } as any;

  const mockRes = {
    json: (data: any) => {
      console.log('Response:', JSON.stringify(data, null, 2));
      return mockRes;
    },
    status: (code: number) => {
      console.log(`Status: ${code}`);
      return mockRes;
    }
  } as any;

  try {
    console.log('1. Testing getAccountBalances...');
    await controller.getAccountBalances(mockReq, mockRes);
    console.log('✅ getAccountBalances works\n');

    console.log('2. Testing getNetWorth...');
    await controller.getNetWorth(mockReq, mockRes);
    console.log('✅ getNetWorth works\n');

    console.log('3. Testing getCreditCardUsage...');
    await controller.getCreditCardUsage(mockReq, mockRes);
    console.log('✅ getCreditCardUsage works\n');

    console.log('4. Testing getExpensesByCategory...');
    await controller.getExpensesByCategory(mockReq, mockRes);
    console.log('✅ getExpensesByCategory works\n');

    console.log('5. Testing getExpensesByMember...');
    await controller.getExpensesByMember(mockReq, mockRes);
    console.log('✅ getExpensesByMember works\n');

    console.log('6. Testing getBudgetComparison...');
    await controller.getBudgetComparison(mockReq, mockRes);
    console.log('✅ getBudgetComparison works\n');

    console.log('7. Testing getGoalProgress...');
    await controller.getGoalProgress(mockReq, mockRes);
    console.log('✅ getGoalProgress works\n');

    console.log('8. Testing getOverview...');
    await controller.getOverview(mockReq, mockRes);
    console.log('✅ getOverview works\n');

    console.log('9. Testing getPerformanceMetrics...');
    await controller.getPerformanceMetrics(mockReq, mockRes);
    console.log('✅ getPerformanceMetrics works\n');

    console.log('🎉 All dashboard endpoints are working!');

  } catch (error) {
    console.error('❌ Error testing endpoints:', error);
  }
}

// Run the test
if (require.main === module) {
  testDashboardEndpoints()
    .catch(console.error);
}

export { testDashboardEndpoints };
