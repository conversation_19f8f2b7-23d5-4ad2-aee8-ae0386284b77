import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function createGoalsTestData() {
  console.log('🎯 Criando dados de teste para metas financeiras...')

  try {
    // Criar algumas metas financeiras
    const goals = await Promise.all([
      prisma.goal.create({
        data: {
          name: 'Viagem para Europa',
          targetAmount: 15000.00,
          currentAmount: 3500.00,
          targetDate: new Date('2025-06-01')
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Reserva de Emergência',
          targetAmount: 30000.00,
          currentAmount: 12000.00,
          targetDate: new Date('2025-12-31')
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Carro Novo',
          targetAmount: 45000.00,
          currentAmount: 8500.00,
          targetDate: new Date('2026-03-01')
        }
      }),
      prisma.goal.create({
        data: {
          name: 'Curso de Especialização',
          targetAmount: 5000.00,
          currentAmount: 1200.00,
          targetDate: new Date('2025-02-01')
        }
      })
    ])

    console.log('✅ Metas financeiras criadas:', goals.length)

    // Criar histórico de progresso para as metas
    const progressHistory = []
    
    // Histórico para Viagem para Europa
    const europeGoal = goals[0]
    progressHistory.push(
      prisma.goalProgressHistory.create({
        data: {
          goalId: europeGoal.id,
          previousAmount: 0,
          newAmount: 1000,
          amountChanged: 1000,
          operation: 'add',
          description: 'Depósito inicial para a viagem'
        }
      }),
      prisma.goalProgressHistory.create({
        data: {
          goalId: europeGoal.id,
          previousAmount: 1000,
          newAmount: 2500,
          amountChanged: 1500,
          operation: 'add',
          description: 'Economia do mês de novembro'
        }
      }),
      prisma.goalProgressHistory.create({
        data: {
          goalId: europeGoal.id,
          previousAmount: 2500,
          newAmount: 3500,
          amountChanged: 1000,
          operation: 'add',
          description: 'Bonus de fim de ano'
        }
      })
    )

    // Histórico para Reserva de Emergência
    const emergencyGoal = goals[1]
    progressHistory.push(
      prisma.goalProgressHistory.create({
        data: {
          goalId: emergencyGoal.id,
          previousAmount: 0,
          newAmount: 5000,
          amountChanged: 5000,
          operation: 'set',
          description: 'Valor inicial transferido da poupança'
        }
      }),
      prisma.goalProgressHistory.create({
        data: {
          goalId: emergencyGoal.id,
          previousAmount: 5000,
          newAmount: 8000,
          amountChanged: 3000,
          operation: 'add',
          description: 'Depósito mensal automático'
        }
      }),
      prisma.goalProgressHistory.create({
        data: {
          goalId: emergencyGoal.id,
          previousAmount: 8000,
          newAmount: 12000,
          amountChanged: 4000,
          operation: 'add',
          description: 'Economia extra do trimestre'
        }
      })
    )

    // Histórico para Carro Novo
    const carGoal = goals[2]
    progressHistory.push(
      prisma.goalProgressHistory.create({
        data: {
          goalId: carGoal.id,
          previousAmount: 0,
          newAmount: 3000,
          amountChanged: 3000,
          operation: 'add',
          description: 'Venda do carro antigo (entrada)'
        }
      }),
      prisma.goalProgressHistory.create({
        data: {
          goalId: carGoal.id,
          previousAmount: 3000,
          newAmount: 6500,
          amountChanged: 3500,
          operation: 'add',
          description: 'Economia dos últimos 3 meses'
        }
      }),
      prisma.goalProgressHistory.create({
        data: {
          goalId: carGoal.id,
          previousAmount: 6500,
          newAmount: 8500,
          amountChanged: 2000,
          operation: 'add',
          description: 'Freelance extra'
        }
      })
    )

    // Histórico para Curso
    const courseGoal = goals[3]
    progressHistory.push(
      prisma.goalProgressHistory.create({
        data: {
          goalId: courseGoal.id,
          previousAmount: 0,
          newAmount: 800,
          amountChanged: 800,
          operation: 'add',
          description: 'Primeira economia para o curso'
        }
      }),
      prisma.goalProgressHistory.create({
        data: {
          goalId: courseGoal.id,
          previousAmount: 800,
          newAmount: 1200,
          amountChanged: 400,
          operation: 'add',
          description: 'Economia mensal'
        }
      })
    )

    await Promise.all(progressHistory)
    console.log('✅ Histórico de progresso criado:', progressHistory.length, 'entradas')

    console.log('🎉 Dados de teste para metas financeiras criados com sucesso!')
    
  } catch (error) {
    console.error('❌ Erro ao criar dados de teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createGoalsTestData()
