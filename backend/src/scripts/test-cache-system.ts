import { DashboardService } from '../services/dashboard.service';
import { dashboardCache } from '../services/dashboard-cache.service';
import { redisCache } from '../lib/redis';

/**
 * Comprehensive cache system test
 */
class CacheSystemTester {
  private dashboardService: DashboardService;
  private testResults: Array<{
    test: string;
    status: 'PASS' | 'FAIL' | 'SKIP';
    duration?: number;
    error?: string;
    details?: any;
  }> = [];

  constructor() {
    this.dashboardService = new DashboardService();
  }

  /**
   * Run all cache system tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Cache System Tests...\n');

    await this.testRedisConnection();
    await this.testCacheBasicOperations();
    await this.testCacheInvalidation();
    await this.testDashboardCacheIntegration();
    await this.testCachePerformance();
    await this.testCacheFailover();
    await this.testCacheHealthCheck();

    this.printResults();
  }

  /**
   * Test Redis connection
   */
  private async testRedisConnection(): Promise<void> {
    const testName = 'Redis Connection';
    console.log(`🔍 Testing: ${testName}`);

    try {
      const startTime = Date.now();
      const isConnected = await redisCache.ping();
      const duration = Date.now() - startTime;

      if (isConnected) {
        this.testResults.push({
          test: testName,
          status: 'PASS',
          duration,
          details: { latency: `${duration}ms` }
        });
        console.log(`   ✅ Redis connected (${duration}ms)\n`);
      } else {
        this.testResults.push({
          test: testName,
          status: 'FAIL',
          error: 'Redis ping failed'
        });
        console.log(`   ❌ Redis connection failed\n`);
      }
    } catch (error) {
      this.testResults.push({
        test: testName,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.log(`   ❌ Redis connection error: ${error}\n`);
    }
  }

  /**
   * Test basic cache operations
   */
  private async testCacheBasicOperations(): Promise<void> {
    const testName = 'Cache Basic Operations';
    console.log(`🔍 Testing: ${testName}`);

    try {
      const testData = { test: 'data', timestamp: Date.now() };
      const testFilters = { accountIds: ['test-account'] };

      // Test SET
      const setResult = await dashboardCache.set('ACCOUNT_BALANCES', testFilters, testData);
      if (!setResult) {
        throw new Error('Cache SET operation failed');
      }

      // Test GET
      const getData = await dashboardCache.get('ACCOUNT_BALANCES', testFilters);
      if (!getData || JSON.stringify(getData) !== JSON.stringify(testData)) {
        throw new Error('Cache GET operation failed or data mismatch');
      }

      // Test DELETE
      const deleteResult = await dashboardCache.invalidate('ACCOUNT_BALANCES');
      
      this.testResults.push({
        test: testName,
        status: 'PASS',
        details: { 
          set: setResult, 
          get: !!getData, 
          delete: deleteResult 
        }
      });
      console.log(`   ✅ Basic cache operations working\n`);
    } catch (error) {
      this.testResults.push({
        test: testName,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.log(`   ❌ Basic cache operations failed: ${error}\n`);
    }
  }

  /**
   * Test cache invalidation
   */
  private async testCacheInvalidation(): Promise<void> {
    const testName = 'Cache Invalidation';
    console.log(`🔍 Testing: ${testName}`);

    try {
      // Set some test data
      const testData = { test: 'invalidation', timestamp: Date.now() };
      const testFilters = { accountIds: ['test-invalidation'] };

      await dashboardCache.set('ACCOUNT_BALANCES', testFilters, testData);
      await dashboardCache.set('NET_WORTH', testFilters, testData);

      // Test specific invalidation
      const deletedCount = await dashboardCache.invalidate('ACCOUNT_BALANCES');

      // Test related invalidation
      await dashboardCache.invalidateRelated('transaction');

      this.testResults.push({
        test: testName,
        status: 'PASS',
        details: { deletedCount }
      });
      console.log(`   ✅ Cache invalidation working\n`);
    } catch (error) {
      this.testResults.push({
        test: testName,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.log(`   ❌ Cache invalidation failed: ${error}\n`);
    }
  }

  /**
   * Test dashboard service cache integration
   */
  private async testDashboardCacheIntegration(): Promise<void> {
    const testName = 'Dashboard Cache Integration';
    console.log(`🔍 Testing: ${testName}`);

    try {
      const startTime = Date.now();

      // First call - should hit database and cache result
      const result1 = await this.dashboardService.getAccountBalances({});
      const firstCallTime = Date.now() - startTime;

      // Second call - should hit cache
      const secondStartTime = Date.now();
      const result2 = await this.dashboardService.getAccountBalances({});
      const secondCallTime = Date.now() - secondStartTime;

      // Results should be identical
      if (JSON.stringify(result1) !== JSON.stringify(result2)) {
        throw new Error('Cache and database results do not match');
      }

      // Second call should be faster (cache hit)
      const speedImprovement = firstCallTime > secondCallTime;

      this.testResults.push({
        test: testName,
        status: 'PASS',
        details: {
          firstCallTime: `${firstCallTime}ms`,
          secondCallTime: `${secondCallTime}ms`,
          speedImprovement
        }
      });
      console.log(`   ✅ Dashboard cache integration working`);
      console.log(`      First call: ${firstCallTime}ms, Second call: ${secondCallTime}ms\n`);
    } catch (error) {
      this.testResults.push({
        test: testName,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.log(`   ❌ Dashboard cache integration failed: ${error}\n`);
    }
  }

  /**
   * Test cache performance
   */
  private async testCachePerformance(): Promise<void> {
    const testName = 'Cache Performance';
    console.log(`🔍 Testing: ${testName}`);

    try {
      const iterations = 100;
      const testData = { performance: 'test', data: new Array(1000).fill('x').join('') };
      
      // Test SET performance
      const setStartTime = Date.now();
      for (let i = 0; i < iterations; i++) {
        await dashboardCache.set('ACCOUNT_BALANCES', { test: i.toString() }, testData);
      }
      const setDuration = Date.now() - setStartTime;
      const avgSetTime = setDuration / iterations;

      // Test GET performance
      const getStartTime = Date.now();
      for (let i = 0; i < iterations; i++) {
        await dashboardCache.get('ACCOUNT_BALANCES', { test: i.toString() });
      }
      const getDuration = Date.now() - getStartTime;
      const avgGetTime = getDuration / iterations;

      this.testResults.push({
        test: testName,
        status: 'PASS',
        details: {
          iterations,
          avgSetTime: `${avgSetTime.toFixed(2)}ms`,
          avgGetTime: `${avgGetTime.toFixed(2)}ms`,
          totalSetTime: `${setDuration}ms`,
          totalGetTime: `${getDuration}ms`
        }
      });
      console.log(`   ✅ Cache performance test completed`);
      console.log(`      Average SET: ${avgSetTime.toFixed(2)}ms, Average GET: ${avgGetTime.toFixed(2)}ms\n`);
    } catch (error) {
      this.testResults.push({
        test: testName,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.log(`   ❌ Cache performance test failed: ${error}\n`);
    }
  }

  /**
   * Test cache failover (Redis unavailable)
   */
  private async testCacheFailover(): Promise<void> {
    const testName = 'Cache Failover';
    console.log(`🔍 Testing: ${testName}`);

    try {
      // This test assumes Redis might be unavailable
      // We'll test the graceful degradation
      
      const testFilters = { accountIds: ['failover-test'] };
      
      // Try to get data when cache might be unavailable
      const result = await dashboardCache.get('ACCOUNT_BALANCES', testFilters);
      
      // Should return null gracefully, not throw error
      this.testResults.push({
        test: testName,
        status: 'PASS',
        details: { gracefulDegradation: true }
      });
      console.log(`   ✅ Cache failover handling working\n`);
    } catch (error) {
      this.testResults.push({
        test: testName,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.log(`   ❌ Cache failover test failed: ${error}\n`);
    }
  }

  /**
   * Test cache health check
   */
  private async testCacheHealthCheck(): Promise<void> {
    const testName = 'Cache Health Check';
    console.log(`🔍 Testing: ${testName}`);

    try {
      const health = await dashboardCache.healthCheck();
      const stats = await dashboardCache.getStats();

      if (!health || !stats) {
        throw new Error('Health check or stats returned null');
      }

      this.testResults.push({
        test: testName,
        status: 'PASS',
        details: { health, statsAvailable: !!stats }
      });
      console.log(`   ✅ Cache health check working`);
      console.log(`      Status: ${health.status}, Redis: ${health.redis}\n`);
    } catch (error) {
      this.testResults.push({
        test: testName,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.log(`   ❌ Cache health check failed: ${error}\n`);
    }
  }

  /**
   * Print test results summary
   */
  private printResults(): void {
    console.log('📊 Cache System Test Results');
    console.log('============================\n');

    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const skipped = this.testResults.filter(r => r.status === 'SKIP').length;

    this.testResults.forEach((result, index) => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⏭️';
      console.log(`${index + 1}. ${icon} ${result.test}`);
      
      if (result.duration) {
        console.log(`   Duration: ${result.duration}ms`);
      }
      
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
      
      console.log('');
    });

    console.log(`Summary: ${passed} passed, ${failed} failed, ${skipped} skipped`);
    
    if (failed === 0) {
      console.log('🎉 All cache system tests passed!');
    } else {
      console.log('⚠️  Some cache system tests failed. Please review the errors above.');
    }
  }
}

/**
 * Main execution
 */
async function main() {
  const tester = new CacheSystemTester();
  
  try {
    await tester.runAllTests();
  } catch (error) {
    console.error('❌ Cache system test suite failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  main()
    .catch((error) => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

export { CacheSystemTester };
