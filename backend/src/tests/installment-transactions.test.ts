import { InstallmentService } from '../services/installment.service';
import { TransactionType } from '@prisma/client';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    transaction: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn()
    },
    $transaction: jest.fn()
  }
}));

describe('InstallmentService', () => {
  let installmentService: InstallmentService;
  let mockPrisma: any;

  beforeEach(() => {
    installmentService = new InstallmentService();
    mockPrisma = require('../lib/prisma').default;
    jest.clearAllMocks();
  });

  describe('createInstallmentTransaction', () => {
    it('should create installment transaction with future installments', async () => {
      // Arrange
      const installmentData = {
        description: 'Compra parcelada',
        totalAmount: 1200.00,
        totalInstallments: 6,
        firstInstallmentDate: new Date('2024-02-01'),
        accountId: 'acc-1',
        categoryId: 'cat-1',
        type: TransactionType.EXPENSE
      };

      const expectedInstallmentAmount = 200.00; // 1200 / 6

      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          transaction: {
            create: mockPrisma.transaction.create
          }
        });
      });

      mockPrisma.transaction.create
        .mockResolvedValueOnce({ id: 'trans-1' }) // First installment
        .mockResolvedValueOnce({ id: 'trans-2' }) // Second installment
        .mockResolvedValueOnce({ id: 'trans-3' }) // Third installment
        .mockResolvedValueOnce({ id: 'trans-4' }) // Fourth installment
        .mockResolvedValueOnce({ id: 'trans-5' }) // Fifth installment
        .mockResolvedValueOnce({ id: 'trans-6' }); // Sixth installment

      // Act
      const result = await installmentService.createInstallmentTransaction(installmentData);

      // Assert
      expect(result.totalInstallments).toBe(6);
      expect(result.installmentAmount).toBe(expectedInstallmentAmount);
      expect(mockPrisma.transaction.create).toHaveBeenCalledTimes(6);

      // Verify first installment (current)
      expect(mockPrisma.transaction.create).toHaveBeenNthCalledWith(1, {
        data: {
          description: 'Compra parcelada (1/6)',
          amount: expectedInstallmentAmount,
          transactionDate: new Date('2024-02-01'),
          type: TransactionType.EXPENSE,
          accountId: 'acc-1',
          categoryId: 'cat-1',
          installmentNumber: 1,
          totalInstallments: 6,
          parentTransactionId: null,
          isFuture: false
        }
      });

      // Verify second installment (future)
      expect(mockPrisma.transaction.create).toHaveBeenNthCalledWith(2, {
        data: {
          description: 'Compra parcelada (2/6)',
          amount: expectedInstallmentAmount,
          transactionDate: new Date('2024-03-03'),
          type: TransactionType.EXPENSE,
          accountId: 'acc-1',
          categoryId: 'cat-1',
          installmentNumber: 2,
          totalInstallments: 6,
          parentTransactionId: 'trans-1',
          isFuture: true
        }
      });
    });

    it('should handle uneven division with remainder in last installment', async () => {
      // Arrange
      const installmentData = {
        description: 'Compra com resto',
        totalAmount: 1000.00,
        totalInstallments: 3,
        firstInstallmentDate: new Date('2024-02-01'),
        accountId: 'acc-1',
        type: TransactionType.EXPENSE
      };

      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          transaction: {
            create: mockPrisma.transaction.create
          }
        });
      });

      mockPrisma.transaction.create
        .mockResolvedValueOnce({ id: 'trans-1' })
        .mockResolvedValueOnce({ id: 'trans-2' })
        .mockResolvedValueOnce({ id: 'trans-3' });

      // Act
      const result = await installmentService.createInstallmentTransaction(installmentData);

      // Assert
      expect(result.totalInstallments).toBe(3);
      expect(result.installmentAmount).toBe(333.33); // Base amount

      // First two installments should be 333.33
      expect(mockPrisma.transaction.create).toHaveBeenNthCalledWith(1, 
        expect.objectContaining({
          data: expect.objectContaining({
            amount: 333.33
          })
        })
      );

      expect(mockPrisma.transaction.create).toHaveBeenNthCalledWith(2, 
        expect.objectContaining({
          data: expect.objectContaining({
            amount: 333.33
          })
        })
      );

      // Last installment should include remainder (333.34)
      expect(mockPrisma.transaction.create).toHaveBeenNthCalledWith(3, 
        expect.objectContaining({
          data: expect.objectContaining({
            amount: 333.34 // 333.33 + 0.01 remainder (rounded)
          })
        })
      );
    });

    it('should validate minimum installments', async () => {
      // Arrange
      const invalidInstallmentData = {
        description: 'Invalid installments',
        totalAmount: 1000.00,
        totalInstallments: 1, // Invalid: minimum is 2
        firstInstallmentDate: new Date(),
        accountId: 'acc-1',
        type: TransactionType.EXPENSE
      };

      // Act & Assert
      await expect(
        installmentService.createInstallmentTransaction(invalidInstallmentData)
      ).rejects.toThrow('Minimum 2 installments required');
    });

    it('should validate maximum installments', async () => {
      // Arrange
      const invalidInstallmentData = {
        description: 'Too many installments',
        totalAmount: 1000.00,
        totalInstallments: 61, // Invalid: maximum is 60
        firstInstallmentDate: new Date(),
        accountId: 'acc-1',
        type: TransactionType.EXPENSE
      };

      // Act & Assert
      await expect(
        installmentService.createInstallmentTransaction(invalidInstallmentData)
      ).rejects.toThrow('Maximum 60 installments allowed');
    });

    it('should validate minimum installment amount', async () => {
      // Arrange
      const invalidInstallmentData = {
        description: 'Very small installments',
        totalAmount: 1.00,
        totalInstallments: 12,
        firstInstallmentDate: new Date(),
        accountId: 'acc-1',
        type: TransactionType.EXPENSE
      };

      // Act & Assert
      await expect(
        installmentService.createInstallmentTransaction(invalidInstallmentData)
      ).rejects.toThrow('Minimum installment amount is');
    });
  });

  describe('updateInstallmentTransaction', () => {
    it('should update remaining installments when editing', async () => {
      // Arrange
      const transactionId = 'trans-1';
      const updateData = {
        description: 'Updated description',
        amount: 250.00 // Changed from original 200.00
      };

      const existingTransaction = {
        id: 'trans-1',
        installmentNumber: 2,
        totalInstallments: 6,
        parentTransactionId: 'parent-1',
        amount: 200.00
      };

      const futureInstallments = [
        { id: 'trans-3', installmentNumber: 3, totalInstallments: 6 },
        { id: 'trans-4', installmentNumber: 4, totalInstallments: 6 },
        { id: 'trans-5', installmentNumber: 5, totalInstallments: 6 },
        { id: 'trans-6', installmentNumber: 6, totalInstallments: 6 }
      ];

      mockPrisma.transaction.findUnique.mockResolvedValue(existingTransaction);
      mockPrisma.transaction.findMany.mockResolvedValue(futureInstallments);

      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          transaction: {
            update: mockPrisma.transaction.update
          }
        });
      });

      // Act
      const result = await installmentService.updateInstallmentTransaction(
        transactionId, 
        updateData
      );

      // Assert
      expect(result.updatedInstallments).toBe(5); // Current + 4 future installments
      expect(mockPrisma.transaction.update).toHaveBeenCalledTimes(5);

      // Verify current transaction update (first call)
      expect(mockPrisma.transaction.update).toHaveBeenNthCalledWith(1, {
        where: { id: 'trans-1' },
        data: {
          description: 'Updated description (2/6)',
          amount: 250.00
        }
      });

      // Verify future installments update (second call)
      expect(mockPrisma.transaction.update).toHaveBeenNthCalledWith(2, {
        where: { id: 'trans-3' },
        data: {
          description: 'Updated description (3/6)',
          amount: 250.00
        }
      });
    });

    it('should handle partial installment updates', async () => {
      // Arrange
      const transactionId = 'trans-1';
      const updateData = {
        description: 'Updated description only'
        // Amount not changed
      };

      const existingTransaction = {
        id: 'trans-1',
        installmentNumber: 1,
        totalInstallments: 3,
        amount: 200.00,
        description: 'Original description'
      };

      mockPrisma.transaction.findUnique.mockResolvedValue(existingTransaction);
      mockPrisma.transaction.findMany.mockResolvedValue([
        { id: 'trans-2', installmentNumber: 2, totalInstallments: 3 },
        { id: 'trans-3', installmentNumber: 3, totalInstallments: 3 }
      ]);

      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          transaction: {
            update: mockPrisma.transaction.update
          }
        });
      });

      // Act
      const result = await installmentService.updateInstallmentTransaction(
        transactionId, 
        updateData
      );

      // Assert
      expect(result.updatedInstallments).toBe(3); // Current + 2 future installments
      
      // Verify only description is updated, amount remains the same
      expect(mockPrisma.transaction.update).toHaveBeenNthCalledWith(1, {
        where: { id: 'trans-1' },
        data: {
          description: 'Updated description only (1/3)'
        }
      });
    });
  });

  describe('cancelRemainingInstallments', () => {
    it('should cancel all future installments', async () => {
      // Arrange
      const transactionId = 'trans-1';
      
      const existingTransaction = {
        id: 'trans-1',
        installmentNumber: 2,
        totalInstallments: 6,
        parentTransactionId: 'parent-1'
      };

      const futureInstallments = [
        { id: 'trans-3', installmentNumber: 3 },
        { id: 'trans-4', installmentNumber: 4 },
        { id: 'trans-5', installmentNumber: 5 },
        { id: 'trans-6', installmentNumber: 6 }
      ];

      mockPrisma.transaction.findUnique.mockResolvedValue(existingTransaction);
      mockPrisma.transaction.findMany.mockResolvedValue(futureInstallments);
      mockPrisma.transaction.deleteMany.mockResolvedValue({ count: 4 });

      // Act
      const result = await installmentService.cancelRemainingInstallments(transactionId);

      // Assert
      expect(result.cancelledInstallments).toBe(4);
      expect(mockPrisma.transaction.deleteMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { parentTransactionId: 'parent-1' },
            { parentTransactionId: 'trans-1' }
          ],
          installmentNumber: {
            gt: 2
          },
          isFuture: true
        }
      });
    });

    it('should handle case with no future installments', async () => {
      // Arrange
      const transactionId = 'trans-6'; // Last installment
      
      const existingTransaction = {
        id: 'trans-6',
        installmentNumber: 6,
        totalInstallments: 6,
        parentTransactionId: 'parent-1'
      };

      mockPrisma.transaction.findUnique.mockResolvedValue(existingTransaction);
      mockPrisma.transaction.findMany.mockResolvedValue([]); // No future installments
      mockPrisma.transaction.deleteMany.mockResolvedValue({ count: 0 });

      // Act
      const result = await installmentService.cancelRemainingInstallments(transactionId);

      // Assert
      expect(result.cancelledInstallments).toBe(0);
      expect(result.message).toContain('No future installments to cancel');
    });
  });

  describe('getInstallmentSummary', () => {
    it('should return installment summary with progress', async () => {
      // Arrange
      const parentTransactionId = 'parent-1';
      
      const allInstallments = [
        { id: 'trans-1', installmentNumber: 1, isFuture: false, amount: 200, transactionDate: new Date('2024-01-01') },
        { id: 'trans-2', installmentNumber: 2, isFuture: false, amount: 200, transactionDate: new Date('2024-02-01') },
        { id: 'trans-3', installmentNumber: 3, isFuture: true, amount: 200, transactionDate: new Date('2024-03-01') },
        { id: 'trans-4', installmentNumber: 4, isFuture: true, amount: 200, transactionDate: new Date('2024-04-01') },
        { id: 'trans-5', installmentNumber: 5, isFuture: true, amount: 200, transactionDate: new Date('2024-05-01') },
        { id: 'trans-6', installmentNumber: 6, isFuture: true, amount: 200, transactionDate: new Date('2024-06-01') }
      ];

      mockPrisma.transaction.findMany.mockResolvedValue(allInstallments);

      // Act
      const summary = await installmentService.getInstallmentSummary(parentTransactionId);

      // Assert
      expect(summary).toEqual({
        totalInstallments: 6,
        completedInstallments: 2,
        remainingInstallments: 4,
        totalAmount: 1200,
        paidAmount: 400,
        remainingAmount: 800,
        progressPercentage: 33.33,
        nextInstallmentDate: expect.any(Date)
      });
    });
  });
});
