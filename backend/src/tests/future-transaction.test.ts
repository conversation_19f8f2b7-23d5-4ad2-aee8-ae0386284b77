import { FutureTransactionJobService } from '../services/future-transaction-job.service';
import { TransactionType } from '@prisma/client';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    transaction: {
      findMany: jest.fn(),
      update: jest.fn(),
      findUnique: jest.fn(),
      count: jest.fn()
    },
    account: {
      findUnique: jest.fn(),
      update: jest.fn()
    },
    $transaction: jest.fn()
  }
}));

describe('FutureTransactionJobService', () => {
  let futureTransactionJobService: FutureTransactionJobService;
  let mockPrisma: any;

  beforeEach(() => {
    futureTransactionJobService = new FutureTransactionJobService();
    mockPrisma = require('../lib/prisma').default;
    jest.clearAllMocks();
  });

  describe('execute', () => {
    it('should process future transactions due today', async () => {
      // Arrange
      const mockFutureTransactions = [
        {
          id: 'trans-1',
          description: 'Future Income',
          amount: 1000.00,
          type: TransactionType.INCOME,
          accountId: 'acc-1',
          isFuture: true,
          transactionDate: new Date(),
          account: {
            id: 'acc-1',
            name: 'Checking Account',
            type: 'CHECKING',
            currency: 'BRL',
            currentBalance: 5000.00
          },
          destinationAccount: null,
          category: null,
          members: []
        }
      ];

      mockPrisma.transaction.findMany.mockResolvedValue(mockFutureTransactions);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          transaction: {
            update: mockPrisma.transaction.update
          },
          account: {
            findUnique: mockPrisma.account.findUnique,
            update: mockPrisma.account.update
          }
        });
      });

      mockPrisma.transaction.update.mockResolvedValue({
        ...mockFutureTransactions[0],
        isFuture: false
      });

      mockPrisma.account.findUnique.mockResolvedValue({
        id: 'acc-1',
        currentBalance: 5000.00
      });

      mockPrisma.account.update.mockResolvedValue({
        id: 'acc-1',
        currentBalance: 6000.00
      });

      // Act
      const result = await futureTransactionJobService.execute();

      // Assert
      expect(result.processedTransactions).toBe(1);
      expect(result.failedTransactions).toBe(0);
      expect(result.skippedTransactions).toBe(0);
      expect(mockPrisma.transaction.update).toHaveBeenCalledWith({
        where: { id: 'trans-1' },
        data: { isFuture: false }
      });
      expect(mockPrisma.account.update).toHaveBeenCalledWith({
        where: { id: 'acc-1' },
        data: { currentBalance: 6000.00 }
      });
    });

    it('should handle transfer transactions correctly', async () => {
      // Arrange
      const mockTransferTransaction = {
        id: 'trans-2',
        description: 'Future Transfer',
        amount: 500.00,
        type: TransactionType.TRANSFER,
        accountId: 'acc-1',
        destinationAccountId: 'acc-2',
        destinationAmount: 500.00,
        isFuture: true,
        transactionDate: new Date(),
        account: {
          id: 'acc-1',
          currentBalance: 1000.00
        },
        destinationAccount: {
          id: 'acc-2',
          currentBalance: 2000.00
        }
      };

      mockPrisma.transaction.findMany.mockResolvedValue([mockTransferTransaction]);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          transaction: {
            update: mockPrisma.transaction.update
          },
          account: {
            findUnique: mockPrisma.account.findUnique,
            update: mockPrisma.account.update
          }
        });
      });

      mockPrisma.account.findUnique
        .mockResolvedValueOnce({ id: 'acc-1', currentBalance: 1000.00 })
        .mockResolvedValueOnce({ id: 'acc-2', currentBalance: 2000.00 });

      // Act
      const result = await futureTransactionJobService.execute();

      // Assert
      expect(result.processedTransactions).toBe(1);
      expect(mockPrisma.account.update).toHaveBeenCalledTimes(2);
      expect(mockPrisma.account.update).toHaveBeenCalledWith({
        where: { id: 'acc-1' },
        data: { currentBalance: 500.00 } // 1000 - 500
      });
      expect(mockPrisma.account.update).toHaveBeenCalledWith({
        where: { id: 'acc-2' },
        data: { currentBalance: 2500.00 } // 2000 + 500
      });
    });

    it('should skip transactions with deleted accounts', async () => {
      // Arrange
      const mockTransactionWithDeletedAccount = {
        id: 'trans-3',
        description: 'Transaction with deleted account',
        amount: 100.00,
        type: TransactionType.EXPENSE,
        accountId: 'acc-deleted',
        isFuture: true,
        transactionDate: new Date(),
        account: null, // Deleted account
        destinationAccount: null
      };

      mockPrisma.transaction.findMany.mockResolvedValue([mockTransactionWithDeletedAccount]);

      // Act
      const result = await futureTransactionJobService.execute();

      // Assert
      expect(result.processedTransactions).toBe(0);
      expect(result.skippedTransactions).toBe(1);
      expect(mockPrisma.transaction.update).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const mockTransaction = {
        id: 'trans-error',
        description: 'Error transaction',
        amount: 100.00,
        type: TransactionType.EXPENSE,
        accountId: 'acc-1',
        isFuture: true,
        transactionDate: new Date(),
        account: {
          id: 'acc-1',
          currentBalance: 1000.00
        }
      };

      mockPrisma.transaction.findMany.mockResolvedValue([mockTransaction]);
      mockPrisma.$transaction.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await futureTransactionJobService.execute();

      // Assert
      expect(result.processedTransactions).toBe(0);
      expect(result.failedTransactions).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].transactionId).toBe('trans-error');
      expect(result.errors[0].error).toBe('Database error');
    });
  });

  describe('getPendingFutureTransactionsStats', () => {
    it('should return correct statistics', async () => {
      // Arrange
      mockPrisma.transaction.count
        .mockResolvedValueOnce(10) // totalPending
        .mockResolvedValueOnce(3)  // dueToday
        .mockResolvedValueOnce(5)  // dueThisWeek
        .mockResolvedValueOnce(8)  // dueThisMonth
        .mockResolvedValueOnce(2); // overdue

      // Act
      const stats = await futureTransactionJobService.getPendingFutureTransactionsStats();

      // Assert
      expect(stats).toEqual({
        totalPending: 10,
        dueToday: 3,
        dueThisWeek: 5,
        dueThisMonth: 8,
        overdue: 2
      });
    });
  });

  describe('shouldProcessTransaction', () => {
    it('should return true for valid transactions', () => {
      // Arrange
      const validTransaction = {
        id: 'trans-1',
        type: TransactionType.INCOME,
        account: {
          id: 'acc-1',
          currentBalance: 1000.00,
          deletedAt: null
        },
        destinationAccount: null
      };

      // Act
      const service = futureTransactionJobService as any;
      const result = service.shouldProcessTransaction(validTransaction);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false for transactions with deleted accounts', () => {
      // Arrange
      const transactionWithDeletedAccount = {
        id: 'trans-1',
        type: TransactionType.INCOME,
        account: null
      };

      // Act
      const service = futureTransactionJobService as any;
      const result = service.shouldProcessTransaction(transactionWithDeletedAccount);

      // Assert
      expect(result).toBe(false);
    });

    it('should return false for transfers with deleted destination accounts', () => {
      // Arrange
      const transferWithDeletedDestination = {
        id: 'trans-1',
        type: TransactionType.TRANSFER,
        account: {
          id: 'acc-1',
          currentBalance: 1000.00,
          deletedAt: null
        },
        destinationAccount: null
      };

      // Act
      const service = futureTransactionJobService as any;
      const result = service.shouldProcessTransaction(transferWithDeletedDestination);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('updateAccountBalance', () => {
    it('should correctly update balance for income transactions', async () => {
      // Arrange
      const mockTx = {
        account: {
          findUnique: jest.fn().mockResolvedValue({
            currentBalance: 1000.00
          }),
          update: jest.fn()
        }
      };

      // Act
      const service = futureTransactionJobService as any;
      await service.updateAccountBalance(mockTx, 'acc-1', TransactionType.INCOME, 500.00);

      // Assert
      expect(mockTx.account.update).toHaveBeenCalledWith({
        where: { id: 'acc-1' },
        data: { currentBalance: 1500.00 }
      });
    });

    it('should correctly update balance for expense transactions', async () => {
      // Arrange
      const mockTx = {
        account: {
          findUnique: jest.fn().mockResolvedValue({
            currentBalance: 1000.00
          }),
          update: jest.fn()
        }
      };

      // Act
      const service = futureTransactionJobService as any;
      await service.updateAccountBalance(mockTx, 'acc-1', TransactionType.EXPENSE, 300.00);

      // Assert
      expect(mockTx.account.update).toHaveBeenCalledWith({
        where: { id: 'acc-1' },
        data: { currentBalance: 700.00 }
      });
    });

    it('should throw error if account not found', async () => {
      // Arrange
      const mockTx = {
        account: {
          findUnique: jest.fn().mockResolvedValue(null),
          update: jest.fn()
        }
      };

      // Act & Assert
      const service = futureTransactionJobService as any;
      await expect(
        service.updateAccountBalance(mockTx, 'non-existent', TransactionType.INCOME, 100.00)
      ).rejects.toThrow('Account non-existent not found');
    });
  });
});
