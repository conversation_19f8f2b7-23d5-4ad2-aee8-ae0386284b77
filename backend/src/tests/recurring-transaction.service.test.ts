import { RecurrenceFrequency, TransactionType } from '@prisma/client';

// Simple test to verify the module structure
describe('RecurringTransactionService', () => {
  it('should have correct enum values', () => {
    expect(RecurrenceFrequency.DAILY).toBe('DAILY');
    expect(RecurrenceFrequency.WEEKLY).toBe('WEEKLY');
    expect(RecurrenceFrequency.MONTHLY).toBe('MONTHLY');
    expect(RecurrenceFrequency.YEARLY).toBe('YEARLY');
  });

  it('should have correct transaction types', () => {
    expect(TransactionType.INCOME).toBe('INCOME');
    expect(TransactionType.EXPENSE).toBe('EXPENSE');
    expect(TransactionType.TRANSFER).toBe('TRANSFER');
  });
});


