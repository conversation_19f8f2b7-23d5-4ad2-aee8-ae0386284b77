import { describe, it, expect, beforeAll, beforeEach, afterEach } from '@jest/globals';
import app from '../index';
import { IntegrationTestHelper, TestDataGenerator, TestAssertions } from './helpers/integration.helper';
import prisma from '../lib/prisma';

describe('API Integration Tests - Comprehensive', () => {
  let testHelper: IntegrationTestHelper;
  let authToken: string;

  beforeAll(async () => {
    testHelper = new IntegrationTestHelper(app);
    authToken = await testHelper.setupAuth();
  });

  beforeEach(async () => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  afterEach(async () => {
    // Cleanup any test data if needed
  });

  describe('Authentication & Authorization', () => {
    it('should reject requests without authentication token', async () => {
      const response = await testHelper.unauthenticated().get('/api/v1/accounts');
      TestAssertions.expectAuthError(response);
    });

    it('should reject requests with invalid token', async () => {
      const response = await testHelper.unauthenticated()
        .get('/api/v1/accounts')
        .set('Authorization', 'Bearer invalid-token');
      
      TestAssertions.expectAuthError(response);
    });

    it('should accept requests with valid token', async () => {
      // Mock successful response
      const mockAccounts = [TestDataGenerator.account()];
      (prisma.account.findMany as jest.Mock).mockResolvedValue(mockAccounts);

      const response = await testHelper.get('/api/v1/accounts');
      expect(response.status).toBe(200);
      TestAssertions.expectSuccessResponse(response);
    });
  });

  describe('Account Management API', () => {
    it('should create a new account', async () => {
      const newAccount = {
        name: 'Test Savings Account',
        type: 'SAVINGS',
        currency: 'USD',
        initialBalance: 1000
      };

      const mockCreatedAccount = TestDataGenerator.account(newAccount);
      (prisma.account.create as jest.Mock).mockResolvedValue(mockCreatedAccount);

      const response = await testHelper.post('/api/v1/accounts', newAccount);
      
      expect(response.status).toBe(201);
      TestAssertions.expectSuccessResponse(response, {
        name: newAccount.name,
        type: newAccount.type,
        currency: newAccount.currency
      });
    });

    it('should validate required fields when creating account', async () => {
      const invalidAccount = {
        name: '', // Empty name should fail validation
        type: 'INVALID_TYPE'
      };

      const response = await testHelper.post('/api/v1/accounts', invalidAccount);
      TestAssertions.expectValidationError(response, 'name');
    });

    it('should retrieve user accounts', async () => {
      const mockAccounts = [
        TestDataGenerator.account({ name: 'Checking Account' }),
        TestDataGenerator.account({ name: 'Savings Account', type: 'SAVINGS' })
      ];
      
      (prisma.account.findMany as jest.Mock).mockResolvedValue(mockAccounts);

      const response = await testHelper.get('/api/v1/accounts');
      
      expect(response.status).toBe(200);
      TestAssertions.expectSuccessResponse(response);
      expect(response.body.data).toHaveLength(2);
    });

    it('should update account details', async () => {
      const accountId = 'test-account-id';
      const updateData = { name: 'Updated Account Name' };
      
      const mockUpdatedAccount = TestDataGenerator.account({
        id: accountId,
        ...updateData
      });
      
      (prisma.account.update as jest.Mock).mockResolvedValue(mockUpdatedAccount);

      const response = await testHelper.put(`/api/v1/accounts/${accountId}`, updateData);
      
      expect(response.status).toBe(200);
      TestAssertions.expectSuccessResponse(response, updateData);
    });

    it('should delete account', async () => {
      const accountId = 'test-account-id';
      
      (prisma.account.delete as jest.Mock).mockResolvedValue({ id: accountId });

      const response = await testHelper.delete(`/api/v1/accounts/${accountId}`);
      
      expect(response.status).toBe(200);
      TestAssertions.expectSuccessResponse(response);
    });
  });

  describe('Transaction Management API', () => {
    it('should create a new transaction', async () => {
      const newTransaction = {
        description: 'Test Purchase',
        amount: 50.99,
        type: 'EXPENSE',
        accountId: 'test-account-id',
        categoryId: 'test-category-id',
        date: new Date().toISOString()
      };

      const mockCreatedTransaction = TestDataGenerator.transaction(newTransaction);
      (prisma.transaction.create as jest.Mock).mockResolvedValue(mockCreatedTransaction);

      const response = await testHelper.post('/api/v1/transactions', newTransaction);
      
      expect(response.status).toBe(201);
      TestAssertions.expectSuccessResponse(response, {
        description: newTransaction.description,
        amount: newTransaction.amount,
        type: newTransaction.type
      });
    });

    it('should retrieve transactions with pagination', async () => {
      const mockTransactions = Array.from({ length: 5 }, (_, i) => 
        TestDataGenerator.transaction({ description: `Transaction ${i + 1}` })
      );
      
      (prisma.transaction.findMany as jest.Mock).mockResolvedValue(mockTransactions);
      (prisma.transaction.count as jest.Mock).mockResolvedValue(50);

      const response = await testHelper.get('/api/v1/transactions', {
        page: 1,
        limit: 5
      });
      
      expect(response.status).toBe(200);
      TestAssertions.expectSuccessResponse(response);
      expect(response.body.data.transactions).toHaveLength(5);
      expect(response.body.data).toHaveProperty('pagination');
    });

    it('should filter transactions by date range', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-01-31';
      
      const mockTransactions = [TestDataGenerator.transaction()];
      (prisma.transaction.findMany as jest.Mock).mockResolvedValue(mockTransactions);

      const response = await testHelper.get('/api/v1/transactions', {
        startDate,
        endDate
      });
      
      expect(response.status).toBe(200);
      TestAssertions.expectSuccessResponse(response);
      
      // Verify that Prisma was called with date filter
      expect(prisma.transaction.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            date: expect.objectContaining({
              gte: expect.any(Date),
              lte: expect.any(Date)
            })
          })
        })
      );
    });
  });

  describe('Category Management API', () => {
    it('should create a new category', async () => {
      const newCategory = {
        name: 'Test Category',
        type: 'EXPENSE',
        color: '#FF5733',
        icon: 'shopping-cart'
      };

      const mockCreatedCategory = TestDataGenerator.category(newCategory);
      (prisma.category.create as jest.Mock).mockResolvedValue(mockCreatedCategory);

      const response = await testHelper.post('/api/v1/categories', newCategory);
      
      expect(response.status).toBe(201);
      TestAssertions.expectSuccessResponse(response, newCategory);
    });

    it('should retrieve categories by type', async () => {
      const mockExpenseCategories = [
        TestDataGenerator.category({ name: 'Food', type: 'EXPENSE' }),
        TestDataGenerator.category({ name: 'Transport', type: 'EXPENSE' })
      ];
      
      (prisma.category.findMany as jest.Mock).mockResolvedValue(mockExpenseCategories);

      const response = await testHelper.get('/api/v1/categories', { type: 'EXPENSE' });
      
      expect(response.status).toBe(200);
      TestAssertions.expectSuccessResponse(response);
      expect(response.body.data).toHaveLength(2);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      (prisma.account.findMany as jest.Mock).mockRejectedValue(
        new Error('Database connection failed')
      );

      const response = await testHelper.get('/api/v1/accounts');
      
      expect(response.status).toBe(500);
      TestAssertions.expectErrorResponse(response);
    });

    it('should handle invalid JSON in request body', async () => {
      const response = await testHelper.post('/api/v1/accounts')
        .set('Content-Type', 'application/json')
        .send('invalid json');
      
      expect(response.status).toBe(400);
      TestAssertions.expectErrorResponse(response);
    });

    it('should handle resource not found errors', async () => {
      const nonExistentId = 'non-existent-id';
      
      (prisma.account.findUnique as jest.Mock).mockResolvedValue(null);

      const response = await testHelper.get(`/api/v1/accounts/${nonExistentId}`);
      
      TestAssertions.expectNotFoundError(response);
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits on API endpoints', async () => {
      // Mock multiple rapid requests
      const requests = Array.from({ length: 10 }, () => 
        testHelper.get('/api/v1/accounts')
      );

      const responses = await Promise.all(requests);
      
      // At least one request should be rate limited (429)
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });
});
