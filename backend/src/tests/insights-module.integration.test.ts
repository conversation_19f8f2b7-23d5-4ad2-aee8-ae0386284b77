/**
 * Integration tests for the insights module
 * Tests the core functionality without external dependencies
 */

import {
  calculateCategorySpending,
  calculateMonthlyTrends,
  detectSpendingAnomalies,
  type TransactionData
} from '../utils/financial-analysis.utils';

import {
  calculateStatisticalSummary,
  generateForecast
} from '../utils/statistical-analysis.utils';

describe('Insights Module Integration Tests', () => {
  // Sample data for testing
  const sampleTransactions: TransactionData[] = [
    {
      id: '1',
      amount: 100,
      date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      categoryId: 'cat1',
      categoryName: 'Alimentação',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'EXPENSE',
      description: 'Supermercado'
    },
    {
      id: '2',
      amount: 50,
      date: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000), // 25 days ago
      categoryId: 'cat1',
      categoryName: 'Alimentação',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'EXPENSE',
      description: 'Restaurante'
    },
    {
      id: '3',
      amount: 200,
      date: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000), // 20 days ago
      categoryId: 'cat2',
      categoryName: 'Transporte',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'EXPENSE',
      description: 'Combustível'
    },
    {
      id: '4',
      amount: 3000,
      date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
      categoryId: 'cat3',
      categoryName: 'Salário',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'INCOME',
      description: 'Salário mensal'
    },
    {
      id: '5',
      amount: 75,
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      categoryId: 'cat1',
      categoryName: 'Alimentação',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'EXPENSE',
      description: 'Supermercado'
    }
  ];

  describe('Financial Analysis Integration', () => {
    it('should analyze spending patterns correctly', () => {
      const categorySpending = calculateCategorySpending(sampleTransactions, false);
      
      expect(categorySpending).toHaveLength(2); // Only expense categories
      expect(categorySpending[0].categoryName).toBe('Alimentação'); // Highest spending
      expect(categorySpending[0].totalAmount).toBe(225); // 100 + 50 + 75
      expect(categorySpending[0].percentage).toBeCloseTo(52.94, 2);
      
      expect(categorySpending[1].categoryName).toBe('Transporte');
      expect(categorySpending[1].totalAmount).toBe(200);
      expect(categorySpending[1].percentage).toBeCloseTo(47.06, 2);
    });

    it('should calculate monthly trends', () => {
      const trends = calculateMonthlyTrends(sampleTransactions, 12);
      
      expect(trends.length).toBeGreaterThanOrEqual(1);
      
      // Total should exclude income
      const totalAmount = trends.reduce((sum, trend) => sum + trend.amount, 0);
      expect(totalAmount).toBe(425); // 100 + 50 + 200 + 75
    });

    it('should detect spending anomalies', () => {
      // Create more data for anomaly detection
      const extendedTransactions: TransactionData[] = [];
      
      // Add normal transactions
      for (let i = 0; i < 20; i++) {
        extendedTransactions.push({
          id: `normal-${i}`,
          amount: 50 + Math.random() * 20, // Normal range 50-70
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE',
          description: 'Compra normal'
        });
      }

      // Add anomalous transaction
      extendedTransactions.push({
        id: 'anomaly',
        amount: 500, // Much higher than normal
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        categoryId: 'cat1',
        categoryName: 'Alimentação',
        accountId: 'acc1',
        accountName: 'Conta Corrente',
        type: 'EXPENSE',
        description: 'Compra anômala'
      });

      const anomalies = detectSpendingAnomalies(extendedTransactions, 90, 2.0);
      
      expect(anomalies.length).toBeGreaterThan(0);
      expect(anomalies[0].transaction.amount).toBe(500);
      expect(anomalies[0].anomalyScore).toBeGreaterThan(2.0);
    });
  });

  describe('Statistical Analysis Integration', () => {
    it('should calculate comprehensive statistics', () => {
      const amounts = sampleTransactions
        .filter(t => t.type === 'EXPENSE')
        .map(t => t.amount);
      
      const stats = calculateStatisticalSummary(amounts);
      
      expect(stats.mean).toBeCloseTo(106.25, 2); // (100 + 50 + 200 + 75) / 4
      expect(stats.min).toBe(50);
      expect(stats.max).toBe(200);
      expect(stats.range).toBe(150);
      expect(stats.standardDeviation).toBeGreaterThan(0);
    });

    it('should generate forecasts', () => {
      const monthlyAmounts = [100, 120, 110, 130, 125, 140, 135, 150];
      
      const forecast = generateForecast(monthlyAmounts, 3);
      
      expect(forecast.predictions).toHaveLength(3);
      expect(forecast.trend).toBe('INCREASING');
      expect(forecast.confidence).toBeGreaterThan(0);
      expect(forecast.predictions.every(p => p >= 0)).toBe(true);
    });
  });

  describe('Insight Generation Logic', () => {
    it('should identify high spending categories', () => {
      const categorySpending = calculateCategorySpending(sampleTransactions, false);
      
      // Logic for SPENDING_PATTERN insight
      const highSpendingCategories = categorySpending.filter(cat => cat.percentage > 30);
      
      expect(highSpendingCategories).toHaveLength(2); // Both categories are > 30%
      expect(highSpendingCategories[0].categoryName).toBe('Alimentação');
      expect(highSpendingCategories[0].percentage).toBeGreaterThan(50);
    });

    it('should detect trend changes', () => {
      // Simulate monthly data with increasing trend
      const monthlyData = [
        { period: '2024-01', amount: 100, transactionCount: 5, date: new Date('2024-01-01') },
        { period: '2024-02', amount: 120, transactionCount: 6, date: new Date('2024-02-01') },
        { period: '2024-03', amount: 140, transactionCount: 7, date: new Date('2024-03-01') },
        { period: '2024-04', amount: 160, transactionCount: 8, date: new Date('2024-04-01') }
      ];

      // Simple trend calculation
      const firstAmount = monthlyData[0].amount;
      const lastAmount = monthlyData[monthlyData.length - 1].amount;
      const changePercentage = ((lastAmount - firstAmount) / firstAmount) * 100;

      expect(changePercentage).toBe(60); // 60% increase
      expect(changePercentage).toBeGreaterThan(15); // Threshold for TREND_ANALYSIS insight
    });

    it('should simulate budget alert logic', () => {
      const monthlyBudget = 400;
      const currentSpending = calculateCategorySpending(sampleTransactions, false)
        .reduce((sum, cat) => sum + cat.totalAmount, 0);
      
      const budgetUsagePercentage = (currentSpending / monthlyBudget) * 100;
      
      expect(budgetUsagePercentage).toBeCloseTo(106.25, 2); // Over budget
      expect(budgetUsagePercentage).toBeGreaterThan(80); // Threshold for BUDGET_ALERT insight
    });
  });

  describe('Data Validation', () => {
    it('should handle empty transaction lists', () => {
      const emptyResult = calculateCategorySpending([]);
      expect(emptyResult).toHaveLength(0);

      const emptyTrends = calculateMonthlyTrends([]);
      expect(emptyTrends).toHaveLength(0);

      const emptyAnomalies = detectSpendingAnomalies([]);
      expect(emptyAnomalies).toHaveLength(0);
    });

    it('should handle transactions without categories', () => {
      const uncategorizedTransactions: TransactionData[] = [
        {
          id: '1',
          amount: 100,
          date: new Date(),
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE',
          description: 'Sem categoria'
        }
      ];

      const result = calculateCategorySpending(uncategorizedTransactions);
      expect(result).toHaveLength(1);
      expect(result[0].categoryName).toBe('Sem Categoria');
      expect(result[0].categoryId).toBe('uncategorized');
    });

    it('should handle invalid statistical data', () => {
      expect(() => calculateStatisticalSummary([])).toThrow('Dataset cannot be empty');
      expect(() => generateForecast([1], 3)).toThrow('Need at least 3 data points');
    });
  });

  describe('Performance Considerations', () => {
    it('should handle large datasets efficiently', () => {
      // Create a large dataset
      const largeDataset: TransactionData[] = [];
      for (let i = 0; i < 1000; i++) {
        largeDataset.push({
          id: `tx-${i}`,
          amount: Math.random() * 1000,
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
          categoryId: `cat-${i % 10}`,
          categoryName: `Category ${i % 10}`,
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE',
          description: `Transaction ${i}`
        });
      }

      const startTime = Date.now();
      const result = calculateCategorySpending(largeDataset);
      const endTime = Date.now();

      expect(result.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete in less than 1 second
    });
  });
});

export { };
