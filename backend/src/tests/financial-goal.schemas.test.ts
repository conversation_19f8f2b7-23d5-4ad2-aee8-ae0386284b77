import {
  CreateFinancialGoalSchema,
  UpdateFinancialGoalSchema,
  UpdateGoalProgressSchema,
  CreateGoalMilestoneSchema,
  UpdateGoalMilestoneSchema,
  FinancialGoalFiltersSchema,
  MilestoneFiltersSchema
} from '../schemas/financial-goal.schemas';

describe('Financial Goal Schemas', () => {
  describe('CreateFinancialGoalSchema', () => {
    const validData = {
      name: 'Casa Própria',
      targetAmount: 300000,
      currentAmount: 50000,
      targetDate: '2025-12-31T00:00:00.000Z',
      familyMemberIds: ['member_1', 'member_2']
    };

    it('should validate correct data', () => {
      const result = CreateFinancialGoalSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('Casa Própria');
        expect(result.data.targetAmount).toBe(300000);
        expect(result.data.currentAmount).toBe(50000);
      }
    });

    it('should set default currentAmount to 0', () => {
      const dataWithoutCurrentAmount = {
        name: validData.name,
        targetAmount: validData.targetAmount,
        targetDate: validData.targetDate,
        familyMemberIds: validData.familyMemberIds
      };

      const result = CreateFinancialGoalSchema.safeParse(dataWithoutCurrentAmount);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.currentAmount).toBe(0);
      }
    });

    it('should reject empty name', () => {
      const invalidData = { ...validData, name: '' };
      const result = CreateFinancialGoalSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject negative target amount', () => {
      const invalidData = { ...validData, targetAmount: -1000 };
      const result = CreateFinancialGoalSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject negative current amount', () => {
      const invalidData = { ...validData, currentAmount: -500 };
      const result = CreateFinancialGoalSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject past target date', () => {
      const invalidData = { ...validData, targetDate: '2020-01-01T00:00:00.000Z' };
      const result = CreateFinancialGoalSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject current amount greater than target amount', () => {
      const invalidData = { ...validData, currentAmount: 400000, targetAmount: 300000 };
      const result = CreateFinancialGoalSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject empty family member IDs array', () => {
      const invalidData = { ...validData, familyMemberIds: [] };
      const result = CreateFinancialGoalSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject too many family member IDs', () => {
      const invalidData = { 
        ...validData, 
        familyMemberIds: Array(15).fill('member_id') 
      };
      const result = CreateFinancialGoalSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('UpdateFinancialGoalSchema', () => {
    it('should validate partial updates', () => {
      const updateData = {
        name: 'Casa Nova',
        targetAmount: 350000
      };
      const result = UpdateFinancialGoalSchema.safeParse(updateData);
      expect(result.success).toBe(true);
    });

    it('should allow empty object', () => {
      const result = UpdateFinancialGoalSchema.safeParse({});
      expect(result.success).toBe(true);
    });

    it('should reject past target date', () => {
      const invalidData = { targetDate: '2020-01-01T00:00:00.000Z' };
      const result = UpdateFinancialGoalSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('UpdateGoalProgressSchema', () => {
    it('should validate add operation', () => {
      const data = {
        amount: 10000,
        operation: 'add' as const,
        description: 'Depósito mensal'
      };
      const result = UpdateGoalProgressSchema.safeParse(data);
      expect(result.success).toBe(true);
    });

    it('should default to add operation', () => {
      const data = { amount: 10000 };
      const result = UpdateGoalProgressSchema.safeParse(data);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.operation).toBe('add');
      }
    });

    it('should reject negative amount', () => {
      const invalidData = { amount: -1000 };
      const result = UpdateGoalProgressSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid operation', () => {
      const invalidData = { amount: 1000, operation: 'invalid' };
      const result = UpdateGoalProgressSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('CreateGoalMilestoneSchema', () => {
    const validData = {
      name: 'Entrada do Apartamento',
      targetAmount: 60000,
      targetDate: '2025-06-30T00:00:00.000Z'
    };

    it('should validate correct data', () => {
      const result = CreateGoalMilestoneSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject past target date', () => {
      const invalidData = { ...validData, targetDate: '2020-01-01T00:00:00.000Z' };
      const result = CreateGoalMilestoneSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject negative target amount', () => {
      const invalidData = { ...validData, targetAmount: -1000 };
      const result = CreateGoalMilestoneSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('FinancialGoalFiltersSchema', () => {
    it('should validate with default values', () => {
      const result = FinancialGoalFiltersSchema.safeParse({});
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.status).toBe('active');
        expect(result.data.page).toBe(1);
        expect(result.data.limit).toBe(20);
        expect(result.data.sortBy).toBe('createdAt');
        expect(result.data.sortOrder).toBe('desc');
      }
    });

    it('should validate with all filters', () => {
      const data = {
        familyMemberId: 'member_1',
        status: 'completed' as const,
        minTargetAmount: 100000,
        maxTargetAmount: 500000,
        targetDateFrom: '2024-01-01T00:00:00.000Z',
        targetDateTo: '2024-12-31T00:00:00.000Z',
        page: 2,
        limit: 10,
        sortBy: 'targetAmount' as const,
        sortOrder: 'asc' as const,
        includeCompleted: true,
        includeMilestones: true
      };
      const result = FinancialGoalFiltersSchema.safeParse(data);
      expect(result.success).toBe(true);
    });

    it('should reject invalid amount range', () => {
      const invalidData = {
        minTargetAmount: 500000,
        maxTargetAmount: 100000
      };
      const result = FinancialGoalFiltersSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid date range', () => {
      const invalidData = {
        targetDateFrom: '2024-12-31T00:00:00.000Z',
        targetDateTo: '2024-01-01T00:00:00.000Z'
      };
      const result = FinancialGoalFiltersSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject page less than 1', () => {
      const invalidData = { page: 0 };
      const result = FinancialGoalFiltersSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject limit greater than 100', () => {
      const invalidData = { limit: 150 };
      const result = FinancialGoalFiltersSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('MilestoneFiltersSchema', () => {
    it('should validate with required goalId', () => {
      const data = { goalId: 'goal_123' };
      const result = MilestoneFiltersSchema.safeParse(data);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.status).toBe('all');
        expect(result.data.sortBy).toBe('targetDate');
        expect(result.data.sortOrder).toBe('asc');
      }
    });

    it('should validate with all filters', () => {
      const data = {
        goalId: 'goal_123',
        status: 'completed' as const,
        targetDateFrom: '2024-01-01T00:00:00.000Z',
        targetDateTo: '2024-12-31T00:00:00.000Z',
        sortBy: 'name' as const,
        sortOrder: 'desc' as const
      };
      const result = MilestoneFiltersSchema.safeParse(data);
      expect(result.success).toBe(true);
    });

    it('should reject missing goalId', () => {
      const result = MilestoneFiltersSchema.safeParse({});
      expect(result.success).toBe(false);
    });

    it('should reject invalid date range', () => {
      const invalidData = {
        goalId: 'goal_123',
        targetDateFrom: '2024-12-31T00:00:00.000Z',
        targetDateTo: '2024-01-01T00:00:00.000Z'
      };
      const result = MilestoneFiltersSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });
});
