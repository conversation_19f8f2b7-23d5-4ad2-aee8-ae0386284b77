import {
  calculateCategorySpending,
  calculateMonthlyTrends,
  calculateTrendDirection,
  detectSpendingAnomalies,
  comparePeriods,
  type TransactionData
} from '../utils/financial-analysis.utils';

describe('Financial Analysis Utils', () => {
  // Sample transaction data for testing - using recent dates
  const now = new Date();
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 15);
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 5);

  const sampleTransactions: TransactionData[] = [
    {
      id: '1',
      amount: 100,
      date: lastMonth,
      categoryId: 'cat1',
      categoryName: 'Alimentação',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'EXPENSE',
      description: 'Supermercado'
    },
    {
      id: '2',
      amount: 50,
      date: new Date(lastMonth.getTime() + 5 * 24 * 60 * 60 * 1000), // 5 days later
      categoryId: 'cat1',
      categoryName: 'Alimenta<PERSON>',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'EXPENSE',
      description: 'Restaurante'
    },
    {
      id: '3',
      amount: 200,
      date: new Date(lastMonth.getTime() + 10 * 24 * 60 * 60 * 1000), // 10 days later
      categoryId: 'cat2',
      categoryName: 'Transporte',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'EXPENSE',
      description: 'Combustível'
    },
    {
      id: '4',
      amount: 3000,
      date: new Date(lastMonth.getTime() + 15 * 24 * 60 * 60 * 1000), // 15 days later
      categoryId: 'cat3',
      categoryName: 'Salário',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'INCOME',
      description: 'Salário mensal'
    },
    {
      id: '5',
      amount: 75,
      date: thisMonth,
      categoryId: 'cat1',
      categoryName: 'Alimentação',
      accountId: 'acc1',
      accountName: 'Conta Corrente',
      type: 'EXPENSE',
      description: 'Supermercado'
    }
  ];

  describe('calculateCategorySpending', () => {
    it('should calculate spending by category correctly', () => {
      const result = calculateCategorySpending(sampleTransactions, false);

      expect(result).toHaveLength(2); // Only expense categories
      // Results are sorted by totalAmount descending, so Alimentação (225) comes first
      expect(result[0].categoryName).toBe('Alimentação');
      expect(result[0].totalAmount).toBe(225); // 100 + 50 + 75
      expect(result[0].transactionCount).toBe(3);
      expect(result[0].averageAmount).toBe(75);

      expect(result[1].categoryName).toBe('Transporte');
      expect(result[1].totalAmount).toBe(200);
      expect(result[1].transactionCount).toBe(1);
      expect(result[1].averageAmount).toBe(200);
    });

    it('should include income when specified', () => {
      const result = calculateCategorySpending(sampleTransactions, true);
      
      expect(result).toHaveLength(3); // All categories including income
      expect(result[0].categoryName).toBe('Salário');
      expect(result[0].totalAmount).toBe(3000);
    });

    it('should calculate percentages correctly', () => {
      const result = calculateCategorySpending(sampleTransactions, false);
      const total = result.reduce((sum, cat) => sum + cat.totalAmount, 0);

      expect(total).toBe(425); // 200 + 225
      // Alimentação comes first (225), Transporte second (200)
      expect(result[0].percentage).toBeCloseTo(52.94, 2); // 225/425 * 100
      expect(result[1].percentage).toBeCloseTo(47.06, 2); // 200/425 * 100
    });

    it('should handle empty transactions', () => {
      const result = calculateCategorySpending([]);
      expect(result).toHaveLength(0);
    });

    it('should handle transactions without categories', () => {
      const transactionsWithoutCategory: TransactionData[] = [
        {
          id: '1',
          amount: 100,
          date: new Date('2024-01-15'),
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE',
          description: 'Sem categoria'
        }
      ];

      const result = calculateCategorySpending(transactionsWithoutCategory);
      expect(result).toHaveLength(1);
      expect(result[0].categoryName).toBe('Sem Categoria');
      expect(result[0].categoryId).toBe('uncategorized');
    });
  });

  describe('calculateMonthlyTrends', () => {
    it('should calculate monthly trends correctly', () => {
      const result = calculateMonthlyTrends(sampleTransactions, 12);

      expect(result.length).toBeGreaterThanOrEqual(1); // At least one month
      if (result.length >= 2) {
        // Check that amounts are calculated correctly (excluding income)
        const lastMonthData = result.find(r => r.amount === 350); // 100 + 50 + 200
        const thisMonthData = result.find(r => r.amount === 75);

        if (lastMonthData) {
          expect(lastMonthData.transactionCount).toBe(3);
        }
        if (thisMonthData) {
          expect(thisMonthData.transactionCount).toBe(1);
        }
      }
    });

    it('should exclude income transactions', () => {
      const result = calculateMonthlyTrends(sampleTransactions, 12);

      // Should not include the 3000 income transaction
      const totalAmount = result.reduce((sum, r) => sum + r.amount, 0);
      expect(totalAmount).toBe(425); // 100 + 50 + 200 + 75, excluding 3000 income
    });

    it('should sort results by date', () => {
      const result = calculateMonthlyTrends(sampleTransactions, 12);

      if (result.length >= 2) {
        expect(result[0].date.getTime()).toBeLessThan(result[1].date.getTime());
      } else {
        expect(result.length).toBeGreaterThanOrEqual(0);
      }
    });

    it('should handle empty transactions', () => {
      const result = calculateMonthlyTrends([]);
      expect(result).toHaveLength(0);
    });
  });

  describe('calculateTrendDirection', () => {
    it('should detect increasing trend', () => {
      const trendData = [
        { period: '2024-01', amount: 100, transactionCount: 1, date: new Date('2024-01-01') },
        { period: '2024-02', amount: 150, transactionCount: 1, date: new Date('2024-02-01') },
        { period: '2024-03', amount: 200, transactionCount: 1, date: new Date('2024-03-01') }
      ];

      const result = calculateTrendDirection(trendData);
      
      expect(result.direction).toBe('INCREASING');
      expect(result.changePercentage).toBeCloseTo(100, 0); // (200-100)/100 * 100
      expect(result.strength).toBeGreaterThan(0.8);
    });

    it('should detect decreasing trend', () => {
      const trendData = [
        { period: '2024-01', amount: 200, transactionCount: 1, date: new Date('2024-01-01') },
        { period: '2024-02', amount: 150, transactionCount: 1, date: new Date('2024-02-01') },
        { period: '2024-03', amount: 100, transactionCount: 1, date: new Date('2024-03-01') }
      ];

      const result = calculateTrendDirection(trendData);
      
      expect(result.direction).toBe('DECREASING');
      expect(result.changePercentage).toBeCloseTo(-50, 0); // (100-200)/200 * 100
      expect(result.strength).toBeGreaterThan(0.8);
    });

    it('should detect stable trend', () => {
      const trendData = [
        { period: '2024-01', amount: 100, transactionCount: 1, date: new Date('2024-01-01') },
        { period: '2024-02', amount: 100, transactionCount: 1, date: new Date('2024-02-01') },
        { period: '2024-03', amount: 100, transactionCount: 1, date: new Date('2024-03-01') },
        { period: '2024-04', amount: 100, transactionCount: 1, date: new Date('2024-04-01') },
        { period: '2024-05', amount: 100, transactionCount: 1, date: new Date('2024-05-01') }
      ];

      const result = calculateTrendDirection(trendData);

      expect(result.direction).toBe('STABLE');
      expect(result.strength).toBeLessThan(0.8);
    });

    it('should handle insufficient data', () => {
      const trendData = [
        { period: '2024-01', amount: 100, transactionCount: 1, date: new Date('2024-01-01') }
      ];

      const result = calculateTrendDirection(trendData);
      
      expect(result.direction).toBe('STABLE');
      expect(result.strength).toBe(0);
      expect(result.changePercentage).toBe(0);
    });
  });

  describe('detectSpendingAnomalies', () => {
    it('should detect anomalous transactions', () => {
      // Create more transactions to have enough data for analysis
      const baseTransactions: TransactionData[] = [];
      for (let i = 0; i < 15; i++) {
        baseTransactions.push({
          id: `base-${i}`,
          amount: 50 + Math.random() * 20, // Normal range 50-70
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000), // Last 15 days
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE',
          description: 'Compra normal'
        });
      }

      const transactionsWithAnomaly: TransactionData[] = [
        ...baseTransactions,
        {
          id: '6',
          amount: 500, // Anomalously high for this category
          date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // Yesterday
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE',
          description: 'Compra anômala'
        }
      ];

      const result = detectSpendingAnomalies(transactionsWithAnomaly, 90, 2.0);

      expect(result.length).toBeGreaterThan(0);
      expect(result[0].transaction.amount).toBe(500);
      expect(result[0].anomalyScore).toBeGreaterThan(2.0);
    });

    it('should handle insufficient data', () => {
      const fewTransactions = sampleTransactions.slice(0, 2);
      const result = detectSpendingAnomalies(fewTransactions, 90, 2.0);
      
      expect(result).toHaveLength(0);
    });

    it('should sort anomalies by score', () => {
      const transactionsWithMultipleAnomalies: TransactionData[] = [
        ...sampleTransactions,
        {
          id: '6',
          amount: 500,
          date: new Date('2024-02-10'),
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE',
          description: 'Anomalia menor'
        },
        {
          id: '7',
          amount: 1000,
          date: new Date('2024-02-15'),
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE',
          description: 'Anomalia maior'
        }
      ];

      const result = detectSpendingAnomalies(transactionsWithMultipleAnomalies, 90, 2.0);
      
      if (result.length > 1) {
        expect(result[0].anomalyScore).toBeGreaterThanOrEqual(result[1].anomalyScore);
      }
    });
  });

  describe('comparePeriods', () => {
    it('should compare periods correctly', () => {
      // Create specific test data for this test
      const previousTransactions = [
        {
          id: '1',
          amount: 100,
          date: new Date('2024-01-15'),
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE' as const,
          description: 'Supermercado'
        },
        {
          id: '2',
          amount: 200,
          date: new Date('2024-01-20'),
          categoryId: 'cat2',
          categoryName: 'Transporte',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE' as const,
          description: 'Combustível'
        }
      ];

      const currentTransactions = [
        {
          id: '3',
          amount: 75,
          date: new Date('2024-02-15'),
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          accountId: 'acc1',
          accountName: 'Conta Corrente',
          type: 'EXPENSE' as const,
          description: 'Supermercado'
        }
      ];

      const result = comparePeriods(currentTransactions, previousTransactions);

      expect(result.totalChange).toBe(-225); // 75 - 300
      expect(result.totalChangePercentage).toBe(-75); // (75-300)/300 * 100
      expect(result.categoryChanges.length).toBeGreaterThanOrEqual(1);
    });

    it('should handle empty periods', () => {
      const result = comparePeriods([], sampleTransactions);
      
      expect(result.totalChange).toBeLessThan(0);
      expect(result.totalChangePercentage).toBeLessThan(0);
    });

    it('should sort category changes by absolute change', () => {
      const currentTransactions = sampleTransactions.filter(t => t.date.getMonth() === 1);
      const previousTransactions = sampleTransactions.filter(t => t.date.getMonth() === 0);

      const result = comparePeriods(currentTransactions, previousTransactions);
      
      if (result.categoryChanges.length > 1) {
        const firstChange = Math.abs(result.categoryChanges[0].change);
        const secondChange = Math.abs(result.categoryChanges[1].change);
        expect(firstChange).toBeGreaterThanOrEqual(secondChange);
      }
    });
  });
});
