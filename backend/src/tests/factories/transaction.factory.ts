/**
 * Transaction Factory for Test Data Generation
 */

import { BaseFactory } from './base.factory';
import { faker } from '@faker-js/faker';

export interface TestTransaction {
  id: string;
  description: string;
  amount: number;
  type: 'INCOME' | 'EXPENSE' | 'TRANSFER';
  date: Date;
  accountId: string;
  categoryId?: string;
  userId: string;
  tags?: string[];
  notes?: string;
  location?: string;
  receipt?: string;
  isRecurring: boolean;
  recurringRule?: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';
    interval: number;
    endDate?: Date;
  };
  transferAccountId?: string; // For transfer transactions
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  createdAt: Date;
  updatedAt: Date;
}

export class TransactionFactory extends BaseFactory<TestTransaction> {
  protected definition(): Partial<TestTransaction> {
    const transactionType = this.pickRandom(['INCOME', 'EXPENSE', 'TRANSFER'] as const);
    const amount = this.generateTransactionAmount(transactionType);
    
    return {
      id: this.generateId(),
      description: this.generateTransactionDescription(transactionType),
      amount,
      type: transactionType,
      date: this.generateTransactionDate(),
      accountId: this.generateId(), // Will be overridden when creating with account
      categoryId: this.generateId(), // Will be overridden when creating with category
      userId: this.generateId(), // Will be overridden when creating with user
      tags: this.generateTags(),
      notes: faker.datatype.boolean(0.3) ? faker.lorem.sentence() : undefined,
      location: faker.datatype.boolean(0.2) ? faker.location.city() : undefined,
      receipt: faker.datatype.boolean(0.1) ? faker.internet.url() : undefined,
      isRecurring: faker.datatype.boolean(0.1),
      recurringRule: undefined, // Will be set for recurring transactions
      transferAccountId: transactionType === 'TRANSFER' ? this.generateId() : undefined,
      status: 'COMPLETED',
      createdAt: this.generatePastTimestamp(),
      updatedAt: this.generateTimestamp(),
    };
  }

  protected getVariant(variantName: string): Partial<TestTransaction> {
    switch (variantName) {
      case 'income':
        return {
          type: 'INCOME',
          amount: this.generateAmount(1000, 10000),
          description: this.pickRandom([
            'Salário',
            'Freelance',
            'Dividendos',
            'Aluguel Recebido',
            'Venda',
            'Bonificação',
          ]),
        };
      
      case 'expense':
        return {
          type: 'EXPENSE',
          amount: -this.generateAmount(10, 1000),
          description: this.pickRandom([
            'Supermercado',
            'Restaurante',
            'Combustível',
            'Farmácia',
            'Shopping',
            'Conta de Luz',
            'Internet',
          ]),
        };
      
      case 'transfer':
        return {
          type: 'TRANSFER',
          amount: this.generateAmount(100, 5000),
          description: 'Transferência entre contas',
          transferAccountId: this.generateId(),
        };
      
      case 'recurring_monthly':
        return {
          isRecurring: true,
          recurringRule: {
            frequency: 'MONTHLY',
            interval: 1,
            endDate: this.generateFutureTimestamp(),
          },
          description: this.pickRandom([
            'Aluguel',
            'Financiamento',
            'Plano de Saúde',
            'Academia',
            'Netflix',
            'Spotify',
          ]),
        };
      
      case 'high_value':
        return {
          amount: this.generateAmount(5000, 50000),
          description: this.pickRandom([
            'Compra de Carro',
            'Reforma da Casa',
            'Viagem Internacional',
            'Investimento',
            'Equipamento',
          ]),
        };
      
      case 'small_expense':
        return {
          type: 'EXPENSE',
          amount: -this.generateAmount(5, 50),
          description: this.pickRandom([
            'Café',
            'Lanche',
            'Estacionamento',
            'Ônibus',
            'Jornal',
            'Água',
          ]),
        };
      
      case 'pending':
        return {
          status: 'PENDING',
          date: this.generateFutureTimestamp(),
        };
      
      case 'cancelled':
        return {
          status: 'CANCELLED',
          notes: 'Transação cancelada pelo usuário',
        };
      
      case 'with_receipt':
        return {
          receipt: faker.internet.url(),
          notes: 'Comprovante anexado',
        };
      
      case 'tagged':
        return {
          tags: ['importante', 'trabalho', 'pessoal'],
        };
      
      default:
        return {};
    }
  }

  /**
   * Create transaction for specific account
   */
  forAccount(accountId: string): TestTransaction {
    return this.create({ accountId });
  }

  /**
   * Create transaction for specific user
   */
  forUser(userId: string): TestTransaction {
    return this.create({ userId });
  }

  /**
   * Create transaction with specific amount
   */
  withAmount(amount: number): TestTransaction {
    return this.create({ amount });
  }

  /**
   * Create transaction for specific date
   */
  onDate(date: Date): TestTransaction {
    return this.create({ date });
  }

  /**
   * Create monthly transactions for a year
   */
  createMonthlyTransactions(
    accountId: string,
    userId: string,
    categoryId: string,
    startDate: Date = new Date()
  ): TestTransaction[] {
    const transactions: TestTransaction[] = [];
    
    for (let month = 0; month < 12; month++) {
      const date = new Date(startDate);
      date.setMonth(date.getMonth() + month);
      
      transactions.push(
        this.create({
          accountId,
          userId,
          categoryId,
          date,
          isRecurring: true,
          recurringRule: {
            frequency: 'MONTHLY',
            interval: 1,
          },
        })
      );
    }
    
    return transactions;
  }

  /**
   * Create transaction history for account
   */
  createTransactionHistory(
    accountId: string,
    userId: string,
    days: number = 90
  ): TestTransaction[] {
    const transactions: TestTransaction[] = [];
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    // Generate 1-5 transactions per day
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const transactionCount = faker.number.int({ min: 0, max: 5 });
      
      for (let i = 0; i < transactionCount; i++) {
        const transactionDate = new Date(d);
        transactionDate.setHours(
          faker.number.int({ min: 6, max: 22 }),
          faker.number.int({ min: 0, max: 59 })
        );
        
        transactions.push(
          this.create({
            accountId,
            userId,
            date: transactionDate,
          })
        );
      }
    }
    
    return transactions;
  }

  /**
   * Create balanced transaction set (income vs expenses)
   */
  createBalancedSet(
    accountId: string,
    userId: string,
    count: number = 20
  ): TestTransaction[] {
    const transactions: TestTransaction[] = [];
    const incomeCount = Math.floor(count * 0.3); // 30% income
    const expenseCount = count - incomeCount; // 70% expenses
    
    // Create income transactions
    for (let i = 0; i < incomeCount; i++) {
      transactions.push(this.variant('income', { accountId, userId }));
    }
    
    // Create expense transactions
    for (let i = 0; i < expenseCount; i++) {
      transactions.push(this.variant('expense', { accountId, userId }));
    }
    
    return transactions;
  }

  private generateTransactionAmount(type: TestTransaction['type']): number {
    switch (type) {
      case 'INCOME':
        return this.generateAmount(100, 10000);
      case 'EXPENSE':
        return -this.generateAmount(10, 1000);
      case 'TRANSFER':
        return this.generateAmount(50, 5000);
      default:
        return this.generateAmount(10, 1000);
    }
  }

  private generateTransactionDescription(type: TestTransaction['type']): string {
    const descriptions = {
      INCOME: [
        'Salário', 'Freelance', 'Dividendos', 'Aluguel Recebido', 'Venda',
        'Bonificação', 'Prêmio', 'Restituição', 'Cashback', 'Rendimento'
      ],
      EXPENSE: [
        'Supermercado', 'Restaurante', 'Combustível', 'Farmácia', 'Shopping',
        'Conta de Luz', 'Internet', 'Telefone', 'Academia', 'Médico',
        'Transporte', 'Educação', 'Entretenimento', 'Roupas', 'Casa'
      ],
      TRANSFER: [
        'Transferência entre contas', 'Depósito', 'Saque', 'PIX',
        'TED', 'DOC', 'Transferência interna'
      ]
    };

    return this.pickRandom(descriptions[type]);
  }

  private generateTransactionDate(): Date {
    // Generate dates within the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    return faker.date.between({ from: sixMonthsAgo, to: new Date() });
  }

  private generateTags(): string[] {
    const allTags = [
      'trabalho', 'pessoal', 'família', 'saúde', 'educação',
      'lazer', 'viagem', 'casa', 'carro', 'investimento',
      'emergência', 'presente', 'festa', 'esporte'
    ];

    const tagCount = faker.number.int({ min: 0, max: 3 });
    return faker.helpers.arrayElements(allTags, tagCount);
  }
}

// Export singleton instance
export const transactionFactory = new TransactionFactory();
