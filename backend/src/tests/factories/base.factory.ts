/**
 * Base Factory for Test Data Generation
 * Provides common utilities and patterns for creating test data
 */

import { faker } from '@faker-js/faker';

export interface FactoryOptions {
  count?: number;
  overrides?: Record<string, any>;
  relations?: Record<string, any>;
}

export abstract class BaseFactory<T> {
  protected abstract definition(): Partial<T>;

  /**
   * Create a single instance with optional overrides
   */
  create(overrides: Partial<T> = {}): T {
    const base = this.definition();
    return { ...base, ...overrides } as T;
  }

  /**
   * Create multiple instances
   */
  createMany(count: number, overrides: Partial<T> = {}): T[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }

  /**
   * Create instance with specific attributes
   */
  with(attributes: Partial<T>): T {
    return this.create(attributes);
  }

  /**
   * Create instance with random variations
   */
  variant(variantName: string, overrides: Partial<T> = {}): T {
    const variantData = this.getVariant(variantName);
    return this.create({ ...variantData, ...overrides });
  }

  /**
   * Override this method to define variants
   */
  protected getVariant(variantName: string): Partial<T> {
    return {};
  }

  /**
   * Generate a unique ID
   */
  protected generateId(): string {
    return faker.string.uuid();
  }

  /**
   * Generate a timestamp
   */
  protected generateTimestamp(): Date {
    return faker.date.recent();
  }

  /**
   * Generate a future timestamp
   */
  protected generateFutureTimestamp(): Date {
    return faker.date.future();
  }

  /**
   * Generate a past timestamp
   */
  protected generatePastTimestamp(): Date {
    return faker.date.past();
  }

  /**
   * Generate a random amount
   */
  protected generateAmount(min: number = 10, max: number = 1000): number {
    return parseFloat(faker.finance.amount({ min, max, dec: 2 }));
  }

  /**
   * Generate a random currency
   */
  protected generateCurrency(): string {
    return faker.helpers.arrayElement(['BRL', 'USD', 'EUR', 'GBP']);
  }

  /**
   * Generate a random email
   */
  protected generateEmail(): string {
    return faker.internet.email();
  }

  /**
   * Generate a random name
   */
  protected generateName(): string {
    return faker.person.fullName();
  }

  /**
   * Generate a random description
   */
  protected generateDescription(): string {
    return faker.lorem.sentence();
  }

  /**
   * Generate a random color
   */
  protected generateColor(): string {
    return faker.internet.color();
  }

  /**
   * Generate a random boolean
   */
  protected generateBoolean(): boolean {
    return faker.datatype.boolean();
  }

  /**
   * Pick random element from array
   */
  protected pickRandom<K>(array: K[]): K {
    return faker.helpers.arrayElement(array);
  }

  /**
   * Generate a sequence number
   */
  protected generateSequence(): number {
    return faker.number.int({ min: 1, max: 1000 });
  }
}

/**
 * Factory Registry for managing all factories
 */
export class FactoryRegistry {
  private static factories: Map<string, BaseFactory<any>> = new Map();

  static register<T>(name: string, factory: BaseFactory<T>): void {
    this.factories.set(name, factory);
  }

  static get<T>(name: string): BaseFactory<T> {
    const factory = this.factories.get(name);
    if (!factory) {
      throw new Error(`Factory '${name}' not found`);
    }
    return factory;
  }

  static create<T>(name: string, overrides: Partial<T> = {}): T {
    return this.get<T>(name).create(overrides);
  }

  static createMany<T>(name: string, count: number, overrides: Partial<T> = {}): T[] {
    return this.get<T>(name).createMany(count, overrides);
  }

  static clear(): void {
    this.factories.clear();
  }
}

/**
 * Utility functions for test data generation
 */
export class TestDataUtils {
  /**
   * Generate consistent test data based on seed
   */
  static withSeed<T>(seed: number, generator: () => T): T {
    faker.seed(seed);
    const result = generator();
    faker.seed(); // Reset seed
    return result;
  }

  /**
   * Generate test data for specific locale
   */
  static withLocale<T>(locale: string, generator: () => T): T {
    const originalLocale = faker.getLocale();
    faker.setLocale(locale);
    const result = generator();
    faker.setLocale(originalLocale);
    return result;
  }

  /**
   * Generate realistic Brazilian test data
   */
  static brazilianData() {
    return {
      cpf: this.generateCPF(),
      phone: this.generateBrazilianPhone(),
      cep: this.generateCEP(),
      state: faker.helpers.arrayElement([
        'SP', 'RJ', 'MG', 'RS', 'PR', 'SC', 'BA', 'GO', 'PE', 'CE'
      ]),
    };
  }

  /**
   * Generate a valid CPF (Brazilian tax ID)
   */
  private static generateCPF(): string {
    const digits = Array.from({ length: 9 }, () => faker.number.int({ min: 0, max: 9 }));
    
    // Calculate first check digit
    let sum = digits.reduce((acc, digit, index) => acc + digit * (10 - index), 0);
    let checkDigit1 = 11 - (sum % 11);
    if (checkDigit1 >= 10) checkDigit1 = 0;
    
    // Calculate second check digit
    sum = digits.reduce((acc, digit, index) => acc + digit * (11 - index), 0) + checkDigit1 * 2;
    let checkDigit2 = 11 - (sum % 11);
    if (checkDigit2 >= 10) checkDigit2 = 0;
    
    return [...digits, checkDigit1, checkDigit2].join('');
  }

  /**
   * Generate Brazilian phone number
   */
  private static generateBrazilianPhone(): string {
    const areaCode = faker.helpers.arrayElement(['11', '21', '31', '41', '51', '61', '71', '81', '85']);
    const number = faker.string.numeric(9);
    return `(${areaCode}) ${number.slice(0, 5)}-${number.slice(5)}`;
  }

  /**
   * Generate Brazilian postal code (CEP)
   */
  private static generateCEP(): string {
    const digits = faker.string.numeric(8);
    return `${digits.slice(0, 5)}-${digits.slice(5)}`;
  }
}
