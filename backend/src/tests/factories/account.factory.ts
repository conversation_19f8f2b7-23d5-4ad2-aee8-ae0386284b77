/**
 * Account Factory for Test Data Generation
 */

import { BaseFactory } from './base.factory';
import { faker } from '@faker-js/faker';

export interface TestAccount {
  id: string;
  name: string;
  type: 'CHECKING' | 'SAVINGS' | 'CREDIT_CARD' | 'INVESTMENT' | 'CASH';
  currency: string;
  balance: number;
  creditLimit?: number;
  isActive: boolean;
  userId: string;
  bankName?: string;
  accountNumber?: string;
  routingNumber?: string;
  description?: string;
  color?: string;
  icon?: string;
  createdAt: Date;
  updatedAt: Date;
}

export class AccountFactory extends BaseFactory<TestAccount> {
  protected definition(): Partial<TestAccount> {
    const accountType = this.pickRandom(['CHECKING', 'SAVINGS', 'CREDIT_CARD', 'INVESTMENT', 'CASH'] as const);
    
    return {
      id: this.generateId(),
      name: this.generateAccountName(accountType),
      type: accountType,
      currency: this.generateCurrency(),
      balance: this.generateBalance(accountType),
      creditLimit: accountType === 'CREDIT_CARD' ? this.generateAmount(1000, 10000) : undefined,
      isActive: true,
      userId: this.generateId(), // Will be overridden when creating with user
      bankName: this.generateBankName(),
      accountNumber: this.generateAccountNumber(),
      routingNumber: this.generateRoutingNumber(),
      description: this.generateDescription(),
      color: this.generateColor(),
      icon: this.generateAccountIcon(accountType),
      createdAt: this.generatePastTimestamp(),
      updatedAt: this.generateTimestamp(),
    };
  }

  protected getVariant(variantName: string): Partial<TestAccount> {
    switch (variantName) {
      case 'checking':
        return {
          type: 'CHECKING',
          name: 'Conta Corrente',
          balance: this.generateAmount(500, 5000),
          creditLimit: undefined,
        };
      
      case 'savings':
        return {
          type: 'SAVINGS',
          name: 'Poupança',
          balance: this.generateAmount(1000, 20000),
          creditLimit: undefined,
        };
      
      case 'credit_card':
        return {
          type: 'CREDIT_CARD',
          name: 'Cartão de Crédito',
          balance: -this.generateAmount(100, 2000), // Negative balance for credit cards
          creditLimit: this.generateAmount(2000, 15000),
        };
      
      case 'investment':
        return {
          type: 'INVESTMENT',
          name: 'Investimentos',
          balance: this.generateAmount(5000, 50000),
          creditLimit: undefined,
        };
      
      case 'cash':
        return {
          type: 'CASH',
          name: 'Dinheiro',
          balance: this.generateAmount(50, 500),
          creditLimit: undefined,
        };
      
      case 'inactive':
        return {
          isActive: false,
          balance: 0,
        };
      
      case 'high_balance':
        return {
          balance: this.generateAmount(50000, 100000),
        };
      
      case 'low_balance':
        return {
          balance: this.generateAmount(0, 100),
        };
      
      case 'brazilian':
        return {
          currency: 'BRL',
          bankName: this.pickRandom([
            'Banco do Brasil',
            'Itaú Unibanco',
            'Bradesco',
            'Santander',
            'Caixa Econômica Federal',
            'Nubank',
            'Inter',
            'C6 Bank',
          ]),
        };
      
      default:
        return {};
    }
  }

  /**
   * Create account for specific user
   */
  forUser(userId: string): TestAccount {
    return this.create({ userId });
  }

  /**
   * Create account with specific balance
   */
  withBalance(balance: number): TestAccount {
    return this.create({ balance });
  }

  /**
   * Create account with specific type
   */
  ofType(type: TestAccount['type']): TestAccount {
    return this.create({ type });
  }

  /**
   * Create a complete set of accounts for a user
   */
  createAccountSet(userId: string): {
    checking: TestAccount;
    savings: TestAccount;
    creditCard: TestAccount;
    investment: TestAccount;
    cash: TestAccount;
  } {
    return {
      checking: this.variant('checking', { userId }),
      savings: this.variant('savings', { userId }),
      creditCard: this.variant('credit_card', { userId }),
      investment: this.variant('investment', { userId }),
      cash: this.variant('cash', { userId }),
    };
  }

  /**
   * Create multiple accounts with different balances
   */
  createBalanceVariety(userId: string, count: number = 5): TestAccount[] {
    const balanceRanges = [
      { min: 0, max: 100 },
      { min: 100, max: 1000 },
      { min: 1000, max: 5000 },
      { min: 5000, max: 20000 },
      { min: 20000, max: 100000 },
    ];

    return Array.from({ length: count }, (_, index) => {
      const range = balanceRanges[index % balanceRanges.length];
      return this.create({
        userId,
        balance: this.generateAmount(range.min, range.max),
      });
    });
  }

  private generateAccountName(type: TestAccount['type']): string {
    const names = {
      CHECKING: ['Conta Corrente', 'Conta Principal', 'Conta Salário'],
      SAVINGS: ['Poupança', 'Reserva de Emergência', 'Poupança Objetivo'],
      CREDIT_CARD: ['Cartão de Crédito', 'Cartão Principal', 'Cartão Empresarial'],
      INVESTMENT: ['Investimentos', 'Renda Fixa', 'Renda Variável', 'Fundos'],
      CASH: ['Dinheiro', 'Carteira', 'Cofre'],
    };

    return this.pickRandom(names[type]);
  }

  private generateBalance(type: TestAccount['type']): number {
    const ranges = {
      CHECKING: { min: 100, max: 10000 },
      SAVINGS: { min: 1000, max: 50000 },
      CREDIT_CARD: { min: -5000, max: 0 }, // Negative for credit cards
      INVESTMENT: { min: 5000, max: 100000 },
      CASH: { min: 50, max: 1000 },
    };

    const range = ranges[type];
    return this.generateAmount(range.min, range.max);
  }

  private generateBankName(): string {
    return this.pickRandom([
      'Banco do Brasil',
      'Itaú Unibanco',
      'Bradesco',
      'Santander',
      'Caixa Econômica Federal',
      'Nubank',
      'Inter',
      'C6 Bank',
      'XP Investimentos',
      'BTG Pactual',
    ]);
  }

  private generateAccountNumber(): string {
    return faker.finance.accountNumber();
  }

  private generateRoutingNumber(): string {
    return faker.finance.routingNumber();
  }

  private generateAccountIcon(type: TestAccount['type']): string {
    const icons = {
      CHECKING: ['credit-card', 'wallet', 'bank'],
      SAVINGS: ['piggy-bank', 'safe', 'vault'],
      CREDIT_CARD: ['credit-card', 'card'],
      INVESTMENT: ['trending-up', 'bar-chart', 'pie-chart'],
      CASH: ['dollar-sign', 'coins', 'banknote'],
    };

    return this.pickRandom(icons[type]);
  }
}

// Export singleton instance
export const accountFactory = new AccountFactory();
