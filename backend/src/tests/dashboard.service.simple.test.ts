import { DashboardService } from '../services/dashboard.service';
import { dashboardCache } from '../services/dashboard-cache.service';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    account: {
      findMany: jest.fn()
    },
    transaction: {
      findMany: jest.fn()
    },
    budget: {
      findMany: jest.fn()
    },
    goal: {
      findMany: jest.fn()
    },
    accountBalanceHistory: {
      findMany: jest.fn()
    }
  }
}));

// Mock Dashboard Cache
jest.mock('../services/dashboard-cache.service', () => ({
  dashboardCache: {
    withCache: jest.fn()
  }
}));

describe('DashboardService - Unit Tests', () => {
  let dashboardService: DashboardService;
  let mockPrisma: any;
  let mockDashboardCache: jest.Mocked<typeof dashboardCache>;

  beforeEach(() => {
    dashboardService = new DashboardService();
    mockPrisma = require('../lib/prisma').default;
    mockDashboardCache = dashboardCache as jest.Mocked<typeof dashboardCache>;
    
    // Reset all mocks
    jest.clearAllMocks();
    
    // Default cache behavior - pass through to actual function
    mockDashboardCache.withCache.mockImplementation(async (operation, filters, dataFetcher) => {
      return await dataFetcher();
    });
  });

  describe('getAccountBalances', () => {
    it('should return account balances with empty data', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue([]);
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getAccountBalances({});

      // Assert
      expect(result).toEqual({
        totalBalance: 0,
        totalBalanceInBRL: 0,
        projectedBalance: 0,
        projectedBalanceInBRL: 0,
        balanceByType: [],
        balanceByCurrency: []
      });

      // Verify Prisma calls
      expect(mockPrisma.account.findMany).toHaveBeenCalled();
      expect(mockPrisma.transaction.findMany).toHaveBeenCalled();

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'ACCOUNT_BALANCES',
        {},
        expect.any(Function)
      );
    });

    it('should handle Prisma errors gracefully', async () => {
      // Arrange
      mockPrisma.account.findMany.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert
      await expect(dashboardService.getAccountBalances({})).rejects.toThrow('Database connection failed');
    });
  });

  describe('getNetWorth', () => {
    it('should return net worth with empty data', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue([]);
      mockPrisma.accountBalanceHistory.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getNetWorth({});

      // Assert
      expect(result).toHaveProperty('currentNetWorth');
      expect(result).toHaveProperty('assets');
      expect(result).toHaveProperty('liabilities');
      expect(result).toHaveProperty('monthlyHistory');
      expect(result.currentNetWorth).toBe(0);

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'NET_WORTH',
        {},
        expect.any(Function)
      );
    });
  });

  describe('getCreditCardUsage', () => {
    it('should return credit card usage with empty data', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getCreditCardUsage({});

      // Assert
      expect(result).toHaveProperty('totalUsage');
      expect(result).toHaveProperty('totalLimit');
      expect(result).toHaveProperty('usagePercentage');
      expect(result).toHaveProperty('availableCredit');
      expect(result).toHaveProperty('cardDetails');
      expect(result).toHaveProperty('alerts');
      expect(result.totalUsage).toBe(0);

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'CREDIT_CARD_USAGE',
        {},
        expect.any(Function)
      );
    });
  });

  describe('getExpensesByCategory', () => {
    it('should return expenses by category with empty data', async () => {
      // Arrange
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getExpensesByCategory({});

      // Assert
      expect(result).toHaveProperty('totalExpenses');
      expect(result).toHaveProperty('categories');
      expect(result.totalExpenses).toBe(0);
      expect(result.categories).toEqual([]);

      // Note: This method may not use cache, so we just verify it was called
      expect(mockPrisma.transaction.findMany).toHaveBeenCalled();
    });
  });

  describe('getExpensesByMember', () => {
    it('should return expenses by member with empty data', async () => {
      // Arrange
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getExpensesByMember({});

      // Assert
      expect(result).toHaveProperty('totalExpenses');
      expect(result).toHaveProperty('members');
      expect(result).toHaveProperty('comparison');
      expect(result.totalExpenses).toBe(0);
      expect(result.members).toEqual([]);

      // Note: This method may not use cache, so we just verify it was called
      expect(mockPrisma.transaction.findMany).toHaveBeenCalled();
    });
  });

  describe('getBudgetComparison', () => {
    it('should return budget comparison with empty data', async () => {
      // Arrange
      mockPrisma.budget.findMany.mockResolvedValue([]);
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getBudgetComparison({});

      // Assert
      expect(result).toHaveProperty('totalBudgeted');
      expect(result).toHaveProperty('totalSpent');
      expect(result).toHaveProperty('totalRemaining');
      expect(result).toHaveProperty('overallProgress');
      expect(result).toHaveProperty('categories');
      expect(result).toHaveProperty('alerts');
      expect(result.totalBudgeted).toBe(0);

      // Note: This method may not use cache, so we just verify it was called
      expect(mockPrisma.budget.findMany).toHaveBeenCalled();
    });
  });

  describe('getGoalProgress', () => {
    it('should return goal progress with empty data', async () => {
      // Arrange
      mockPrisma.goal.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getGoalProgress({});

      // Assert
      expect(result).toHaveProperty('totalGoals');
      expect(result).toHaveProperty('activeGoals');
      expect(result).toHaveProperty('completedGoals');
      expect(result).toHaveProperty('goals');
      expect(result).toHaveProperty('summary');
      expect(result.totalGoals).toBe(0);

      // Note: This method may not use cache, so we just verify it was called
      expect(mockPrisma.goal.findMany).toHaveBeenCalled();
    });
  });

  describe('getOverview', () => {
    it('should return complete dashboard overview', async () => {
      // Arrange - Mock all Prisma calls to return empty data
      mockPrisma.account.findMany.mockResolvedValue([]);
      mockPrisma.transaction.findMany.mockResolvedValue([]);
      mockPrisma.budget.findMany.mockResolvedValue([]);
      mockPrisma.goal.findMany.mockResolvedValue([]);
      mockPrisma.accountBalanceHistory.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getOverview({});

      // Assert
      expect(result).toHaveProperty('accountBalances');
      expect(result).toHaveProperty('netWorth');
      expect(result).toHaveProperty('creditCardUsage');
      expect(result).toHaveProperty('expensesByCategory');
      expect(result).toHaveProperty('expensesByMember');
      expect(result).toHaveProperty('budgetComparison');
      expect(result).toHaveProperty('goalProgress');
      expect(result).toHaveProperty('generatedAt');
      expect(result).toHaveProperty('filters');

      // Overview calls individual methods, so cache is called multiple times
      expect(mockDashboardCache.withCache).toHaveBeenCalledTimes(3); // At least 3 calls
    });
  });

  describe('getDatabaseMetrics', () => {
    it('should return database performance metrics', async () => {
      // Act
      const result = await dashboardService.getDatabaseMetrics();

      // Assert
      expect(result).toHaveProperty('slowQueries');
      expect(result).toHaveProperty('averageQueryTime');
      expect(result).toHaveProperty('totalQueries');
      expect(Array.isArray(result.slowQueries)).toBe(true);
      expect(typeof result.averageQueryTime).toBe('number');
      expect(typeof result.totalQueries).toBe('number');
    });
  });

  describe('getPerformanceMetrics', () => {
    it('should return performance logs array', () => {
      // Act
      const result = dashboardService.getPerformanceMetrics();

      // Assert
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle cache failures gracefully', async () => {
      // Arrange
      mockDashboardCache.withCache.mockRejectedValue(new Error('Cache error'));
      mockPrisma.account.findMany.mockResolvedValue([]);

      // Act & Assert - Should propagate cache error
      await expect(dashboardService.getAccountBalances({})).rejects.toThrow('Cache error');
    });

    it('should handle database connection errors', async () => {
      // Arrange
      mockPrisma.account.findMany.mockRejectedValue(new Error('Connection timeout'));

      // Act & Assert
      await expect(dashboardService.getAccountBalances({})).rejects.toThrow('Connection timeout');
    });
  });

  describe('Filter Validation', () => {
    it('should accept valid filters', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue([]);
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      // Use valid CUID format for IDs
      const validFilters = {
        accountIds: ['clp123456789012345678901', 'clp123456789012345678902'],
        currencies: ['BRL' as const, 'USD' as const],
        dateRange: {
          startDate: '2024-01-01T00:00:00.000Z',
          endDate: '2024-12-31T23:59:59.999Z'
        }
      };

      // Act
      const result = await dashboardService.getAccountBalances(validFilters);

      // Assert
      expect(result).toBeDefined();
      // The service validates filters and may use defaults if invalid
      expect(mockPrisma.account.findMany).toHaveBeenCalled();
    });
  });
});
