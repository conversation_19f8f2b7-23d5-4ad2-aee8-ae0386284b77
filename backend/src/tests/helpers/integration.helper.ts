import request from 'supertest';
import { Express } from 'express';
import { generateTestToken, generateTestTokenWithPayload } from './auth.helper';

/**
 * Integration test helper for API endpoints
 */
export class IntegrationTestHelper {
  private app: Express;
  private authToken: string | null = null;

  constructor(app: Express) {
    this.app = app;
  }

  /**
   * Setup authentication for requests
   */
  async setupAuth(customPayload?: any): Promise<string> {
    this.authToken = customPayload 
      ? await generateTestTokenWithPayload(customPayload)
      : await generateTestToken();
    return this.authToken;
  }

  /**
   * Make authenticated GET request
   */
  async get(endpoint: string, query?: any) {
    const req = request(this.app).get(endpoint);
    
    if (this.authToken) {
      req.set('Authorization', `Bearer ${this.authToken}`);
    }
    
    if (query) {
      req.query(query);
    }
    
    return req;
  }

  /**
   * Make authenticated POST request
   */
  async post(endpoint: string, data?: any) {
    const req = request(this.app).post(endpoint);
    
    if (this.authToken) {
      req.set('Authorization', `Bearer ${this.authToken}`);
    }
    
    if (data) {
      req.send(data);
    }
    
    return req;
  }

  /**
   * Make authenticated PUT request
   */
  async put(endpoint: string, data?: any) {
    const req = request(this.app).put(endpoint);
    
    if (this.authToken) {
      req.set('Authorization', `Bearer ${this.authToken}`);
    }
    
    if (data) {
      req.send(data);
    }
    
    return req;
  }

  /**
   * Make authenticated PATCH request
   */
  async patch(endpoint: string, data?: any) {
    const req = request(this.app).patch(endpoint);
    
    if (this.authToken) {
      req.set('Authorization', `Bearer ${this.authToken}`);
    }
    
    if (data) {
      req.send(data);
    }
    
    return req;
  }

  /**
   * Make authenticated DELETE request
   */
  async delete(endpoint: string) {
    const req = request(this.app).delete(endpoint);
    
    if (this.authToken) {
      req.set('Authorization', `Bearer ${this.authToken}`);
    }
    
    return req;
  }

  /**
   * Make unauthenticated request (for testing auth failures)
   */
  unauthenticated() {
    return {
      get: (endpoint: string, query?: any) => {
        const req = request(this.app).get(endpoint);
        if (query) req.query(query);
        return req;
      },
      post: (endpoint: string, data?: any) => {
        const req = request(this.app).post(endpoint);
        if (data) req.send(data);
        return req;
      },
      put: (endpoint: string, data?: any) => {
        const req = request(this.app).put(endpoint);
        if (data) req.send(data);
        return req;
      },
      patch: (endpoint: string, data?: any) => {
        const req = request(this.app).patch(endpoint);
        if (data) req.send(data);
        return req;
      },
      delete: (endpoint: string) => request(this.app).delete(endpoint)
    };
  }

  /**
   * Clear authentication
   */
  clearAuth() {
    this.authToken = null;
  }
}

/**
 * Common test data generators
 */
export const TestDataGenerator = {
  user: (overrides = {}) => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  }),

  account: (overrides = {}) => ({
    id: 'test-account-id',
    name: 'Test Account',
    type: 'CHECKING',
    currency: 'USD',
    balance: 1000,
    userId: 'test-user-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  }),

  transaction: (overrides = {}) => ({
    id: 'test-transaction-id',
    description: 'Test Transaction',
    amount: 100,
    type: 'EXPENSE',
    accountId: 'test-account-id',
    categoryId: 'test-category-id',
    userId: 'test-user-id',
    date: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  }),

  category: (overrides = {}) => ({
    id: 'test-category-id',
    name: 'Test Category',
    type: 'EXPENSE',
    color: '#FF0000',
    icon: 'shopping-cart',
    userId: 'test-user-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  })
};

/**
 * Common assertion helpers
 */
export const TestAssertions = {
  expectSuccessResponse: (response: any, expectedData?: any) => {
    expect(response.body).toHaveProperty('success', true);
    if (expectedData) {
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toMatchObject(expectedData);
    }
  },

  expectErrorResponse: (response: any, expectedError?: string) => {
    expect(response.body).toHaveProperty('success', false);
    expect(response.body).toHaveProperty('error');
    if (expectedError) {
      expect(response.body.error).toContain(expectedError);
    }
  },

  expectValidationError: (response: any, field?: string) => {
    expect(response.status).toBe(400);
    expect(response.body).toHaveProperty('success', false);
    expect(response.body).toHaveProperty('error');
    if (field) {
      expect(response.body.error).toContain(field);
    }
  },

  expectAuthError: (response: any) => {
    expect(response.status).toBe(401);
    expect(response.body).toHaveProperty('success', false);
    expect(response.body).toHaveProperty('error');
  },

  expectNotFoundError: (response: any) => {
    expect(response.status).toBe(404);
    expect(response.body).toHaveProperty('success', false);
    expect(response.body).toHaveProperty('error');
  }
};
