import jwt from 'jsonwebtoken';

/**
 * Generate a test JWT token for authentication in tests
 */
export const generateTestToken = async (): Promise<string> => {
  const testUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User'
  };

  const secret = process.env.JWT_SECRET || 'test-secret';
  
  return jwt.sign(testUser, secret, { expiresIn: '1h' });
};

/**
 * Generate a test token with custom payload
 */
export const generateTestTokenWithPayload = async (payload: any): Promise<string> => {
  const secret = process.env.JWT_SECRET || 'test-secret';
  
  return jwt.sign(payload, secret, { expiresIn: '1h' });
};

/**
 * Generate an expired test token
 */
export const generateExpiredTestToken = async (): Promise<string> => {
  const testUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User'
  };

  const secret = process.env.JWT_SECRET || 'test-secret';
  
  return jwt.sign(testUser, secret, { expiresIn: '-1h' }); // Expired 1 hour ago
};

/**
 * Generate an invalid test token
 */
export const generateInvalidTestToken = (): string => {
  return 'invalid.jwt.token';
};
