import { jest } from '@jest/globals';
import Redis from 'ioredis';

// Load test environment variables
require('dotenv').config({ path: '.env.test' });

// Ensure test environment
process.env.NODE_ENV = 'test';

// Global Redis client for tests
let redisClient: Redis;

// Mock Prisma client for tests
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    account: {
      create: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    familyMember: {
      findMany: jest.fn(),
    },
    category: {
      create: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
      count: jest.fn(),
    },
    budget: {
      findFirst: jest.fn(),
    },
    accountMember: {
      createMany: jest.fn(),
      deleteMany: jest.fn(),
      findMany: jest.fn(),
    },
    transaction: {
      create: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
      count: jest.fn(),
    },
    user: {
      create: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    recurringTransaction: {
      create: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
      count: jest.fn(),
      aggregate: jest.fn(),
    },
    accountBalanceHistory: {
      findFirst: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

// Mock authentication middleware for tests
jest.mock('../middleware/auth.middleware', () => ({
  authenticateToken: (req: any, res: any, next: any) => {
    req.user = { id: 'test-user-id', email: '<EMAIL>' };
    next();
  },
  requireActiveUser: (req: any, res: any, next: any) => {
    next();
  },
}));

// Global test setup
beforeAll(async () => {
  try {
    // Initialize Redis client for tests
    redisClient = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6380'),
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    // Test Redis connection
    await redisClient.ping();
    console.log('✅ Redis connection established for tests');

    // Store Redis client globally for use in tests
    (global as any).__REDIS_CLIENT__ = redisClient;

  } catch (error) {
    console.warn('⚠️ Redis connection failed, tests will use mocks:', (error as Error).message);
  }
});

afterAll(async () => {
  try {
    // Cleanup Redis connection
    if (redisClient) {
      await redisClient.quit();
      console.log('✅ Redis connection closed');
    }
  } catch (error) {
    console.warn('⚠️ Error closing Redis connection:', (error as Error).message);
  }
});

beforeEach(async () => {
  // Reset mocks before each test
  jest.clearAllMocks();

  // Clear Redis cache if available
  try {
    if (redisClient && redisClient.status === 'ready') {
      await redisClient.flushdb();
    }
  } catch (error) {
    // Ignore Redis errors in tests
  }
});

afterEach(async () => {
  // Cleanup after each test
  try {
    // Clear any remaining Redis data
    if (redisClient && redisClient.status === 'ready') {
      await redisClient.flushdb();
    }
  } catch (error) {
    // Ignore Redis errors in tests
  }
});

// Helper function to generate test JWT token
export function generateTestToken(): string {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    {
      id: 'test-user-id',
      email: '<EMAIL>'
    },
    process.env.JWT_SECRET || 'test-jwt-secret',
    { expiresIn: '1h' }
  );
}
