import {
  normalizeText,
  tokenizeText,
  levenshteinDistance,
  levenshteinSimilarity,
  jaccardSimilarity,
  cosineSimilarity,
  combinedSimilarity,
  findMostSimilar,
  extractKeywords
} from '../utils/text-similarity.utils';

describe('Text Similarity Utils', () => {
  describe('normalizeText', () => {
    it('should convert to lowercase', () => {
      expect(normalizeText('HELLO WORLD')).toBe('hello world');
    });

    it('should remove accents', () => {
      expect(normalizeText('café açúcar')).toBe('cafe acucar');
    });

    it('should remove punctuation', () => {
      expect(normalizeText('hello, world!')).toBe('hello world');
    });

    it('should normalize whitespace', () => {
      expect(normalizeText('hello    world   ')).toBe('hello world');
    });

    it('should handle empty string', () => {
      expect(normalizeText('')).toBe('');
    });
  });

  describe('tokenizeText', () => {
    it('should split text into words', () => {
      const tokens = tokenizeText('hello world test');
      expect(tokens).toEqual(['hello', 'world', 'test']);
    });

    it('should filter out stop words', () => {
      const tokens = tokenizeText('the quick brown fox');
      expect(tokens).toEqual(['quick', 'brown', 'fox']);
    });

    it('should filter out short words', () => {
      const tokens = tokenizeText('a big cat is on the mat');
      expect(tokens).toEqual(['big', 'cat', 'mat']);
    });

    it('should handle Portuguese stop words', () => {
      const tokens = tokenizeText('compra de pão no mercado');
      expect(tokens).toEqual(['compra', 'pao', 'mercado']);
    });

    it('should handle empty string', () => {
      expect(tokenizeText('')).toEqual([]);
    });
  });

  describe('levenshteinDistance', () => {
    it('should calculate distance for identical strings', () => {
      expect(levenshteinDistance('hello', 'hello')).toBe(0);
    });

    it('should calculate distance for completely different strings', () => {
      expect(levenshteinDistance('abc', 'xyz')).toBe(3);
    });

    it('should calculate distance for one insertion', () => {
      expect(levenshteinDistance('cat', 'cats')).toBe(1);
    });

    it('should calculate distance for one deletion', () => {
      expect(levenshteinDistance('cats', 'cat')).toBe(1);
    });

    it('should calculate distance for one substitution', () => {
      expect(levenshteinDistance('cat', 'bat')).toBe(1);
    });

    it('should handle empty strings', () => {
      expect(levenshteinDistance('', 'abc')).toBe(3);
      expect(levenshteinDistance('abc', '')).toBe(3);
      expect(levenshteinDistance('', '')).toBe(0);
    });
  });

  describe('levenshteinSimilarity', () => {
    it('should return 1 for identical strings', () => {
      expect(levenshteinSimilarity('hello', 'hello')).toBe(1);
    });

    it('should return 0 for completely different strings of same length', () => {
      expect(levenshteinSimilarity('abc', 'xyz')).toBe(0);
    });

    it('should return value between 0 and 1 for similar strings', () => {
      const similarity = levenshteinSimilarity('cat', 'bat');
      expect(similarity).toBeGreaterThan(0);
      expect(similarity).toBeLessThan(1);
    });

    it('should handle case insensitive comparison', () => {
      expect(levenshteinSimilarity('Hello', 'hello')).toBe(1);
    });

    it('should handle accents', () => {
      expect(levenshteinSimilarity('café', 'cafe')).toBe(1);
    });
  });

  describe('jaccardSimilarity', () => {
    it('should return 1 for identical token sets', () => {
      const tokens1 = ['hello', 'world'];
      const tokens2 = ['hello', 'world'];
      expect(jaccardSimilarity(tokens1, tokens2)).toBe(1);
    });

    it('should return 0 for completely different token sets', () => {
      const tokens1 = ['hello', 'world'];
      const tokens2 = ['foo', 'bar'];
      expect(jaccardSimilarity(tokens1, tokens2)).toBe(0);
    });

    it('should calculate partial similarity', () => {
      const tokens1 = ['hello', 'world', 'test'];
      const tokens2 = ['hello', 'world', 'example'];
      const similarity = jaccardSimilarity(tokens1, tokens2);
      expect(similarity).toBeCloseTo(0.5); // 2 common / 4 total
    });

    it('should handle empty arrays', () => {
      expect(jaccardSimilarity([], [])).toBe(1);
      expect(jaccardSimilarity(['hello'], [])).toBe(0);
      expect(jaccardSimilarity([], ['hello'])).toBe(0);
    });
  });

  describe('cosineSimilarity', () => {
    it('should return 1 for identical texts', () => {
      expect(cosineSimilarity('hello world', 'hello world')).toBeCloseTo(1, 5);
    });

    it('should return 0 for completely different texts', () => {
      const similarity = cosineSimilarity('hello world', 'foo bar');
      expect(similarity).toBe(0);
    });

    it('should calculate partial similarity', () => {
      const similarity = cosineSimilarity('hello world test', 'hello world example');
      expect(similarity).toBeGreaterThan(0);
      expect(similarity).toBeLessThan(1);
    });

    it('should handle empty strings', () => {
      expect(cosineSimilarity('', '')).toBe(1);
      expect(cosineSimilarity('hello', '')).toBe(0);
      expect(cosineSimilarity('', 'hello')).toBe(0);
    });
  });

  describe('combinedSimilarity', () => {
    it('should return 1 for identical texts', () => {
      expect(combinedSimilarity('hello world', 'hello world')).toBeCloseTo(1, 5);
    });

    it('should return value between 0 and 1 for similar texts', () => {
      const similarity = combinedSimilarity('hello world', 'hello earth');
      expect(similarity).toBeGreaterThan(0);
      expect(similarity).toBeLessThan(1);
    });

    it('should accept custom weights', () => {
      const similarity1 = combinedSimilarity('hello world', 'hello earth');
      const similarity2 = combinedSimilarity('hello world', 'hello earth', {
        levenshtein: 1,
        jaccard: 0,
        cosine: 0
      });
      expect(similarity1).not.toBe(similarity2);
    });
  });

  describe('findMostSimilar', () => {
    const candidates = [
      'compra no supermercado',
      'pagamento de conta',
      'transferência bancária',
      'compra de combustível'
    ];

    it('should find most similar text', () => {
      const result = findMostSimilar('compra no mercado', candidates);
      expect(result).not.toBeNull();
      expect(result!.text).toBe('compra no supermercado');
      expect(result!.index).toBe(0);
    });

    it('should return null for empty candidates', () => {
      const result = findMostSimilar('test', []);
      expect(result).toBeNull();
    });

    it('should work with different algorithms', () => {
      const result = findMostSimilar('compra no mercado', candidates, 'levenshtein');
      expect(result).not.toBeNull();
      expect(result!.similarity).toBeGreaterThan(0);
    });
  });

  describe('extractKeywords', () => {
    it('should extract keywords from text', () => {
      const keywords = extractKeywords('compra de pão no supermercado grande');
      expect(keywords).toContain('compra');
      expect(keywords).toContain('supermercado');
      expect(keywords).toContain('grande');
    });

    it('should limit number of keywords', () => {
      const keywords = extractKeywords('one two three four five six seven', 3);
      expect(keywords).toHaveLength(3);
    });

    it('should prefer longer words', () => {
      const keywords = extractKeywords('supermercado big');
      expect(keywords[0]).toBe('supermercado');
    });

    it('should handle empty text', () => {
      const keywords = extractKeywords('');
      expect(keywords).toEqual([]);
    });
  });

  describe('Real-world transaction examples', () => {
    it('should identify similar transaction descriptions', () => {
      const transactions = [
        'Compra Supermercado Extra',
        'Pagamento Conta Luz',
        'Transferência PIX',
        'Compra Posto Shell'
      ];

      const newTransaction = 'Compra Supermercado Carrefour';
      const result = findMostSimilar(newTransaction, transactions);
      
      expect(result).not.toBeNull();
      expect(result!.text).toBe('Compra Supermercado Extra');
      expect(result!.similarity).toBeGreaterThan(0.5);
    });

    it('should handle financial terms correctly', () => {
      const similarity = combinedSimilarity(
        'Pagamento cartão de crédito',
        'Pagamento fatura cartão'
      );
      expect(similarity).toBeGreaterThan(0.5);
    });
  });
});
