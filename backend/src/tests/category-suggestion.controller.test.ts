import request from 'supertest';
import express from 'express';
import { categorySuggestionController } from '../controllers/category-suggestion.controller';
import { categorySuggestionService } from '../services/category-suggestion.service';
import { TransactionType } from '@prisma/client';

// Mock the category suggestion service
jest.mock('../services/category-suggestion.service', () => ({
  categorySuggestionService: {
    getSuggestions: jest.fn(),
    getSuggestionsForExpense: jest.fn(),
    getSuggestionsForIncome: jest.fn()
  }
}));

const mockService = categorySuggestionService as jest.Mocked<typeof categorySuggestionService>;

// Create test app
const app = express();
app.use(express.json());

// Add routes
app.post('/suggestions', categorySuggestionController.getSuggestions.bind(categorySuggestionController));
app.post('/suggestions/expense', categorySuggestionController.getSuggestionsForExpense.bind(categorySuggestionController));
app.post('/suggestions/income', categorySuggestionController.getSuggestionsForIncome.bind(categorySuggestionController));
app.post('/suggestions/bulk', categorySuggestionController.getBulkSuggestions.bind(categorySuggestionController));
app.post('/suggestions/feedback', categorySuggestionController.submitFeedback.bind(categorySuggestionController));

describe('CategorySuggestionController', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /suggestions', () => {
    it('should return suggestions for valid request', async () => {
      const mockSuggestions = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          confidence: 0.85,
          reason: 'Descrição similar',
          matchType: 'description' as const
        }
      ];

      mockService.getSuggestions.mockResolvedValue(mockSuggestions);

      const response = await request(app)
        .post('/suggestions')
        .send({
          description: 'Compra no supermercado',
          amount: 150,
          type: TransactionType.EXPENSE
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.suggestions).toEqual(mockSuggestions);
      expect(response.body.data.metadata.totalSuggestions).toBe(1);
      expect(response.body.data.metadata.processingTimeMs).toBeGreaterThan(0);
    });

    it('should return validation error for invalid request', async () => {
      const response = await request(app)
        .post('/suggestions')
        .send({
          // Missing required description
          amount: 150
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should handle service errors gracefully', async () => {
      mockService.getSuggestions.mockRejectedValue(new Error('Service error'));

      const response = await request(app)
        .post('/suggestions')
        .send({
          description: 'Test transaction',
          amount: 100
        });

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INTERNAL_SERVER_ERROR');
    });
  });

  describe('POST /suggestions/expense', () => {
    it('should return expense-optimized suggestions', async () => {
      const mockSuggestions = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          confidence: 0.9,
          reason: 'Otimizado para despesas',
          matchType: 'description' as const
        }
      ];

      mockService.getSuggestionsForExpense.mockResolvedValue(mockSuggestions);

      const response = await request(app)
        .post('/suggestions/expense')
        .send({
          description: 'Compra no supermercado',
          amount: 150,
          type: TransactionType.EXPENSE
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.suggestions).toEqual(mockSuggestions);
      expect(response.body.data.metadata.algorithmWeights.description).toBe(0.5);
    });
  });

  describe('POST /suggestions/income', () => {
    it('should return income-optimized suggestions', async () => {
      const mockSuggestions = [
        {
          categoryId: 'cat1',
          categoryName: 'Salário',
          confidence: 0.95,
          reason: 'Otimizado para receitas',
          matchType: 'description' as const
        }
      ];

      mockService.getSuggestionsForIncome.mockResolvedValue(mockSuggestions);

      const response = await request(app)
        .post('/suggestions/income')
        .send({
          description: 'Salário empresa',
          amount: 3000,
          type: TransactionType.INCOME
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.suggestions).toEqual(mockSuggestions);
      expect(response.body.data.metadata.algorithmWeights.description).toBe(0.6);
    });
  });

  describe('POST /suggestions/bulk', () => {
    it('should process multiple transactions', async () => {
      const mockSuggestions = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          confidence: 0.8,
          reason: 'Bulk suggestion',
          matchType: 'description' as const
        }
      ];

      mockService.getSuggestions.mockResolvedValue(mockSuggestions);

      const response = await request(app)
        .post('/suggestions/bulk')
        .send({
          transactions: [
            {
              id: 'trans1',
              description: 'Compra supermercado',
              amount: 150,
              type: TransactionType.EXPENSE
            },
            {
              id: 'trans2',
              description: 'Pagamento conta luz',
              amount: 120,
              type: TransactionType.EXPENSE
            }
          ],
          maxSuggestionsPerTransaction: 3,
          minConfidence: 0.3
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.results).toHaveLength(2);
      expect(response.body.data.metadata.totalTransactions).toBe(2);
      expect(response.body.data.metadata.successfulSuggestions).toBe(2);
      expect(response.body.data.metadata.failedSuggestions).toBe(0);
    });

    it('should handle individual transaction errors in bulk', async () => {
      mockService.getSuggestions
        .mockResolvedValueOnce([]) // First transaction succeeds
        .mockRejectedValueOnce(new Error('Service error')); // Second transaction fails

      const response = await request(app)
        .post('/suggestions/bulk')
        .send({
          transactions: [
            {
              id: 'trans1',
              description: 'Valid transaction',
              amount: 150
            },
            {
              id: 'trans2',
              description: 'Error transaction',
              amount: 120
            }
          ]
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.metadata.successfulSuggestions).toBe(1);
      expect(response.body.data.metadata.failedSuggestions).toBe(1);
      expect(response.body.data.results[1].error).toBeDefined();
    });

    it('should validate bulk request structure', async () => {
      const response = await request(app)
        .post('/suggestions/bulk')
        .send({
          transactions: [] // Empty array should fail validation
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /suggestions/feedback', () => {
    it('should accept valid feedback', async () => {
      const response = await request(app)
        .post('/suggestions/feedback')
        .send({
          transactionId: 'trans123',
          suggestedCategoryId: 'cat1',
          actualCategoryId: 'cat2',
          wasAccepted: false,
          confidence: 0.8,
          matchType: 'description',
          feedback: 'Sugestão não foi adequada'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Feedback registrado com sucesso');
    });

    it('should validate feedback data', async () => {
      const response = await request(app)
        .post('/suggestions/feedback')
        .send({
          // Missing required fields
          transactionId: 'trans123'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('Error handling', () => {
    it('should handle unknown errors', async () => {
      mockService.getSuggestions.mockRejectedValue('Unknown error');

      const response = await request(app)
        .post('/suggestions')
        .send({
          description: 'Test transaction',
          amount: 100
        });

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('UNKNOWN_ERROR');
    });
  });
});
