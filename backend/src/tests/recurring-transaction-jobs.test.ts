import { RecurrenceFrequency, TransactionType } from '@prisma/client';

describe('RecurringTransactionJobService', () => {
  it('should have correct frequency types for job processing', () => {
    expect(RecurrenceFrequency.DAILY).toBe('DAILY');
    expect(RecurrenceFrequency.WEEKLY).toBe('WEEKLY');
    expect(RecurrenceFrequency.MONTHLY).toBe('MONTHLY');
    expect(RecurrenceFrequency.YEARLY).toBe('YEARLY');
  });

  it('should have correct transaction types for job processing', () => {
    expect(TransactionType.INCOME).toBe('INCOME');
    expect(TransactionType.EXPENSE).toBe('EXPENSE');
    expect(TransactionType.TRANSFER).toBe('TRANSFER');
  });
});
