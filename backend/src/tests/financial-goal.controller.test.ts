import { Request, Response } from 'express';
import { financialGoalController } from '../controllers/financial-goal.controller';
import { financialGoalService } from '../services/financial-goal.service';

// Mock the service
jest.mock('../services/financial-goal.service', () => ({
  financialGoalService: {
    create: jest.fn(),
    findAll: jest.fn(),
    findById: jest.fn(),
    update: jest.fn(),
    updateProgress: jest.fn(),
    delete: jest.fn(),
    getSummary: jest.fn()
  }
}));

const mockFinancialGoalService = financialGoalService as jest.Mocked<typeof financialGoalService>;

describe('FinancialGoalController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockJson: jest.Mock;
  let mockStatus: jest.Mock;

  beforeEach(() => {
    mockJson = jest.fn();
    mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    
    mockRequest = {};
    mockResponse = {
      status: mockStatus,
      json: mockJson
    };

    jest.clearAllMocks();
  });

  describe('create', () => {
    const validGoalData = {
      name: 'Casa Própria',
      targetAmount: 300000,
      currentAmount: 50000,
      targetDate: '2025-12-31T00:00:00.000Z',
      familyMemberIds: ['member_1', 'member_2']
    };

    const mockCreatedGoal = {
      id: 'goal_123',
      name: 'Casa Própria',
      targetAmount: 300000,
      currentAmount: 50000,
      targetDate: '2025-12-31T00:00:00.000Z',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      version: 1,
      progress: {
        percentage: 16.67,
        remainingAmount: 250000,
        isCompleted: false,
        isOverdue: false,
        daysRemaining: 365,
        status: 'in_progress'
      },
      members: [
        { id: 'member_1', name: 'João Silva', color: '#3B82F6' },
        { id: 'member_2', name: 'Maria Silva', color: '#EF4444' }
      ]
    };

    it('should create a financial goal successfully', async () => {
      // Setup
      mockRequest.body = validGoalData;
      mockFinancialGoalService.create.mockResolvedValue(mockCreatedGoal);

      // Execute
      await financialGoalController.create(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockFinancialGoalService.create).toHaveBeenCalledWith(validGoalData);
      expect(mockStatus).toHaveBeenCalledWith(201);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockCreatedGoal,
        message: 'Meta financeira criada com sucesso'
      });
    });

    it('should handle validation errors', async () => {
      // Setup
      mockRequest.body = { name: '' }; // Invalid data

      // Execute
      await financialGoalController.create(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: expect.any(Array)
        }
      });
    });

    it('should handle service errors', async () => {
      // Setup
      mockRequest.body = validGoalData;
      mockFinancialGoalService.create.mockRejectedValue(new Error('Erro interno'));

      // Execute
      await financialGoalController.create(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Erro interno',
          code: 'BUSINESS_LOGIC_ERROR'
        }
      });
    });
  });

  describe('findAll', () => {
    const mockGoals = [
      {
        id: 'goal_1',
        name: 'Casa Própria',
        targetAmount: 300000,
        currentAmount: 50000,
        targetDate: '2025-12-31T00:00:00.000Z',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        version: 1,
        progress: {
          percentage: 16.67,
          remainingAmount: 250000,
          isCompleted: false,
          isOverdue: false,
          status: 'in_progress'
        },
        members: []
      }
    ];

    const mockPaginatedResponse = {
      data: mockGoals,
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1
      }
    };

    it('should return paginated goals', async () => {
      // Setup
      mockRequest.query = {
        page: '1',
        limit: '20',
        status: 'active'
      };
      mockFinancialGoalService.findAll.mockResolvedValue(mockPaginatedResponse);

      // Execute
      await financialGoalController.findAll(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockFinancialGoalService.findAll).toHaveBeenCalled();
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockGoals,
        pagination: mockPaginatedResponse.pagination,
        message: 'Metas financeiras recuperadas com sucesso'
      });
    });

    it('should handle query parameter conversion', async () => {
      // Setup
      mockRequest.query = {
        minTargetAmount: '100000',
        maxTargetAmount: '500000',
        page: '2',
        limit: '10',
        includeCompleted: 'true',
        includeMilestones: 'false'
      };
      mockFinancialGoalService.findAll.mockResolvedValue(mockPaginatedResponse);

      // Execute
      await financialGoalController.findAll(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockFinancialGoalService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          minTargetAmount: 100000,
          maxTargetAmount: 500000,
          page: 2,
          limit: 10,
          includeCompleted: true,
          includeMilestones: false
        })
      );
    });
  });

  describe('findById', () => {
    const mockGoal = {
      id: 'goal_123',
      name: 'Casa Própria',
      targetAmount: 300000,
      currentAmount: 50000,
      progress: {
        percentage: 16.67,
        remainingAmount: 250000,
        isCompleted: false,
        isOverdue: false,
        status: 'in_progress'
      },
      members: []
    };

    it('should return goal by id', async () => {
      // Setup
      mockRequest.params = { id: 'goal_123' };
      mockRequest.query = {};
      mockFinancialGoalService.findById.mockResolvedValue(mockGoal);

      // Execute
      await financialGoalController.findById(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockFinancialGoalService.findById).toHaveBeenCalledWith('goal_123', true);
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockGoal,
        message: 'Meta financeira recuperada com sucesso'
      });
    });

    it('should return 404 for non-existent goal', async () => {
      // Setup
      mockRequest.params = { id: 'nonexistent' };
      mockRequest.query = {};
      mockFinancialGoalService.findById.mockResolvedValue(null);

      // Execute
      await financialGoalController.findById(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(404);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Meta financeira não encontrada',
          code: 'GOAL_NOT_FOUND'
        }
      });
    });

    it('should return 400 for invalid id', async () => {
      // Setup
      mockRequest.params = {};
      mockRequest.query = {};

      // Execute
      await financialGoalController.findById(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'ID da meta financeira é obrigatório',
          code: 'INVALID_ID'
        }
      });
    });
  });

  describe('updateProgress', () => {
    const progressData = {
      amount: 10000,
      operation: 'add',
      description: 'Depósito mensal'
    };

    const mockProgressResponse = {
      goalId: 'goal_123',
      previousAmount: 50000,
      newAmount: 60000,
      operation: 'add',
      amountChanged: 10000,
      newProgress: {
        percentage: 20,
        remainingAmount: 240000,
        isCompleted: false,
        isOverdue: false,
        status: 'in_progress'
      },
      message: 'Adicionado R$ 10.000,00 à meta. Progresso atual: 20.0%'
    };

    it('should update goal progress successfully', async () => {
      // Setup
      mockRequest.params = { id: 'goal_123' };
      mockRequest.body = progressData;
      mockFinancialGoalService.updateProgress.mockResolvedValue(mockProgressResponse);

      // Execute
      await financialGoalController.updateProgress(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockFinancialGoalService.updateProgress).toHaveBeenCalledWith('goal_123', progressData);
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockProgressResponse,
        message: mockProgressResponse.message
      });
    });

    it('should return 400 for invalid progress data', async () => {
      // Setup
      mockRequest.params = { id: 'goal_123' };
      mockRequest.body = { amount: -1000 }; // Invalid data

      // Execute
      await financialGoalController.updateProgress(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: expect.any(Array)
        }
      });
    });
  });

  describe('getSummary', () => {
    const mockSummary = {
      totalGoals: 5,
      completedGoals: 2,
      activeGoals: 2,
      overdueGoals: 1,
      totalTargetAmount: 1000000,
      totalCurrentAmount: 300000,
      overallProgress: 30,
      averageProgress: 35
    };

    it('should return goals summary', async () => {
      // Setup
      mockRequest.query = {};
      mockFinancialGoalService.getSummary.mockResolvedValue(mockSummary);

      // Execute
      await financialGoalController.getSummary(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockFinancialGoalService.getSummary).toHaveBeenCalledWith(undefined);
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockSummary,
        message: 'Resumo das metas financeiras recuperado com sucesso'
      });
    });

    it('should filter summary by family member', async () => {
      // Setup
      mockRequest.query = { familyMemberId: 'member_1' };
      mockFinancialGoalService.getSummary.mockResolvedValue(mockSummary);

      // Execute
      await financialGoalController.getSummary(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockFinancialGoalService.getSummary).toHaveBeenCalledWith('member_1');
    });
  });
});
