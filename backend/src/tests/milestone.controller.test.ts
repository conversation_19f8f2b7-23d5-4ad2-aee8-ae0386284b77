import { Request, Response } from 'express';
import { milestoneController } from '../controllers/milestone.controller';
import { milestoneService } from '../services/milestone.service';

// Mock the service
jest.mock('../services/milestone.service', () => ({
  milestoneService: {
    create: jest.fn(),
    findByGoal: jest.fn(),
    findById: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    markCompleted: jest.fn(),
    markPending: jest.fn()
  }
}));

const mockMilestoneService = milestoneService as jest.Mocked<typeof milestoneService>;

describe('MilestoneController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockJson: jest.Mock;
  let mockStatus: jest.Mock;

  beforeEach(() => {
    mockJson = jest.fn();
    mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    
    mockRequest = {};
    mockResponse = {
      status: mockStatus,
      json: mockJson
    };

    jest.clearAllMocks();
  });

  describe('create', () => {
    const validMilestoneData = {
      name: 'Entrada do Apartamento',
      targetAmount: 60000,
      targetDate: '2024-06-30T00:00:00.000Z'
    };

    const mockCreatedMilestone = {
      id: 'milestone_123',
      goalId: 'goal_123',
      name: 'Entrada do Apartamento',
      targetAmount: 60000,
      targetDate: '2024-06-30T00:00:00.000Z',
      isCompleted: false,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      version: 1
    };

    it('should create a milestone successfully', async () => {
      // Setup
      mockRequest.params = { goalId: 'goal_123' };
      mockRequest.body = validMilestoneData;
      mockMilestoneService.create.mockResolvedValue(mockCreatedMilestone);

      // Execute
      await milestoneController.create(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockMilestoneService.create).toHaveBeenCalledWith('goal_123', validMilestoneData);
      expect(mockStatus).toHaveBeenCalledWith(201);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockCreatedMilestone,
        message: 'Marco da meta criado com sucesso'
      });
    });

    it('should return 400 for invalid goal id', async () => {
      // Setup
      mockRequest.params = {};
      mockRequest.body = validMilestoneData;

      // Execute
      await milestoneController.create(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'ID da meta financeira é obrigatório',
          code: 'INVALID_GOAL_ID'
        }
      });
    });

    it('should handle validation errors', async () => {
      // Setup
      mockRequest.params = { goalId: 'goal_123' };
      mockRequest.body = { name: '' }; // Invalid data

      // Execute
      await milestoneController.create(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: expect.any(Array)
        }
      });
    });
  });

  describe('findByGoal', () => {
    const mockMilestones = [
      {
        id: 'milestone_1',
        goalId: 'goal_123',
        name: 'Entrada',
        targetAmount: 60000,
        targetDate: '2024-06-30T00:00:00.000Z',
        isCompleted: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        version: 1
      }
    ];

    it('should return milestones for a goal', async () => {
      // Setup
      mockRequest.params = { goalId: 'goal_123' };
      mockRequest.query = {
        status: 'all',
        sortBy: 'targetDate',
        sortOrder: 'asc'
      };
      mockMilestoneService.findByGoal.mockResolvedValue(mockMilestones);

      // Execute
      await milestoneController.findByGoal(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockMilestoneService.findByGoal).toHaveBeenCalledWith('goal_123', {
        status: 'all',
        sortBy: 'targetDate',
        sortOrder: 'asc'
      });
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockMilestones,
        message: 'Marcos da meta recuperados com sucesso'
      });
    });

    it('should handle date query parameters', async () => {
      // Setup
      mockRequest.params = { goalId: 'goal_123' };
      mockRequest.query = {
        targetDateFrom: '2024-05-01T00:00:00.000Z',
        targetDateTo: '2024-06-01T00:00:00.000Z'
      };
      mockMilestoneService.findByGoal.mockResolvedValue(mockMilestones);

      // Execute
      await milestoneController.findByGoal(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockMilestoneService.findByGoal).toHaveBeenCalledWith('goal_123', 
        expect.objectContaining({
          targetDateFrom: new Date('2024-05-01T00:00:00.000Z'),
          targetDateTo: new Date('2024-06-01T00:00:00.000Z')
        })
      );
    });
  });

  describe('findById', () => {
    const mockMilestone = {
      id: 'milestone_123',
      goalId: 'goal_123',
      name: 'Entrada',
      targetAmount: 60000,
      targetDate: '2024-06-30T00:00:00.000Z',
      isCompleted: false,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      version: 1
    };

    it('should return milestone by id', async () => {
      // Setup
      mockRequest.params = { id: 'milestone_123' };
      mockMilestoneService.findById.mockResolvedValue(mockMilestone);

      // Execute
      await milestoneController.findById(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockMilestoneService.findById).toHaveBeenCalledWith('milestone_123');
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockMilestone,
        message: 'Marco da meta recuperado com sucesso'
      });
    });

    it('should return 404 for non-existent milestone', async () => {
      // Setup
      mockRequest.params = { id: 'nonexistent' };
      mockMilestoneService.findById.mockResolvedValue(null);

      // Execute
      await milestoneController.findById(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(404);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Marco da meta não encontrado',
          code: 'MILESTONE_NOT_FOUND'
        }
      });
    });
  });

  describe('update', () => {
    const updateData = {
      name: 'Entrada Atualizada',
      targetAmount: 65000
    };

    const mockUpdatedMilestone = {
      id: 'milestone_123',
      goalId: 'goal_123',
      name: 'Entrada Atualizada',
      targetAmount: 65000,
      targetDate: '2024-06-30T00:00:00.000Z',
      isCompleted: false,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      version: 1
    };

    it('should update milestone successfully', async () => {
      // Setup
      mockRequest.params = { id: 'milestone_123' };
      mockRequest.body = updateData;
      mockMilestoneService.update.mockResolvedValue(mockUpdatedMilestone);

      // Execute
      await milestoneController.update(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockMilestoneService.update).toHaveBeenCalledWith('milestone_123', updateData);
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockUpdatedMilestone,
        message: 'Marco da meta atualizado com sucesso'
      });
    });

    it('should return 400 for empty update data', async () => {
      // Setup
      mockRequest.params = { id: 'milestone_123' };
      mockRequest.body = {};

      // Execute
      await milestoneController.update(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Nenhum dado fornecido para atualização',
          code: 'NO_UPDATE_DATA'
        }
      });
    });
  });

  describe('markCompleted', () => {
    const mockCompletedMilestone = {
      id: 'milestone_123',
      goalId: 'goal_123',
      name: 'Entrada',
      targetAmount: 60000,
      targetDate: '2024-06-30T00:00:00.000Z',
      isCompleted: true,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      version: 1
    };

    it('should mark milestone as completed', async () => {
      // Setup
      mockRequest.params = { id: 'milestone_123' };
      mockMilestoneService.markCompleted.mockResolvedValue(mockCompletedMilestone);

      // Execute
      await milestoneController.markCompleted(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockMilestoneService.markCompleted).toHaveBeenCalledWith('milestone_123');
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockCompletedMilestone,
        message: 'Marco marcado como concluído com sucesso'
      });
    });
  });

  describe('markPending', () => {
    const mockPendingMilestone = {
      id: 'milestone_123',
      goalId: 'goal_123',
      name: 'Entrada',
      targetAmount: 60000,
      targetDate: '2024-06-30T00:00:00.000Z',
      isCompleted: false,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      version: 1
    };

    it('should mark milestone as pending', async () => {
      // Setup
      mockRequest.params = { id: 'milestone_123' };
      mockMilestoneService.markPending.mockResolvedValue(mockPendingMilestone);

      // Execute
      await milestoneController.markPending(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockMilestoneService.markPending).toHaveBeenCalledWith('milestone_123');
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        data: mockPendingMilestone,
        message: 'Marco marcado como pendente com sucesso'
      });
    });
  });

  describe('delete', () => {
    it('should delete milestone successfully', async () => {
      // Setup
      mockRequest.params = { id: 'milestone_123' };
      mockMilestoneService.delete.mockResolvedValue(undefined);

      // Execute
      await milestoneController.delete(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockMilestoneService.delete).toHaveBeenCalledWith('milestone_123');
      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        message: 'Marco da meta deletado com sucesso'
      });
    });

    it('should handle service errors', async () => {
      // Setup
      mockRequest.params = { id: 'milestone_123' };
      mockMilestoneService.delete.mockRejectedValue(new Error('Marco não encontrado'));

      // Execute
      await milestoneController.delete(mockRequest as Request, mockResponse as Response);

      // Assertions
      expect(mockStatus).toHaveBeenCalledWith(404);
      expect(mockJson).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Marco não encontrado',
          code: 'NOT_FOUND'
        }
      });
    });
  });
});
