import { CategorySuggestionService } from '../services/category-suggestion.service';
import { HistoricalAnalysisService } from '../services/historical-analysis.service';
import {
  normalizeText,
  tokenizeText,
  levenshteinSimilarity,
  combinedSimilarity,
  findMostSimilar
} from '../utils/text-similarity.utils';
import { TransactionType } from '@prisma/client';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  default: {
    transaction: {
      findMany: jest.fn(),
      groupBy: jest.fn()
    },
    category: {
      findMany: jest.fn()
    }
  }
}));

describe('Category Suggestion System - Comprehensive Tests', () => {
  let suggestionService: CategorySuggestionService;
  let analysisService: HistoricalAnalysisService;

  beforeEach(() => {
    suggestionService = new CategorySuggestionService();
    analysisService = new HistoricalAnalysisService();
    analysisService.clearCache();
  });

  describe('Text Similarity Utils - Real World Scenarios', () => {
    it('should handle Brazilian Portuguese financial terms correctly', () => {
      const transactions = [
        'Compra Supermercado Extra',
        'Pagamento Cartão de Crédito',
        'Transferência PIX',
        'Depósito Salário',
        'Saque Caixa Eletrônico'
      ];

      // Test normalization
      expect(normalizeText('Compra Supermercado Extra')).toBe('compra supermercado extra');
      expect(normalizeText('Pagamento Cartão de Crédito')).toBe('pagamento cartao de credito');

      // Test tokenization
      const tokens = tokenizeText('Compra no Supermercado Extra');
      expect(tokens).toContain('compra');
      expect(tokens).toContain('supermercado');
      expect(tokens).toContain('extra');
      expect(tokens).not.toContain('no'); // Stop word

      // Test similarity
      const similarity = levenshteinSimilarity('Compra Supermercado Extra', 'Compra Supermercado Carrefour');
      expect(similarity).toBeGreaterThan(0.7);

      // Test finding most similar
      const result = findMostSimilar('Compra Supermercado Carrefour', transactions);
      expect(result?.text).toBe('Compra Supermercado Extra');
      expect(result?.similarity).toBeGreaterThan(0.7);
    });

    it('should handle financial amounts and patterns', () => {
      const descriptions = [
        'Compra R$ 150,00 Supermercado',
        'Pagamento R$ 1.200,00 Aluguel',
        'Transferência R$ 500,00 PIX'
      ];

      descriptions.forEach(desc => {
        const normalized = normalizeText(desc);
        expect(normalized).not.toContain('R$');
        expect(normalized).not.toContain(',');
      });
    });
  });

  describe('Category Suggestion Service - Edge Cases', () => {
    it('should handle empty historical data gracefully', async () => {
      // Mock empty data
      jest.spyOn(analysisService, 'getCategoryFrequencies').mockResolvedValue([]);
      jest.spyOn(analysisService, 'getTransactionPatterns').mockResolvedValue([]);
      jest.spyOn(analysisService, 'getValueRangePatterns').mockResolvedValue([]);

      const suggestions = await suggestionService.getSuggestions({
        description: 'Nova transação sem histórico',
        amount: 100,
        type: TransactionType.EXPENSE
      });

      expect(suggestions).toHaveLength(3); // Fallback suggestions
      expect(suggestions[0].categoryName).toBe('Alimentação');
    });

    it('should prioritize different algorithms correctly', async () => {
      const mockFrequencies = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          frequency: 50,
          averageAmount: 120,
          totalTransactions: 50,
          keywords: ['compra', 'supermercado', 'mercado']
        }
      ];

      const mockPatterns = [
        {
          description: 'compra supermercado',
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          amount: 120,
          frequency: 10,
          keywords: ['compra', 'supermercado']
        }
      ];

      const mockValueRanges = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          minAmount: 50,
          maxAmount: 300,
          averageAmount: 120,
          transactionCount: 50
        }
      ];

      jest.spyOn(analysisService, 'getCategoryFrequencies').mockResolvedValue(mockFrequencies);
      jest.spyOn(analysisService, 'getTransactionPatterns').mockResolvedValue(mockPatterns);
      jest.spyOn(analysisService, 'getValueRangePatterns').mockResolvedValue(mockValueRanges);
      jest.spyOn(analysisService, 'findCategoriesByKeywords').mockResolvedValue(['cat1']);

      const suggestions = await suggestionService.getSuggestions({
        description: 'Compra no Supermercado Carrefour',
        amount: 125,
        type: TransactionType.EXPENSE
      });

      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions[0].categoryId).toBe('cat1');
      expect(suggestions[0].matchType).toBe('combined');
      expect(suggestions[0].confidence).toBeGreaterThan(0.5);
    });

    it('should handle custom configuration weights', async () => {
      const mockData = {
        frequencies: [{
          categoryId: 'cat1',
          categoryName: 'Test',
          frequency: 10,
          averageAmount: 100,
          totalTransactions: 10,
          keywords: ['test']
        }],
        patterns: [{
          description: 'test transaction',
          categoryId: 'cat1',
          categoryName: 'Test',
          amount: 100,
          frequency: 5,
          keywords: ['test']
        }],
        valueRanges: [{
          categoryId: 'cat1',
          categoryName: 'Test',
          minAmount: 50,
          maxAmount: 150,
          averageAmount: 100,
          transactionCount: 10
        }]
      };

      jest.spyOn(analysisService, 'getCategoryFrequencies').mockResolvedValue(mockData.frequencies);
      jest.spyOn(analysisService, 'getTransactionPatterns').mockResolvedValue(mockData.patterns);
      jest.spyOn(analysisService, 'getValueRangePatterns').mockResolvedValue(mockData.valueRanges);
      jest.spyOn(analysisService, 'findCategoriesByKeywords').mockResolvedValue(['cat1']);

      // Test with description-heavy weights
      const descriptionHeavy = await suggestionService.getSuggestions({
        description: 'Test transaction similar',
        amount: 100
      }, {
        descriptionWeight: 0.8,
        amountWeight: 0.1,
        frequencyWeight: 0.05,
        keywordWeight: 0.05,
        minConfidence: 0.1,
        maxSuggestions: 5
      });

      // Test with amount-heavy weights
      const amountHeavy = await suggestionService.getSuggestions({
        description: 'Different description',
        amount: 100
      }, {
        descriptionWeight: 0.1,
        amountWeight: 0.8,
        frequencyWeight: 0.05,
        keywordWeight: 0.05,
        minConfidence: 0.1,
        maxSuggestions: 5
      });

      expect(descriptionHeavy.length).toBeGreaterThan(0);
      expect(amountHeavy.length).toBeGreaterThan(0);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large datasets efficiently', async () => {
      // Mock large dataset
      const largeFrequencies = Array.from({ length: 100 }, (_, i) => ({
        categoryId: `cat${i}`,
        categoryName: `Category ${i}`,
        frequency: Math.floor(Math.random() * 50) + 1,
        averageAmount: Math.floor(Math.random() * 1000) + 50,
        totalTransactions: Math.floor(Math.random() * 50) + 1,
        keywords: [`keyword${i}`, `word${i}`]
      }));

      jest.spyOn(analysisService, 'getCategoryFrequencies').mockResolvedValue(largeFrequencies);
      jest.spyOn(analysisService, 'getTransactionPatterns').mockResolvedValue([]);
      jest.spyOn(analysisService, 'getValueRangePatterns').mockResolvedValue([]);
      jest.spyOn(analysisService, 'findCategoriesByKeywords').mockResolvedValue([]);

      const startTime = Date.now();
      const suggestions = await suggestionService.getSuggestions({
        description: 'Test transaction',
        amount: 100
      });
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete in under 1 second
      expect(suggestions.length).toBeLessThanOrEqual(5); // Respects max suggestions
    });

    it('should cache analysis results for performance', async () => {
      const mockFrequencies = [{
        categoryId: 'cat1',
        categoryName: 'Test',
        frequency: 10,
        averageAmount: 100,
        totalTransactions: 10,
        keywords: ['test']
      }];

      const getCategoriesSpy = jest.spyOn(analysisService, 'getCategoryFrequencies')
        .mockResolvedValue(mockFrequencies);

      // First call
      await analysisService.getCategoryFrequencies();
      expect(getCategoriesSpy).toHaveBeenCalledTimes(1);

      // Second call should use cache
      await analysisService.getCategoryFrequencies();
      expect(getCategoriesSpy).toHaveBeenCalledTimes(1);

      // Clear cache and call again
      analysisService.clearCache();
      await analysisService.getCategoryFrequencies();
      expect(getCategoriesSpy).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle service errors gracefully', async () => {
      jest.spyOn(analysisService, 'getCategoryFrequencies').mockRejectedValue(new Error('Database error'));

      const suggestions = await suggestionService.getSuggestions({
        description: 'Test transaction',
        amount: 100
      });

      // Should return fallback suggestions
      expect(suggestions).toHaveLength(3);
      expect(suggestions[0].categoryName).toBe('Alimentação');
    });

    it('should handle malformed data gracefully', async () => {
      // Mock malformed data
      jest.spyOn(analysisService, 'getCategoryFrequencies').mockResolvedValue([
        {
          categoryId: '',
          categoryName: '',
          frequency: -1,
          averageAmount: NaN,
          totalTransactions: 0,
          keywords: []
        }
      ]);

      const suggestions = await suggestionService.getSuggestions({
        description: 'Test transaction',
        amount: 100
      });

      // Should handle gracefully and return fallback
      expect(suggestions).toHaveLength(3);
    });
  });

  describe('Real-world Integration Scenarios', () => {
    it('should provide accurate suggestions for common Brazilian transactions', async () => {
      const brazilianData = {
        frequencies: [
          {
            categoryId: 'alimentacao',
            categoryName: 'Alimentação',
            frequency: 45,
            averageAmount: 150,
            totalTransactions: 45,
            keywords: ['compra', 'supermercado', 'mercado', 'padaria']
          },
          {
            categoryId: 'transporte',
            categoryName: 'Transporte',
            frequency: 30,
            averageAmount: 80,
            totalTransactions: 30,
            keywords: ['uber', 'combustivel', 'posto', 'gasolina']
          },
          {
            categoryId: 'utilidades',
            categoryName: 'Utilidades',
            frequency: 12,
            averageAmount: 200,
            totalTransactions: 12,
            keywords: ['conta', 'luz', 'agua', 'internet', 'telefone']
          }
        ],
        patterns: [
          {
            description: 'compra supermercado extra',
            categoryId: 'alimentacao',
            categoryName: 'Alimentação',
            amount: 150,
            frequency: 15,
            keywords: ['compra', 'supermercado']
          },
          {
            description: 'posto shell combustivel',
            categoryId: 'transporte',
            categoryName: 'Transporte',
            amount: 80,
            frequency: 10,
            keywords: ['posto', 'combustivel']
          }
        ]
      };

      jest.spyOn(analysisService, 'getCategoryFrequencies').mockResolvedValue(brazilianData.frequencies);
      jest.spyOn(analysisService, 'getTransactionPatterns').mockResolvedValue(brazilianData.patterns);
      jest.spyOn(analysisService, 'getValueRangePatterns').mockResolvedValue([]);
      jest.spyOn(analysisService, 'findCategoriesByKeywords').mockImplementation(async (description) => {
        if (description.includes('supermercado') || description.includes('compra')) return ['alimentacao'];
        if (description.includes('posto') || description.includes('combustivel')) return ['transporte'];
        if (description.includes('conta') || description.includes('luz')) return ['utilidades'];
        return [];
      });

      // Test grocery shopping
      const grocerySuggestions = await suggestionService.getSuggestions({
        description: 'Compra no Supermercado Carrefour',
        amount: 145,
        type: TransactionType.EXPENSE
      });

      expect(grocerySuggestions[0].categoryId).toBe('alimentacao');
      expect(grocerySuggestions[0].confidence).toBeGreaterThan(0.6);

      // Test gas station
      const gasSuggestions = await suggestionService.getSuggestions({
        description: 'Posto BR Combustível',
        amount: 85,
        type: TransactionType.EXPENSE
      });

      expect(gasSuggestions[0].categoryId).toBe('transporte');
      expect(gasSuggestions[0].confidence).toBeGreaterThan(0.5);
    });
  });
});
