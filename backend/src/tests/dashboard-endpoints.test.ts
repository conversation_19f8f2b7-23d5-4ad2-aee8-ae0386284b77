import request from 'supertest';
import app from '../index';
import { generateTestToken } from './helpers/auth.helper';

// Mock the dashboard service
jest.mock('../services/dashboard.service');

describe('Dashboard API Endpoints', () => {
  let authToken: string;

  beforeAll(async () => {
    authToken = await generateTestToken();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/dashboard/overview', () => {
    it('should return dashboard overview with valid token', async () => {
      const mockOverview = {
        accountBalances: { totalBalance: 1000 },
        netWorth: { current: 5000 },
        creditCardUsage: { totalUsed: 500 },
        expensesByCategory: [],
        expensesByMember: [],
        budgetComparison: [],
        goalProgress: []
      };

      // Mock the dashboard service
      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getOverview = jest.fn().mockResolvedValue(mockOverview);

      const response = await request(app)
        .get('/api/v1/dashboard/overview')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockOverview
      });
    });

    it('should return 401 without valid token', async () => {
      await request(app)
        .get('/api/dashboard/overview')
        .expect(401);
    });

    it('should handle query parameters correctly', async () => {
      const mockOverview = { accountBalances: { totalBalance: 1000 } };

      const { DashboardService } = require('../services/dashboard.service');
      const mockGetOverview = jest.fn().mockResolvedValue(mockOverview);
      DashboardService.prototype.getOverview = mockGetOverview;

      await request(app)
        .get('/api/dashboard/overview')
        .query({
          accountIds: 'acc1,acc2',
          month: '12',
          year: '2024'
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify the service was called with transformed filters
      expect(mockGetOverview).toHaveBeenCalledWith({
        accountIds: ['acc1', 'acc2'],
        period: { month: 12, year: 2024 }
      });
    });
  });

  describe('GET /api/dashboard/account-balances', () => {
    it('should return account balances', async () => {
      const mockBalances = {
        totalBalance: 1000,
        totalBalanceInBRL: 1000,
        projectedBalance: 1100,
        projectedBalanceInBRL: 1100,
        balanceByType: [],
        balanceByCurrency: []
      };

      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getAccountBalances = jest.fn().mockResolvedValue(mockBalances);

      const response = await request(app)
        .get('/api/dashboard/account-balances')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockBalances
      });
    });
  });

  describe('GET /api/dashboard/net-worth', () => {
    it('should return net worth data', async () => {
      const mockNetWorth = {
        current: 5000,
        currentInBRL: 5000,
        projected: 5500,
        projectedInBRL: 5500,
        history: [],
        growth: { amount: 500, percentage: 10 }
      };

      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getNetWorth = jest.fn().mockResolvedValue(mockNetWorth);

      const response = await request(app)
        .get('/api/dashboard/net-worth')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockNetWorth
      });
    });
  });

  describe('GET /api/dashboard/credit-card-usage', () => {
    it('should return credit card usage', async () => {
      const mockUsage = {
        totalUsed: 500,
        totalUsedInBRL: 500,
        totalLimit: 2000,
        totalLimitInBRL: 2000,
        utilizationRate: 25,
        cardUsage: []
      };

      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getCreditCardUsage = jest.fn().mockResolvedValue(mockUsage);

      const response = await request(app)
        .get('/api/dashboard/credit-card-usage')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockUsage
      });
    });
  });

  describe('GET /api/dashboard/expenses-by-category', () => {
    it('should return expenses by category', async () => {
      const mockExpenses = {
        totalExpenses: 1000,
        totalExpensesInBRL: 1000,
        categories: []
      };

      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getExpensesByCategory = jest.fn().mockResolvedValue(mockExpenses);

      const response = await request(app)
        .get('/api/dashboard/expenses-by-category')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockExpenses
      });
    });
  });

  describe('GET /api/dashboard/expenses-by-member', () => {
    it('should return expenses by member', async () => {
      const mockExpenses = {
        totalExpenses: 1000,
        totalExpensesInBRL: 1000,
        members: []
      };

      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getExpensesByMember = jest.fn().mockResolvedValue(mockExpenses);

      const response = await request(app)
        .get('/api/dashboard/expenses-by-member')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockExpenses
      });
    });
  });

  describe('GET /api/dashboard/budget-comparison', () => {
    it('should return budget comparison', async () => {
      const mockComparison = {
        totalBudget: 2000,
        totalBudgetInBRL: 2000,
        totalSpent: 1500,
        totalSpentInBRL: 1500,
        totalRemaining: 500,
        totalRemainingInBRL: 500,
        overallUtilization: 75,
        categories: []
      };

      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getBudgetComparison = jest.fn().mockResolvedValue(mockComparison);

      const response = await request(app)
        .get('/api/dashboard/budget-comparison')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockComparison
      });
    });
  });

  describe('GET /api/dashboard/goal-progress', () => {
    it('should return goal progress', async () => {
      const mockProgress = {
        totalGoals: 3,
        completedGoals: 1,
        inProgressGoals: 2,
        totalTargetAmount: 10000,
        totalTargetAmountInBRL: 10000,
        totalCurrentAmount: 3000,
        totalCurrentAmountInBRL: 3000,
        overallProgress: 30,
        goals: []
      };

      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getGoalProgress = jest.fn().mockResolvedValue(mockProgress);

      const response = await request(app)
        .get('/api/dashboard/goal-progress')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockProgress
      });
    });
  });

  describe('GET /api/dashboard/performance-metrics', () => {
    it('should return performance metrics', async () => {
      const mockMetrics = {
        slowQueries: [],
        averageQueryTime: 150,
        totalQueries: 100
      };

      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getDatabaseMetrics = jest.fn().mockResolvedValue(mockMetrics);

      const response = await request(app)
        .get('/api/dashboard/performance-metrics')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockMetrics
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid filters gracefully', async () => {
      const response = await request(app)
        .get('/api/dashboard/overview')
        .query({
          month: 'invalid',
          year: 'invalid'
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid dashboard filters');
    });

    it('should handle service errors gracefully', async () => {
      const { DashboardService } = require('../services/dashboard.service');
      DashboardService.prototype.getOverview = jest.fn().mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .get('/api/dashboard/overview')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Failed to fetch dashboard overview');
    });
  });
});
