import { TransactionType } from '@prisma/client';
import {
  CategorySuggestionRequestSchema,
  CategorySuggestionSchema,
  CategorySuggestionResponseSchema,
  BulkCategorySuggestionRequestSchema,
  SuggestionConfigSchema,
  SuggestionFeedbackSchema,
  validateCategorySuggestionRequest,
  validateBulkCategorySuggestionRequest,
  validateSuggestionConfig,
  validateSuggestionFeedback
} from '../schemas/category-suggestion.schemas';

describe('Category Suggestion Schemas', () => {
  describe('CategorySuggestionRequestSchema', () => {
    it('should validate valid request', () => {
      const validRequest = {
        description: 'Compra no supermercado',
        amount: 150.50,
        type: TransactionType.EXPENSE,
        userId: '123e4567-e89b-12d3-a456-426614174000',
        maxSuggestions: 5,
        minConfidence: 0.3
      };

      const result = CategorySuggestionRequestSchema.safeParse(validRequest);
      expect(result.success).toBe(true);
    });

    it('should require description', () => {
      const invalidRequest = {
        amount: 150.50,
        type: TransactionType.EXPENSE
      };

      const result = CategorySuggestionRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Required');
      }
    });

    it('should validate amount is positive', () => {
      const invalidRequest = {
        description: 'Test transaction',
        amount: -100
      };

      const result = CategorySuggestionRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Valor deve ser positivo');
      }
    });

    it('should validate maxSuggestions range', () => {
      const invalidRequest = {
        description: 'Test transaction',
        maxSuggestions: 15
      };

      const result = CategorySuggestionRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Máximo de 10 sugestões permitidas');
      }
    });

    it('should validate minConfidence range', () => {
      const invalidRequest = {
        description: 'Test transaction',
        minConfidence: 1.5
      };

      const result = CategorySuggestionRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Confiança mínima deve ser entre 0 e 1');
      }
    });

    it('should apply default values', () => {
      const request = {
        description: 'Test transaction'
      };

      const result = CategorySuggestionRequestSchema.safeParse(request);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.maxSuggestions).toBe(5);
        expect(result.data.minConfidence).toBe(0.3);
      }
    });
  });

  describe('CategorySuggestionSchema', () => {
    it('should validate valid suggestion', () => {
      const validSuggestion = {
        categoryId: 'cat123',
        categoryName: 'Alimentação',
        confidence: 0.85,
        reason: 'Descrição similar a transações anteriores',
        matchType: 'description'
      };

      const result = CategorySuggestionSchema.safeParse(validSuggestion);
      expect(result.success).toBe(true);
    });

    it('should validate confidence range', () => {
      const invalidSuggestion = {
        categoryId: 'cat123',
        categoryName: 'Alimentação',
        confidence: 1.5,
        reason: 'Test reason',
        matchType: 'description'
      };

      const result = CategorySuggestionSchema.safeParse(invalidSuggestion);
      expect(result.success).toBe(false);
    });

    it('should validate matchType enum', () => {
      const invalidSuggestion = {
        categoryId: 'cat123',
        categoryName: 'Alimentação',
        confidence: 0.8,
        reason: 'Test reason',
        matchType: 'invalid_type'
      };

      const result = CategorySuggestionSchema.safeParse(invalidSuggestion);
      expect(result.success).toBe(false);
    });
  });

  describe('BulkCategorySuggestionRequestSchema', () => {
    it('should validate valid bulk request', () => {
      const validRequest = {
        transactions: [
          {
            id: 'trans1',
            description: 'Compra supermercado',
            amount: 150,
            type: TransactionType.EXPENSE
          },
          {
            description: 'Pagamento conta luz',
            amount: 120
          }
        ],
        userId: '123e4567-e89b-12d3-a456-426614174000',
        maxSuggestionsPerTransaction: 3,
        minConfidence: 0.3
      };

      const result = BulkCategorySuggestionRequestSchema.safeParse(validRequest);
      expect(result.success).toBe(true);
    });

    it('should require at least one transaction', () => {
      const invalidRequest = {
        transactions: []
      };

      const result = BulkCategorySuggestionRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Pelo menos uma transação é obrigatória');
      }
    });

    it('should limit maximum transactions', () => {
      const invalidRequest = {
        transactions: Array.from({ length: 51 }, (_, i) => ({
          description: `Transaction ${i}`,
          amount: 100
        }))
      };

      const result = BulkCategorySuggestionRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Máximo de 50 transações por lote');
      }
    });
  });

  describe('SuggestionConfigSchema', () => {
    it('should validate valid config', () => {
      const validConfig = {
        descriptionWeight: 0.4,
        amountWeight: 0.2,
        frequencyWeight: 0.2,
        keywordWeight: 0.2,
        minConfidence: 0.3,
        maxSuggestions: 5
      };

      const result = SuggestionConfigSchema.safeParse(validConfig);
      expect(result.success).toBe(true);
    });

    it('should require weights to sum to 1.0', () => {
      const invalidConfig = {
        descriptionWeight: 0.5,
        amountWeight: 0.3,
        frequencyWeight: 0.3,
        keywordWeight: 0.2 // Sum = 1.3
      };

      const result = SuggestionConfigSchema.safeParse(invalidConfig);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('A soma dos pesos deve ser igual a 1.0');
      }
    });

    it('should apply default values', () => {
      const config = {};

      const result = SuggestionConfigSchema.safeParse(config);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.descriptionWeight).toBe(0.4);
        expect(result.data.amountWeight).toBe(0.2);
        expect(result.data.frequencyWeight).toBe(0.2);
        expect(result.data.keywordWeight).toBe(0.2);
        expect(result.data.minConfidence).toBe(0.3);
        expect(result.data.maxSuggestions).toBe(5);
      }
    });
  });

  describe('SuggestionFeedbackSchema', () => {
    it('should validate valid feedback', () => {
      const validFeedback = {
        transactionId: 'trans123',
        suggestedCategoryId: 'cat1',
        actualCategoryId: 'cat2',
        wasAccepted: false,
        confidence: 0.8,
        matchType: 'description',
        userId: '123e4567-e89b-12d3-a456-426614174000',
        feedback: 'Sugestão não foi adequada'
      };

      const result = SuggestionFeedbackSchema.safeParse(validFeedback);
      expect(result.success).toBe(true);
    });

    it('should apply default timestamp', () => {
      const feedback = {
        transactionId: 'trans123',
        suggestedCategoryId: 'cat1',
        actualCategoryId: 'cat2',
        wasAccepted: true,
        confidence: 0.8,
        matchType: 'description'
      };

      const result = SuggestionFeedbackSchema.safeParse(feedback);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.timestamp).toBeInstanceOf(Date);
      }
    });
  });

  describe('Validation helper functions', () => {
    it('should validate category suggestion request', () => {
      const validRequest = {
        description: 'Test transaction',
        amount: 100
      };

      const result = validateCategorySuggestionRequest(validRequest);
      expect(result.success).toBe(true);
    });

    it('should validate bulk category suggestion request', () => {
      const validRequest = {
        transactions: [
          { description: 'Test transaction', amount: 100 }
        ]
      };

      const result = validateBulkCategorySuggestionRequest(validRequest);
      expect(result.success).toBe(true);
    });

    it('should validate suggestion config', () => {
      const validConfig = {
        descriptionWeight: 0.5,
        amountWeight: 0.2,
        frequencyWeight: 0.2,
        keywordWeight: 0.1
      };

      const result = validateSuggestionConfig(validConfig);
      expect(result.success).toBe(true);
    });

    it('should validate suggestion feedback', () => {
      const validFeedback = {
        transactionId: 'trans123',
        suggestedCategoryId: 'cat1',
        actualCategoryId: 'cat2',
        wasAccepted: true,
        confidence: 0.8,
        matchType: 'description'
      };

      const result = validateSuggestionFeedback(validFeedback);
      expect(result.success).toBe(true);
    });
  });
});
