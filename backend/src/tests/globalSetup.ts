import { RedisMemoryServer } from 'redis-memory-server';

let redisServer: RedisMemoryServer;

export default async function globalSetup() {
  console.log('🚀 Setting up test environment...');
  
  try {
    // Start Redis Memory Server
    redisServer = new RedisMemoryServer({
      instance: {
        port: 6380, // Use different port to avoid conflicts
        args: ['--save', '', '--appendonly', 'no'], // Disable persistence for tests
      },
    });
    
    await redisServer.start();
    const redisHost = await redisServer.getHost();
    const redisPort = await redisServer.getPort();
    
    // Set environment variables for Redis connection
    process.env.REDIS_HOST = redisHost;
    process.env.REDIS_PORT = redisPort.toString();
    process.env.REDIS_URL = `redis://${redisHost}:${redisPort}`;
    
    console.log(`✅ Redis Memory Server started on ${redisHost}:${redisPort}`);
    
    // Store server instance globally for teardown
    (global as any).__REDIS_SERVER__ = redisServer;
    
  } catch (error) {
    console.error('❌ Failed to start Redis Memory Server:', error);
    throw error;
  }
  
  console.log('✅ Test environment setup complete');
}
