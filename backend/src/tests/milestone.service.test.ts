import { milestoneService } from '../services/milestone.service';
import prisma from '../lib/prisma';
import {
  CreateGoalMilestoneRequest,
  UpdateGoalMilestoneRequest,
  MilestoneFilters
} from '../schemas/financial-goal.schemas';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  goal: {
    findFirst: jest.fn(),
  },
  goalMilestone: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  $transaction: jest.fn(),
}));

const mockPrisma = prisma as any;

describe('MilestoneService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('create', () => {
    const validMilestoneData: CreateGoalMilestoneRequest = {
      name: 'Entrada do Apartamento',
      targetAmount: 60000,
      targetDate: new Date('2024-06-30')
    };

    const mockGoal = {
      id: 'goal_123',
      name: 'Casa Própria',
      targetAmount: 300000,
      deletedAt: null
    };

    const mockCreatedMilestone = {
      id: 'milestone_123',
      goalId: 'goal_123',
      name: 'Entrada do Apartamento',
      targetAmount: 60000,
      targetDate: new Date('2024-06-30'),
      isCompleted: false,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      version: 1
    };

    it('should create a milestone successfully', async () => {
      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockGoal);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          goalMilestone: {
            create: jest.fn().mockResolvedValue(mockCreatedMilestone)
          }
        });
      });

      // Execute
      const result = await milestoneService.create('goal_123', validMilestoneData);

      // Assertions
      expect(result).toEqual({
        id: 'milestone_123',
        goalId: 'goal_123',
        name: 'Entrada do Apartamento',
        targetAmount: 60000,
        targetDate: '2024-06-30T00:00:00.000Z',
        isCompleted: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        version: 1
      });

      expect(mockPrisma.goal.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'goal_123',
          deletedAt: null
        }
      });
    });

    it('should throw error if goal not found', async () => {
      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(null);

      // Execute & Assert
      await expect(milestoneService.create('nonexistent', validMilestoneData))
        .rejects
        .toThrow('Meta financeira não encontrada');
    });

    it('should throw error if milestone amount exceeds goal amount', async () => {
      const excessiveMilestoneData: CreateGoalMilestoneRequest = {
        name: 'Marco Impossível',
        targetAmount: 400000, // Exceeds goal's 300000
        targetDate: new Date('2024-06-30')
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockGoal);

      // Execute & Assert
      await expect(milestoneService.create('goal_123', excessiveMilestoneData))
        .rejects
        .toThrow('Valor alvo do marco não pode exceder o valor alvo da meta');
    });
  });

  describe('findByGoal', () => {
    const mockMilestones = [
      {
        id: 'milestone_1',
        goalId: 'goal_123',
        name: 'Entrada',
        targetAmount: 60000,
        targetDate: new Date('2024-06-30'),
        isCompleted: false,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1
      },
      {
        id: 'milestone_2',
        goalId: 'goal_123',
        name: 'Documentação',
        targetAmount: 10000,
        targetDate: new Date('2024-05-15'),
        isCompleted: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1
      }
    ];

    const mockGoal = {
      id: 'goal_123',
      deletedAt: null
    };

    it('should return milestones for a goal', async () => {
      const filters: Omit<MilestoneFilters, 'goalId'> = {
        status: 'all',
        sortBy: 'targetDate',
        sortOrder: 'asc'
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockGoal);
      mockPrisma.goalMilestone.findMany.mockResolvedValue(mockMilestones);

      // Execute
      const result = await milestoneService.findByGoal('goal_123', filters);

      // Assertions
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Entrada');
      expect(result[1].name).toBe('Documentação');

      expect(mockPrisma.goalMilestone.findMany).toHaveBeenCalledWith({
        where: {
          goalId: 'goal_123'
        },
        orderBy: {
          targetDate: 'asc'
        }
      });
    });

    it('should filter by status correctly', async () => {
      const filters: Omit<MilestoneFilters, 'goalId'> = {
        status: 'completed',
        sortBy: 'targetDate',
        sortOrder: 'asc'
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockGoal);
      mockPrisma.goalMilestone.findMany.mockResolvedValue([mockMilestones[1]]);

      // Execute
      const result = await milestoneService.findByGoal('goal_123', filters);

      // Assertions
      expect(result).toHaveLength(1);
      expect(result[0].isCompleted).toBe(true);

      expect(mockPrisma.goalMilestone.findMany).toHaveBeenCalledWith({
        where: {
          goalId: 'goal_123',
          isCompleted: true
        },
        orderBy: {
          targetDate: 'asc'
        }
      });
    });

    it('should filter by date range', async () => {
      const filters: Omit<MilestoneFilters, 'goalId'> = {
        status: 'all',
        targetDateFrom: new Date('2024-05-01'),
        targetDateTo: new Date('2024-06-01'),
        sortBy: 'targetDate',
        sortOrder: 'asc'
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockGoal);
      mockPrisma.goalMilestone.findMany.mockResolvedValue([mockMilestones[1]]);

      // Execute
      const result = await milestoneService.findByGoal('goal_123', filters);

      // Assertions - Just verify the service was called and returned expected structure
      expect(result).toHaveLength(1);
      expect(mockPrisma.goalMilestone.findMany).toHaveBeenCalled();
      expect(mockPrisma.goal.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'goal_123',
          deletedAt: null
        }
      });
    });

    it('should throw error if goal not found', async () => {
      const filters: Omit<MilestoneFilters, 'goalId'> = {
        status: 'all',
        sortBy: 'targetDate',
        sortOrder: 'asc'
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(null);

      // Execute & Assert
      await expect(milestoneService.findByGoal('nonexistent', filters))
        .rejects
        .toThrow('Meta financeira não encontrada');
    });
  });

  describe('update', () => {
    const mockExistingMilestone = {
      id: 'milestone_123',
      goalId: 'goal_123',
      name: 'Entrada',
      targetAmount: 60000,
      targetDate: new Date('2024-06-30'),
      isCompleted: false,
      goal: {
        id: 'goal_123',
        targetAmount: 300000,
        deletedAt: null
      }
    };

    const updateData: UpdateGoalMilestoneRequest = {
      name: 'Entrada Atualizada',
      targetAmount: 65000
    };

    it('should update milestone successfully', async () => {
      const mockUpdatedMilestone = {
        ...mockExistingMilestone,
        name: 'Entrada Atualizada',
        targetAmount: 65000,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1
      };

      // Setup mocks
      mockPrisma.goalMilestone.findUnique.mockResolvedValue(mockExistingMilestone);
      mockPrisma.goalMilestone.update.mockResolvedValue(mockUpdatedMilestone);

      // Execute
      const result = await milestoneService.update('milestone_123', updateData);

      // Assertions
      expect(result.name).toBe('Entrada Atualizada');
      expect(result.targetAmount).toBe(65000);

      expect(mockPrisma.goalMilestone.update).toHaveBeenCalledWith({
        where: { id: 'milestone_123' },
        data: {
          name: 'Entrada Atualizada',
          targetAmount: expect.any(Object) // Prisma.Decimal
        }
      });
    });

    it('should throw error if milestone not found', async () => {
      // Setup mocks
      mockPrisma.goalMilestone.findUnique.mockResolvedValue(null);

      // Execute & Assert
      await expect(milestoneService.update('nonexistent', updateData))
        .rejects
        .toThrow('Marco da meta não encontrado');
    });

    it('should throw error if updated amount exceeds goal amount', async () => {
      const excessiveUpdateData: UpdateGoalMilestoneRequest = {
        targetAmount: 400000 // Exceeds goal's 300000
      };

      // Setup mocks
      mockPrisma.goalMilestone.findUnique.mockResolvedValue(mockExistingMilestone);

      // Execute & Assert
      await expect(milestoneService.update('milestone_123', excessiveUpdateData))
        .rejects
        .toThrow('Valor alvo do marco não pode exceder o valor alvo da meta');
    });
  });

  describe('markCompleted and markPending', () => {
    const mockMilestone = {
      id: 'milestone_123',
      goalId: 'goal_123',
      name: 'Entrada',
      targetAmount: 60000,
      targetDate: new Date('2024-06-30'),
      isCompleted: false,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      version: 1,
      goal: {
        deletedAt: null
      }
    };

    it('should mark milestone as completed', async () => {
      const mockCompletedMilestone = {
        ...mockMilestone,
        isCompleted: true
      };

      // Setup mocks
      mockPrisma.goalMilestone.findUnique.mockResolvedValue(mockMilestone);
      mockPrisma.goalMilestone.update.mockResolvedValue(mockCompletedMilestone);

      // Execute
      const result = await milestoneService.markCompleted('milestone_123');

      // Assertions
      expect(result.isCompleted).toBe(true);
    });

    it('should mark milestone as pending', async () => {
      const mockPendingMilestone = {
        ...mockMilestone,
        isCompleted: false
      };

      // Setup mocks
      mockPrisma.goalMilestone.findUnique.mockResolvedValue({
        ...mockMilestone,
        isCompleted: true
      });
      mockPrisma.goalMilestone.update.mockResolvedValue(mockPendingMilestone);

      // Execute
      const result = await milestoneService.markPending('milestone_123');

      // Assertions
      expect(result.isCompleted).toBe(false);
    });
  });

  describe('delete', () => {
    const mockMilestone = {
      id: 'milestone_123',
      goal: {
        deletedAt: null
      }
    };

    it('should delete milestone successfully', async () => {
      // Setup mocks
      mockPrisma.goalMilestone.findUnique.mockResolvedValue(mockMilestone);
      mockPrisma.goalMilestone.delete.mockResolvedValue(mockMilestone);

      // Execute
      await milestoneService.delete('milestone_123');

      // Assertions
      expect(mockPrisma.goalMilestone.delete).toHaveBeenCalledWith({
        where: { id: 'milestone_123' }
      });
    });

    it('should throw error if milestone not found', async () => {
      // Setup mocks
      mockPrisma.goalMilestone.findUnique.mockResolvedValue(null);

      // Execute & Assert
      await expect(milestoneService.delete('nonexistent'))
        .rejects
        .toThrow('Marco da meta não encontrado');
    });
  });
});
