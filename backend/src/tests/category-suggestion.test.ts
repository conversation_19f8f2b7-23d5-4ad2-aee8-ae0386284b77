import { CategorySuggestionService, CategorySuggestionRequest } from '../services/category-suggestion.service';
import { historicalAnalysisService } from '../services/historical-analysis.service';
import { TransactionType } from '@prisma/client';

// Mock the historical analysis service
jest.mock('../services/historical-analysis.service', () => ({
  historicalAnalysisService: {
    getCategoryFrequencies: jest.fn(),
    getTransactionPatterns: jest.fn(),
    getValueRangePatterns: jest.fn(),
    findCategoriesByKeywords: jest.fn()
  }
}));

const mockHistoricalService = historicalAnalysisService as jest.Mocked<typeof historicalAnalysisService>;

describe('CategorySuggestionService', () => {
  let service: CategorySuggestionService;

  beforeEach(() => {
    service = new CategorySuggestionService();
    jest.clearAllMocks();
  });

  describe('getSuggestions', () => {
    it('should return fallback suggestions when no historical data', async () => {
      mockHistoricalService.getCategoryFrequencies.mockResolvedValue([]);
      mockHistoricalService.getTransactionPatterns.mockResolvedValue([]);
      mockHistoricalService.getValueRangePatterns.mockResolvedValue([]);

      const request: CategorySuggestionRequest = {
        description: 'Compra no supermercado',
        amount: 150,
        type: TransactionType.EXPENSE
      };

      const suggestions = await service.getSuggestions(request);

      expect(suggestions).toHaveLength(3);
      expect(suggestions[0].categoryName).toBe('Alimentação');
      expect(suggestions[0].confidence).toBe(0.5);
    });

    it('should generate suggestions based on description similarity', async () => {
      const mockFrequencies = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          frequency: 10,
          averageAmount: 120,
          totalTransactions: 10,
          keywords: ['compra', 'supermercado']
        }
      ];

      const mockPatterns = [
        {
          description: 'compra supermercado extra',
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          amount: 120,
          frequency: 5,
          keywords: ['compra', 'supermercado']
        }
      ];

      const mockValueRanges = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          minAmount: 50,
          maxAmount: 200,
          averageAmount: 120,
          transactionCount: 10
        }
      ];

      mockHistoricalService.getCategoryFrequencies.mockResolvedValue(mockFrequencies);
      mockHistoricalService.getTransactionPatterns.mockResolvedValue(mockPatterns);
      mockHistoricalService.getValueRangePatterns.mockResolvedValue(mockValueRanges);
      mockHistoricalService.findCategoriesByKeywords.mockResolvedValue(['cat1']);

      const request: CategorySuggestionRequest = {
        description: 'Compra no Supermercado Carrefour',
        amount: 150,
        type: TransactionType.EXPENSE
      };

      const suggestions = await service.getSuggestions(request);

      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions[0].categoryId).toBe('cat1');
      expect(suggestions[0].categoryName).toBe('Alimentação');
      expect(suggestions[0].confidence).toBeGreaterThan(0.3);
    });

    it('should filter suggestions by minimum confidence', async () => {
      const mockFrequencies = [
        {
          categoryId: 'cat1',
          categoryName: 'Test Category',
          frequency: 1,
          averageAmount: 100,
          totalTransactions: 1,
          keywords: ['test']
        }
      ];

      mockHistoricalService.getCategoryFrequencies.mockResolvedValue(mockFrequencies);
      mockHistoricalService.getTransactionPatterns.mockResolvedValue([]);
      mockHistoricalService.getValueRangePatterns.mockResolvedValue([]);
      mockHistoricalService.findCategoriesByKeywords.mockResolvedValue([]);

      const request: CategorySuggestionRequest = {
        description: 'Completely different description',
        amount: 1000,
        type: TransactionType.EXPENSE
      };

      const suggestions = await service.getSuggestions(request, { minConfidence: 0.8 });

      // Should return empty array since confidence is too low and we have historical data
      expect(suggestions).toHaveLength(0);
    });

    it('should limit number of suggestions', async () => {
      const mockFrequencies = Array.from({ length: 10 }, (_, i) => ({
        categoryId: `cat${i}`,
        categoryName: `Category ${i}`,
        frequency: 10 - i,
        averageAmount: 100,
        totalTransactions: 10 - i,
        keywords: [`keyword${i}`]
      }));

      mockHistoricalService.getCategoryFrequencies.mockResolvedValue(mockFrequencies);
      mockHistoricalService.getTransactionPatterns.mockResolvedValue([]);
      mockHistoricalService.getValueRangePatterns.mockResolvedValue([]);
      mockHistoricalService.findCategoriesByKeywords.mockResolvedValue([]);

      const request: CategorySuggestionRequest = {
        description: 'Test transaction',
        amount: 100,
        type: TransactionType.EXPENSE
      };

      const suggestions = await service.getSuggestions(request, { maxSuggestions: 3 });

      expect(suggestions.length).toBeLessThanOrEqual(3);
    });
  });

  describe('getSuggestionsForExpense', () => {
    it('should use higher description weight for expenses', async () => {
      const mockFrequencies = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          frequency: 5,
          averageAmount: 100,
          totalTransactions: 5,
          keywords: ['compra']
        }
      ];

      const mockPatterns = [
        {
          description: 'compra supermercado',
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          amount: 100,
          frequency: 3,
          keywords: ['compra']
        }
      ];

      mockHistoricalService.getCategoryFrequencies.mockResolvedValue(mockFrequencies);
      mockHistoricalService.getTransactionPatterns.mockResolvedValue(mockPatterns);
      mockHistoricalService.getValueRangePatterns.mockResolvedValue([]);
      mockHistoricalService.findCategoriesByKeywords.mockResolvedValue(['cat1']);

      const request: CategorySuggestionRequest = {
        description: 'Compra no supermercado',
        amount: 100,
        type: TransactionType.EXPENSE
      };

      const suggestions = await service.getSuggestionsForExpense(request);

      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions[0].categoryId).toBe('cat1');
    });
  });

  describe('getSuggestionsForIncome', () => {
    it('should use higher description weight for income', async () => {
      const mockFrequencies = [
        {
          categoryId: 'cat1',
          categoryName: 'Salário',
          frequency: 5,
          averageAmount: 3000,
          totalTransactions: 5,
          keywords: ['salario']
        }
      ];

      const mockPatterns = [
        {
          description: 'salario empresa',
          categoryId: 'cat1',
          categoryName: 'Salário',
          amount: 3000,
          frequency: 3,
          keywords: ['salario']
        }
      ];

      mockHistoricalService.getCategoryFrequencies.mockResolvedValue(mockFrequencies);
      mockHistoricalService.getTransactionPatterns.mockResolvedValue(mockPatterns);
      mockHistoricalService.getValueRangePatterns.mockResolvedValue([]);
      mockHistoricalService.findCategoriesByKeywords.mockResolvedValue(['cat1']);

      const request: CategorySuggestionRequest = {
        description: 'Salário da empresa',
        amount: 3000,
        type: TransactionType.INCOME
      };

      const suggestions = await service.getSuggestionsForIncome(request);

      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions[0].categoryId).toBe('cat1');
    });
  });

  describe('error handling', () => {
    it('should return fallback suggestions on error', async () => {
      mockHistoricalService.getCategoryFrequencies.mockRejectedValue(new Error('Database error'));

      const request: CategorySuggestionRequest = {
        description: 'Test transaction',
        amount: 100,
        type: TransactionType.EXPENSE
      };

      const suggestions = await service.getSuggestions(request);

      expect(suggestions).toHaveLength(3);
      expect(suggestions[0].categoryName).toBe('Alimentação');
    });
  });

  describe('suggestion merging', () => {
    it('should combine confidence scores for same category from different algorithms', async () => {
      const mockFrequencies = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          frequency: 10,
          averageAmount: 120,
          totalTransactions: 10,
          keywords: ['compra', 'supermercado']
        }
      ];

      const mockPatterns = [
        {
          description: 'compra supermercado',
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          amount: 120,
          frequency: 5,
          keywords: ['compra', 'supermercado']
        }
      ];

      const mockValueRanges = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          minAmount: 50,
          maxAmount: 200,
          averageAmount: 120,
          transactionCount: 10
        }
      ];

      mockHistoricalService.getCategoryFrequencies.mockResolvedValue(mockFrequencies);
      mockHistoricalService.getTransactionPatterns.mockResolvedValue(mockPatterns);
      mockHistoricalService.getValueRangePatterns.mockResolvedValue(mockValueRanges);
      mockHistoricalService.findCategoriesByKeywords.mockResolvedValue(['cat1']);

      const request: CategorySuggestionRequest = {
        description: 'Compra no supermercado',
        amount: 120,
        type: TransactionType.EXPENSE
      };

      const suggestions = await service.getSuggestions(request);

      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions[0].matchType).toBe('combined');
      expect(suggestions[0].reason).toContain('|'); // Multiple reasons combined
    });
  });
});
