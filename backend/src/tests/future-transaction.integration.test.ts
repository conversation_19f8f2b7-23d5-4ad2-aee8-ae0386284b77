import request from 'supertest';
import app from '../index';
import jwt from 'jsonwebtoken';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    transaction: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn()
    },
    account: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn()
    },
    $transaction: jest.fn()
  }
}));

// Mock Future Transaction Job Service
jest.mock('../services/future-transaction-job.service', () => ({
  futureTransactionJobService: {
    getPendingFutureTransactionsStats: jest.fn(),
    execute: jest.fn()
  }
}));

describe('Future Transaction API Integration Tests', () => {
  let authToken: string;
  let mockPrisma: any;
  let mockJobService: any;

  beforeAll(() => {
    // Create a valid JWT token for testing
    authToken = jwt.sign(
      { userId: 'test-user-id', email: '<EMAIL>' },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  beforeEach(() => {
    mockPrisma = require('../lib/prisma').default;
    mockJobService = require('../services/future-transaction-job.service').futureTransactionJobService;
    jest.clearAllMocks();

    // Default mock implementations
    mockPrisma.transaction.findMany.mockResolvedValue([]);
    mockPrisma.transaction.findUnique.mockResolvedValue(null);
    mockPrisma.account.findMany.mockResolvedValue([]);
    mockJobService.getPendingFutureTransactionsStats.mockResolvedValue({
      totalPending: 0,
      dueToday: 0,
      dueThisWeek: 0,
      dueThisMonth: 0,
      overdue: 0
    });
  });

  describe('GET /api/v1/future-transactions', () => {
    it('should return future transactions with valid token', async () => {
      const mockFutureTransactions = [
        {
          id: 'trans-1',
          description: 'Future Payment',
          amount: 500.00,
          transactionDate: new Date('2024-12-31'),
          isFuture: true,
          type: 'EXPENSE'
        }
      ];

      mockPrisma.transaction.findMany.mockResolvedValue(mockFutureTransactions);

      const response = await request(app)
        .get('/api/v1/future-transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.any(Object)
      });
    });

    it('should return 401 without valid token', async () => {
      await request(app)
        .get('/api/v1/future-transactions')
        .expect(401);
    });

    it('should handle query parameters correctly', async () => {
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      const response = await request(app)
        .get('/api/v1/future-transactions')
        .query({
          accountId: 'clp123456789012345678901',
          page: '1',
          limit: '10',
          sortBy: 'transactionDate',
          sortOrder: 'desc'
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('GET /api/v1/future-transactions/projected-balance', () => {
    it('should return projected balance', async () => {
      // Mock dashboard service response
      mockPrisma.account.findMany.mockResolvedValue([
        {
          id: 'acc-1',
          currentBalance: 1000.00,
          currency: 'BRL',
          type: 'CHECKING'
        }
      ]);

      const response = await request(app)
        .get('/api/v1/future-transactions/projected-balance')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          currentBalance: expect.any(Number),
          projectedBalance: expect.any(Number),
          projectionDate: expect.any(String)
        })
      });
    });

    it('should handle query parameters for projected balance', async () => {
      mockPrisma.account.findMany.mockResolvedValue([]);

      const response = await request(app)
        .get('/api/v1/future-transactions/projected-balance')
        .query({
          accountIds: 'clp123456789012345678901,clp123456789012345678902',
          currencies: 'BRL,USD',
          projectionDate: '2024-12-31T23:59:59.999Z',
          includeRecurring: 'true'
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('GET /api/v1/future-transactions/stats', () => {
    it('should return future transaction statistics', async () => {
      const mockStats = {
        totalPending: 5,
        dueToday: 2,
        dueThisWeek: 3,
        dueThisMonth: 4,
        overdue: 1
      };

      mockJobService.getPendingFutureTransactionsStats.mockResolvedValue(mockStats);

      const response = await request(app)
        .get('/api/v1/future-transactions/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: mockStats
      });
    });
  });

  describe('GET /api/v1/future-transactions/due-today', () => {
    it('should return future transactions due today', async () => {
      const mockTransactionsDueToday = [
        {
          id: 'trans-today',
          description: 'Payment due today',
          amount: 200.00,
          transactionDate: new Date(),
          isFuture: true
        }
      ];

      mockPrisma.transaction.findMany.mockResolvedValue(mockTransactionsDueToday);

      const response = await request(app)
        .get('/api/v1/future-transactions/due-today')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          dueDate: expect.any(String)
        })
      });
    });
  });

  describe('DELETE /api/v1/future-transactions/:id', () => {
    it('should cancel a future transaction', async () => {
      const mockFutureTransaction = {
        id: 'trans-future',
        description: 'Future transaction',
        isFuture: true,
        amount: 100.00
      };

      mockPrisma.transaction.findUnique.mockResolvedValue(mockFutureTransaction);
      mockPrisma.transaction.delete = jest.fn().mockResolvedValue(mockFutureTransaction);

      const response = await request(app)
        .delete('/api/v1/future-transactions/trans-future')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Future transaction cancelled successfully'
      });
    });

    it('should return 404 for non-existent transaction', async () => {
      mockPrisma.transaction.findUnique.mockResolvedValue(null);

      const response = await request(app)
        .delete('/api/v1/future-transactions/non-existent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).toEqual({
        success: false,
        error: 'Transaction not found'
      });
    });

    it('should return 400 for already processed transaction', async () => {
      const mockProcessedTransaction = {
        id: 'trans-processed',
        description: 'Processed transaction',
        isFuture: false,
        amount: 100.00
      };

      mockPrisma.transaction.findUnique.mockResolvedValue(mockProcessedTransaction);

      const response = await request(app)
        .delete('/api/v1/future-transactions/trans-processed')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        error: 'Cannot cancel a transaction that has already been processed'
      });
    });
  });

  describe('PUT /api/v1/future-transactions/:id', () => {
    it('should update a future transaction', async () => {
      const mockFutureTransaction = {
        id: 'trans-future',
        description: 'Future transaction',
        isFuture: true,
        amount: 100.00
      };

      const updatedTransaction = {
        ...mockFutureTransaction,
        description: 'Updated future transaction',
        amount: 150.00
      };

      mockPrisma.transaction.findUnique.mockResolvedValue(mockFutureTransaction);
      mockPrisma.transaction.update = jest.fn().mockResolvedValue(updatedTransaction);

      const response = await request(app)
        .put('/api/v1/future-transactions/trans-future')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          description: 'Updated future transaction',
          amount: 150.00
        })
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          description: 'Updated future transaction',
          amount: 150.00
        })
      });
    });

    it('should return 400 for already processed transaction', async () => {
      const mockProcessedTransaction = {
        id: 'trans-processed',
        description: 'Processed transaction',
        isFuture: false,
        amount: 100.00
      };

      mockPrisma.transaction.findUnique.mockResolvedValue(mockProcessedTransaction);

      const response = await request(app)
        .put('/api/v1/future-transactions/trans-processed')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          description: 'Updated description'
        })
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        error: 'Cannot update a transaction that has already been processed'
      });
    });
  });

  describe('POST /api/v1/future-transactions/process', () => {
    it('should manually trigger future transaction processing', async () => {
      const mockExecutionStats = {
        processedTransactions: 3,
        failedTransactions: 0,
        skippedTransactions: 1,
        totalProcessingTime: 150,
        errors: []
      };

      mockJobService.execute.mockResolvedValue(mockExecutionStats);

      const response = await request(app)
        .post('/api/v1/future-transactions/process')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          targetDate: '2024-12-01T00:00:00.000Z'
        })
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Future transaction processing completed',
        data: mockExecutionStats
      });
    });

    it('should handle processing without target date', async () => {
      const mockExecutionStats = {
        processedTransactions: 1,
        failedTransactions: 0,
        skippedTransactions: 0,
        totalProcessingTime: 50,
        errors: []
      };

      mockJobService.execute.mockResolvedValue(mockExecutionStats);

      const response = await request(app)
        .post('/api/v1/future-transactions/process')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(mockJobService.execute).toHaveBeenCalledWith(expect.any(Date));
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      mockPrisma.transaction.findMany.mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .get('/api/v1/future-transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        error: 'Failed to fetch future transactions'
      });
    });

    it('should handle invalid query parameters', async () => {
      const response = await request(app)
        .get('/api/v1/future-transactions/projected-balance')
        .query({
          projectionDate: 'invalid-date'
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid query parameters');
    });
  });
});
