import request from 'supertest';
import express from 'express';
import { categorySuggestionController } from '../controllers/category-suggestion.controller';
import { TransactionType } from '@prisma/client';

// Mock the services to avoid database dependencies
jest.mock('../services/category-suggestion.service', () => ({
  categorySuggestionService: {
    getSuggestions: jest.fn(),
    getSuggestionsForExpense: jest.fn(),
    getSuggestionsForIncome: jest.fn()
  }
}));

jest.mock('../services/historical-analysis.service', () => ({
  historicalAnalysisService: {
    getCategoryFrequencies: jest.fn(),
    getTransactionPatterns: jest.fn(),
    getValueRangePatterns: jest.fn(),
    findCategoriesByKeywords: jest.fn()
  }
}));

// Mock auth middleware to bypass authentication in tests
jest.mock('../middleware/auth.middleware', () => ({
  authMiddleware: (req: any, res: any, next: any) => {
    req.user = { id: 'test-user-id' };
    next();
  }
}));

import { categorySuggestionService } from '../services/category-suggestion.service';

const mockService = categorySuggestionService as jest.Mocked<typeof categorySuggestionService>;

// Create test app with category suggestion controller routes
const app = express();
app.use(express.json());

// Add routes directly without middleware for testing
app.post('/api/v1/category-suggestions', categorySuggestionController.getSuggestions.bind(categorySuggestionController));
app.post('/api/v1/category-suggestions/expense', categorySuggestionController.getSuggestionsForExpense.bind(categorySuggestionController));
app.post('/api/v1/category-suggestions/income', categorySuggestionController.getSuggestionsForIncome.bind(categorySuggestionController));
app.post('/api/v1/category-suggestions/bulk', categorySuggestionController.getBulkSuggestions.bind(categorySuggestionController));
app.post('/api/v1/category-suggestions/feedback', categorySuggestionController.submitFeedback.bind(categorySuggestionController));

describe('Category Suggestion Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/v1/category-suggestions', () => {
    it('should return category suggestions', async () => {
      const mockSuggestions = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          confidence: 0.85,
          reason: 'Descrição similar a transações anteriores',
          matchType: 'description' as const
        }
      ];

      mockService.getSuggestions.mockResolvedValue(mockSuggestions);

      const response = await request(app)
        .post('/api/v1/category-suggestions')
        .send({
          description: 'Compra no supermercado',
          amount: 150,
          type: TransactionType.EXPENSE
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.suggestions).toEqual(mockSuggestions);
      expect(response.body.data.metadata.totalSuggestions).toBe(1);
    });

    it('should handle validation errors', async () => {
      const response = await request(app)
        .post('/api/v1/category-suggestions')
        .send({
          // Missing required description
          amount: 150
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /api/v1/category-suggestions/expense', () => {
    it('should return expense-optimized suggestions', async () => {
      const mockSuggestions = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          confidence: 0.9,
          reason: 'Otimizado para despesas',
          matchType: 'description' as const
        }
      ];

      mockService.getSuggestionsForExpense.mockResolvedValue(mockSuggestions);

      const response = await request(app)
        .post('/api/v1/category-suggestions/expense')
        .send({
          description: 'Compra no supermercado',
          amount: 150,
          type: TransactionType.EXPENSE
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.suggestions).toEqual(mockSuggestions);
      expect(response.body.data.metadata.algorithmWeights.description).toBe(0.5);
    });
  });

  describe('POST /api/v1/category-suggestions/income', () => {
    it('should return income-optimized suggestions', async () => {
      const mockSuggestions = [
        {
          categoryId: 'cat1',
          categoryName: 'Salário',
          confidence: 0.95,
          reason: 'Otimizado para receitas',
          matchType: 'description' as const
        }
      ];

      mockService.getSuggestionsForIncome.mockResolvedValue(mockSuggestions);

      const response = await request(app)
        .post('/api/v1/category-suggestions/income')
        .send({
          description: 'Salário empresa',
          amount: 3000,
          type: TransactionType.INCOME
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.suggestions).toEqual(mockSuggestions);
      expect(response.body.data.metadata.algorithmWeights.description).toBe(0.6);
    });
  });

  describe('POST /api/v1/category-suggestions/bulk', () => {
    it('should process multiple transactions', async () => {
      const mockSuggestions = [
        {
          categoryId: 'cat1',
          categoryName: 'Alimentação',
          confidence: 0.8,
          reason: 'Bulk suggestion',
          matchType: 'description' as const
        }
      ];

      mockService.getSuggestions.mockResolvedValue(mockSuggestions);

      const response = await request(app)
        .post('/api/v1/category-suggestions/bulk')
        .send({
          transactions: [
            {
              id: 'trans1',
              description: 'Compra supermercado',
              amount: 150,
              type: TransactionType.EXPENSE
            },
            {
              id: 'trans2',
              description: 'Pagamento conta luz',
              amount: 120,
              type: TransactionType.EXPENSE
            }
          ],
          maxSuggestionsPerTransaction: 3,
          minConfidence: 0.3
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.results).toHaveLength(2);
      expect(response.body.data.metadata.totalTransactions).toBe(2);
    });
  });

  describe('POST /api/v1/category-suggestions/feedback', () => {
    it('should accept feedback', async () => {
      const response = await request(app)
        .post('/api/v1/category-suggestions/feedback')
        .send({
          transactionId: 'trans123',
          suggestedCategoryId: 'cat1',
          actualCategoryId: 'cat2',
          wasAccepted: false,
          confidence: 0.8,
          matchType: 'description',
          feedback: 'Sugestão não foi adequada'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Feedback registrado com sucesso');
    });
  });

  describe('Route integration', () => {
    it('should have category suggestion routes properly mounted', async () => {
      mockService.getSuggestions.mockResolvedValue([]);

      const response = await request(app)
        .post('/api/v1/category-suggestions')
        .send({
          description: 'Test transaction',
          amount: 100
        });

      expect(response.status).toBe(200);
    });

    it('should handle 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/api/v1/category-suggestions/non-existent');

      expect(response.status).toBe(404);
    });
  });
});
