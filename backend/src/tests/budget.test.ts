import { budgetService } from '../services/budget.service';
import prisma from '../lib/prisma';
import {
  CreateBudgetRequest,
  UpdateBudgetRequest,
  BudgetFilters,
  CreateBudgetSchema,
  UpdateBudgetSchema,
  BudgetFiltersSchema
} from '../schemas/budget.schemas';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  budget: {
    findFirst: jest.fn(),
    findMany: jest.fn(),
    count: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  category: {
    findFirst: jest.fn(),
  },
  familyMember: {
    findFirst: jest.fn(),
  },
  transaction: {
    aggregate: jest.fn(),
    count: jest.fn(),
  },
  $transaction: jest.fn(),
}));

const mockPrisma = prisma as any;

describe('BudgetService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('create', () => {
    const validBudgetData: CreateBudgetRequest = {
      plannedAmount: 1000,
      month: 12,
      year: 2024,
      categoryId: 'cat_123',
      familyMemberId: 'member_123'
    };

    const mockCategory = {
      id: 'cat_123',
      name: 'Alimentação',
      color: '#10B981',
      deletedAt: null,
      parent: null
    };

    const mockFamilyMember = {
      id: 'member_123',
      name: 'João Silva',
      color: '#3B82F6',
      deletedAt: null
    };

    const mockCreatedBudget = {
      id: 'budget_123',
      plannedAmount: 1000,
      month: 12,
      year: 2024,
      categoryId: 'cat_123',
      familyMemberId: 'member_123',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      version: 1,
      category: mockCategory,
      familyMember: mockFamilyMember
    };

    it('should create a budget successfully', async () => {
      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(null); // No existing budget
      mockPrisma.category.findFirst.mockResolvedValue(mockCategory);
      mockPrisma.familyMember.findFirst.mockResolvedValue(mockFamilyMember);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          budget: {
            create: jest.fn().mockResolvedValue(mockCreatedBudget)
          }
        });
      });

      // Execute
      const result = await budgetService.create(validBudgetData);

      // Assertions
      expect(result).toEqual({
        id: 'budget_123',
        plannedAmount: 1000,
        month: 12,
        year: 2024,
        categoryId: 'cat_123',
        familyMemberId: 'member_123',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        version: 1,
        category: {
          id: 'cat_123',
          name: 'Alimentação',
          color: '#10B981'
        },
        familyMember: {
          id: 'member_123',
          name: 'João Silva',
          color: '#3B82F6'
        }
      });

      expect(mockPrisma.budget.findFirst).toHaveBeenCalledWith({
        where: {
          categoryId: 'cat_123',
          familyMemberId: 'member_123',
          month: 12,
          year: 2024,
          deletedAt: null
        }
      });
    });

    it('should throw error if budget already exists', async () => {
      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(mockCreatedBudget);

      // Execute & Assert
      await expect(budgetService.create(validBudgetData))
        .rejects
        .toThrow('Já existe um orçamento para esta categoria, membro e período');
    });

    it('should throw error if category not found', async () => {
      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(null);
      mockPrisma.category.findFirst.mockResolvedValue(null);

      // Execute & Assert
      await expect(budgetService.create(validBudgetData))
        .rejects
        .toThrow('Categoria não encontrada ou foi arquivada');
    });

    it('should throw error if family member not found', async () => {
      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(null);
      mockPrisma.category.findFirst.mockResolvedValue(mockCategory);
      mockPrisma.familyMember.findFirst.mockResolvedValue(null);

      // Execute & Assert
      await expect(budgetService.create(validBudgetData))
        .rejects
        .toThrow('Membro da família não encontrado ou foi arquivado');
    });

    it('should create budget without family member', async () => {
      const budgetDataWithoutMember = { ...validBudgetData };
      delete budgetDataWithoutMember.familyMemberId;

      const mockBudgetWithoutMember = {
        ...mockCreatedBudget,
        familyMemberId: null,
        familyMember: null
      };

      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(null);
      mockPrisma.category.findFirst.mockResolvedValue(mockCategory);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          budget: {
            create: jest.fn().mockResolvedValue(mockBudgetWithoutMember)
          }
        });
      });

      // Execute
      const result = await budgetService.create(budgetDataWithoutMember);

      // Assertions
      expect(result.familyMemberId).toBeNull();
      expect(result.familyMember).toBeUndefined();
    });
  });

  describe('findAll', () => {
    const mockBudgets = [
      {
        id: 'budget_1',
        plannedAmount: 1000,
        month: 12,
        year: 2024,
        categoryId: 'cat_1',
        familyMemberId: 'member_1',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1,
        category: {
          id: 'cat_1',
          name: 'Alimentação',
          color: '#10B981',
          parent: null
        },
        familyMember: {
          id: 'member_1',
          name: 'João Silva',
          color: '#3B82F6'
        }
      }
    ];

    it('should return paginated budgets', async () => {
      const filters: BudgetFilters = {
        page: 1,
        limit: 20,
        includeProgress: false,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      // Setup mocks
      mockPrisma.budget.findMany.mockResolvedValue(mockBudgets);
      mockPrisma.budget.count.mockResolvedValue(1);

      // Execute
      const result = await budgetService.findAll(filters);

      // Assertions
      expect(result.data).toHaveLength(1);
      expect(result.pagination).toEqual({
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1
      });
    });

    it('should apply filters correctly', async () => {
      const filters: BudgetFilters = {
        categoryId: 'cat_1',
        familyMemberId: 'member_1',
        month: 12,
        year: 2024,
        page: 1,
        limit: 20,
        includeProgress: false,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      // Setup mocks
      mockPrisma.budget.findMany.mockResolvedValue(mockBudgets);
      mockPrisma.budget.count.mockResolvedValue(1);

      // Execute
      await budgetService.findAll(filters);

      // Assertions
      expect(mockPrisma.budget.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          categoryId: 'cat_1',
          familyMemberId: 'member_1',
          month: 12,
          year: 2024
        },
        include: {
          category: {
            include: {
              parent: true
            }
          },
          familyMember: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: 0,
        take: 20
      });
    });
  });

  describe('findById', () => {
    const mockBudget = {
      id: 'budget_123',
      plannedAmount: 1000,
      month: 12,
      year: 2024,
      categoryId: 'cat_123',
      familyMemberId: 'member_123',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      version: 1,
      category: {
        id: 'cat_123',
        name: 'Alimentação',
        color: '#10B981',
        parent: null
      },
      familyMember: {
        id: 'member_123',
        name: 'João Silva',
        color: '#3B82F6'
      }
    };

    it('should return budget by id', async () => {
      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(mockBudget);

      // Execute
      const result = await budgetService.findById('budget_123');

      // Assertions
      expect(result).toBeDefined();
      expect(result?.id).toBe('budget_123');
      expect(mockPrisma.budget.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'budget_123',
          deletedAt: null
        },
        include: {
          category: {
            include: {
              parent: true
            }
          },
          familyMember: true
        }
      });
    });

    it('should return null if budget not found', async () => {
      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(null);

      // Execute
      const result = await budgetService.findById('nonexistent');

      // Assertions
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    const mockExistingBudget = {
      id: 'budget_123',
      plannedAmount: 1000,
      month: 12,
      year: 2024,
      categoryId: 'cat_123',
      familyMemberId: 'member_123'
    };

    const updateData: UpdateBudgetRequest = {
      plannedAmount: 1500
    };

    it('should update budget successfully', async () => {
      const mockUpdatedBudget = {
        ...mockExistingBudget,
        plannedAmount: 1500,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1,
        category: {
          id: 'cat_123',
          name: 'Alimentação',
          color: '#10B981',
          parent: null
        },
        familyMember: {
          id: 'member_123',
          name: 'João Silva',
          color: '#3B82F6'
        }
      };

      // Setup mocks
      mockPrisma.budget.findFirst
        .mockResolvedValueOnce(mockExistingBudget) // First call for existence check
        .mockResolvedValueOnce(null); // Second call for conflict check
      mockPrisma.budget.update.mockResolvedValue(mockUpdatedBudget);

      // Execute
      const result = await budgetService.update('budget_123', updateData);

      // Assertions
      expect(result.plannedAmount).toBe(1500);
      expect(mockPrisma.budget.update).toHaveBeenCalledWith({
        where: { id: 'budget_123' },
        data: { plannedAmount: expect.any(Object) }, // Prisma.Decimal
        include: {
          category: {
            include: {
              parent: true
            }
          },
          familyMember: true
        }
      });
    });

    it('should throw error if budget not found', async () => {
      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(null);

      // Execute & Assert
      await expect(budgetService.update('nonexistent', updateData))
        .rejects
        .toThrow('Orçamento não encontrado');
    });
  });

  describe('delete', () => {
    it('should soft delete budget successfully', async () => {
      const mockBudget = {
        id: 'budget_123',
        deletedAt: null
      };

      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(mockBudget);
      mockPrisma.budget.update.mockResolvedValue({ ...mockBudget, deletedAt: new Date() });

      // Execute
      await budgetService.delete('budget_123');

      // Assertions
      expect(mockPrisma.budget.update).toHaveBeenCalledWith({
        where: { id: 'budget_123' },
        data: { deletedAt: expect.any(Date) }
      });
    });

    it('should throw error if budget not found', async () => {
      // Setup mocks
      mockPrisma.budget.findFirst.mockResolvedValue(null);

      // Execute & Assert
      await expect(budgetService.delete('nonexistent'))
        .rejects
        .toThrow('Orçamento não encontrado');
    });
  });
});

describe('Budget Progress Calculations', () => {
  describe('calculateBudgetProgress', () => {
    it('should calculate progress correctly for under budget scenario', async () => {
      // Mock transaction data showing spending under budget
      const mockTransactionAggregation = {
        _sum: { amount: 500 },
        _avg: { amount: 100 }
      };

      mockPrisma.transaction.aggregate.mockResolvedValue(mockTransactionAggregation);
      mockPrisma.transaction.count.mockResolvedValue(5);

      // Test the private method through a public method that uses it
      const mockBudget = {
        id: 'budget_123',
        plannedAmount: 1000,
        month: 12,
        year: 2024,
        categoryId: 'cat_123',
        familyMemberId: 'member_123',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1,
        category: {
          id: 'cat_123',
          name: 'Alimentação',
          color: '#10B981',
          parent: null
        },
        familyMember: {
          id: 'member_123',
          name: 'João Silva',
          color: '#3B82F6'
        }
      };

      mockPrisma.budget.findFirst.mockResolvedValue(mockBudget);

      const result = await budgetService.findById('budget_123', true);

      // Verify progress calculations
      expect(result?.progress?.actualAmount).toBe(500);
      expect(result?.progress?.remainingAmount).toBe(500);
      expect(result?.progress?.percentageUsed).toBe(50);
      expect(result?.progress?.isOverBudget).toBe(false);
      expect(result?.progress?.status).toBe('under_budget');
    });

    it('should calculate progress correctly for over budget scenario', async () => {
      // Mock transaction data showing spending over budget
      const mockTransactionAggregation = {
        _sum: { amount: 1500 },
        _avg: { amount: 300 }
      };

      mockPrisma.transaction.aggregate.mockResolvedValue(mockTransactionAggregation);
      mockPrisma.transaction.count.mockResolvedValue(5);

      const mockBudget = {
        id: 'budget_123',
        plannedAmount: 1000,
        month: 12,
        year: 2024,
        categoryId: 'cat_123',
        familyMemberId: 'member_123',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1,
        category: {
          id: 'cat_123',
          name: 'Alimentação',
          color: '#10B981',
          parent: null
        },
        familyMember: {
          id: 'member_123',
          name: 'João Silva',
          color: '#3B82F6'
        }
      };

      mockPrisma.budget.findFirst.mockResolvedValue(mockBudget);

      const result = await budgetService.findById('budget_123', true);

      // Verify over budget calculations
      expect(result?.progress?.actualAmount).toBe(1500);
      expect(result?.progress?.isOverBudget).toBe(true);
      expect(result?.progress?.overBudgetAmount).toBe(500);
      expect(result?.progress?.status).toBe('over_budget');
    });

    it('should handle edge cases (no transactions, zero budget)', async () => {
      // Mock no transactions
      const mockTransactionAggregation = {
        _sum: { amount: null },
        _avg: { amount: null }
      };

      mockPrisma.transaction.aggregate.mockResolvedValue(mockTransactionAggregation);
      mockPrisma.transaction.count.mockResolvedValue(0);

      const mockBudget = {
        id: 'budget_123',
        plannedAmount: 1000,
        month: 12,
        year: 2024,
        categoryId: 'cat_123',
        familyMemberId: null,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1,
        category: {
          id: 'cat_123',
          name: 'Alimentação',
          color: '#10B981',
          parent: null
        },
        familyMember: null
      };

      mockPrisma.budget.findFirst.mockResolvedValue(mockBudget);

      const result = await budgetService.findById('budget_123', true);

      // Verify edge case handling
      expect(result?.progress?.actualAmount).toBe(0);
      expect(result?.progress?.transactionCount).toBe(0);
      expect(result?.progress?.averageTransactionAmount).toBe(0);
      expect(result?.progress?.percentageUsed).toBe(0);
      expect(result?.progress?.status).toBe('under_budget');
    });
  });
});

describe('Budget Schema Validation Tests', () => {
  describe('CreateBudgetSchema', () => {
    it('should validate valid budget data', () => {
      const validData = {
        plannedAmount: 1000.50,
        month: 12,
        year: 2024,
        categoryId: 'clx123456789',
        familyMemberId: 'clx987654321'
      };

      const result = CreateBudgetSchema.parse(validData);
      expect(result.plannedAmount).toBe(1000.50);
      expect(result.month).toBe(12);
      expect(result.year).toBe(2024);
    });

    it('should reject invalid amounts', () => {
      const invalidData = {
        plannedAmount: -100, // Negative amount
        month: 12,
        year: 2024,
        categoryId: 'clx123456789'
      };

      expect(() => CreateBudgetSchema.parse(invalidData))
        .toThrow('Valor planejado deve ser positivo');
    });

    it('should reject invalid months', () => {
      const invalidData = {
        plannedAmount: 1000,
        month: 13, // Invalid month
        year: 2024,
        categoryId: 'clx123456789'
      };

      expect(() => CreateBudgetSchema.parse(invalidData))
        .toThrow('Mês deve estar entre 1 e 12');
    });

    it('should reject invalid years', () => {
      const invalidData = {
        plannedAmount: 1000,
        month: 12,
        year: 2019, // Too old
        categoryId: 'clx123456789'
      };

      expect(() => CreateBudgetSchema.parse(invalidData))
        .toThrow('Ano deve ser maior ou igual a 2020');
    });

    it('should reject invalid category IDs', () => {
      const invalidData = {
        plannedAmount: 1000,
        month: 12,
        year: 2024,
        categoryId: 'invalid-id' // Not a CUID
      };

      expect(() => CreateBudgetSchema.parse(invalidData))
        .toThrow('ID da categoria inválido');
    });
  });

  describe('UpdateBudgetSchema', () => {
    it('should allow partial updates', () => {
      const partialData = {
        plannedAmount: 1500
      };

      const result = UpdateBudgetSchema.parse(partialData);
      expect(result.plannedAmount).toBe(1500);
      expect(result.month).toBeUndefined();
    });

    it('should validate updated fields', () => {
      const invalidData = {
        plannedAmount: -500 // Negative amount
      };

      expect(() => UpdateBudgetSchema.parse(invalidData))
        .toThrow('Valor planejado deve ser positivo');
    });
  });

  describe('BudgetFiltersSchema', () => {
    it('should apply default values', () => {
      const result = BudgetFiltersSchema.parse({});

      expect(result.page).toBe(1);
      expect(result.limit).toBe(20);
      expect(result.includeProgress).toBe(false);
      expect(result.sortBy).toBe('createdAt');
      expect(result.sortOrder).toBe('desc');
    });

    it('should validate pagination limits', () => {
      const invalidData = {
        limit: 150 // Too high
      };

      expect(() => BudgetFiltersSchema.parse(invalidData))
        .toThrow('Limite deve ser menor ou igual a 100');
    });
  });
});

describe('Budget Integration Scenarios', () => {
  describe('Budget lifecycle', () => {
    it('should handle complete budget lifecycle', async () => {
      // This would test: create -> read -> update -> delete
      // In a real integration test environment
    });

    it('should handle budget with transactions', async () => {
      // This would test budget progress calculations with real transaction data
    });

    it('should handle budget conflicts', async () => {
      // This would test duplicate budget prevention
    });
  });

  describe('Budget reporting', () => {
    it('should generate accurate reports', async () => {
      // This would test report generation with multiple budgets
    });

    it('should handle date range filtering', async () => {
      // This would test date-based filtering in reports
    });
  });
});
