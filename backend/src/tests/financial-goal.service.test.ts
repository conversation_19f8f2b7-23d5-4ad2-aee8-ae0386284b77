import { financialGoalService } from '../services/financial-goal.service';
import prisma from '../lib/prisma';
import {
  CreateFinancialGoalRequest,
  UpdateFinancialGoalRequest,
  UpdateGoalProgressRequest,
  FinancialGoalFilters
} from '../schemas/financial-goal.schemas';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  goal: {
    findFirst: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    count: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    aggregate: jest.fn(),
  },
  goalMember: {
    createMany: jest.fn(),
    deleteMany: jest.fn(),
  },
  familyMember: {
    findMany: jest.fn(),
  },
  $transaction: jest.fn(),
}));

const mockPrisma = prisma as any;

describe('FinancialGoalService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('create', () => {
    const validGoalData: CreateFinancialGoalRequest = {
      name: 'Casa Própria',
      targetAmount: 300000,
      currentAmount: 50000,
      targetDate: new Date('2025-12-31'),
      familyMemberIds: ['member_1', 'member_2']
    };

    const mockFamilyMembers = [
      {
        id: 'member_1',
        name: 'João Silva',
        color: '#3B82F6',
        deletedAt: null
      },
      {
        id: 'member_2',
        name: 'Maria Silva',
        color: '#EF4444',
        deletedAt: null
      }
    ];

    const mockCreatedGoal = {
      id: 'goal_123',
      name: 'Casa Própria',
      targetAmount: 300000,
      currentAmount: 50000,
      targetDate: new Date('2025-12-31'),
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      version: 1
    };

    const mockCompleteGoal = {
      ...mockCreatedGoal,
      members: [
        {
          familyMember: mockFamilyMembers[0]
        },
        {
          familyMember: mockFamilyMembers[1]
        }
      ],
      milestones: []
    };

    it('should create a financial goal successfully', async () => {
      // Setup mocks
      mockPrisma.familyMember.findMany.mockResolvedValue(mockFamilyMembers);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          goal: {
            create: jest.fn().mockResolvedValue(mockCreatedGoal)
          },
          goalMember: {
            createMany: jest.fn().mockResolvedValue({ count: 2 })
          }
        });
      });
      mockPrisma.goal.findUnique.mockResolvedValue(mockCompleteGoal);

      // Execute
      const result = await financialGoalService.create(validGoalData);

      // Assertions
      expect(result).toEqual({
        id: 'goal_123',
        name: 'Casa Própria',
        targetAmount: 300000,
        currentAmount: 50000,
        targetDate: '2025-12-31T00:00:00.000Z',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        version: 1,
        progress: {
          percentage: 16.67,
          remainingAmount: 250000,
          isCompleted: false,
          isOverdue: false,
          daysRemaining: expect.any(Number),
          status: 'in_progress',
          milestoneProgress: undefined
        },
        members: [
          {
            id: 'member_1',
            name: 'João Silva',
            color: '#3B82F6'
          },
          {
            id: 'member_2',
            name: 'Maria Silva',
            color: '#EF4444'
          }
        ],
        milestones: []
      });

      expect(mockPrisma.familyMember.findMany).toHaveBeenCalledWith({
        where: {
          id: { in: ['member_1', 'member_2'] },
          deletedAt: null
        }
      });
    });

    it('should throw error if family members not found', async () => {
      // Setup mocks - only return one member instead of two
      mockPrisma.familyMember.findMany.mockResolvedValue([mockFamilyMembers[0]]);

      // Execute & Assert
      await expect(financialGoalService.create(validGoalData))
        .rejects
        .toThrow('Um ou mais membros da família não foram encontrados ou foram arquivados');
    });

    it('should create goal without target date', async () => {
      const goalDataWithoutDate = { ...validGoalData };
      delete goalDataWithoutDate.targetDate;

      const mockGoalWithoutDate = {
        ...mockCreatedGoal,
        targetDate: null
      };

      const mockCompleteGoalWithoutDate = {
        ...mockCompleteGoal,
        targetDate: null
      };

      // Setup mocks
      mockPrisma.familyMember.findMany.mockResolvedValue(mockFamilyMembers);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          goal: {
            create: jest.fn().mockResolvedValue(mockGoalWithoutDate)
          },
          goalMember: {
            createMany: jest.fn().mockResolvedValue({ count: 2 })
          }
        });
      });
      mockPrisma.goal.findUnique.mockResolvedValue(mockCompleteGoalWithoutDate);

      // Execute
      const result = await financialGoalService.create(goalDataWithoutDate);

      // Assertions
      expect(result.targetDate).toBeUndefined();
      expect(result.progress.daysRemaining).toBeUndefined();
    });
  });

  describe('findAll', () => {
    const mockGoals = [
      {
        id: 'goal_1',
        name: 'Casa Própria',
        targetAmount: 300000,
        currentAmount: 50000,
        targetDate: new Date('2025-12-31'),
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1,
        members: [
          {
            familyMember: {
              id: 'member_1',
              name: 'João Silva',
              color: '#3B82F6'
            }
          }
        ],
        milestones: []
      }
    ];

    it('should return paginated goals', async () => {
      const filters: FinancialGoalFilters = {
        status: 'active',
        page: 1,
        limit: 20,
        sortBy: 'createdAt',
        sortOrder: 'desc',
        includeCompleted: false,
        includeMilestones: false
      };

      // Setup mocks
      mockPrisma.goal.findMany.mockResolvedValue(mockGoals);
      mockPrisma.goal.count.mockResolvedValue(1);

      // Execute
      const result = await financialGoalService.findAll(filters);

      // Assertions
      expect(result.data).toHaveLength(1);
      expect(result.pagination).toEqual({
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1
      });
    });

    it('should apply filters correctly', async () => {
      const filters: FinancialGoalFilters = {
        familyMemberId: 'member_1',
        status: 'active',
        minTargetAmount: 100000,
        maxTargetAmount: 500000,
        page: 1,
        limit: 20,
        sortBy: 'targetAmount',
        sortOrder: 'asc',
        includeCompleted: false,
        includeMilestones: true
      };

      // Setup mocks
      mockPrisma.goal.findMany.mockResolvedValue(mockGoals);
      mockPrisma.goal.count.mockResolvedValue(1);

      // Execute
      const result = await financialGoalService.findAll(filters);

      // Assertions - Just verify the service was called and returned expected structure
      expect(result.data).toHaveLength(1);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(20);
      expect(mockPrisma.goal.findMany).toHaveBeenCalled();
      expect(mockPrisma.goal.count).toHaveBeenCalled();
    });
  });

  describe('updateProgress', () => {
    const mockExistingGoal = {
      id: 'goal_123',
      name: 'Casa Própria',
      targetAmount: 300000,
      currentAmount: 50000,
      targetDate: new Date('2025-12-31'),
      deletedAt: null
    };

    const progressData: UpdateGoalProgressRequest = {
      amount: 10000,
      operation: 'add',
      description: 'Depósito mensal'
    };

    it('should add amount to goal progress', async () => {
      const mockUpdatedGoal = {
        ...mockExistingGoal,
        currentAmount: 60000,
        members: [],
        milestones: []
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockExistingGoal);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          goal: {
            update: jest.fn().mockResolvedValue(mockUpdatedGoal)
          }
        });
      });

      // Execute
      const result = await financialGoalService.updateProgress('goal_123', progressData);

      // Assertions
      expect(result.previousAmount).toBe(50000);
      expect(result.newAmount).toBe(60000);
      expect(result.operation).toBe('add');
      expect(result.amountChanged).toBe(10000);
      expect(result.newProgress.percentage).toBe(20);
    });

    it('should subtract amount from goal progress', async () => {
      const subtractData: UpdateGoalProgressRequest = {
        amount: 5000,
        operation: 'subtract'
      };

      const mockUpdatedGoal = {
        ...mockExistingGoal,
        currentAmount: 45000,
        members: [],
        milestones: []
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockExistingGoal);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          goal: {
            update: jest.fn().mockResolvedValue(mockUpdatedGoal)
          }
        });
      });

      // Execute
      const result = await financialGoalService.updateProgress('goal_123', subtractData);

      // Assertions
      expect(result.newAmount).toBe(45000);
      expect(result.amountChanged).toBe(-5000);
    });

    it('should set exact amount for goal progress', async () => {
      const setData: UpdateGoalProgressRequest = {
        amount: 75000,
        operation: 'set'
      };

      const mockUpdatedGoal = {
        ...mockExistingGoal,
        currentAmount: 75000,
        members: [],
        milestones: []
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockExistingGoal);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          goal: {
            update: jest.fn().mockResolvedValue(mockUpdatedGoal)
          }
        });
      });

      // Execute
      const result = await financialGoalService.updateProgress('goal_123', setData);

      // Assertions
      expect(result.newAmount).toBe(75000);
      expect(result.amountChanged).toBe(25000);
    });

    it('should throw error if goal not found', async () => {
      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(null);

      // Execute & Assert
      await expect(financialGoalService.updateProgress('nonexistent', progressData))
        .rejects
        .toThrow('Meta financeira não encontrada');
    });

    it('should throw error if amount exceeds 150% of target', async () => {
      const excessiveData: UpdateGoalProgressRequest = {
        amount: 500000,
        operation: 'set'
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockExistingGoal);

      // Execute & Assert
      await expect(financialGoalService.updateProgress('goal_123', excessiveData))
        .rejects
        .toThrow('Valor atual não pode exceder 150% do valor alvo');
    });
  });

  describe('findById', () => {
    const mockGoal = {
      id: 'goal_123',
      name: 'Casa Própria',
      targetAmount: 300000,
      currentAmount: 50000,
      targetDate: new Date('2025-12-31'),
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      version: 1,
      members: [
        {
          familyMember: {
            id: 'member_1',
            name: 'João Silva',
            color: '#3B82F6'
          }
        }
      ],
      milestones: []
    };

    it('should return goal by id', async () => {
      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockGoal);

      // Execute
      const result = await financialGoalService.findById('goal_123');

      // Assertions
      expect(result).toBeDefined();
      expect(result?.id).toBe('goal_123');
      expect(result?.name).toBe('Casa Própria');
      expect(mockPrisma.goal.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'goal_123',
          deletedAt: null
        },
        include: {
          members: {
            include: {
              familyMember: true
            }
          },
          milestones: {
            orderBy: { targetDate: 'asc' }
          }
        }
      });
    });

    it('should return null if goal not found', async () => {
      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(null);

      // Execute
      const result = await financialGoalService.findById('nonexistent');

      // Assertions
      expect(result).toBeNull();
    });

    it('should return goal without milestones when includeMilestones is false', async () => {
      const mockGoalWithoutMilestones = {
        id: mockGoal.id,
        name: mockGoal.name,
        targetAmount: mockGoal.targetAmount,
        currentAmount: mockGoal.currentAmount,
        targetDate: mockGoal.targetDate,
        createdAt: mockGoal.createdAt,
        updatedAt: mockGoal.updatedAt,
        version: mockGoal.version,
        members: mockGoal.members
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockGoalWithoutMilestones);

      // Execute
      const result = await financialGoalService.findById('goal_123', false);

      // Assertions
      expect(result).toBeDefined();
      expect(result?.milestones).toBeUndefined();
    });
  });

  describe('update', () => {
    const mockExistingGoal = {
      id: 'goal_123',
      name: 'Casa Própria',
      targetAmount: 300000,
      currentAmount: 50000,
      targetDate: new Date('2025-12-31'),
      deletedAt: null
    };

    const updateData: UpdateFinancialGoalRequest = {
      name: 'Casa Nova',
      targetAmount: 350000
    };

    it('should update goal successfully', async () => {
      const mockUpdatedGoal = {
        ...mockExistingGoal,
        name: 'Casa Nova',
        targetAmount: 350000,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1,
        members: [],
        milestones: []
      };

      const mockCompleteGoal = {
        ...mockUpdatedGoal,
        members: [
          {
            familyMember: {
              id: 'member_1',
              name: 'João Silva',
              color: '#3B82F6'
            }
          }
        ]
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockExistingGoal);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          goal: {
            update: jest.fn().mockResolvedValue(mockUpdatedGoal)
          }
        });
      });
      mockPrisma.goal.findUnique.mockResolvedValue(mockCompleteGoal);

      // Execute
      const result = await financialGoalService.update('goal_123', updateData);

      // Assertions
      expect(result.name).toBe('Casa Nova');
      expect(result.targetAmount).toBe(350000);
    });

    it('should throw error if goal not found', async () => {
      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(null);

      // Execute & Assert
      await expect(financialGoalService.update('nonexistent', updateData))
        .rejects
        .toThrow('Meta financeira não encontrada');
    });

    it('should update family members when provided', async () => {
      const updateDataWithMembers: UpdateFinancialGoalRequest = {
        familyMemberIds: ['member_1', 'member_2']
      };

      const mockFamilyMembers = [
        { id: 'member_1', name: 'João Silva', color: '#3B82F6', deletedAt: null },
        { id: 'member_2', name: 'Maria Silva', color: '#EF4444', deletedAt: null }
      ];

      const mockUpdatedGoal = {
        ...mockExistingGoal,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        version: 1,
        members: [],
        milestones: []
      };

      const mockCompleteGoal = {
        ...mockUpdatedGoal,
        members: mockFamilyMembers.map(member => ({ familyMember: member }))
      };

      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockExistingGoal);
      mockPrisma.familyMember.findMany.mockResolvedValue(mockFamilyMembers);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          goal: {
            update: jest.fn().mockResolvedValue(mockUpdatedGoal)
          },
          goalMember: {
            deleteMany: jest.fn().mockResolvedValue({ count: 0 }),
            createMany: jest.fn().mockResolvedValue({ count: 2 })
          }
        });
      });
      mockPrisma.goal.findUnique.mockResolvedValue(mockCompleteGoal);

      // Execute
      const result = await financialGoalService.update('goal_123', updateDataWithMembers);

      // Assertions
      expect(result.members).toHaveLength(2);
    });
  });

  describe('delete', () => {
    const mockGoal = {
      id: 'goal_123',
      deletedAt: null
    };

    it('should soft delete goal successfully', async () => {
      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(mockGoal);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          goal: {
            update: jest.fn().mockResolvedValue({ ...mockGoal, deletedAt: new Date() })
          }
        });
      });

      // Execute
      await financialGoalService.delete('goal_123');

      // Assertions
      expect(mockPrisma.$transaction).toHaveBeenCalled();
    });

    it('should throw error if goal not found', async () => {
      // Setup mocks
      mockPrisma.goal.findFirst.mockResolvedValue(null);

      // Execute & Assert
      await expect(financialGoalService.delete('nonexistent'))
        .rejects
        .toThrow('Meta financeira não encontrada');
    });
  });

  describe('getSummary', () => {
    const mockGoals = [
      {
        id: 'goal_1',
        targetAmount: 300000,
        currentAmount: 150000,
        targetDate: new Date('2025-12-31')
      },
      {
        id: 'goal_2',
        targetAmount: 100000,
        currentAmount: 100000,
        targetDate: new Date('2024-06-30')
      },
      {
        id: 'goal_3',
        targetAmount: 200000,
        currentAmount: 50000,
        targetDate: new Date('2023-12-31') // Overdue
      }
    ];

    const mockAggregation = {
      _sum: {
        targetAmount: 600000,
        currentAmount: 300000
      },
      _count: 3
    };

    it('should return summary statistics', async () => {
      // Setup mocks
      mockPrisma.goal.findMany.mockResolvedValue(mockGoals);
      mockPrisma.goal.aggregate.mockResolvedValue(mockAggregation);

      // Execute
      const result = await financialGoalService.getSummary();

      // Assertions
      expect(result.totalGoals).toBe(3);
      expect(result.completedGoals).toBe(1);
      expect(result.activeGoals).toBe(1);
      expect(result.overdueGoals).toBe(1);
      expect(result.totalTargetAmount).toBe(600000);
      expect(result.totalCurrentAmount).toBe(300000);
      expect(result.overallProgress).toBe(50);
    });

    it('should filter summary by family member', async () => {
      // Setup mocks
      mockPrisma.goal.findMany.mockResolvedValue([mockGoals[0]]);
      mockPrisma.goal.aggregate.mockResolvedValue({
        _sum: { targetAmount: 300000, currentAmount: 150000 },
        _count: 1
      });

      // Execute
      const result = await financialGoalService.getSummary('member_1');

      // Assertions
      expect(result.totalGoals).toBe(1);
      expect(mockPrisma.goal.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          members: {
            some: {
              familyMemberId: 'member_1'
            }
          }
        },
        select: {
          id: true,
          targetAmount: true,
          currentAmount: true,
          targetDate: true
        }
      });
    });
  });
});
