import { DashboardService } from '../services/dashboard.service';
import { dashboardCache } from '../services/dashboard-cache.service';
import {
  mockAccounts,
  mockTransactions,
  mockCategories,
  mockFamilyMembers,
  mockBudgets,
  mockGoals,
  mockAccountBalanceHistory,
  createMockAccount,
  createMockTransaction,
  createMockGoal
} from './fixtures/dashboard.fixtures';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    account: {
      findMany: jest.fn()
    },
    transaction: {
      findMany: jest.fn()
    },
    budget: {
      findMany: jest.fn()
    },
    goal: {
      findMany: jest.fn()
    },
    accountBalanceHistory: {
      findMany: jest.fn()
    }
  }
}));

// Mock Dashboard Cache
jest.mock('../services/dashboard-cache.service', () => ({
  dashboardCache: {
    withCache: jest.fn()
  }
}));

describe('DashboardService', () => {
  let dashboardService: DashboardService;
  let mockPrisma: any;
  let mockDashboardCache: jest.Mocked<typeof dashboardCache>;

  beforeEach(() => {
    dashboardService = new DashboardService();
    mockPrisma = require('../lib/prisma').default;
    mockDashboardCache = dashboardCache as jest.Mocked<typeof dashboardCache>;
    
    // Reset all mocks
    jest.clearAllMocks();
    
    // Default cache behavior - pass through to actual function
    mockDashboardCache.withCache.mockImplementation(async (operation, filters, dataFetcher) => {
      return await dataFetcher();
    });
  });

  describe('getAccountBalances', () => {
    it('should return account balances aggregation with no filters', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue(mockAccounts);
      mockPrisma.transaction.findMany.mockResolvedValue([]); // No future transactions

      // Act
      const result = await dashboardService.getAccountBalances({});

      // Assert
      expect(result).toEqual({
        totalBalance: 17500.00, // 5000 + 15000 + (-2500) + (1000 * 5.5)
        totalBalanceInBRL: 17500.00,
        projectedBalance: 17500.00, // No future transactions
        projectedBalanceInBRL: 17500.00,
        balanceByType: [
          {
            type: 'CHECKING',
            balance: 10500.00, // 5000 + (1000 * 5.5)
            balanceInBRL: 10500.00,
            count: 2
          },
          {
            type: 'SAVINGS',
            balance: 15000.00,
            balanceInBRL: 15000.00,
            count: 1
          },
          {
            type: 'CREDIT_CARD',
            balance: -2500.00,
            balanceInBRL: -2500.00,
            count: 1
          }
        ],
        balanceByCurrency: [
          {
            currency: 'BRL',
            balance: 17500.00,
            balanceInBRL: 17500.00,
            exchangeRate: undefined,
            count: 3
          },
          {
            currency: 'USD',
            balance: 1000.00,
            balanceInBRL: 5500.00,
            exchangeRate: 5.5,
            count: 1
          }
        ]
      });

      // Verify Prisma calls
      expect(mockPrisma.account.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          includeInTotal: true
        }
      });

      expect(mockPrisma.transaction.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          isFuture: true
        },
        include: {
          account: {
            select: {
              id: true,
              currency: true,
              exchangeRate: true
            }
          }
        }
      });

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'ACCOUNT_BALANCES',
        {},
        expect.any(Function)
      );
    });

    it('should filter accounts by accountIds', async () => {
      // Arrange
      const filteredAccounts = [mockAccounts[0], mockAccounts[1]];
      mockPrisma.account.findMany.mockResolvedValue(filteredAccounts);
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      const filters = { accountIds: ['acc-1', 'acc-2'] };

      // Act
      const result = await dashboardService.getAccountBalances(filters);

      // Assert
      expect(result.totalBalance).toBe(20000.00); // 5000 + 15000
      expect(mockPrisma.account.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          includeInTotal: true,
          id: { in: ['acc-1', 'acc-2'] }
        }
      });
    });

    it('should filter accounts by currencies', async () => {
      // Arrange
      const brlAccounts = mockAccounts.filter(acc => acc.currency === 'BRL');
      mockPrisma.account.findMany.mockResolvedValue(brlAccounts);
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      const filters = { currencies: ['BRL'] };

      // Act
      const result = await dashboardService.getAccountBalances(filters);

      // Assert
      expect(result.balanceByCurrency).toHaveLength(1);
      expect(result.balanceByCurrency[0].currency).toBe('BRL');
      expect(mockPrisma.account.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          includeInTotal: true,
          currency: { in: ['BRL'] }
        }
      });
    });

    it('should include future transactions in projected balance', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue([mockAccounts[0]]);
      
      const futureTransactions = [
        createMockTransaction({
          id: 'future-1',
          amount: 1000.00,
          accountId: 'acc-1',
          isFuture: true,
          transactionDate: new Date('2024-12-31'),
          account: mockAccounts[0]
        })
      ];
      mockPrisma.transaction.findMany.mockResolvedValue(futureTransactions);

      // Act
      const result = await dashboardService.getAccountBalances({});

      // Assert
      expect(result.totalBalance).toBe(5000.00);
      expect(result.projectedBalance).toBe(6000.00); // 5000 + 1000
      expect(result.projectedBalanceInBRL).toBe(6000.00);
    });

    it('should handle empty accounts gracefully', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue([]);
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getAccountBalances({});

      // Assert
      expect(result).toEqual({
        totalBalance: 0,
        totalBalanceInBRL: 0,
        projectedBalance: 0,
        projectedBalanceInBRL: 0,
        balanceByType: [],
        balanceByCurrency: []
      });
    });

    it('should handle Prisma errors gracefully', async () => {
      // Arrange
      mockPrisma.account.findMany.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert
      await expect(dashboardService.getAccountBalances({})).rejects.toThrow('Database connection failed');
    });
  });

  describe('getNetWorth', () => {
    it('should calculate net worth correctly', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue(mockAccounts);
      mockPrisma.transaction.findMany.mockResolvedValue([]);
      mockPrisma.accountBalanceHistory.findMany.mockResolvedValue(mockAccountBalanceHistory);

      // Act
      const result = await dashboardService.getNetWorth({});

      // Assert
      expect(result.current).toBe(17500.00);
      expect(result.currentInBRL).toBe(17500.00);
      expect(result.projected).toBe(17500.00);
      expect(result.projectedInBRL).toBe(17500.00);
      expect(result.history).toHaveLength(2); // 2 unique dates in history
      expect(result.growth.amount).toBe(500.00); // 17500 - 17000 (from history)
      expect(result.growth.percentage).toBeCloseTo(2.94, 2); // (500/17000) * 100

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'NET_WORTH',
        {},
        expect.any(Function)
      );
    });

    it('should filter net worth by date range', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue(mockAccounts);
      mockPrisma.transaction.findMany.mockResolvedValue([]);
      
      const filteredHistory = mockAccountBalanceHistory.filter(h => 
        h.balanceDate >= new Date('2024-12-01')
      );
      mockPrisma.accountBalanceHistory.findMany.mockResolvedValue(filteredHistory);

      const filters = {
        dateRange: {
          startDate: '2024-12-01T00:00:00.000Z',
          endDate: '2024-12-31T23:59:59.999Z'
        }
      };

      // Act
      const result = await dashboardService.getNetWorth(filters);

      // Assert
      expect(result.history).toHaveLength(1); // Only December data
      expect(mockPrisma.accountBalanceHistory.findMany).toHaveBeenCalledWith({
        where: {
          balanceDate: {
            gte: new Date('2024-12-01T00:00:00.000Z'),
            lte: new Date('2024-12-31T23:59:59.999Z')
          }
        },
        include: {
          account: {
            select: {
              type: true,
              currency: true,
              exchangeRate: true,
              includeInTotal: true
            }
          }
        },
        orderBy: {
          balanceDate: 'asc'
        }
      });
    });

    it('should handle accounts with different currencies in net worth', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue(mockAccounts);
      mockPrisma.transaction.findMany.mockResolvedValue([]);
      mockPrisma.accountBalanceHistory.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getNetWorth({});

      // Assert
      // Should convert USD to BRL: 1000 * 5.5 = 5500
      expect(result.currentInBRL).toBe(17500.00); // 5000 + 15000 + (-2500) + 5500
    });
  });

  describe('getCreditCardUsage', () => {
    it('should analyze credit card usage correctly', async () => {
      // Arrange
      const creditCards = [mockAccounts[2]]; // Only credit card account
      mockPrisma.account.findMany.mockResolvedValue(creditCards);

      // Act
      const result = await dashboardService.getCreditCardUsage({});

      // Assert
      expect(result).toEqual({
        totalUsed: 2500.00, // Absolute value of negative balance
        totalUsedInBRL: 2500.00,
        totalLimit: 10000.00,
        totalLimitInBRL: 10000.00,
        utilizationRate: 25.00, // (2500/10000) * 100
        cardUsage: [
          {
            accountId: 'acc-3',
            accountName: 'Cartão de Crédito',
            used: 2500.00,
            usedInBRL: 2500.00,
            limit: 10000.00,
            limitInBRL: 10000.00,
            available: 7500.00,
            availableInBRL: 7500.00,
            utilizationRate: 25.00,
            currency: 'BRL',
            exchangeRate: 1.0
          }
        ]
      });

      // Verify Prisma call
      expect(mockPrisma.account.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          type: 'CREDIT_CARD'
        }
      });

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'CREDIT_CARD_USAGE',
        {},
        expect.any(Function)
      );
    });

    it('should handle multiple credit cards with different currencies', async () => {
      // Arrange
      const multiCurrencyCards = [
        mockAccounts[2], // BRL card
        createMockAccount({
          id: 'acc-5',
          name: 'USD Credit Card',
          type: 'CREDIT_CARD',
          currency: 'USD',
          currentBalance: -500.00,
          exchangeRate: 5.5,
          creditLimit: 2000.00
        })
      ];
      mockPrisma.account.findMany.mockResolvedValue(multiCurrencyCards);

      // Act
      const result = await dashboardService.getCreditCardUsage({});

      // Assert
      expect(result.totalUsed).toBe(3000.00); // 2500 + 500
      expect(result.totalUsedInBRL).toBe(5250.00); // 2500 + (500 * 5.5)
      expect(result.cardUsage).toHaveLength(2);
    });

    it('should handle no credit cards gracefully', async () => {
      // Arrange
      mockPrisma.account.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getCreditCardUsage({});

      // Assert
      expect(result).toEqual({
        totalUsed: 0,
        totalUsedInBRL: 0,
        totalLimit: 0,
        totalLimitInBRL: 0,
        utilizationRate: 0,
        cardUsage: []
      });
    });
  });

  describe('getExpensesByCategory', () => {
    it('should aggregate expenses by category correctly', async () => {
      // Arrange
      const expenseTransactions = mockTransactions.filter(t => t.type === 'EXPENSE');
      mockPrisma.transaction.findMany.mockResolvedValue(expenseTransactions);

      // Act
      const result = await dashboardService.getExpensesByCategory({});

      // Assert
      expect(result.totalExpenses).toBe(430.00); // 150 + 80 + 200
      expect(result.totalExpensesInBRL).toBe(430.00);
      expect(result.categories).toHaveLength(3); // 3 different categories

      // Find specific category results
      const alimentacaoCategory = result.categories.find(c => c.categoryId === 'cat-2');
      expect(alimentacaoCategory).toEqual({
        categoryId: 'cat-2',
        categoryName: 'Supermercado',
        parentCategoryId: 'cat-1',
        parentCategoryName: 'Alimentação',
        totalAmount: 150.00,
        totalAmountInBRL: 150.00,
        transactionCount: 1,
        percentage: 34.88 // (150/430) * 100
      });

      // Verify Prisma call
      expect(mockPrisma.transaction.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          type: 'EXPENSE'
        },
        include: {
          category: {
            include: {
              parent: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'EXPENSES_BY_CATEGORY',
        {},
        expect.any(Function)
      );
    });

    it('should filter expenses by date range', async () => {
      // Arrange
      const filteredTransactions = mockTransactions.filter(t =>
        t.type === 'EXPENSE' &&
        t.transactionDate >= new Date('2024-12-02')
      );
      mockPrisma.transaction.findMany.mockResolvedValue(filteredTransactions);

      const filters = {
        dateRange: {
          startDate: '2024-12-02T00:00:00.000Z',
          endDate: '2024-12-31T23:59:59.999Z'
        }
      };

      // Act
      const result = await dashboardService.getExpensesByCategory(filters);

      // Assert
      expect(result.totalExpenses).toBe(280.00); // 80 + 200 (excluding first transaction)
      expect(mockPrisma.transaction.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          type: 'EXPENSE',
          transactionDate: {
            gte: new Date('2024-12-02T00:00:00.000Z'),
            lte: new Date('2024-12-31T23:59:59.999Z')
          }
        },
        include: {
          category: {
            include: {
              parent: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });
    });

    it('should filter expenses by category IDs', async () => {
      // Arrange
      const categoryFilteredTransactions = mockTransactions.filter(t =>
        t.type === 'EXPENSE' &&
        ['cat-2', 'cat-3'].includes(t.categoryId!)
      );
      mockPrisma.transaction.findMany.mockResolvedValue(categoryFilteredTransactions);

      const filters = { categoryIds: ['cat-2', 'cat-3'] };

      // Act
      const result = await dashboardService.getExpensesByCategory(filters);

      // Assert
      expect(result.categories).toHaveLength(2);
      expect(mockPrisma.transaction.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          type: 'EXPENSE',
          categoryId: { in: ['cat-2', 'cat-3'] }
        },
        include: {
          category: {
            include: {
              parent: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });
    });

    it('should handle transactions without categories', async () => {
      // Arrange
      const transactionsWithoutCategory = [
        createMockTransaction({
          id: 'trans-no-cat',
          categoryId: null,
          category: null,
          amount: -100.00,
          type: 'EXPENSE'
        })
      ];
      mockPrisma.transaction.findMany.mockResolvedValue(transactionsWithoutCategory);

      // Act
      const result = await dashboardService.getExpensesByCategory({});

      // Assert
      expect(result.categories).toHaveLength(1);
      expect(result.categories[0]).toEqual({
        categoryId: null,
        categoryName: 'Sem categoria',
        parentCategoryId: null,
        parentCategoryName: null,
        totalAmount: 100.00,
        totalAmountInBRL: 100.00,
        transactionCount: 1,
        percentage: 100.00
      });
    });
  });

  describe('getExpensesByMember', () => {
    it('should aggregate expenses by family member correctly', async () => {
      // Arrange
      const expenseTransactions = mockTransactions.filter(t => t.type === 'EXPENSE');
      mockPrisma.transaction.findMany.mockResolvedValue(expenseTransactions);

      // Act
      const result = await dashboardService.getExpensesByMember({});

      // Assert
      expect(result.totalExpenses).toBe(430.00);
      expect(result.totalExpensesInBRL).toBe(430.00);
      expect(result.members).toHaveLength(2); // João and Maria

      // Find specific member results
      const joaoMember = result.members.find(m => m.familyMemberId === 'member-1');
      expect(joaoMember).toEqual({
        familyMemberId: 'member-1',
        familyMemberName: 'João Silva',
        totalAmount: 230.00, // 150 (solo) + 80 (shared with Maria)
        totalAmountInBRL: 230.00,
        transactionCount: 2,
        percentage: 53.49 // (230/430) * 100
      });

      const mariaMember = result.members.find(m => m.familyMemberId === 'member-2');
      expect(mariaMember).toEqual({
        familyMemberId: 'member-2',
        familyMemberName: 'Maria Silva',
        totalAmount: 280.00, // 80 (shared with João) + 200 (solo)
        totalAmountInBRL: 280.00,
        transactionCount: 2,
        percentage: 65.12 // (280/430) * 100
      });

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'EXPENSES_BY_MEMBER',
        {},
        expect.any(Function)
      );
    });

    it('should filter expenses by family member IDs', async () => {
      // Arrange
      const memberFilteredTransactions = mockTransactions.filter(t =>
        t.type === 'EXPENSE' &&
        t.members.some(m => m.familyMemberId === 'member-1')
      );
      mockPrisma.transaction.findMany.mockResolvedValue(memberFilteredTransactions);

      const filters = { familyMemberIds: ['member-1'] };

      // Act
      const result = await dashboardService.getExpensesByMember(filters);

      // Assert
      expect(result.members).toHaveLength(1);
      expect(result.members[0].familyMemberId).toBe('member-1');

      expect(mockPrisma.transaction.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          type: 'EXPENSE',
          members: {
            some: {
              familyMemberId: { in: ['member-1'] }
            }
          }
        },
        include: {
          members: {
            include: {
              familyMember: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });
    });

    it('should handle transactions without family members', async () => {
      // Arrange
      const transactionsWithoutMembers = [
        createMockTransaction({
          id: 'trans-no-members',
          amount: -50.00,
          type: 'EXPENSE',
          members: []
        })
      ];
      mockPrisma.transaction.findMany.mockResolvedValue(transactionsWithoutMembers);

      // Act
      const result = await dashboardService.getExpensesByMember({});

      // Assert
      expect(result.totalExpenses).toBe(50.00);
      expect(result.members).toHaveLength(0); // No members assigned
    });
  });

  describe('getBudgetComparison', () => {
    it('should compare budgets with actual expenses correctly', async () => {
      // Arrange
      mockPrisma.budget.findMany.mockResolvedValue(mockBudgets);

      // Mock expense transactions for budget comparison
      const budgetExpenses = [
        createMockTransaction({
          id: 'budget-expense-1',
          amount: -300.00,
          categoryId: 'cat-1', // Alimentação
          type: 'EXPENSE',
          members: [{ familyMemberId: 'member-1', familyMember: mockFamilyMembers[0] }]
        }),
        createMockTransaction({
          id: 'budget-expense-2',
          amount: -150.00,
          categoryId: 'cat-4', // Transporte
          type: 'EXPENSE',
          members: [{ familyMemberId: 'member-2', familyMember: mockFamilyMembers[1] }]
        })
      ];
      mockPrisma.transaction.findMany.mockResolvedValue(budgetExpenses);

      // Act
      const result = await dashboardService.getBudgetComparison({});

      // Assert
      expect(result.totalBudget).toBe(2500.00); // 800 + 500 + 1200
      expect(result.totalBudgetInBRL).toBe(2500.00);
      expect(result.totalSpent).toBe(450.00); // 300 + 150
      expect(result.totalSpentInBRL).toBe(450.00);
      expect(result.totalRemaining).toBe(2050.00); // 2500 - 450
      expect(result.totalRemainingInBRL).toBe(2050.00);
      expect(result.overallUtilization).toBe(18.00); // (450/2500) * 100

      expect(result.categories).toHaveLength(2); // 2 categories with expenses

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'BUDGET_COMPARISON',
        {},
        expect.any(Function)
      );
    });

    it('should filter budget comparison by period', async () => {
      // Arrange
      const periodBudgets = mockBudgets.filter(b => b.month === 12 && b.year === 2024);
      mockPrisma.budget.findMany.mockResolvedValue(periodBudgets);
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      const filters = { period: { month: 12, year: 2024 } };

      // Act
      const result = await dashboardService.getBudgetComparison(filters);

      // Assert
      expect(mockPrisma.budget.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          month: 12,
          year: 2024
        },
        include: {
          category: {
            select: {
              id: true,
              name: true
            }
          },
          familyMember: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });
    });

    it('should handle no budgets gracefully', async () => {
      // Arrange
      mockPrisma.budget.findMany.mockResolvedValue([]);
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      // Act
      const result = await dashboardService.getBudgetComparison({});

      // Assert
      expect(result).toEqual({
        totalBudget: 0,
        totalBudgetInBRL: 0,
        totalSpent: 0,
        totalSpentInBRL: 0,
        totalRemaining: 0,
        totalRemainingInBRL: 0,
        overallUtilization: 0,
        categories: []
      });
    });
  });

  describe('getGoalProgress', () => {
    it('should calculate goal progress correctly', async () => {
      // Arrange
      mockPrisma.goal.findMany.mockResolvedValue(mockGoals);

      // Act
      const result = await dashboardService.getGoalProgress({});

      // Assert
      expect(result.totalGoals).toBe(2);
      expect(result.completedGoals).toBe(0); // None completed yet
      expect(result.inProgressGoals).toBe(2);
      expect(result.totalTargetAmount).toBe(50000.00); // 20000 + 30000
      expect(result.totalTargetAmountInBRL).toBe(50000.00);
      expect(result.totalCurrentAmount).toBe(20000.00); // 5000 + 15000
      expect(result.totalCurrentAmountInBRL).toBe(20000.00);
      expect(result.overallProgress).toBe(40.00); // (20000/50000) * 100

      expect(result.goals).toHaveLength(2);

      // Check specific goal
      const europeGoal = result.goals.find(g => g.goalId === 'goal-1');
      expect(europeGoal).toEqual({
        goalId: 'goal-1',
        goalName: 'Viagem para Europa',
        targetAmount: 20000.00,
        targetAmountInBRL: 20000.00,
        currentAmount: 5000.00,
        currentAmountInBRL: 5000.00,
        remainingAmount: 15000.00,
        remainingAmountInBRL: 15000.00,
        progress: 25.00, // (5000/20000) * 100
        targetDate: new Date('2025-06-01'),
        daysRemaining: expect.any(Number),
        isCompleted: false,
        members: [
          {
            familyMemberId: 'member-1',
            familyMemberName: 'João Silva'
          },
          {
            familyMemberId: 'member-2',
            familyMemberName: 'Maria Silva'
          }
        ]
      });

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'GOAL_PROGRESS',
        {},
        expect.any(Function)
      );
    });

    it('should filter goals by family member IDs', async () => {
      // Arrange
      const memberFilteredGoals = mockGoals.filter(g =>
        g.members.some(m => m.familyMemberId === 'member-1')
      );
      mockPrisma.goal.findMany.mockResolvedValue(memberFilteredGoals);

      const filters = { familyMemberIds: ['member-1'] };

      // Act
      const result = await dashboardService.getGoalProgress(filters);

      // Assert
      expect(result.goals).toHaveLength(2); // Both goals include member-1
      expect(mockPrisma.goal.findMany).toHaveBeenCalledWith({
        where: {
          deletedAt: null,
          members: {
            some: {
              familyMemberId: { in: ['member-1'] }
            }
          }
        },
        include: {
          members: {
            include: {
              familyMember: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          milestones: true
        }
      });
    });

    it('should handle completed goals correctly', async () => {
      // Arrange
      const completedGoal = createMockGoal({
        id: 'completed-goal',
        currentAmount: 20000.00,
        targetAmount: 20000.00 // Completed
      });
      mockPrisma.goal.findMany.mockResolvedValue([completedGoal]);

      // Act
      const result = await dashboardService.getGoalProgress({});

      // Assert
      expect(result.completedGoals).toBe(1);
      expect(result.inProgressGoals).toBe(0);
      expect(result.goals[0].isCompleted).toBe(true);
      expect(result.goals[0].progress).toBe(100.00);
    });
  });

  describe('getOverview', () => {
    it('should return complete dashboard overview', async () => {
      // Arrange - Mock all the individual service methods
      const mockAccountBalances = {
        totalBalance: 17500.00,
        totalBalanceInBRL: 17500.00,
        projectedBalance: 17500.00,
        projectedBalanceInBRL: 17500.00,
        balanceByType: [],
        balanceByCurrency: []
      };

      const mockNetWorth = {
        current: 17500.00,
        currentInBRL: 17500.00,
        projected: 17500.00,
        projectedInBRL: 17500.00,
        history: [],
        growth: { amount: 500.00, percentage: 2.94 }
      };

      // Mock the individual methods
      jest.spyOn(dashboardService, 'getAccountBalances').mockResolvedValue(mockAccountBalances);
      jest.spyOn(dashboardService, 'getNetWorth').mockResolvedValue(mockNetWorth);
      jest.spyOn(dashboardService, 'getCreditCardUsage').mockResolvedValue({
        totalUsed: 2500.00,
        totalUsedInBRL: 2500.00,
        totalLimit: 10000.00,
        totalLimitInBRL: 10000.00,
        utilizationRate: 25.00,
        cardUsage: []
      });
      jest.spyOn(dashboardService, 'getExpensesByCategory').mockResolvedValue({
        totalExpenses: 430.00,
        totalExpensesInBRL: 430.00,
        categories: []
      });
      jest.spyOn(dashboardService, 'getExpensesByMember').mockResolvedValue({
        totalExpenses: 430.00,
        totalExpensesInBRL: 430.00,
        members: []
      });
      jest.spyOn(dashboardService, 'getBudgetComparison').mockResolvedValue({
        totalBudget: 2500.00,
        totalBudgetInBRL: 2500.00,
        totalSpent: 450.00,
        totalSpentInBRL: 450.00,
        totalRemaining: 2050.00,
        totalRemainingInBRL: 2050.00,
        overallUtilization: 18.00,
        categories: []
      });
      jest.spyOn(dashboardService, 'getGoalProgress').mockResolvedValue({
        totalGoals: 2,
        completedGoals: 0,
        inProgressGoals: 2,
        totalTargetAmount: 50000.00,
        totalTargetAmountInBRL: 50000.00,
        totalCurrentAmount: 20000.00,
        totalCurrentAmountInBRL: 20000.00,
        overallProgress: 40.00,
        goals: []
      });

      // Act
      const result = await dashboardService.getOverview({});

      // Assert
      expect(result).toEqual({
        accountBalances: mockAccountBalances,
        netWorth: mockNetWorth,
        creditCardUsage: expect.any(Object),
        expensesByCategory: expect.any(Object),
        expensesByMember: expect.any(Object),
        budgetComparison: expect.any(Object),
        goalProgress: expect.any(Object)
      });

      // Verify all methods were called with the same filters
      expect(dashboardService.getAccountBalances).toHaveBeenCalledWith({});
      expect(dashboardService.getNetWorth).toHaveBeenCalledWith({});
      expect(dashboardService.getCreditCardUsage).toHaveBeenCalledWith({});
      expect(dashboardService.getExpensesByCategory).toHaveBeenCalledWith({});
      expect(dashboardService.getExpensesByMember).toHaveBeenCalledWith({});
      expect(dashboardService.getBudgetComparison).toHaveBeenCalledWith({});
      expect(dashboardService.getGoalProgress).toHaveBeenCalledWith({});

      // Verify cache was used
      expect(mockDashboardCache.withCache).toHaveBeenCalledWith(
        'OVERVIEW',
        {},
        expect.any(Function)
      );
    });
  });

  describe('getDatabaseMetrics', () => {
    it('should return performance metrics', async () => {
      // Arrange - Add some performance logs to the service
      const service = dashboardService as any;
      service.performanceLogs = [
        { operation: 'getAccountBalances', duration: 150, timestamp: Date.now(), filters: {} },
        { operation: 'getNetWorth', duration: 2500, timestamp: Date.now(), filters: {} }, // Slow query
        { operation: 'getCreditCardUsage', duration: 80, timestamp: Date.now(), filters: {} }
      ];

      // Act
      const result = await dashboardService.getDatabaseMetrics();

      // Assert
      expect(result).toEqual({
        slowQueries: [
          expect.objectContaining({
            operation: 'getNetWorth',
            duration: 2500
          })
        ],
        averageQueryTime: 910.00, // (150 + 2500 + 80) / 3
        totalQueries: 3
      });
    });

    it('should handle empty performance logs', async () => {
      // Arrange - Clear performance logs
      const service = dashboardService as any;
      service.performanceLogs = [];

      // Act
      const result = await dashboardService.getDatabaseMetrics();

      // Assert
      expect(result).toEqual({
        slowQueries: [],
        averageQueryTime: 0,
        totalQueries: 0
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle cache failures gracefully', async () => {
      // Arrange
      mockDashboardCache.withCache.mockRejectedValue(new Error('Cache error'));
      mockPrisma.account.findMany.mockResolvedValue(mockAccounts);
      mockPrisma.transaction.findMany.mockResolvedValue([]);

      // Act & Assert - Should not throw, should fallback to direct execution
      await expect(dashboardService.getAccountBalances({})).rejects.toThrow('Cache error');
    });

    it('should handle invalid filter validation', async () => {
      // This test would be more relevant in integration tests
      // since filter validation happens in the middleware layer
      expect(true).toBe(true); // Placeholder
    });
  });
});
