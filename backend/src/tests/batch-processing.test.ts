import { FutureTransactionJobService } from '../services/future-transaction-job.service';
import { TransactionType } from '@prisma/client';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    transaction: {
      findMany: jest.fn(),
      update: jest.fn(),
      count: jest.fn()
    },
    account: {
      findUnique: jest.fn(),
      update: jest.fn()
    },
    $transaction: jest.fn()
  }
}));

describe('Batch Processing Performance Tests', () => {
  let futureTransactionJobService: FutureTransactionJobService;
  let mockPrisma: any;

  beforeEach(() => {
    futureTransactionJobService = new FutureTransactionJobService();
    mockPrisma = require('../lib/prisma').default;
    jest.clearAllMocks();
  });

  describe('Large Volume Processing', () => {
    it('should process 100 future transactions efficiently', async () => {
      // Arrange
      const batchSize = 100;
      const mockTransactions = Array.from({ length: batchSize }, (_, index) => ({
        id: `trans-${index + 1}`,
        description: `Future transaction ${index + 1}`,
        amount: 100.00 + index,
        type: TransactionType.EXPENSE,
        accountId: `acc-${(index % 10) + 1}`, // 10 different accounts
        isFuture: true,
        transactionDate: new Date(),
        account: {
          id: `acc-${(index % 10) + 1}`,
          currentBalance: 5000.00,
          currency: 'BRL'
        },
        destinationAccount: null,
        category: null,
        members: []
      }));

      mockPrisma.transaction.findMany.mockResolvedValue(mockTransactions);
      
      // Mock successful processing for all transactions
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          transaction: { update: mockPrisma.transaction.update },
          account: { 
            findUnique: mockPrisma.account.findUnique,
            update: mockPrisma.account.update 
          }
        });
      });

      mockPrisma.transaction.update.mockResolvedValue({});
      mockPrisma.account.findUnique.mockResolvedValue({ currentBalance: 5000.00 });
      mockPrisma.account.update.mockResolvedValue({});

      const startTime = Date.now();

      // Act
      const result = await futureTransactionJobService.execute();

      // Assert
      const executionTime = Date.now() - startTime;
      
      expect(result.processedTransactions).toBe(batchSize);
      expect(result.failedTransactions).toBe(0);
      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
      
      // Verify all transactions were processed
      expect(mockPrisma.transaction.update).toHaveBeenCalledTimes(batchSize);
      expect(mockPrisma.account.update).toHaveBeenCalledTimes(batchSize);
    });

    it('should handle partial failures in large batches gracefully', async () => {
      // Arrange
      const batchSize = 50;
      const failureRate = 0.1; // 10% failure rate
      
      const mockTransactions = Array.from({ length: batchSize }, (_, index) => ({
        id: `trans-${index + 1}`,
        description: `Future transaction ${index + 1}`,
        amount: 100.00,
        type: TransactionType.EXPENSE,
        accountId: 'acc-1',
        isFuture: true,
        transactionDate: new Date(),
        account: {
          id: 'acc-1',
          currentBalance: 5000.00
        }
      }));

      mockPrisma.transaction.findMany.mockResolvedValue(mockTransactions);
      
      // Mock failures for some transactions
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        const shouldFail = Math.random() < failureRate;
        if (shouldFail) {
          throw new Error('Simulated processing error');
        }
        return await callback({
          transaction: { update: mockPrisma.transaction.update },
          account: { 
            findUnique: mockPrisma.account.findUnique,
            update: mockPrisma.account.update 
          }
        });
      });

      mockPrisma.account.findUnique.mockResolvedValue({ currentBalance: 5000.00 });

      // Act
      const result = await futureTransactionJobService.execute();

      // Assert
      expect(result.processedTransactions + result.failedTransactions).toBe(batchSize);
      expect(result.failedTransactions).toBeGreaterThan(0);
      expect(result.failedTransactions).toBeLessThan(batchSize);
      expect(result.errors.length).toBe(result.failedTransactions);
    });

    it('should process transactions with different types efficiently', async () => {
      // Arrange
      const transactionTypes = [
        TransactionType.INCOME,
        TransactionType.EXPENSE,
        TransactionType.TRANSFER
      ];

      const mockTransactions = Array.from({ length: 60 }, (_, index) => {
        const type = transactionTypes[index % 3];
        return {
          id: `trans-${index + 1}`,
          description: `${type} transaction ${index + 1}`,
          amount: 100.00,
          type,
          accountId: 'acc-1',
          destinationAccountId: type === TransactionType.TRANSFER ? 'acc-2' : null,
          isFuture: true,
          transactionDate: new Date(),
          account: {
            id: 'acc-1',
            currentBalance: 10000.00
          },
          destinationAccount: type === TransactionType.TRANSFER ? {
            id: 'acc-2',
            currentBalance: 5000.00
          } : null
        };
      });

      mockPrisma.transaction.findMany.mockResolvedValue(mockTransactions);
      
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          transaction: { update: mockPrisma.transaction.update },
          account: { 
            findUnique: mockPrisma.account.findUnique,
            update: mockPrisma.account.update 
          }
        });
      });

      mockPrisma.account.findUnique.mockResolvedValue({ currentBalance: 10000.00 });

      const startTime = Date.now();

      // Act
      const result = await futureTransactionJobService.execute();

      // Assert
      const executionTime = Date.now() - startTime;
      
      expect(result.processedTransactions).toBe(60);
      expect(executionTime).toBeLessThan(3000); // Should handle mixed types efficiently
      
      // Transfers should update two accounts each
      const transferCount = 20; // 60 / 3 types
      const expectedAccountUpdates = 60 + transferCount; // Regular updates + extra for transfers
      expect(mockPrisma.account.update).toHaveBeenCalledTimes(expectedAccountUpdates);
    });
  });

  describe('Memory and Resource Management', () => {
    it('should handle large datasets without memory leaks', async () => {
      // Arrange
      const largeDataset = Array.from({ length: 1000 }, (_, index) => ({
        id: `trans-${index + 1}`,
        description: `Large dataset transaction ${index + 1}`,
        amount: Math.random() * 1000,
        type: TransactionType.EXPENSE,
        accountId: `acc-${(index % 50) + 1}`, // 50 different accounts
        isFuture: true,
        transactionDate: new Date(),
        account: {
          id: `acc-${(index % 50) + 1}`,
          currentBalance: 10000.00
        }
      }));

      // Process in chunks to simulate real-world pagination
      const chunkSize = 100;
      const chunks: any[] = [];
      for (let i = 0; i < largeDataset.length; i += chunkSize) {
        chunks.push(largeDataset.slice(i, i + chunkSize));
      }

      let totalProcessed = 0;
      const startMemory = process.memoryUsage().heapUsed;

      // Act
      for (const chunk of chunks) {
        mockPrisma.transaction.findMany.mockResolvedValue(chunk);
        
        mockPrisma.$transaction.mockImplementation(async (callback: any) => {
          return await callback({
            transaction: { update: jest.fn() },
            account: { 
              findUnique: jest.fn().mockResolvedValue({ currentBalance: 10000.00 }),
              update: jest.fn()
            }
          });
        });

        const chunkService = new FutureTransactionJobService();
        const result = await chunkService.execute();
        totalProcessed += result.processedTransactions;
      }

      // Assert
      const endMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = endMemory - startMemory;
      
      expect(totalProcessed).toBe(largeDataset.length);
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // Less than 100MB increase
    });

    it('should handle concurrent processing requests', async () => {
      // Arrange
      const concurrentRequests = 5;
      const transactionsPerRequest = 20;

      const createMockTransactions = (offset: number) => 
        Array.from({ length: transactionsPerRequest }, (_, index) => ({
          id: `trans-${offset + index + 1}`,
          description: `Concurrent transaction ${offset + index + 1}`,
          amount: 100.00,
          type: TransactionType.EXPENSE,
          accountId: 'acc-1',
          isFuture: true,
          transactionDate: new Date(),
          account: {
            id: 'acc-1',
            currentBalance: 5000.00
          }
        }));

      // Act
      const promises = Array.from({ length: concurrentRequests }, async (_, index) => {
        const service = new FutureTransactionJobService();
        const mockTransactions = createMockTransactions(index * transactionsPerRequest);
        
        mockPrisma.transaction.findMany.mockResolvedValue(mockTransactions);
        mockPrisma.$transaction.mockImplementation(async (callback: any) => {
          // Add small delay to simulate real processing
          await new Promise(resolve => setTimeout(resolve, 10));
          return await callback({
            transaction: { update: jest.fn() },
            account: { 
              findUnique: jest.fn().mockResolvedValue({ currentBalance: 5000.00 }),
              update: jest.fn()
            }
          });
        });

        return service.execute();
      });

      const startTime = Date.now();
      const results = await Promise.all(promises);
      const executionTime = Date.now() - startTime;

      // Assert
      const totalProcessed = results.reduce((sum, result) => sum + result.processedTransactions, 0);
      
      expect(totalProcessed).toBe(concurrentRequests * transactionsPerRequest);
      expect(executionTime).toBeLessThan(2000); // Should handle concurrency efficiently
      
      // All requests should succeed
      results.forEach(result => {
        expect(result.processedTransactions).toBe(transactionsPerRequest);
        expect(result.failedTransactions).toBe(0);
      });
    });
  });

  describe('Performance Benchmarks', () => {
    it('should meet performance targets for typical workloads', async () => {
      // Arrange - Typical daily workload
      const typicalWorkload = {
        income: 50,
        expenses: 200,
        transfers: 30
      };

      const mockTransactions = [
        ...Array.from({ length: typicalWorkload.income }, (_, i) => ({
          id: `income-${i}`,
          type: TransactionType.INCOME,
          amount: 1000 + i,
          accountId: 'acc-1',
          account: { currentBalance: 5000 }
        })),
        ...Array.from({ length: typicalWorkload.expenses }, (_, i) => ({
          id: `expense-${i}`,
          type: TransactionType.EXPENSE,
          amount: 50 + i,
          accountId: 'acc-1',
          account: { currentBalance: 5000 }
        })),
        ...Array.from({ length: typicalWorkload.transfers }, (_, i) => ({
          id: `transfer-${i}`,
          type: TransactionType.TRANSFER,
          amount: 200 + i,
          accountId: 'acc-1',
          destinationAccountId: 'acc-2',
          account: { currentBalance: 5000 },
          destinationAccount: { currentBalance: 3000 }
        }))
      ];

      mockPrisma.transaction.findMany.mockResolvedValue(mockTransactions);
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        return await callback({
          transaction: { update: jest.fn() },
          account: { 
            findUnique: jest.fn().mockResolvedValue({ currentBalance: 5000 }),
            update: jest.fn()
          }
        });
      });

      const startTime = Date.now();

      // Act
      const result = await futureTransactionJobService.execute();

      // Assert
      const executionTime = Date.now() - startTime;
      const totalTransactions = typicalWorkload.income + typicalWorkload.expenses + typicalWorkload.transfers;
      const transactionsPerSecond = totalTransactions / (executionTime / 1000);

      expect(result.processedTransactions).toBe(totalTransactions);
      expect(transactionsPerSecond).toBeGreaterThan(50); // At least 50 transactions per second
      expect(executionTime).toBeLessThan(10000); // Complete within 10 seconds
    });

    it('should scale linearly with transaction count', async () => {
      // Arrange - Test different batch sizes
      const batchSizes = [10, 50, 100];
      const results: any[] = [];

      for (const batchSize of batchSizes) {
        const mockTransactions = Array.from({ length: batchSize }, (_, index) => ({
          id: `trans-${index}`,
          type: TransactionType.EXPENSE,
          amount: 100,
          accountId: 'acc-1',
          account: { currentBalance: 10000 }
        }));

        mockPrisma.transaction.findMany.mockResolvedValue(mockTransactions);
        mockPrisma.$transaction.mockImplementation(async (callback: any) => {
          return await callback({
            transaction: { update: jest.fn() },
            account: { 
              findUnique: jest.fn().mockResolvedValue({ currentBalance: 10000 }),
              update: jest.fn()
            }
          });
        });

        const startTime = Date.now();
        const service = new FutureTransactionJobService();
        await service.execute();
        const executionTime = Date.now() - startTime;

        results.push({
          batchSize,
          executionTime,
          transactionsPerMs: batchSize / executionTime
        });
      }

      // Assert - Performance should scale reasonably
      const efficiency = results.map(r => r.transactionsPerMs);
      
      // Efficiency shouldn't degrade significantly with larger batches
      const efficiencyRatio = efficiency[2] / efficiency[0]; // 100 vs 10
      expect(efficiencyRatio).toBeGreaterThan(0.3); // At least 30% efficiency maintained
    });
  });
});
