import { TransactionService } from '../services/transaction.service';
import { TransactionType } from '@prisma/client';
import { 
  createFutureTransactionSchema,
  updateFutureTransactionSchema,
  isValidFutureDate 
} from '../schemas/future-transaction.schemas';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    transaction: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    },
    account: {
      findUnique: jest.fn(),
      update: jest.fn()
    },
    category: {
      findUnique: jest.fn()
    },
    $transaction: jest.fn()
  }
}));

describe('Transaction Validation Tests', () => {
  let transactionService: TransactionService;
  let mockPrisma: any;

  beforeEach(() => {
    transactionService = new TransactionService();
    mockPrisma = require('../lib/prisma').default;
    jest.clearAllMocks();
  });

  describe('Future Transaction Schema Validation', () => {
    it('should validate valid future transaction data', () => {
      // Arrange
      const validData = {
        description: 'Future payment',
        amount: 100.50,
        transactionDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
        type: TransactionType.EXPENSE,
        accountId: 'clp123456789012345678901',
        categoryId: 'clp123456789012345678902',
        isFuture: true as const
      };

      // Act & Assert
      expect(() => createFutureTransactionSchema.parse(validData)).not.toThrow();
    });

    it('should reject future transaction with past date', () => {
      // Arrange
      const invalidData = {
        description: 'Past transaction',
        amount: 100.50,
        transactionDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Yesterday
        type: TransactionType.EXPENSE,
        accountId: 'clp123456789012345678901',
        isFuture: true as const
      };

      // Act & Assert
      expect(() => createFutureTransactionSchema.parse(invalidData)).toThrow();
    });

    it('should reject transfer without destination account', () => {
      // Arrange
      const invalidTransfer = {
        description: 'Invalid transfer',
        amount: 100.50,
        transactionDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        type: TransactionType.TRANSFER,
        accountId: 'clp123456789012345678901',
        // Missing destinationAccountId
        isFuture: true as const
      };

      // Act & Assert
      expect(() => createFutureTransactionSchema.parse(invalidTransfer)).toThrow();
    });

    it('should validate installment configuration', () => {
      // Arrange
      const invalidInstallments = {
        description: 'Invalid installments',
        amount: 100.50,
        transactionDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        type: TransactionType.EXPENSE,
        accountId: 'clp123456789012345678901',
        installmentNumber: 5,
        totalInstallments: 3, // Invalid: installment number > total
        isFuture: true as const
      };

      // Act & Assert
      expect(() => createFutureTransactionSchema.parse(invalidInstallments)).toThrow();
    });

    it('should validate currency codes', () => {
      // Arrange
      const invalidCurrency = {
        description: 'Invalid currency',
        amount: 100.50,
        transactionDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        type: TransactionType.EXPENSE,
        accountId: 'clp123456789012345678901',
        sourceCurrency: 'INVALID', // Invalid currency code
        isFuture: true as const
      };

      // Act & Assert
      expect(() => createFutureTransactionSchema.parse(invalidCurrency)).toThrow();
    });

    it('should validate CUID format for IDs', () => {
      // Arrange
      const invalidId = {
        description: 'Invalid ID',
        amount: 100.50,
        transactionDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        type: TransactionType.EXPENSE,
        accountId: 'invalid-id-format',
        isFuture: true as const
      };

      // Act & Assert
      expect(() => createFutureTransactionSchema.parse(invalidId)).toThrow();
    });
  });

  describe('Business Logic Validation', () => {
    it('should validate account exists and is active', async () => {
      // Arrange
      const transactionData = {
        description: 'Test transaction',
        amount: 100.00,
        type: TransactionType.EXPENSE,
        accountId: 'non-existent-account',
        transactionDate: new Date(),
        isFuture: false,
        familyMemberIds: []
      };

      mockPrisma.account.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(
        transactionService.create(transactionData)
      ).rejects.toThrow('Account not found');
    });

    it('should validate sufficient balance for expenses', async () => {
      // Arrange
      const account = {
        id: 'acc-1',
        currentBalance: 50.00,
        currency: 'BRL'
      };

      const transactionData = {
        description: 'Expensive transaction',
        amount: 100.00, // More than available balance
        type: TransactionType.EXPENSE,
        accountId: 'acc-1',
        transactionDate: new Date()
      };

      mockPrisma.account.findUnique.mockResolvedValue(account);

      // Act & Assert
      await expect(
        transactionService.create(transactionData)
      ).rejects.toThrow('Insufficient balance');
    });

    it('should validate category exists if provided', async () => {
      // Arrange
      const account = {
        id: 'acc-1',
        currentBalance: 1000.00,
        currency: 'BRL'
      };

      const transactionData = {
        description: 'Transaction with invalid category',
        amount: 100.00,
        type: TransactionType.EXPENSE,
        accountId: 'acc-1',
        categoryId: 'non-existent-category',
        transactionDate: new Date(),
        isFuture: false,
        familyMemberIds: []
      };

      mockPrisma.account.findUnique.mockResolvedValue(account);
      mockPrisma.category.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(
        transactionService.create(transactionData)
      ).rejects.toThrow('Category not found');
    });

    it('should validate transfer between different accounts', async () => {
      // Arrange
      const account = {
        id: 'acc-1',
        currentBalance: 1000.00,
        currency: 'BRL'
      };

      const transferData = {
        description: 'Self transfer',
        amount: 100.00,
        type: TransactionType.TRANSFER,
        accountId: 'acc-1',
        destinationAccountId: 'acc-1', // Same account
        transactionDate: new Date(),
        isFuture: false,
        familyMemberIds: []
      };

      mockPrisma.account.findUnique.mockResolvedValue(account);

      // Act & Assert
      await expect(
        transactionService.create(transferData)
      ).rejects.toThrow('Cannot transfer to the same account');
    });

    it('should validate positive amounts', async () => {
      // Arrange
      const account = {
        id: 'acc-1',
        currentBalance: 1000.00,
        currency: 'BRL'
      };

      const transactionData = {
        description: 'Negative amount',
        amount: -100.00, // Negative amount
        type: TransactionType.EXPENSE,
        accountId: 'acc-1',
        transactionDate: new Date(),
        isFuture: false,
        familyMemberIds: []
      };

      mockPrisma.account.findUnique.mockResolvedValue(account);

      // Act & Assert
      await expect(
        transactionService.create(transactionData)
      ).rejects.toThrow('Amount must be positive');
    });

    it('should validate exchange rate for different currencies', async () => {
      // Arrange
      const sourceAccount = {
        id: 'acc-1',
        currency: 'USD',
        currentBalance: 1000.00
      };

      const destinationAccount = {
        id: 'acc-2',
        currency: 'BRL',
        currentBalance: 1000.00
      };

      const transferData = {
        description: 'Currency transfer without rate',
        amount: 100.00,
        type: TransactionType.TRANSFER,
        accountId: 'acc-1',
        destinationAccountId: 'acc-2',
        // Missing exchange rate for different currencies
        transactionDate: new Date(),
        isFuture: false,
        familyMemberIds: []
      };

      mockPrisma.account.findUnique
        .mockResolvedValueOnce(sourceAccount)
        .mockResolvedValueOnce(destinationAccount);

      // Act & Assert
      await expect(
        transactionService.create(transferData)
      ).rejects.toThrow('Exchange rate required for different currencies');
    });
  });

  describe('Date Validation Helpers', () => {
    it('should validate future dates correctly', () => {
      // Arrange
      const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow
      const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Yesterday
      const currentDate = new Date(); // Now

      // Act & Assert
      expect(isValidFutureDate(futureDate)).toBe(true);
      expect(isValidFutureDate(pastDate)).toBe(false);
      expect(isValidFutureDate(currentDate)).toBe(false);
    });

    it('should reject dates too far in the future', () => {
      // Arrange
      const tooFarFuture = new Date();
      tooFarFuture.setFullYear(tooFarFuture.getFullYear() + 10); // 10 years from now

      // Act & Assert
      expect(isValidFutureDate(tooFarFuture)).toBe(false);
    });

    it('should handle invalid date strings', () => {
      // Act & Assert
      expect(isValidFutureDate('invalid-date')).toBe(false);
      expect(isValidFutureDate('')).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      // Arrange
      const transactionData = {
        description: 'Test transaction',
        amount: 100.00,
        type: TransactionType.EXPENSE,
        accountId: 'acc-1',
        transactionDate: new Date(),
        isFuture: false,
        familyMemberIds: []
      };

      mockPrisma.account.findUnique.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert
      await expect(
        transactionService.create(transactionData)
      ).rejects.toThrow('Database connection failed');
    });

    it('should handle transaction rollback on partial failure', async () => {
      // Arrange
      const transferData = {
        description: 'Transfer with rollback',
        amount: 100.00,
        type: TransactionType.TRANSFER,
        accountId: 'acc-1',
        destinationAccountId: 'acc-2',
        transactionDate: new Date(),
        isFuture: false,
        familyMemberIds: []
      };

      const sourceAccount = {
        id: 'acc-1',
        currentBalance: 1000.00,
        currency: 'BRL'
      };

      const destinationAccount = {
        id: 'acc-2',
        currentBalance: 500.00,
        currency: 'BRL'
      };

      mockPrisma.account.findUnique
        .mockResolvedValueOnce(sourceAccount)
        .mockResolvedValueOnce(destinationAccount);

      // Simulate transaction failure
      mockPrisma.$transaction.mockRejectedValue(new Error('Transaction failed'));

      // Act & Assert
      await expect(
        transactionService.create(transferData)
      ).rejects.toThrow('Transaction failed');

      // Verify no partial updates occurred
      expect(mockPrisma.account.update).not.toHaveBeenCalled();
    });

    it('should validate concurrent modification protection', async () => {
      // Arrange
      const transactionData = {
        description: 'Concurrent update test',
        amount: 100.00,
        type: TransactionType.EXPENSE,
        accountId: 'acc-1',
        transactionDate: new Date(),
        isFuture: false,
        familyMemberIds: []
      };

      const account = {
        id: 'acc-1',
        currentBalance: 1000.00,
        currency: 'BRL',
        version: 1 // Optimistic locking version
      };

      mockPrisma.account.findUnique.mockResolvedValue(account);
      
      // Simulate concurrent modification error
      mockPrisma.$transaction.mockRejectedValue(
        new Error('Record was modified by another transaction')
      );

      // Act & Assert
      await expect(
        transactionService.create(transactionData)
      ).rejects.toThrow('Record was modified by another transaction');
    });
  });
});
