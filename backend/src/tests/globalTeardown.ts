import { RedisMemoryServer } from 'redis-memory-server';

export default async function globalTeardown() {
  console.log('🧹 Cleaning up test environment...');
  
  try {
    // Get Redis server instance from global
    const redisServer: RedisMemoryServer = (global as any).__REDIS_SERVER__;
    
    if (redisServer) {
      await redisServer.stop();
      console.log('✅ Redis Memory Server stopped');
    }
    
  } catch (error) {
    console.error('❌ Error during test cleanup:', error);
  }
  
  console.log('✅ Test environment cleanup complete');
}
