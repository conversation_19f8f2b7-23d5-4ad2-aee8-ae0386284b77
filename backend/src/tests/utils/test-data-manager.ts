/**
 * Test Data Manager
 * Central system for managing test data creation, seeding, and cleanup
 */

import { PrismaClient } from '@prisma/client';
import { UserSeeder } from '../seeders/user.seeder';
import { AccountSeeder } from '../seeders/account.seeder';
import { CategorySeeder } from '../seeders/category.seeder';
import { TransactionSeeder } from '../seeders/transaction.seeder';
import { SeederRegistry, DatabaseUtils } from '../seeders/base.seeder';
import { FixtureManager, testScenarios } from '../fixtures';

export interface TestDataOptions {
  users?: number;
  accountsPerUser?: number;
  transactionsPerAccount?: number;
  clean?: boolean;
  useFixtures?: boolean;
  scenario?: string;
}

export interface TestDataResult {
  users: any[];
  accounts: any[];
  categories: any[];
  transactions: any[];
  stats: {
    totalUsers: number;
    totalAccounts: number;
    totalCategories: number;
    totalTransactions: number;
    executionTime: number;
  };
}

export class TestDataManager {
  private prisma: PrismaClient;
  private userSeeder: UserSeeder;
  private accountSeeder: AccountSeeder;
  private categorySeeder: CategorySeeder;
  private transactionSeeder: TransactionSeeder;
  private dbUtils: DatabaseUtils;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
    this.userSeeder = new UserSeeder(this.prisma);
    this.accountSeeder = new AccountSeeder(this.prisma);
    this.categorySeeder = new CategorySeeder(this.prisma);
    this.transactionSeeder = new TransactionSeeder(this.prisma);
    this.dbUtils = new DatabaseUtils(this.prisma);

    this.registerSeeders();
  }

  /**
   * Register all seeders with the registry
   */
  private registerSeeders(): void {
    SeederRegistry.clear();
    SeederRegistry.register('users', this.userSeeder, 0);
    SeederRegistry.register('categories', this.categorySeeder, 1);
    SeederRegistry.register('accounts', this.accountSeeder, 2);
    SeederRegistry.register('transactions', this.transactionSeeder, 3);
  }

  /**
   * Create complete test dataset
   */
  async createTestData(options: TestDataOptions = {}): Promise<TestDataResult> {
    const startTime = Date.now();
    
    const {
      users = 5,
      accountsPerUser = 3,
      transactionsPerAccount = 20,
      clean = true,
      useFixtures = false,
      scenario,
    } = options;

    console.log('🌱 Creating test data...');
    console.log(`Options:`, { users, accountsPerUser, transactionsPerAccount, clean, useFixtures, scenario });

    try {
      // Verify database connection
      const isConnected = await this.dbUtils.verifyConnection();
      if (!isConnected) {
        throw new Error('Database connection failed');
      }

      // Clean existing test data if requested
      if (clean) {
        await this.cleanAllTestData();
      }

      let result: TestDataResult;

      if (useFixtures && scenario) {
        result = await this.createFromScenario(scenario);
      } else if (useFixtures) {
        result = await this.createFromFixtures();
      } else {
        result = await this.createFromSeeders({
          users,
          accountsPerUser,
          transactionsPerAccount,
        });
      }

      const executionTime = Date.now() - startTime;
      result.stats.executionTime = executionTime;

      console.log('✅ Test data creation completed');
      console.log(`📊 Stats:`, result.stats);
      console.log(`⏱️ Execution time: ${executionTime}ms`);

      return result;

    } catch (error) {
      console.error('❌ Test data creation failed:', error);
      throw error;
    }
  }

  /**
   * Create test data using seeders
   */
  private async createFromSeeders(options: {
    users: number;
    accountsPerUser: number;
    transactionsPerAccount: number;
  }): Promise<TestDataResult> {
    console.log('🔧 Creating test data using seeders...');

    // Create users
    const users = await this.userSeeder.run({ count: options.users });
    
    // Create categories for all users
    const categories = await this.categorySeeder.run();
    
    // Create accounts for all users
    const accounts = await this.accountSeeder.run({ count: options.accountsPerUser });
    
    // Create transactions
    const transactions = await this.transactionSeeder.run({ 
      count: options.transactionsPerAccount * accounts.length 
    });

    return {
      users,
      accounts,
      categories,
      transactions,
      stats: {
        totalUsers: users.length,
        totalAccounts: accounts.length,
        totalCategories: categories.length,
        totalTransactions: transactions.length,
        executionTime: 0, // Will be set by caller
      },
    };
  }

  /**
   * Create test data from fixtures
   */
  private async createFromFixtures(): Promise<TestDataResult> {
    console.log('📋 Creating test data using fixtures...');

    const fixtures = FixtureManager.loadScenario('userWithTransactionHistory');
    
    // Create data from fixtures
    const users = [];
    const accounts = [];
    const categories = [];
    const transactions = [];

    for (const fixture of fixtures) {
      if (fixture.email) {
        // User fixture
        const user = await this.prisma.user.create({ data: fixture });
        users.push(user);
      } else if (fixture.type && ['CHECKING', 'SAVINGS', 'CREDIT_CARD'].includes(fixture.type)) {
        // Account fixture
        const account = await this.prisma.account.create({ data: fixture });
        accounts.push(account);
      } else if (fixture.type && ['INCOME', 'EXPENSE'].includes(fixture.type)) {
        // Category fixture
        const category = await this.prisma.category.create({ data: fixture });
        categories.push(category);
      } else if (fixture.amount !== undefined) {
        // Transaction fixture
        const transaction = await this.prisma.transaction.create({ data: fixture });
        transactions.push(transaction);
      }
    }

    return {
      users,
      accounts,
      categories,
      transactions,
      stats: {
        totalUsers: users.length,
        totalAccounts: accounts.length,
        totalCategories: categories.length,
        totalTransactions: transactions.length,
        executionTime: 0,
      },
    };
  }

  /**
   * Create test data from specific scenario
   */
  private async createFromScenario(scenarioName: string): Promise<TestDataResult> {
    console.log(`🎭 Creating test data using scenario: ${scenarioName}`);

    if (!testScenarios[scenarioName]) {
      throw new Error(`Scenario '${scenarioName}' not found`);
    }

    return this.createFromFixtures();
  }

  /**
   * Clean all test data
   */
  async cleanAllTestData(): Promise<void> {
    console.log('🧹 Cleaning all test data...');

    try {
      await this.dbUtils.cleanTestData();
      await SeederRegistry.cleanupAll();
      FixtureManager.clear();
      
      console.log('✅ Test data cleanup completed');
    } catch (error) {
      console.error('❌ Test data cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<{
    users: number;
    accounts: number;
    transactions: number;
    categories: number;
  }> {
    return this.dbUtils.getStats();
  }

  /**
   * Create minimal test dataset for quick tests
   */
  async createMinimalTestData(): Promise<TestDataResult> {
    console.log('⚡ Creating minimal test data...');

    return this.createTestData({
      users: 1,
      accountsPerUser: 2,
      transactionsPerAccount: 5,
      clean: true,
      useFixtures: false,
    });
  }

  /**
   * Create comprehensive test dataset
   */
  async createComprehensiveTestData(): Promise<TestDataResult> {
    console.log('🏗️ Creating comprehensive test data...');

    return this.createTestData({
      users: 10,
      accountsPerUser: 5,
      transactionsPerAccount: 50,
      clean: true,
      useFixtures: false,
    });
  }

  /**
   * Create test data for specific user
   */
  async createUserTestData(userId: string): Promise<{
    accounts: any[];
    categories: any[];
    transactions: any[];
  }> {
    console.log(`👤 Creating test data for user: ${userId}`);

    try {
      // Create categories for user
      const categories = await this.categorySeeder.createCategoriesForUser(userId);
      
      // Create accounts for user
      const accounts = await this.accountSeeder.createAccountsForUser(userId, 3);
      
      // Create transactions for accounts
      const transactions = [];
      for (const account of accounts) {
        const categoryIds = categories.map(c => c.id);
        const accountTransactions = await this.transactionSeeder.createTransactionsForAccount(
          account.id,
          userId,
          categoryIds,
          20
        );
        transactions.push(...accountTransactions);
      }

      console.log(`✅ Created test data for user ${userId}`);
      return { accounts, categories, transactions };

    } catch (error) {
      console.error(`❌ Failed to create test data for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Reset database to clean state
   */
  async resetDatabase(): Promise<void> {
    console.log('🔄 Resetting database...');

    try {
      await this.cleanAllTestData();
      await this.dbUtils.resetSequences();
      
      console.log('✅ Database reset completed');
    } catch (error) {
      console.error('❌ Database reset failed:', error);
      throw error;
    }
  }

  /**
   * Disconnect from database
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }

  /**
   * Get available test scenarios
   */
  getAvailableScenarios(): string[] {
    return Object.keys(testScenarios);
  }

  /**
   * Get available fixtures
   */
  getAvailableFixtures() {
    return FixtureManager.getAvailableFixtures();
  }
}

// Export singleton instance for convenience
export const testDataManager = new TestDataManager();
