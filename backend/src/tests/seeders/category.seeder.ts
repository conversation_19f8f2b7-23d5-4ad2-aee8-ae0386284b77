/**
 * Category Seeder for Test Database Population
 */

import { BaseSeeder, SeederOptions } from './base.seeder';
import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';

export interface TestCategory {
  id: string;
  name: string;
  type: 'INCOME' | 'EXPENSE';
  color: string;
  icon: string;
  description?: string;
  userId: string;
  parentId?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class CategorySeeder extends BaseSeeder {
  private createdCategoryIds: string[] = [];

  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  getName(): string {
    return 'CategorySeeder';
  }

  async run(options: SeederOptions = {}): Promise<TestCategory[]> {
    this.ensureTestEnvironment();
    
    const { clean = false } = options;
    
    if (clean) {
      await this.cleanup();
    }

    // Get existing users to create categories for
    const users = await this.prisma.user.findMany({
      where: {
        OR: [
          { email: { endsWith: '@test.com' } },
          { email: { contains: 'test_' } },
        ],
      },
      take: 10,
    });

    if (users.length === 0) {
      this.log('No test users found. Please run UserSeeder first.');
      return [];
    }

    this.log(`Creating categories for ${users.length} users...`);

    try {
      const categories: TestCategory[] = [];

      for (const user of users) {
        // Create standard expense categories
        const expenseCategories = this.getStandardExpenseCategories(user.id);
        
        for (const categoryData of expenseCategories) {
          const createdCategory = await this.prisma.category.create({
            data: categoryData,
          });

          categories.push(createdCategory as TestCategory);
          this.createdCategoryIds.push(createdCategory.id);
        }

        // Create standard income categories
        const incomeCategories = this.getStandardIncomeCategories(user.id);
        
        for (const categoryData of incomeCategories) {
          const createdCategory = await this.prisma.category.create({
            data: categoryData,
          });

          categories.push(createdCategory as TestCategory);
          this.createdCategoryIds.push(createdCategory.id);
        }

        // Create some subcategories
        const parentCategories = categories.filter(c => c.userId === user.id).slice(0, 3);
        
        for (const parent of parentCategories) {
          const subcategories = this.createSubcategories(parent.id, user.id, 2);
          
          for (const subcategoryData of subcategories) {
            const createdSubcategory = await this.prisma.category.create({
              data: subcategoryData,
            });

            categories.push(createdSubcategory as TestCategory);
            this.createdCategoryIds.push(createdSubcategory.id);
          }
        }
      }

      this.log(`Successfully created ${categories.length} categories`);
      return categories;

    } catch (error) {
      this.logError('Failed to create categories', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    this.log('Cleaning up test categories...');

    try {
      if (this.createdCategoryIds.length > 0) {
        await this.prisma.category.deleteMany({
          where: {
            id: {
              in: this.createdCategoryIds,
            },
          },
        });
        this.createdCategoryIds = [];
      }

      // Also clean up categories with test patterns
      await this.prisma.category.deleteMany({
        where: {
          OR: [
            { name: { contains: 'test_' } },
            { description: { contains: 'test_data' } },
            { user: { email: { endsWith: '@test.com' } } },
          ],
        },
      });

      this.log('Category cleanup completed');
    } catch (error) {
      this.logError('Failed to cleanup categories', error);
      throw error;
    }
  }

  /**
   * Get standard expense categories
   */
  private getStandardExpenseCategories(userId: string): Partial<TestCategory>[] {
    const expenseCategories = [
      { name: 'Alimentação', icon: 'utensils', color: '#FF6B6B' },
      { name: 'Transporte', icon: 'car', color: '#4ECDC4' },
      { name: 'Moradia', icon: 'home', color: '#45B7D1' },
      { name: 'Saúde', icon: 'heart', color: '#96CEB4' },
      { name: 'Educação', icon: 'book', color: '#FFEAA7' },
      { name: 'Lazer', icon: 'gamepad-2', color: '#DDA0DD' },
      { name: 'Roupas', icon: 'shirt', color: '#98D8C8' },
      { name: 'Tecnologia', icon: 'smartphone', color: '#6C5CE7' },
      { name: 'Serviços', icon: 'tool', color: '#A29BFE' },
      { name: 'Outros', icon: 'more-horizontal', color: '#636E72' },
    ];

    return expenseCategories.map(cat => ({
      id: faker.string.uuid(),
      name: cat.name,
      type: 'EXPENSE' as const,
      color: cat.color,
      icon: cat.icon,
      description: `Categoria de ${cat.name.toLowerCase()}`,
      userId,
      isActive: true,
      createdAt: faker.date.past(),
      updatedAt: new Date(),
    }));
  }

  /**
   * Get standard income categories
   */
  private getStandardIncomeCategories(userId: string): Partial<TestCategory>[] {
    const incomeCategories = [
      { name: 'Salário', icon: 'briefcase', color: '#00B894' },
      { name: 'Freelance', icon: 'user', color: '#00CEC9' },
      { name: 'Investimentos', icon: 'trending-up', color: '#6C5CE7' },
      { name: 'Aluguel', icon: 'home', color: '#FDCB6E' },
      { name: 'Vendas', icon: 'shopping-bag', color: '#E17055' },
      { name: 'Prêmios', icon: 'award', color: '#F39C12' },
      { name: 'Outros', icon: 'plus', color: '#636E72' },
    ];

    return incomeCategories.map(cat => ({
      id: faker.string.uuid(),
      name: cat.name,
      type: 'INCOME' as const,
      color: cat.color,
      icon: cat.icon,
      description: `Categoria de ${cat.name.toLowerCase()}`,
      userId,
      isActive: true,
      createdAt: faker.date.past(),
      updatedAt: new Date(),
    }));
  }

  /**
   * Create subcategories for a parent category
   */
  private createSubcategories(parentId: string, userId: string, count: number): Partial<TestCategory>[] {
    const subcategories: Partial<TestCategory>[] = [];

    for (let i = 0; i < count; i++) {
      subcategories.push({
        id: faker.string.uuid(),
        name: `Subcategoria ${i + 1}`,
        type: faker.helpers.arrayElement(['INCOME', 'EXPENSE']),
        color: faker.internet.color(),
        icon: 'tag',
        description: `Subcategoria de teste ${i + 1}`,
        userId,
        parentId,
        isActive: true,
        createdAt: faker.date.past(),
        updatedAt: new Date(),
      });
    }

    return subcategories;
  }

  /**
   * Create categories for specific user
   */
  async createCategoriesForUser(userId: string): Promise<TestCategory[]> {
    this.ensureTestEnvironment();

    try {
      const categories: TestCategory[] = [];

      // Create expense categories
      const expenseCategories = this.getStandardExpenseCategories(userId);
      
      for (const categoryData of expenseCategories) {
        const createdCategory = await this.prisma.category.create({
          data: categoryData,
        });

        categories.push(createdCategory as TestCategory);
        this.createdCategoryIds.push(createdCategory.id);
      }

      // Create income categories
      const incomeCategories = this.getStandardIncomeCategories(userId);
      
      for (const categoryData of incomeCategories) {
        const createdCategory = await this.prisma.category.create({
          data: categoryData,
        });

        categories.push(createdCategory as TestCategory);
        this.createdCategoryIds.push(createdCategory.id);
      }

      this.log(`Created ${categories.length} categories for user ${userId}`);
      return categories;

    } catch (error) {
      this.logError('Failed to create categories for user', error);
      throw error;
    }
  }

  /**
   * Get created category IDs
   */
  getCreatedCategoryIds(): string[] {
    return [...this.createdCategoryIds];
  }

  /**
   * Get categories by user ID
   */
  async getCategoriesByUser(userId: string): Promise<TestCategory[]> {
    try {
      const categories = await this.prisma.category.findMany({
        where: { userId },
      });

      return categories as TestCategory[];
    } catch (error) {
      this.logError('Failed to get categories by user', error);
      throw error;
    }
  }

  /**
   * Get categories by type
   */
  async getCategoriesByType(userId: string, type: 'INCOME' | 'EXPENSE'): Promise<TestCategory[]> {
    try {
      const categories = await this.prisma.category.findMany({
        where: { 
          userId,
          type,
        },
      });

      return categories as TestCategory[];
    } catch (error) {
      this.logError('Failed to get categories by type', error);
      throw error;
    }
  }
}
