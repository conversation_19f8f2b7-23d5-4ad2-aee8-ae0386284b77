/**
 * Base Seeder for Test Database Population
 */

import { PrismaClient } from '@prisma/client';

export interface SeederOptions {
  clean?: boolean;
  count?: number;
  relations?: boolean;
}

export abstract class BaseSeeder {
  protected prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Run the seeder
   */
  abstract run(options?: SeederOptions): Promise<any>;

  /**
   * Clean up data created by this seeder
   */
  abstract cleanup(): Promise<void>;

  /**
   * Get seeder name for logging
   */
  abstract getName(): string;

  /**
   * Log seeder activity
   */
  protected log(message: string): void {
    console.log(`[${this.getName()}] ${message}`);
  }

  /**
   * Log error
   */
  protected logError(message: string, error?: any): void {
    console.error(`[${this.getName()}] ERROR: ${message}`, error);
  }

  /**
   * Generate unique identifier for test data
   */
  protected generateTestId(prefix: string = 'test'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if running in test environment
   */
  protected isTestEnvironment(): boolean {
    return process.env.NODE_ENV === 'test';
  }

  /**
   * Ensure we're in test environment before running
   */
  protected ensureTestEnvironment(): void {
    if (!this.isTestEnvironment()) {
      throw new Error('Seeders can only be run in test environment');
    }
  }
}

/**
 * Seeder Registry for managing all seeders
 */
export class SeederRegistry {
  private static seeders: Map<string, BaseSeeder> = new Map();
  private static executionOrder: string[] = [];

  /**
   * Register a seeder
   */
  static register(name: string, seeder: BaseSeeder, order: number = 0): void {
    this.seeders.set(name, seeder);
    
    // Insert in execution order
    this.executionOrder.splice(order, 0, name);
  }

  /**
   * Get a seeder by name
   */
  static get(name: string): BaseSeeder {
    const seeder = this.seeders.get(name);
    if (!seeder) {
      throw new Error(`Seeder '${name}' not found`);
    }
    return seeder;
  }

  /**
   * Run a specific seeder
   */
  static async run(name: string, options?: SeederOptions): Promise<any> {
    const seeder = this.get(name);
    console.log(`🌱 Running seeder: ${name}`);
    
    try {
      const result = await seeder.run(options);
      console.log(`✅ Seeder completed: ${name}`);
      return result;
    } catch (error) {
      console.error(`❌ Seeder failed: ${name}`, error);
      throw error;
    }
  }

  /**
   * Run all seeders in order
   */
  static async runAll(options?: SeederOptions): Promise<void> {
    console.log('🌱 Running all seeders...');
    
    for (const name of this.executionOrder) {
      await this.run(name, options);
    }
    
    console.log('✅ All seeders completed');
  }

  /**
   * Clean up a specific seeder
   */
  static async cleanup(name: string): Promise<void> {
    const seeder = this.get(name);
    console.log(`🧹 Cleaning up seeder: ${name}`);
    
    try {
      await seeder.cleanup();
      console.log(`✅ Cleanup completed: ${name}`);
    } catch (error) {
      console.error(`❌ Cleanup failed: ${name}`, error);
      throw error;
    }
  }

  /**
   * Clean up all seeders in reverse order
   */
  static async cleanupAll(): Promise<void> {
    console.log('🧹 Cleaning up all seeders...');
    
    const reverseOrder = [...this.executionOrder].reverse();
    
    for (const name of reverseOrder) {
      await this.cleanup(name);
    }
    
    console.log('✅ All cleanup completed');
  }

  /**
   * Get list of registered seeders
   */
  static list(): string[] {
    return this.executionOrder;
  }

  /**
   * Clear all registered seeders
   */
  static clear(): void {
    this.seeders.clear();
    this.executionOrder = [];
  }
}

/**
 * Database utilities for seeders
 */
export class DatabaseUtils {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Clean all test data from database
   */
  async cleanTestData(): Promise<void> {
    console.log('🧹 Cleaning test data from database...');
    
    try {
      // Delete in reverse dependency order
      await this.prisma.transaction.deleteMany({
        where: {
          OR: [
            { description: { contains: 'test_' } },
            { notes: { contains: 'test_data' } },
          ],
        },
      });

      await this.prisma.account.deleteMany({
        where: {
          OR: [
            { name: { contains: 'test_' } },
            { description: { contains: 'test_data' } },
          ],
        },
      });

      await this.prisma.category.deleteMany({
        where: {
          OR: [
            { name: { contains: 'test_' } },
            { description: { contains: 'test_data' } },
          ],
        },
      });

      await this.prisma.user.deleteMany({
        where: {
          OR: [
            { email: { contains: 'test_' } },
            { name: { contains: 'test_' } },
            { email: { endsWith: '@test.com' } },
          ],
        },
      });

      console.log('✅ Test data cleaned successfully');
    } catch (error) {
      console.error('❌ Failed to clean test data:', error);
      throw error;
    }
  }

  /**
   * Reset database sequences
   */
  async resetSequences(): Promise<void> {
    console.log('🔄 Resetting database sequences...');
    
    try {
      // This is PostgreSQL specific
      await this.prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('"User"', 'id'), 1, false)`;
      await this.prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('"Account"', 'id'), 1, false)`;
      await this.prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('"Transaction"', 'id'), 1, false)`;
      await this.prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('"Category"', 'id'), 1, false)`;
      
      console.log('✅ Sequences reset successfully');
    } catch (error) {
      console.warn('⚠️ Could not reset sequences (might not be PostgreSQL):', error.message);
    }
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<{
    users: number;
    accounts: number;
    transactions: number;
    categories: number;
  }> {
    const [users, accounts, transactions, categories] = await Promise.all([
      this.prisma.user.count(),
      this.prisma.account.count(),
      this.prisma.transaction.count(),
      this.prisma.category.count(),
    ]);

    return { users, accounts, transactions, categories };
  }

  /**
   * Verify database connection
   */
  async verifyConnection(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      return false;
    }
  }
}
