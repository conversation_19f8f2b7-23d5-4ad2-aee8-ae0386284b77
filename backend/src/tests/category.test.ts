import request from 'supertest';
import app from '../index';

describe('Category API', () => {
  let authToken: string;

  beforeAll(async () => {
    // This would normally be set up with proper test data
    // For now, we'll skip authentication in tests
    authToken = 'test-token';
  });

  describe('POST /api/v1/categories', () => {
    it('should create a new parent category', async () => {
      const categoryData = {
        name: 'Alimentação',
        color: '#10B981'
      };

      const response = await request(app)
        .post('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .send(categoryData);

      // Note: This test will fail without proper authentication and database setup
      // It's here to demonstrate the API structure
      expect(response.status).toBeDefined();
    });

    it('should create a new subcategory', async () => {
      const categoryData = {
        name: 'Restaurantes',
        color: '#059669',
        parentId: 'test-parent-category-id'
      };

      const response = await request(app)
        .post('/api/v1/categories')
        .set('Authorization', `Bear<PERSON> ${authToken}`)
        .send(categoryData);

      expect(response.status).toBeDefined();
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should validate name length', async () => {
      const categoryData = {
        name: 'A' // Too short
      };

      const response = await request(app)
        .post('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .send(categoryData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should validate color format', async () => {
      const categoryData = {
        name: 'Test Category',
        color: 'invalid-color'
      };

      const response = await request(app)
        .post('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .send(categoryData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should prevent creating subcategory of subcategory (max 2 levels)', async () => {
      const categoryData = {
        name: 'Invalid Deep Category',
        parentId: 'test-subcategory-id' // This is already a subcategory
      };

      const response = await request(app)
        .post('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .send(categoryData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('HIERARCHY_ERROR');
    });

    it('should prevent duplicate names in same level', async () => {
      const categoryData = {
        name: 'Existing Category Name',
        parentId: 'test-parent-category-id'
      };

      const response = await request(app)
        .post('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .send(categoryData);

      expect(response.status).toBe(409);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('CONFLICT');
    });
  });

  describe('GET /api/v1/categories', () => {
    it('should get all categories', async () => {
      const response = await request(app)
        .get('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
      // More specific assertions would be added with proper test setup
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/v1/categories?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should support filtering by name', async () => {
      const response = await request(app)
        .get('/api/v1/categories?name=Alimentação')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should support filtering by parent category', async () => {
      const response = await request(app)
        .get('/api/v1/categories?parentId=test-parent-id')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should support filtering only parent categories', async () => {
      const response = await request(app)
        .get('/api/v1/categories?onlyParents=true')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should support filtering only subcategories', async () => {
      const response = await request(app)
        .get('/api/v1/categories?onlyChildren=true')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should support including children in response', async () => {
      const response = await request(app)
        .get('/api/v1/categories?includeChildren=true')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should support including archived categories', async () => {
      const response = await request(app)
        .get('/api/v1/categories?includeArchived=true')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });
  });

  describe('GET /api/v1/categories/tree', () => {
    it('should get category tree', async () => {
      const response = await request(app)
        .get('/api/v1/categories/tree')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should support including archived categories in tree', async () => {
      const response = await request(app)
        .get('/api/v1/categories/tree?includeArchived=true')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });
  });

  describe('GET /api/v1/categories/:id', () => {
    it('should get category by ID', async () => {
      const categoryId = 'test-category-id';
      
      const response = await request(app)
        .get(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should return 404 for non-existent category', async () => {
      const categoryId = 'non-existent-id';
      
      const response = await request(app)
        .get(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('CATEGORY_NOT_FOUND');
    });

    it('should support including archived category', async () => {
      const categoryId = 'test-archived-category-id';

      const response = await request(app)
        .get(`/api/v1/categories/${categoryId}?includeArchived=true`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });
  });

  describe('PUT /api/v1/categories/:id', () => {
    it('should update category', async () => {
      const categoryId = 'test-category-id';
      const updateData = {
        name: 'Updated Category Name',
        color: '#FF5733'
      };

      const response = await request(app)
        .put(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBeDefined();
    });

    it('should update category parent', async () => {
      const categoryId = 'test-category-id';
      const updateData = {
        parentId: 'new-parent-category-id'
      };

      const response = await request(app)
        .put(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBeDefined();
    });

    it('should remove parent (move to root level)', async () => {
      const categoryId = 'test-subcategory-id';
      const updateData = {
        parentId: null
      };

      const response = await request(app)
        .put(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBeDefined();
    });

    it('should prevent circular reference', async () => {
      const categoryId = 'test-parent-category-id';
      const updateData = {
        parentId: 'test-child-category-id' // Child of this category
      };

      const response = await request(app)
        .put(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('HIERARCHY_ERROR');
    });

    it('should prevent self-reference', async () => {
      const categoryId = 'test-category-id';
      const updateData = {
        parentId: categoryId // Self-reference
      };

      const response = await request(app)
        .put(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('HIERARCHY_ERROR');
    });

    it('should prevent exceeding max hierarchy depth', async () => {
      const categoryId = 'test-category-id';
      const updateData = {
        parentId: 'test-subcategory-id' // Already has a parent
      };

      const response = await request(app)
        .put(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('HIERARCHY_ERROR');
    });

    it('should validate color format on update', async () => {
      const categoryId = 'test-category-id';
      const updateData = {
        color: 'invalid-color-format'
      };

      const response = await request(app)
        .put(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 404 for non-existent category', async () => {
      const categoryId = 'non-existent-id';
      const updateData = {
        name: 'Updated Name'
      };

      const response = await request(app)
        .put(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('NOT_FOUND');
    });
  });

  describe('PATCH /api/v1/categories/:id/archive', () => {
    it('should archive category', async () => {
      const categoryId = 'test-category-id';

      const response = await request(app)
        .patch(`/api/v1/categories/${categoryId}/archive`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ archived: true });

      expect(response.status).toBeDefined();
    });

    it('should unarchive category', async () => {
      const categoryId = 'test-archived-category-id';

      const response = await request(app)
        .patch(`/api/v1/categories/${categoryId}/archive`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ archived: false });

      expect(response.status).toBeDefined();
    });

    it('should archive parent category and all children', async () => {
      const categoryId = 'test-parent-category-id';

      const response = await request(app)
        .patch(`/api/v1/categories/${categoryId}/archive`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ archived: true });

      expect(response.status).toBeDefined();
    });

    it('should validate archived field', async () => {
      const categoryId = 'test-category-id';

      const response = await request(app)
        .patch(`/api/v1/categories/${categoryId}/archive`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ archived: 'invalid' });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 404 for non-existent category', async () => {
      const categoryId = 'non-existent-id';

      const response = await request(app)
        .patch(`/api/v1/categories/${categoryId}/archive`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ archived: true });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('NOT_FOUND');
    });
  });

  describe('DELETE /api/v1/categories/:id', () => {
    it('should delete category without dependencies', async () => {
      const categoryId = 'test-category-id';

      const response = await request(app)
        .delete(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should delete parent category and all children', async () => {
      const categoryId = 'test-parent-category-id';

      const response = await request(app)
        .delete(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBeDefined();
    });

    it('should prevent deletion of category with transactions', async () => {
      const categoryId = 'test-category-with-transactions';

      const response = await request(app)
        .delete(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('HAS_DEPENDENCIES');
    });

    it('should prevent deletion of category with budgets', async () => {
      const categoryId = 'test-category-with-budgets';

      const response = await request(app)
        .delete(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('HAS_DEPENDENCIES');
    });

    it('should prevent deletion of parent category with children that have dependencies', async () => {
      const categoryId = 'test-parent-with-dependent-children';

      const response = await request(app)
        .delete(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('HAS_DEPENDENCIES');
    });

    it('should return 404 for non-existent category', async () => {
      const categoryId = 'non-existent-id';

      const response = await request(app)
        .delete(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('NOT_FOUND');
    });
  });
});

// Export for potential integration with other test files
export { };
