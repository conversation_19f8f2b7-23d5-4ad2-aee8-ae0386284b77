import { DashboardCacheService } from '../services/dashboard-cache.service';
import { RedisCacheManager } from '../lib/redis';

// Mock Redis for testing
jest.mock('../lib/redis', () => ({
  redisCache: {
    isAvailable: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    delPattern: jest.fn(),
    ping: jest.fn(),
    getStats: jest.fn()
  }
}));

describe('DashboardCacheService', () => {
  let cacheService: DashboardCacheService;
  let mockRedisCache: jest.Mocked<RedisCacheManager>;

  beforeEach(() => {
    cacheService = new DashboardCacheService();
    mockRedisCache = require('../lib/redis').redisCache;
    jest.clearAllMocks();
  });

  describe('Cache Operations', () => {
    const testFilters = { accountIds: ['test-account'] };
    const testData = { totalBalance: 1000 };

    it('should return null when cache miss', async () => {
      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(null);

      const result = await cacheService.get('ACCOUNT_BALANCES', testFilters);
      expect(result).toBeNull();
    });

    it('should return cached data when cache hit', async () => {
      const cachedData = {
        data: testData,
        timestamp: Date.now(),
        filters: testFilters
      };

      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(cachedData);

      const result = await cacheService.get('ACCOUNT_BALANCES', testFilters);
      expect(result).toEqual(testData);
    });

    it('should handle expired cache entries', async () => {
      const expiredData = {
        data: testData,
        timestamp: Date.now() - 400000, // 400 seconds ago (expired for 5min TTL)
        filters: testFilters
      };

      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(expiredData);
      mockRedisCache.del.mockResolvedValue(true);

      const result = await cacheService.get('ACCOUNT_BALANCES', testFilters);
      expect(result).toBeNull();
      expect(mockRedisCache.del).toHaveBeenCalled();
    });

    it('should set cache with correct TTL', async () => {
      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.set.mockResolvedValue(true);

      const success = await cacheService.set('ACCOUNT_BALANCES', testFilters, testData);
      
      expect(success).toBe(true);
      expect(mockRedisCache.set).toHaveBeenCalledWith(
        expect.stringContaining('dashboard:ACCOUNT_BALANCES:'),
        expect.objectContaining({
          data: testData,
          timestamp: expect.any(Number),
          filters: testFilters
        }),
        300 // 5 minutes TTL
      );
    });

    it('should handle Redis unavailable gracefully', async () => {
      mockRedisCache.isAvailable.mockReturnValue(false);

      const getResult = await cacheService.get('ACCOUNT_BALANCES', testFilters);
      const setResult = await cacheService.set('ACCOUNT_BALANCES', testFilters, testData);

      expect(getResult).toBeNull();
      expect(setResult).toBe(false);
      expect(mockRedisCache.get).not.toHaveBeenCalled();
      expect(mockRedisCache.set).not.toHaveBeenCalled();
    });
  });

  describe('Cache Invalidation', () => {
    it('should invalidate specific operation cache', async () => {
      mockRedisCache.delPattern.mockResolvedValue(5);

      const deletedCount = await cacheService.invalidate('ACCOUNT_BALANCES');
      
      expect(deletedCount).toBe(5);
      expect(mockRedisCache.delPattern).toHaveBeenCalledWith('dashboard:ACCOUNT_BALANCES:*');
    });

    it('should invalidate all dashboard cache', async () => {
      mockRedisCache.delPattern.mockResolvedValue(10);

      const deletedCount = await cacheService.invalidateAll();
      
      expect(deletedCount).toBe(10);
      expect(mockRedisCache.delPattern).toHaveBeenCalledWith('dashboard:*');
    });

    it('should invalidate related caches for transaction changes', async () => {
      mockRedisCache.delPattern.mockResolvedValue(1);

      await cacheService.invalidateRelated('transaction');
      
      // Should invalidate multiple operation types
      expect(mockRedisCache.delPattern).toHaveBeenCalledTimes(6);
      expect(mockRedisCache.delPattern).toHaveBeenCalledWith('dashboard:ACCOUNT_BALANCES:*');
      expect(mockRedisCache.delPattern).toHaveBeenCalledWith('dashboard:NET_WORTH:*');
      expect(mockRedisCache.delPattern).toHaveBeenCalledWith('dashboard:EXPENSES_BY_CATEGORY:*');
    });
  });

  describe('withCache wrapper', () => {
    const testFilters = { accountIds: ['test'] };
    const testData = { totalBalance: 1000 };

    it('should return cached data when available', async () => {
      const cachedData = {
        data: testData,
        timestamp: Date.now(),
        filters: testFilters
      };

      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(cachedData);

      const dataFetcher = jest.fn().mockResolvedValue(testData);
      
      const result = await cacheService.withCache('ACCOUNT_BALANCES', testFilters, dataFetcher);
      
      expect(result).toEqual(testData);
      expect(dataFetcher).not.toHaveBeenCalled();
    });

    it('should fetch and cache data when cache miss', async () => {
      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(null);
      mockRedisCache.set.mockResolvedValue(true);

      const dataFetcher = jest.fn().mockResolvedValue(testData);
      
      const result = await cacheService.withCache('ACCOUNT_BALANCES', testFilters, dataFetcher);
      
      expect(result).toEqual(testData);
      expect(dataFetcher).toHaveBeenCalled();
      expect(mockRedisCache.set).toHaveBeenCalled();
    });

    it('should still return data even if caching fails', async () => {
      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(null);
      mockRedisCache.set.mockRejectedValue(new Error('Cache error'));

      const dataFetcher = jest.fn().mockResolvedValue(testData);
      
      const result = await cacheService.withCache('ACCOUNT_BALANCES', testFilters, dataFetcher);
      
      expect(result).toEqual(testData);
      expect(dataFetcher).toHaveBeenCalled();
    });
  });

  describe('Health Check', () => {
    it('should return healthy status when Redis is working', async () => {
      mockRedisCache.ping.mockResolvedValue(true);

      const health = await cacheService.healthCheck();
      
      expect(health.status).toBe('healthy');
      expect(health.redis).toBe(true);
      expect(health.latency).toBeDefined();
    });

    it('should return unhealthy status when Redis is down', async () => {
      mockRedisCache.ping.mockResolvedValue(false);

      const health = await cacheService.healthCheck();
      
      expect(health.status).toBe('unhealthy');
      expect(health.redis).toBe(false);
      expect(health.error).toBeDefined();
    });

    it('should return degraded status for slow Redis', async () => {
      // Mock slow response
      mockRedisCache.ping.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(true), 150))
      );

      const health = await cacheService.healthCheck();
      
      expect(health.status).toBe('degraded');
      expect(health.redis).toBe(true);
      expect(health.latency).toBeGreaterThan(100);
    });
  });

  describe('Cache Key Generation', () => {
    it('should generate consistent keys for same filters', async () => {
      const filters1 = { accountIds: ['a', 'b'], currencies: ['USD' as const] };
      const filters2 = { currencies: ['USD' as const], accountIds: ['a', 'b'] }; // Different order

      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(null);

      await cacheService.get('ACCOUNT_BALANCES', filters1);
      await cacheService.get('ACCOUNT_BALANCES', filters2);

      // Should call with same key (filters normalized)
      const calls = mockRedisCache.get.mock.calls;
      expect(calls[0][0]).toBe(calls[1][0]);
    });

    it('should generate different keys for different filters', async () => {
      const filters1 = { accountIds: ['a'] };
      const filters2 = { accountIds: ['b'] };

      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(null);

      await cacheService.get('ACCOUNT_BALANCES', filters1);
      await cacheService.get('ACCOUNT_BALANCES', filters2);

      const calls = mockRedisCache.get.mock.calls;
      expect(calls[0][0]).not.toBe(calls[1][0]);
    });
  });
});
