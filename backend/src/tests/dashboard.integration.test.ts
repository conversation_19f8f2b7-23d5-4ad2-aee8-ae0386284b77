import request from 'supertest';
import app from '../index';
import jwt from 'jsonwebtoken';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    account: {
      findMany: jest.fn()
    },
    transaction: {
      findMany: jest.fn()
    },
    budget: {
      findMany: jest.fn()
    },
    goal: {
      findMany: jest.fn()
    },
    accountBalanceHistory: {
      findMany: jest.fn()
    }
  }
}));

// Mock Dashboard Cache
jest.mock('../services/dashboard-cache.service', () => ({
  dashboardCache: {
    withCache: jest.fn()
  }
}));

describe('Dashboard API Integration Tests', () => {
  let authToken: string;
  let mockPrisma: any;

  beforeAll(() => {
    // Create a valid JWT token for testing
    authToken = jwt.sign(
      { userId: 'test-user-id', email: '<EMAIL>' },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  beforeEach(() => {
    mockPrisma = require('../lib/prisma').default;
    jest.clearAllMocks();

    // Default mock implementations
    mockPrisma.account.findMany.mockResolvedValue([]);
    mockPrisma.transaction.findMany.mockResolvedValue([]);
    mockPrisma.budget.findMany.mockResolvedValue([]);
    mockPrisma.goal.findMany.mockResolvedValue([]);
    mockPrisma.accountBalanceHistory.findMany.mockResolvedValue([]);

    // Mock cache to pass through
    const { dashboardCache } = require('../services/dashboard-cache.service');
    dashboardCache.withCache.mockImplementation(async (operation: string, filters: any, dataFetcher: Function) => {
      return await dataFetcher();
    });
  });

  describe('GET /api/v1/dashboard/overview', () => {
    it('should return dashboard overview with valid token', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/overview')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          accountBalances: expect.any(Object),
          netWorth: expect.any(Object),
          creditCardUsage: expect.any(Object),
          expensesByCategory: expect.any(Object),
          expensesByMember: expect.any(Object),
          budgetComparison: expect.any(Object),
          goalProgress: expect.any(Object),
          generatedAt: expect.any(String),
          filters: expect.any(Object)
        })
      });
    });

    it('should return 401 without valid token', async () => {
      await request(app)
        .get('/api/v1/dashboard/overview')
        .expect(401);
    });

    it('should handle query parameters correctly', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/overview')
        .query({
          month: '12',
          year: '2024',
          accountIds: 'clp123456789012345678901,clp123456789012345678902',
          currencies: 'BRL,USD'
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.filters).toEqual(
        expect.objectContaining({
          period: expect.objectContaining({
            month: 12,
            year: 2024
          }),
          accountIds: ['clp123456789012345678901', 'clp123456789012345678902'],
          currencies: ['BRL', 'USD']
        })
      );
    });
  });

  describe('GET /api/v1/dashboard/account-balances', () => {
    it('should return account balances', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/account-balances')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          totalBalance: expect.any(Number),
          totalBalanceInBRL: expect.any(Number),
          projectedBalance: expect.any(Number),
          projectedBalanceInBRL: expect.any(Number),
          balanceByType: expect.any(Array),
          balanceByCurrency: expect.any(Array)
        })
      });
    });

    it('should filter by account types', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/account-balances')
        .query({ accountTypes: 'CHECKING,SAVINGS' })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('GET /api/v1/dashboard/net-worth', () => {
    it('should return net worth data', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/net-worth')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          currentNetWorth: expect.any(Number),
          assets: expect.any(Object),
          liabilities: expect.any(Object),
          monthlyHistory: expect.any(Array)
        })
      });
    });
  });

  describe('GET /api/v1/dashboard/credit-card-usage', () => {
    it('should return credit card usage', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/credit-card-usage')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          totalUsage: expect.any(Number),
          totalLimit: expect.any(Number),
          usagePercentage: expect.any(Number),
          availableCredit: expect.any(Number),
          cardDetails: expect.any(Array),
          alerts: expect.any(Array)
        })
      });
    });
  });

  describe('GET /api/v1/dashboard/expenses-by-category', () => {
    it('should return expenses by category', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/expenses-by-category')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          totalExpenses: expect.any(Number),
          categories: expect.any(Array)
        })
      });
    });
  });

  describe('GET /api/v1/dashboard/expenses-by-member', () => {
    it('should return expenses by member', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/expenses-by-member')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          totalExpenses: expect.any(Number),
          members: expect.any(Array),
          comparison: expect.any(Object)
        })
      });
    });
  });

  describe('GET /api/v1/dashboard/budget-comparison', () => {
    it('should return budget comparison', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/budget-comparison')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          totalBudgeted: expect.any(Number),
          totalSpent: expect.any(Number),
          totalRemaining: expect.any(Number),
          overallProgress: expect.any(Number),
          categories: expect.any(Array),
          alerts: expect.any(Array)
        })
      });
    });
  });

  describe('GET /api/v1/dashboard/goal-progress', () => {
    it('should return goal progress', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/goal-progress')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          totalGoals: expect.any(Number),
          activeGoals: expect.any(Number),
          completedGoals: expect.any(Number),
          goals: expect.any(Array),
          summary: expect.any(Object)
        })
      });
    });
  });

  describe('GET /api/v1/dashboard/performance-metrics', () => {
    it('should return performance metrics', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/performance-metrics')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        data: expect.objectContaining({
          slowQueries: expect.any(Array),
          averageQueryTime: expect.any(Number),
          totalQueries: expect.any(Number)
        })
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid filters gracefully', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/overview')
        .query({
          month: 'invalid',
          year: 'invalid'
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid dashboard filters');
    });

    it('should handle service errors gracefully', async () => {
      // Mock a service error
      mockPrisma.account.findMany.mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .get('/api/v1/dashboard/overview')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Failed to fetch dashboard overview');
    });
  });

  describe('Rate Limiting', () => {
    it('should handle rate limiting', async () => {
      // Make multiple requests quickly to trigger rate limiting
      const requests = Array(101).fill(null).map(() =>
        request(app)
          .get('/api/v1/dashboard/overview')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const responses = await Promise.allSettled(requests);
      
      // Some requests should be rate limited (429)
      const rateLimitedResponses = responses.filter(
        (result) => result.status === 'fulfilled' && result.value.status === 429
      );

      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    }, 10000); // Increase timeout for this test
  });

  describe('Cache Headers', () => {
    it('should set appropriate cache headers', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/overview')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.headers['cache-control']).toBeDefined();
      expect(response.headers['etag']).toBeDefined();
    });
  });
});
