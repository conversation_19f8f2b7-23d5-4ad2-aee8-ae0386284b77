import { DashboardService } from '../services/dashboard.service';
import { dashboardCache } from '../services/dashboard-cache.service';
import { redisCache } from '../lib/redis';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    account: {
      findMany: jest.fn()
    },
    transaction: {
      findMany: jest.fn()
    },
    budget: {
      findMany: jest.fn()
    },
    goal: {
      findMany: jest.fn()
    },
    accountBalanceHistory: {
      findMany: jest.fn()
    }
  }
}));

// Mock Redis
jest.mock('../lib/redis');

describe('Dashboard Service Integration with Cache', () => {
  let dashboardService: DashboardService;
  let mockRedisCache: jest.Mocked<typeof redisCache>;

  beforeEach(() => {
    dashboardService = new DashboardService();
    mockRedisCache = redisCache as jest.Mocked<typeof redisCache>;
    jest.clearAllMocks();
  });

  describe('Cache Integration', () => {
    const mockAccountData = [
      {
        id: 'acc1',
        type: 'CHECKING',
        currency: 'BRL',
        currentBalance: 1000,
        exchangeRate: 1,
        creditLimit: null,
        includeInTotal: true
      }
    ];

    const expectedResult = {
      totalBalance: 1000,
      totalBalanceInBRL: 1000,
      projectedBalance: 1000,
      projectedBalanceInBRL: 1000,
      balanceByType: [
        {
          type: 'CHECKING',
          balance: 1000,
          balanceInBRL: 1000,
          count: 1
        }
      ],
      balanceByCurrency: [
        {
          currency: 'BRL',
          balance: 1000,
          balanceInBRL: 1000,
          exchangeRate: undefined,
          count: 1
        }
      ]
    };

    it('should cache account balances on first call', async () => {
      // Mock Redis as available but no cached data
      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(null);
      mockRedisCache.set.mockResolvedValue(true);

      // Mock Prisma data
      const prisma = require('../lib/prisma').default;
      prisma.account.findMany.mockResolvedValue(mockAccountData);
      prisma.transaction.findMany.mockResolvedValue([]); // No future transactions

      const result = await dashboardService.getAccountBalances({});

      expect(result).toEqual(expectedResult);
      expect(mockRedisCache.get).toHaveBeenCalled();
      expect(mockRedisCache.set).toHaveBeenCalled();
      expect(prisma.account.findMany).toHaveBeenCalled();
    });

    it('should return cached data on second call', async () => {
      // Mock Redis with cached data
      const cachedData = {
        data: expectedResult,
        timestamp: Date.now(),
        filters: {}
      };

      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(cachedData);

      const prisma = require('../lib/prisma').default;
      
      const result = await dashboardService.getAccountBalances({});

      expect(result).toEqual(expectedResult);
      expect(mockRedisCache.get).toHaveBeenCalled();
      expect(mockRedisCache.set).not.toHaveBeenCalled();
      expect(prisma.account.findMany).not.toHaveBeenCalled();
    });

    it('should work without cache when Redis is unavailable', async () => {
      // Mock Redis as unavailable
      mockRedisCache.isAvailable.mockReturnValue(false);

      const prisma = require('../lib/prisma').default;
      prisma.account.findMany.mockResolvedValue(mockAccountData);
      prisma.transaction.findMany.mockResolvedValue([]);

      const result = await dashboardService.getAccountBalances({});

      expect(result).toEqual(expectedResult);
      expect(mockRedisCache.get).not.toHaveBeenCalled();
      expect(mockRedisCache.set).not.toHaveBeenCalled();
      expect(prisma.account.findMany).toHaveBeenCalled();
    });

    it('should handle cache errors gracefully', async () => {
      // Mock Redis to throw errors
      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockRejectedValue(new Error('Redis error'));

      const prisma = require('../lib/prisma').default;
      prisma.account.findMany.mockResolvedValue(mockAccountData);
      prisma.transaction.findMany.mockResolvedValue([]);

      const result = await dashboardService.getAccountBalances({});

      expect(result).toEqual(expectedResult);
      expect(prisma.account.findMany).toHaveBeenCalled();
    });
  });

  describe('Cache Invalidation Scenarios', () => {
    it('should invalidate related caches when transaction is created', async () => {
      const spy = jest.spyOn(dashboardCache, 'invalidateRelated');
      
      await dashboardCache.invalidateRelated('transaction');
      
      expect(spy).toHaveBeenCalledWith('transaction');
    });

    it('should invalidate account-related caches when account is updated', async () => {
      const spy = jest.spyOn(dashboardCache, 'invalidateRelated');
      
      await dashboardCache.invalidateRelated('account');
      
      expect(spy).toHaveBeenCalledWith('account');
    });
  });

  describe('Performance Monitoring', () => {
    it('should log performance metrics', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(null);
      mockRedisCache.set.mockResolvedValue(true);

      const prisma = require('../lib/prisma').default;
      prisma.account.findMany.mockResolvedValue([]);
      prisma.transaction.findMany.mockResolvedValue([]);

      await dashboardService.getAccountBalances({});

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Cache MISS for ACCOUNT_BALANCES')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Data fetched for ACCOUNT_BALANCES')
      );

      consoleSpy.mockRestore();
    });

    it('should track slow queries', async () => {
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(null);
      mockRedisCache.set.mockResolvedValue(true);

      const prisma = require('../lib/prisma').default;
      // Mock slow database response
      prisma.account.findMany.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve([]), 2500))
      );
      prisma.transaction.findMany.mockResolvedValue([]);

      await dashboardService.getAccountBalances({});

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('Slow dashboard query detected'),
        expect.any(Object)
      );

      consoleWarnSpy.mockRestore();
    });
  });

  describe('Cache Health Monitoring', () => {
    it('should provide cache health status', async () => {
      mockRedisCache.ping.mockResolvedValue(true);

      const health = await dashboardCache.healthCheck();

      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('redis');
      expect(['healthy', 'degraded', 'unhealthy']).toContain(health.status);
    });

    it('should provide cache statistics', async () => {
      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.getStats.mockResolvedValue({
        isConnected: true,
        connectionAttempts: 0,
        info: 'used_memory_human:1.5M\r\n'
      });

      const stats = await dashboardCache.getStats();

      expect(stats).toHaveProperty('isAvailable');
      expect(stats).toHaveProperty('operations');
      expect(stats.operations).toHaveProperty('ACCOUNT_BALANCES');
    });
  });

  describe('Filter Validation with Cache', () => {
    it('should validate filters before caching', async () => {
      const invalidFilters = {
        dateRange: {
          startDate: 'invalid-date',
          endDate: '2024-12-31T23:59:59.999Z'
        }
      };

      mockRedisCache.isAvailable.mockReturnValue(true);
      mockRedisCache.get.mockResolvedValue(null);
      mockRedisCache.set.mockResolvedValue(true);

      const prisma = require('../lib/prisma').default;
      prisma.account.findMany.mockResolvedValue([]);
      prisma.transaction.findMany.mockResolvedValue([]);

      // Should not throw error, should use fallback filters
      const result = await dashboardService.getAccountBalances(invalidFilters);

      expect(result).toBeDefined();
      expect(mockRedisCache.get).toHaveBeenCalled();
    });
  });
});
