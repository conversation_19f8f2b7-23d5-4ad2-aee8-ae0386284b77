import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { Request, Response, NextFunction } from 'express';
import { authenticateToken, requireActiveUser } from '../middleware/auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { rateLimiter } from '../middleware/rate-limit.middleware';
import { errorHandler } from '../middleware/error.middleware';
import { cacheMiddleware } from '../middleware/cache.middleware';
import jwt from 'jsonwebtoken';

// Mock Redis for cache tests
const mockRedisClient = {
  get: jest.fn(),
  setex: jest.fn(),
  del: jest.fn(),
  ping: jest.fn().mockResolvedValue('PONG')
};

// Mock request, response, and next function
const createMockReq = (overrides = {}): Partial<Request> => ({
  headers: {},
  body: {},
  query: {},
  params: {},
  user: undefined,
  ...overrides
});

const createMockRes = (): Partial<Response> => {
  const res: Partial<Response> = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    setHeader: jest.fn().mockReturnThis(),
    locals: {}
  };
  return res;
};

const createMockNext = (): NextFunction => jest.fn();

describe('Middleware Tests - Comprehensive', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = createMockReq();
    mockRes = createMockRes();
    mockNext = createMockNext();
    jest.clearAllMocks();
  });

  describe('Authentication Middleware', () => {
    describe('authenticateToken', () => {
      it('should authenticate valid JWT token', async () => {
        const validToken = jwt.sign(
          { id: 'user-123', email: '<EMAIL>' },
          process.env.JWT_SECRET || 'test-secret'
        );

        mockReq.headers = {
          authorization: `Bearer ${validToken}`
        };

        await authenticateToken(mockReq as Request, mockRes as Response, mockNext);

        expect(mockReq.user).toEqual({
          id: 'user-123',
          email: '<EMAIL>',
          iat: expect.any(Number)
        });
        expect(mockNext).toHaveBeenCalledWith();
      });

      it('should reject request without authorization header', async () => {
        await authenticateToken(mockReq as Request, mockRes as Response, mockNext);

        expect(mockRes.status).toHaveBeenCalledWith(401);
        expect(mockRes.json).toHaveBeenCalledWith({
          success: false,
          error: 'Access token required'
        });
        expect(mockNext).not.toHaveBeenCalled();
      });

      it('should reject request with invalid token format', async () => {
        mockReq.headers = {
          authorization: 'InvalidFormat token'
        };

        await authenticateToken(mockReq as Request, mockRes as Response, mockNext);

        expect(mockRes.status).toHaveBeenCalledWith(401);
        expect(mockRes.json).toHaveBeenCalledWith({
          success: false,
          error: 'Invalid token format'
        });
      });

      it('should reject request with expired token', async () => {
        const expiredToken = jwt.sign(
          { id: 'user-123', email: '<EMAIL>' },
          process.env.JWT_SECRET || 'test-secret',
          { expiresIn: '-1h' } // Expired 1 hour ago
        );

        mockReq.headers = {
          authorization: `Bearer ${expiredToken}`
        };

        await authenticateToken(mockReq as Request, mockRes as Response, mockNext);

        expect(mockRes.status).toHaveBeenCalledWith(401);
        expect(mockRes.json).toHaveBeenCalledWith({
          success: false,
          error: 'Token expired'
        });
      });

      it('should reject request with malformed token', async () => {
        mockReq.headers = {
          authorization: 'Bearer malformed.token.here'
        };

        await authenticateToken(mockReq as Request, mockRes as Response, mockNext);

        expect(mockRes.status).toHaveBeenCalledWith(401);
        expect(mockRes.json).toHaveBeenCalledWith({
          success: false,
          error: 'Invalid token'
        });
      });
    });

    describe('requireActiveUser', () => {
      it('should allow active user to proceed', async () => {
        mockReq.user = {
          id: 'user-123',
          email: '<EMAIL>',
          isActive: true
        };

        await requireActiveUser(mockReq as Request, mockRes as Response, mockNext);

        expect(mockNext).toHaveBeenCalledWith();
      });

      it('should reject inactive user', async () => {
        mockReq.user = {
          id: 'user-123',
          email: '<EMAIL>',
          isActive: false
        };

        await requireActiveUser(mockReq as Request, mockRes as Response, mockNext);

        expect(mockRes.status).toHaveBeenCalledWith(403);
        expect(mockRes.json).toHaveBeenCalledWith({
          success: false,
          error: 'Account is not active'
        });
      });

      it('should reject request without user context', async () => {
        await requireActiveUser(mockReq as Request, mockRes as Response, mockNext);

        expect(mockRes.status).toHaveBeenCalledWith(401);
        expect(mockRes.json).toHaveBeenCalledWith({
          success: false,
          error: 'Authentication required'
        });
      });
    });
  });

  describe('Validation Middleware', () => {
    const mockSchema = {
      validate: jest.fn()
    };

    beforeEach(() => {
      mockSchema.validate.mockClear();
    });

    it('should validate request body successfully', async () => {
      const validData = { name: 'Test', email: '<EMAIL>' };
      mockReq.body = validData;
      
      mockSchema.validate.mockReturnValue({ error: null, value: validData });

      const middleware = validateRequest(mockSchema as any);
      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockSchema.validate).toHaveBeenCalledWith(validData);
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should reject invalid request body', async () => {
      const invalidData = { name: '', email: 'invalid-email' };
      mockReq.body = invalidData;
      
      const validationError = {
        details: [
          { message: 'Name is required', path: ['name'] },
          { message: 'Email must be valid', path: ['email'] }
        ]
      };
      
      mockSchema.validate.mockReturnValue({ error: validationError, value: null });

      const middleware = validateRequest(mockSchema as any);
      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'Validation failed',
        details: expect.arrayContaining([
          expect.objectContaining({ message: 'Name is required' }),
          expect.objectContaining({ message: 'Email must be valid' })
        ])
      });
    });
  });

  describe('Cache Middleware', () => {
    beforeEach(() => {
      // Mock global Redis client
      (global as any).__REDIS_CLIENT__ = mockRedisClient;
    });

    it('should return cached response when available', async () => {
      const cachedData = JSON.stringify({
        success: true,
        data: { message: 'Cached response' }
      });
      
      mockRedisClient.get.mockResolvedValue(cachedData);
      mockReq.originalUrl = '/api/v1/test';
      mockReq.user = { id: 'user-123' };

      const middleware = cacheMiddleware(300); // 5 minutes
      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRedisClient.get).toHaveBeenCalledWith('cache:user-123:/api/v1/test');
      expect(mockRes.json).toHaveBeenCalledWith(JSON.parse(cachedData));
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should proceed to next middleware when no cache found', async () => {
      mockRedisClient.get.mockResolvedValue(null);
      mockReq.originalUrl = '/api/v1/test';
      mockReq.user = { id: 'user-123' };

      const middleware = cacheMiddleware(300);
      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRedisClient.get).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should handle Redis connection errors gracefully', async () => {
      mockRedisClient.get.mockRejectedValue(new Error('Redis connection failed'));
      mockReq.originalUrl = '/api/v1/test';
      mockReq.user = { id: 'user-123' };

      const middleware = cacheMiddleware(300);
      await middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('Error Handler Middleware', () => {
    it('should handle validation errors', async () => {
      const validationError = new Error('Validation failed');
      (validationError as any).name = 'ValidationError';
      (validationError as any).details = [
        { message: 'Field is required', path: ['field'] }
      ];

      await errorHandler(validationError, mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'Validation failed',
        details: expect.any(Array)
      });
    });

    it('should handle authentication errors', async () => {
      const authError = new Error('Unauthorized');
      (authError as any).name = 'UnauthorizedError';

      await errorHandler(authError, mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'Unauthorized'
      });
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database connection failed');
      (dbError as any).name = 'PrismaClientKnownRequestError';
      (dbError as any).code = 'P2002';

      await errorHandler(dbError, mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'Database error occurred'
      });
    });

    it('should handle generic errors', async () => {
      const genericError = new Error('Something went wrong');

      await errorHandler(genericError, mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error'
      });
    });
  });
});
