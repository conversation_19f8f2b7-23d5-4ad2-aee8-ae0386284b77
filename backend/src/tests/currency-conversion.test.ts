import { CurrencyService } from '../services/currency.service';

// Mock external currency API
jest.mock('axios', () => ({
  get: jest.fn()
}));

describe('CurrencyService', () => {
  let currencyService: CurrencyService;
  let mockAxios: any;

  beforeEach(() => {
    currencyService = new CurrencyService();
    mockAxios = require('axios');
    jest.clearAllMocks();
  });

  describe('convertAmount', () => {
    it('should convert amount with manual exchange rate', async () => {
      // Arrange
      const fromCurrency = 'USD';
      const toCurrency = 'BRL';
      const amount = 100;
      const manualRate = 5.25;

      // Act
      const result = await currencyService.convertAmount(
        amount,
        fromCurrency,
        toCurrency,
        manualRate
      );

      // Assert
      expect(result).toEqual({
        originalAmount: 100,
        convertedAmount: 525,
        fromCurrency: 'USD',
        toCurrency: 'BRL',
        exchangeRate: 5.25,
        isManualRate: true,
        timestamp: expect.any(Date)
      });
    });

    it('should convert amount with automatic exchange rate', async () => {
      // Arrange
      const fromCurrency = 'USD';
      const toCurrency = 'BRL';
      const amount = 100;
      const apiRate = 5.30;

      mockAxios.get.mockResolvedValue({
        data: {
          rates: {
            BRL: apiRate
          }
        }
      });

      // Act
      const result = await currencyService.convertAmount(
        amount,
        fromCurrency,
        toCurrency
      );

      // Assert
      expect(result).toEqual({
        originalAmount: 100,
        convertedAmount: 530,
        fromCurrency: 'USD',
        toCurrency: 'BRL',
        exchangeRate: 5.30,
        isManualRate: false,
        timestamp: expect.any(Date)
      });
      expect(mockAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('exchangerate-api.com')
      );
    });

    it('should return same amount for same currency conversion', async () => {
      // Arrange
      const currency = 'BRL';
      const amount = 100;

      // Act
      const result = await currencyService.convertAmount(
        amount,
        currency,
        currency
      );

      // Assert
      expect(result).toEqual({
        originalAmount: 100,
        convertedAmount: 100,
        fromCurrency: 'BRL',
        toCurrency: 'BRL',
        exchangeRate: 1,
        isManualRate: false,
        timestamp: expect.any(Date)
      });
      expect(mockAxios.get).not.toHaveBeenCalled();
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      const fromCurrency = 'USD';
      const toCurrency = 'BRL';
      const amount = 100;

      mockAxios.get.mockRejectedValue(new Error('API Error'));

      // Act & Assert
      await expect(
        currencyService.convertAmount(amount, fromCurrency, toCurrency)
      ).rejects.toThrow('Failed to fetch exchange rate');
    });

    it('should validate currency codes', async () => {
      // Arrange
      const invalidCurrency = 'INVALID';
      const validCurrency = 'USD';
      const amount = 100;

      // Act & Assert
      await expect(
        currencyService.convertAmount(amount, invalidCurrency, validCurrency)
      ).rejects.toThrow('Invalid currency code');

      await expect(
        currencyService.convertAmount(amount, validCurrency, invalidCurrency)
      ).rejects.toThrow('Invalid currency code');
    });

    it('should validate amount is positive', async () => {
      // Arrange
      const fromCurrency = 'USD';
      const toCurrency = 'BRL';
      const negativeAmount = -100;

      // Act & Assert
      await expect(
        currencyService.convertAmount(negativeAmount, fromCurrency, toCurrency)
      ).rejects.toThrow('Amount must be positive');
    });

    it('should validate manual exchange rate is positive', async () => {
      // Arrange
      const fromCurrency = 'USD';
      const toCurrency = 'BRL';
      const amount = 100;
      const negativeRate = -5.25;

      // Act & Assert
      await expect(
        currencyService.convertAmount(amount, fromCurrency, toCurrency, negativeRate)
      ).rejects.toThrow('Exchange rate must be positive');
    });
  });

  describe('getSupportedCurrencies', () => {
    it('should return list of supported currencies', () => {
      // Act
      const currencies = currencyService.getSupportedCurrencies();

      // Assert
      expect(currencies).toBeInstanceOf(Array);
      expect(currencies).toContain('BRL');
      expect(currencies).toContain('USD');
      expect(currencies).toContain('EUR');
      expect(currencies.length).toBeGreaterThan(0);
    });
  });

  describe('getCurrentRate', () => {
    it('should fetch current exchange rate from API', async () => {
      // Arrange
      const fromCurrency = 'USD';
      const toCurrency = 'BRL';
      const apiRate = 5.30;

      mockAxios.get.mockResolvedValue({
        data: {
          rates: {
            BRL: apiRate
          }
        }
      });

      // Act
      const rate = await currencyService.getCurrentRate(fromCurrency, toCurrency);

      // Assert
      expect(rate).toBe(5.30);
      expect(mockAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('exchangerate-api.com')
      );
    });

    it('should return 1 for same currency', async () => {
      // Arrange
      const currency = 'BRL';

      // Act
      const rate = await currencyService.getCurrentRate(currency, currency);

      // Assert
      expect(rate).toBe(1);
      expect(mockAxios.get).not.toHaveBeenCalled();
    });

    it('should handle missing rate in API response', async () => {
      // Arrange
      const fromCurrency = 'USD';
      const toCurrency = 'UNKNOWN';

      mockAxios.get.mockResolvedValue({
        data: {
          rates: {
            BRL: 5.30
          }
        }
      });

      // Act & Assert
      await expect(
        currencyService.getCurrentRate(fromCurrency, toCurrency)
      ).rejects.toThrow('Exchange rate not available');
    });
  });

  describe('formatCurrency', () => {
    it('should format currency with correct locale and symbol', () => {
      // Act
      const formattedBRL = currencyService.formatCurrency(1234.56, 'BRL');
      const formattedUSD = currencyService.formatCurrency(1234.56, 'USD');
      const formattedEUR = currencyService.formatCurrency(1234.56, 'EUR');

      // Assert
      expect(formattedBRL).toMatch(/R\$.*1\.234,56/);
      expect(formattedUSD).toMatch(/\$1,234\.56/);
      expect(formattedEUR).toMatch(/1\.234,56.*€/);
    });

    it('should handle zero and negative amounts', () => {
      // Act
      const zero = currencyService.formatCurrency(0, 'BRL');
      const negative = currencyService.formatCurrency(-100, 'BRL');

      // Assert
      expect(zero).toMatch(/R\$.*0,00/);
      expect(negative).toMatch(/-R\$.*100,00/);
    });
  });

  describe('calculateConversionFee', () => {
    it('should calculate conversion fee based on amount', () => {
      // Arrange
      const amount = 1000;
      const feePercentage = 2.5; // 2.5%

      // Act
      const fee = currencyService.calculateConversionFee(amount, feePercentage);

      // Assert
      expect(fee).toBe(25); // 1000 * 0.025
    });

    it('should return zero fee for same currency', () => {
      // Arrange
      const amount = 1000;

      // Act
      const fee = currencyService.calculateConversionFee(amount, 0);

      // Assert
      expect(fee).toBe(0);
    });

    it('should handle minimum fee', () => {
      // Arrange
      const smallAmount = 10;
      const feePercentage = 2.5;
      const minimumFee = 5;

      // Act
      const fee = currencyService.calculateConversionFee(
        smallAmount, 
        feePercentage, 
        minimumFee
      );

      // Assert
      expect(fee).toBe(5); // Minimum fee applied
    });
  });
});
