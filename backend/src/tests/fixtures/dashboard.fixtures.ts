/**
 * Test fixtures for Dashboard Service tests
 * Realistic data that represents real-world scenarios
 */

export const mockAccounts = [
  {
    id: 'acc-1',
    name: '<PERSON><PERSON> Corrente Principal',
    type: 'CHECKING',
    currency: 'BRL',
    currentBalance: 5000.00,
    exchangeRate: 1.0,
    creditLimit: null,
    includeInTotal: true,
    deletedAt: null,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-12-01')
  },
  {
    id: 'acc-2',
    name: '<PERSON><PERSON>an<PERSON>',
    type: 'SAVINGS',
    currency: 'BRL',
    currentBalance: 15000.00,
    exchangeRate: 1.0,
    creditLimit: null,
    includeInTotal: true,
    deletedAt: null,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-12-01')
  },
  {
    id: 'acc-3',
    name: 'Car<PERSON><PERSON> de Crédito',
    type: 'CREDIT_CARD',
    currency: 'BRL',
    currentBalance: -2500.00, // Negative balance = debt
    exchangeRate: 1.0,
    creditLimit: 10000.00,
    includeInTotal: true,
    deletedAt: null,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-12-01')
  },
  {
    id: 'acc-4',
    name: 'USD Account',
    type: 'CHECKING',
    currency: 'USD',
    currentBalance: 1000.00,
    exchangeRate: 5.5, // 1 USD = 5.5 BRL
    creditLimit: null,
    includeInTotal: true,
    deletedAt: null,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-12-01')
  }
];

export const mockCategories = [
  {
    id: 'cat-1',
    name: 'Alimentação',
    parentId: null,
    deletedAt: null
  },
  {
    id: 'cat-2',
    name: 'Supermercado',
    parentId: 'cat-1',
    deletedAt: null
  },
  {
    id: 'cat-3',
    name: 'Restaurantes',
    parentId: 'cat-1',
    deletedAt: null
  },
  {
    id: 'cat-4',
    name: 'Transporte',
    parentId: null,
    deletedAt: null
  },
  {
    id: 'cat-5',
    name: 'Combustível',
    parentId: 'cat-4',
    deletedAt: null
  }
];

export const mockFamilyMembers = [
  {
    id: 'member-1',
    name: 'João Silva',
    deletedAt: null
  },
  {
    id: 'member-2',
    name: 'Maria Silva',
    deletedAt: null
  },
  {
    id: 'member-3',
    name: 'Pedro Silva',
    deletedAt: null
  }
];

export const mockTransactions = [
  {
    id: 'trans-1',
    description: 'Supermercado Extra',
    amount: -150.00,
    transactionDate: new Date('2024-12-01'),
    type: 'EXPENSE',
    categoryId: 'cat-2',
    accountId: 'acc-1',
    parentTransactionId: null,
    installmentNumber: null,
    totalInstallments: null,
    exchangeRate: 1.0,
    destinationAccountId: null,
    isFuture: false,
    deletedAt: null,
    category: mockCategories[1],
    members: [
      {
        transactionId: 'trans-1',
        familyMemberId: 'member-1',
        familyMember: mockFamilyMembers[0]
      }
    ]
  },
  {
    id: 'trans-2',
    description: 'Restaurante Italiano',
    amount: -80.00,
    transactionDate: new Date('2024-12-02'),
    type: 'EXPENSE',
    categoryId: 'cat-3',
    accountId: 'acc-1',
    parentTransactionId: null,
    installmentNumber: null,
    totalInstallments: null,
    exchangeRate: 1.0,
    destinationAccountId: null,
    isFuture: false,
    deletedAt: null,
    category: mockCategories[2],
    members: [
      {
        transactionId: 'trans-2',
        familyMemberId: 'member-1',
        familyMember: mockFamilyMembers[0]
      },
      {
        transactionId: 'trans-2',
        familyMemberId: 'member-2',
        familyMember: mockFamilyMembers[1]
      }
    ]
  },
  {
    id: 'trans-3',
    description: 'Salário',
    amount: 5000.00,
    transactionDate: new Date('2024-12-01'),
    type: 'INCOME',
    categoryId: null,
    accountId: 'acc-1',
    parentTransactionId: null,
    installmentNumber: null,
    totalInstallments: null,
    exchangeRate: 1.0,
    destinationAccountId: null,
    isFuture: false,
    deletedAt: null,
    category: null,
    members: [
      {
        transactionId: 'trans-3',
        familyMemberId: 'member-1',
        familyMember: mockFamilyMembers[0]
      }
    ]
  },
  {
    id: 'trans-4',
    description: 'Combustível',
    amount: -200.00,
    transactionDate: new Date('2024-12-03'),
    type: 'EXPENSE',
    categoryId: 'cat-5',
    accountId: 'acc-3', // Credit card
    parentTransactionId: null,
    installmentNumber: null,
    totalInstallments: null,
    exchangeRate: 1.0,
    destinationAccountId: null,
    isFuture: false,
    deletedAt: null,
    category: mockCategories[4],
    members: [
      {
        transactionId: 'trans-4',
        familyMemberId: 'member-2',
        familyMember: mockFamilyMembers[1]
      }
    ]
  },
  {
    id: 'trans-5',
    description: 'Transferência para Poupança',
    amount: -1000.00,
    transactionDate: new Date('2024-12-05'),
    type: 'TRANSFER',
    categoryId: null,
    accountId: 'acc-1',
    parentTransactionId: null,
    installmentNumber: null,
    totalInstallments: null,
    exchangeRate: 1.0,
    destinationAccountId: 'acc-2',
    isFuture: false,
    deletedAt: null,
    category: null,
    members: []
  }
];

export const mockBudgets = [
  {
    id: 'budget-1',
    categoryId: 'cat-1',
    familyMemberId: 'member-1',
    amount: 800.00,
    month: 12,
    year: 2024,
    deletedAt: null,
    category: mockCategories[0],
    familyMember: mockFamilyMembers[0]
  },
  {
    id: 'budget-2',
    categoryId: 'cat-4',
    familyMemberId: 'member-2',
    amount: 500.00,
    month: 12,
    year: 2024,
    deletedAt: null,
    category: mockCategories[3],
    familyMember: mockFamilyMembers[1]
  },
  {
    id: 'budget-3',
    categoryId: 'cat-1',
    familyMemberId: null, // Family budget
    amount: 1200.00,
    month: 12,
    year: 2024,
    deletedAt: null,
    category: mockCategories[0],
    familyMember: null
  }
];

export const mockGoals = [
  {
    id: 'goal-1',
    name: 'Viagem para Europa',
    targetAmount: 20000.00,
    currentAmount: 5000.00,
    targetDate: new Date('2025-06-01'),
    deletedAt: null,
    members: [
      {
        goalId: 'goal-1',
        familyMemberId: 'member-1',
        familyMember: mockFamilyMembers[0]
      },
      {
        goalId: 'goal-1',
        familyMemberId: 'member-2',
        familyMember: mockFamilyMembers[1]
      }
    ],
    milestones: [
      {
        id: 'milestone-1',
        goalId: 'goal-1',
        name: 'Primeira etapa',
        targetAmount: 5000.00,
        targetDate: new Date('2024-12-31'),
        isCompleted: true
      }
    ]
  },
  {
    id: 'goal-2',
    name: 'Reserva de Emergência',
    targetAmount: 30000.00,
    currentAmount: 15000.00,
    targetDate: new Date('2025-12-31'),
    deletedAt: null,
    members: [
      {
        goalId: 'goal-2',
        familyMemberId: 'member-1',
        familyMember: mockFamilyMembers[0]
      }
    ],
    milestones: []
  }
];

export const mockAccountBalanceHistory = [
  {
    id: 'history-1',
    accountId: 'acc-1',
    balance: 4500.00,
    balanceDate: new Date('2024-11-01'),
    account: mockAccounts[0]
  },
  {
    id: 'history-2',
    accountId: 'acc-1',
    balance: 5000.00,
    balanceDate: new Date('2024-12-01'),
    account: mockAccounts[0]
  },
  {
    id: 'history-3',
    accountId: 'acc-2',
    balance: 14000.00,
    balanceDate: new Date('2024-11-01'),
    account: mockAccounts[1]
  },
  {
    id: 'history-4',
    accountId: 'acc-2',
    balance: 15000.00,
    balanceDate: new Date('2024-12-01'),
    account: mockAccounts[1]
  }
];

// Helper functions for test data manipulation
export const createMockAccount = (overrides: Partial<typeof mockAccounts[0]> = {}) => ({
  ...mockAccounts[0],
  ...overrides
});

export const createMockTransaction = (overrides: Partial<typeof mockTransactions[0]> = {}) => ({
  ...mockTransactions[0],
  ...overrides
});

export const createMockBudget = (overrides: Partial<typeof mockBudgets[0]> = {}) => ({
  ...mockBudgets[0],
  ...overrides
});

export const createMockGoal = (overrides: Partial<typeof mockGoals[0]> = {}) => ({
  ...mockGoals[0],
  ...overrides
});
