import { z } from 'zod';
import { AccountType } from '@prisma/client';

/**
 * Supported currencies
 */
export const SUPPORTED_CURRENCIES = ['BRL', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF'] as const;

/**
 * Schema for creating a new account
 */
export const CreateAccountSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim(),
  type: z.nativeEnum(AccountType, {
    errorMap: () => ({ message: 'Tipo de conta inválido' })
  }),
  currency: z.enum(SUPPORTED_CURRENCIES, {
    errorMap: () => ({ message: 'Moeda não suportada' })
  }).default('BRL'),
  creditLimit: z.number()
    .positive('Limite de crédito deve ser positivo')
    .max(*********.99, 'Limite de crédito muito alto')
    .optional()
    .transform(val => val ? Number(val.toFixed(2)) : undefined),
  exchangeRate: z.number()
    .positive('Taxa de câmbio deve ser positiva')
    .max(999999.999999, 'Taxa de câmbio muito alta')
    .optional()
    .transform(val => val ? Number(val.toFixed(6)) : undefined),
  includeInTotal: z.boolean().default(true),
  logoPath: z.string()
    .max(255, 'Caminho do logo muito longo')
    .optional(),
  familyMemberIds: z.array(z.string().cuid('ID de membro da família inválido'))
    .min(1, 'Pelo menos um membro da família deve ser associado')
    .max(10, 'Máximo de 10 membros da família por conta')
    .optional()
}).refine((data) => {
  // Credit limit is required for credit cards
  if (data.type === AccountType.CREDIT_CARD && !data.creditLimit) {
    return false;
  }
  return true;
}, {
  message: 'Limite de crédito é obrigatório para cartões de crédito',
  path: ['creditLimit']
}).refine((data) => {
  // Exchange rate is required for foreign currencies
  if (data.currency !== 'BRL' && !data.exchangeRate) {
    return false;
  }
  return true;
}, {
  message: 'Taxa de câmbio é obrigatória para moedas estrangeiras',
  path: ['exchangeRate']
}).refine((data) => {
  // Credit limit should not be set for non-credit card accounts
  if (data.type !== AccountType.CREDIT_CARD && data.creditLimit) {
    return false;
  }
  return true;
}, {
  message: 'Limite de crédito só pode ser definido para cartões de crédito',
  path: ['creditLimit']
});

/**
 * Schema for updating an account
 */
export const UpdateAccountSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim()
    .optional(),
  creditLimit: z.number()
    .positive('Limite de crédito deve ser positivo')
    .max(*********.99, 'Limite de crédito muito alto')
    .optional()
    .transform(val => val ? Number(val.toFixed(2)) : undefined),
  exchangeRate: z.number()
    .positive('Taxa de câmbio deve ser positiva')
    .max(999999.999999, 'Taxa de câmbio muito alta')
    .optional()
    .transform(val => val ? Number(val.toFixed(6)) : undefined),
  includeInTotal: z.boolean().optional(),
  logoPath: z.string()
    .max(255, 'Caminho do logo muito longo')
    .optional(),
  familyMemberIds: z.array(z.string().cuid('ID de membro da família inválido'))
    .min(1, 'Pelo menos um membro da família deve ser associado')
    .max(10, 'Máximo de 10 membros da família por conta')
    .optional()
});

/**
 * Schema for account query filters
 */
export const AccountFiltersSchema = z.object({
  name: z.string().optional(),
  type: z.nativeEnum(AccountType).optional(),
  currency: z.enum(SUPPORTED_CURRENCIES).optional(),
  includeInTotal: z.string()
    .transform(val => val === 'true')
    .optional(),
  familyMemberId: z.string().cuid('ID de membro da família inválido').optional(),
  includeArchived: z.string()
    .transform(val => val === 'true')
    .optional(),
  includeDeleted: z.string()
    .transform(val => val === 'true')
    .optional(),
  page: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0, 'Página deve ser maior que 0')
    .optional(),
  limit: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0 && val <= 100, 'Limite deve ser entre 1 e 100')
    .optional()
});

/**
 * Schema for archiving/unarchiving account
 */
export const ArchiveAccountSchema = z.object({
  archived: z.boolean()
});

/**
 * Schema for SVG logo upload
 */
export const LogoUploadSchema = z.object({
  file: z.object({
    mimetype: z.literal('image/svg+xml', {
      errorMap: () => ({ message: 'Apenas arquivos SVG são permitidos' })
    }),
    size: z.number()
      .max(1024 * 1024, 'Arquivo SVG deve ter no máximo 1MB'),
    originalname: z.string()
      .regex(/\.svg$/i, 'Arquivo deve ter extensão .svg')
  })
});

/**
 * Type definitions
 */
export type CreateAccountRequest = z.infer<typeof CreateAccountSchema>;
export type UpdateAccountRequest = z.infer<typeof UpdateAccountSchema>;
export type AccountFilters = z.infer<typeof AccountFiltersSchema>;
export type ArchiveAccountRequest = z.infer<typeof ArchiveAccountSchema>;
export type LogoUploadRequest = z.infer<typeof LogoUploadSchema>;

/**
 * Account response type
 */
export interface AccountResponse {
  id: string;
  name: string;
  type: AccountType;
  currency: string;
  creditLimit?: number;
  exchangeRate?: number;
  includeInTotal: boolean;
  logoPath?: string;
  archived: boolean;
  createdAt: string;
  updatedAt: string;
  version: number;
  // Calculated fields
  currentBalance?: number;
  availableBalance?: number;
  balanceInBRL?: number;
  // Related data
  familyMembers?: Array<{
    id: string;
    name: string;
    color: string;
  }>;
}

/**
 * Paginated accounts response
 */
export interface PaginatedAccountsResponse {
  data: AccountResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
