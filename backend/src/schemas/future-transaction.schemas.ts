import { z } from 'zod';
import { TransactionType } from '@prisma/client';

/**
 * Schema for validating future transaction creation
 */
export const createFutureTransactionSchema = z.object({
  description: z.string().min(1, 'Description is required').max(255, 'Description too long'),
  amount: z.number().positive('Amount must be positive'),
  transactionDate: z.string().refine((date) => {
    const transactionDate = new Date(date);
    const now = new Date();
    return transactionDate > now;
  }, 'Transaction date must be in the future'),
  type: z.nativeEnum(TransactionType),
  accountId: z.string().cuid('Invalid account ID'),
  categoryId: z.string().cuid('Invalid category ID').optional(),
  destinationAccountId: z.string().cuid('Invalid destination account ID').optional(),
  exchangeRate: z.number().positive('Exchange rate must be positive').optional(),
  installmentNumber: z.number().int().positive().optional(),
  totalInstallments: z.number().int().positive().optional(),
  parentTransactionId: z.string().cuid('Invalid parent transaction ID').optional(),
  familyMemberIds: z.array(z.string().cuid('Invalid family member ID')).optional(),
  tagIds: z.array(z.string().cuid('Invalid tag ID')).optional(),
  sourceCurrency: z.string().length(3, 'Currency must be 3 characters').optional(),
  destinationCurrency: z.string().length(3, 'Currency must be 3 characters').optional(),
  sourceAmount: z.number().positive('Source amount must be positive').optional(),
  destinationAmount: z.number().positive('Destination amount must be positive').optional(),
  isFuture: z.literal(true) // Must be true for future transactions
}).refine((data) => {
  // Validate transfer-specific fields
  if (data.type === TransactionType.TRANSFER) {
    return data.destinationAccountId !== undefined;
  }
  return true;
}, {
  message: 'Destination account is required for transfer transactions',
  path: ['destinationAccountId']
}).refine((data) => {
  // Validate installment fields
  if (data.installmentNumber !== undefined || data.totalInstallments !== undefined) {
    return data.installmentNumber !== undefined && 
           data.totalInstallments !== undefined &&
           data.installmentNumber <= data.totalInstallments;
  }
  return true;
}, {
  message: 'Invalid installment configuration',
  path: ['installmentNumber']
});

/**
 * Schema for validating future transaction updates
 */
export const updateFutureTransactionSchema = z.object({
  description: z.string().min(1, 'Description is required').max(255, 'Description too long').optional(),
  amount: z.number().positive('Amount must be positive').optional(),
  transactionDate: z.string().refine((date) => {
    const transactionDate = new Date(date);
    const now = new Date();
    return transactionDate > now;
  }, 'Transaction date must be in the future').optional(),
  categoryId: z.string().cuid('Invalid category ID').optional(),
  destinationAccountId: z.string().cuid('Invalid destination account ID').optional(),
  exchangeRate: z.number().positive('Exchange rate must be positive').optional(),
  familyMemberIds: z.array(z.string().cuid('Invalid family member ID')).optional(),
  tagIds: z.array(z.string().cuid('Invalid tag ID')).optional(),
  sourceCurrency: z.string().length(3, 'Currency must be 3 characters').optional(),
  destinationCurrency: z.string().length(3, 'Currency must be 3 characters').optional(),
  sourceAmount: z.number().positive('Source amount must be positive').optional(),
  destinationAmount: z.number().positive('Destination amount must be positive').optional()
});

/**
 * Schema for validating projected balance query parameters
 */
export const projectedBalanceQuerySchema = z.object({
  accountIds: z.union([
    z.string().transform(str => str.split(',')),
    z.array(z.string())
  ]).optional(),
  currencies: z.union([
    z.string().transform(str => str.split(',')),
    z.array(z.string())
  ]).optional(),
  projectionDate: z.string().refine((date) => {
    const projectionDate = new Date(date);
    const now = new Date();
    return projectionDate >= now;
  }, 'Projection date must be today or in the future').optional(),
  includeRecurring: z.union([
    z.string().transform(str => str.toLowerCase() === 'true'),
    z.boolean()
  ]).optional().default(false)
});

/**
 * Schema for validating future transaction filters
 */
export const futureTransactionFiltersSchema = z.object({
  accountId: z.string().cuid('Invalid account ID').optional(),
  categoryId: z.string().cuid('Invalid category ID').optional(),
  familyMemberId: z.string().cuid('Invalid family member ID').optional(),
  startDate: z.string().datetime('Invalid start date format').optional(),
  endDate: z.string().datetime('Invalid end date format').optional(),
  page: z.union([
    z.string().transform(str => parseInt(str)),
    z.number()
  ]).refine(val => Number.isInteger(val) && val > 0, 'Page must be a positive integer').optional().default(1),
  limit: z.union([
    z.string().transform(str => parseInt(str)),
    z.number()
  ]).refine(val => Number.isInteger(val) && val > 0 && val <= 100, 'Limit must be a positive integer not exceeding 100').optional().default(20),
  sortBy: z.enum(['transactionDate', 'amount', 'description', 'createdAt']).optional().default('transactionDate'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc')
}).refine((data) => {
  // Validate date range
  if (data.startDate && data.endDate) {
    return new Date(data.startDate as string) <= new Date(data.endDate as string);
  }
  return true;
}, {
  message: 'Start date must be before or equal to end date',
  path: ['endDate']
});

/**
 * Schema for validating manual processing request
 */
export const manualProcessingSchema = z.object({
  targetDate: z.string().datetime('Invalid target date format').refine((date) => {
    const targetDate = new Date(date);
    const now = new Date();
    const maxPastDate = new Date();
    maxPastDate.setDate(maxPastDate.getDate() - 30); // Allow up to 30 days in the past
    
    return targetDate >= maxPastDate && targetDate <= now;
  }, 'Target date must be within the last 30 days and not in the future').optional()
});

/**
 * Schema for validating future transaction date constraints
 */
export const futureDateValidationSchema = z.object({
  transactionDate: z.string().datetime('Invalid date format').refine((date) => {
    const transactionDate = new Date(date);
    const now = new Date();
    const maxFutureDate = new Date();
    maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 5); // Max 5 years in the future
    
    return transactionDate > now && transactionDate <= maxFutureDate;
  }, 'Transaction date must be in the future but not more than 5 years ahead')
});

/**
 * Type definitions derived from schemas
 */
export type CreateFutureTransactionData = z.infer<typeof createFutureTransactionSchema>;
export type UpdateFutureTransactionData = z.infer<typeof updateFutureTransactionSchema>;
export type ProjectedBalanceQuery = z.infer<typeof projectedBalanceQuerySchema>;
export type FutureTransactionFilters = z.infer<typeof futureTransactionFiltersSchema>;
export type ManualProcessingRequest = z.infer<typeof manualProcessingSchema>;
export type FutureDateValidation = z.infer<typeof futureDateValidationSchema>;

/**
 * Validation middleware factory for future transactions
 */
export const validateFutureTransactionDate = (dateField: string = 'transactionDate') => {
  return (req: any, res: any, next: any) => {
    try {
      const dateValue = req.body[dateField];
      
      if (!dateValue) {
        return res.status(400).json({
          success: false,
          error: `${dateField} is required`,
          code: 'VALIDATION_ERROR'
        });
      }

      futureDateValidationSchema.parse({ transactionDate: dateValue });
      next();
      
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Invalid future transaction date',
          details: error.errors,
          code: 'VALIDATION_ERROR'
        });
      }
      
      next(error);
    }
  };
};

/**
 * Helper function to validate if a date is appropriate for future transactions
 */
export const isValidFutureDate = (date: string | Date): boolean => {
  try {
    const transactionDate = new Date(date);
    const now = new Date();
    const maxFutureDate = new Date();
    maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 5);
    
    return transactionDate > now && transactionDate <= maxFutureDate;
  } catch {
    return false;
  }
};

/**
 * Helper function to calculate days until a future transaction
 */
export const getDaysUntilTransaction = (transactionDate: string | Date): number => {
  const transaction = new Date(transactionDate);
  const now = new Date();
  const diffTime = transaction.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};
