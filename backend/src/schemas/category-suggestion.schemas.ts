import { z } from 'zod';
import { TransactionType } from '@prisma/client';

/**
 * Schema for category suggestion request
 */
export const CategorySuggestionRequestSchema = z.object({
  description: z.string()
    .min(1, 'Descrição é obrigatória')
    .max(500, 'Descrição deve ter no máximo 500 caracteres')
    .trim(),
  amount: z.number()
    .positive('Valor deve ser positivo')
    .max(999999999.99, 'Valor muito alto')
    .optional()
    .transform(val => val ? Number(val.toFixed(2)) : undefined),
  type: z.nativeEnum(TransactionType, {
    errorMap: () => ({ message: 'Tipo de transação inválido' })
  }).optional(),
  userId: z.string()
    .uuid('ID do usuário deve ser um UUID válido')
    .optional(),
  maxSuggestions: z.number()
    .int('Número máximo de sugestões deve ser um inteiro')
    .min(1, 'Deve solicitar pelo menos 1 sugestão')
    .max(10, 'Máximo de 10 sugestões permitidas')
    .optional()
    .default(5),
  minConfidence: z.number()
    .min(0, 'Confiança mínima deve ser entre 0 e 1')
    .max(1, 'Confiança mínima deve ser entre 0 e 1')
    .optional()
    .default(0.3)
});

/**
 * Schema for individual category suggestion
 */
export const CategorySuggestionSchema = z.object({
  categoryId: z.string()
    .min(1, 'ID da categoria é obrigatório'),
  categoryName: z.string()
    .min(1, 'Nome da categoria é obrigatório'),
  confidence: z.number()
    .min(0, 'Confiança deve ser entre 0 e 1')
    .max(1, 'Confiança deve ser entre 0 e 1'),
  reason: z.string()
    .min(1, 'Razão da sugestão é obrigatória'),
  matchType: z.enum(['description', 'amount', 'frequency', 'keyword', 'combined'], {
    errorMap: () => ({ message: 'Tipo de correspondência inválido' })
  })
});

/**
 * Schema for category suggestion response
 */
export const CategorySuggestionResponseSchema = z.object({
  suggestions: z.array(CategorySuggestionSchema)
    .max(10, 'Máximo de 10 sugestões na resposta'),
  metadata: z.object({
    totalSuggestions: z.number()
      .int('Total de sugestões deve ser um inteiro')
      .min(0, 'Total de sugestões deve ser não-negativo'),
    hasHistoricalData: z.boolean(),
    processingTimeMs: z.number()
      .min(0, 'Tempo de processamento deve ser não-negativo')
      .optional(),
    algorithmWeights: z.object({
      description: z.number().min(0).max(1),
      amount: z.number().min(0).max(1),
      frequency: z.number().min(0).max(1),
      keyword: z.number().min(0).max(1)
    }).optional()
  })
});

/**
 * Schema for bulk category suggestion request
 */
export const BulkCategorySuggestionRequestSchema = z.object({
  transactions: z.array(
    z.object({
      id: z.string().optional(),
      description: z.string().min(1, 'Descrição é obrigatória'),
      amount: z.number().positive('Valor deve ser positivo').optional(),
      type: z.nativeEnum(TransactionType).optional()
    })
  ).min(1, 'Pelo menos uma transação é obrigatória')
    .max(50, 'Máximo de 50 transações por lote'),
  userId: z.string().uuid('ID do usuário deve ser um UUID válido').optional(),
  maxSuggestionsPerTransaction: z.number()
    .int('Número máximo de sugestões deve ser um inteiro')
    .min(1, 'Deve solicitar pelo menos 1 sugestão')
    .max(5, 'Máximo de 5 sugestões por transação')
    .optional()
    .default(3),
  minConfidence: z.number()
    .min(0, 'Confiança mínima deve ser entre 0 e 1')
    .max(1, 'Confiança mínima deve ser entre 0 e 1')
    .optional()
    .default(0.3)
});

/**
 * Schema for bulk category suggestion response
 */
export const BulkCategorySuggestionResponseSchema = z.object({
  results: z.array(
    z.object({
      transactionId: z.string().optional(),
      transactionDescription: z.string(),
      suggestions: z.array(CategorySuggestionSchema),
      error: z.string().optional()
    })
  ),
  metadata: z.object({
    totalTransactions: z.number().int().min(0),
    successfulSuggestions: z.number().int().min(0),
    failedSuggestions: z.number().int().min(0),
    averageConfidence: z.number().min(0).max(1).optional(),
    processingTimeMs: z.number().min(0).optional()
  })
});

/**
 * Schema for suggestion algorithm configuration
 */
export const SuggestionConfigSchema = z.object({
  descriptionWeight: z.number()
    .min(0, 'Peso da descrição deve ser entre 0 e 1')
    .max(1, 'Peso da descrição deve ser entre 0 e 1')
    .optional()
    .default(0.4),
  amountWeight: z.number()
    .min(0, 'Peso do valor deve ser entre 0 e 1')
    .max(1, 'Peso do valor deve ser entre 0 e 1')
    .optional()
    .default(0.2),
  frequencyWeight: z.number()
    .min(0, 'Peso da frequência deve ser entre 0 e 1')
    .max(1, 'Peso da frequência deve ser entre 0 e 1')
    .optional()
    .default(0.2),
  keywordWeight: z.number()
    .min(0, 'Peso das palavras-chave deve ser entre 0 e 1')
    .max(1, 'Peso das palavras-chave deve ser entre 0 e 1')
    .optional()
    .default(0.2),
  minConfidence: z.number()
    .min(0, 'Confiança mínima deve ser entre 0 e 1')
    .max(1, 'Confiança mínima deve ser entre 0 e 1')
    .optional()
    .default(0.3),
  maxSuggestions: z.number()
    .int('Número máximo de sugestões deve ser um inteiro')
    .min(1, 'Deve permitir pelo menos 1 sugestão')
    .max(10, 'Máximo de 10 sugestões permitidas')
    .optional()
    .default(5)
}).refine(
  (data) => {
    const totalWeight = data.descriptionWeight + data.amountWeight + 
                       data.frequencyWeight + data.keywordWeight;
    return Math.abs(totalWeight - 1.0) < 0.01; // Allow small floating point errors
  },
  {
    message: 'A soma dos pesos deve ser igual a 1.0',
    path: ['weights']
  }
);

/**
 * Schema for suggestion feedback (for learning/improvement)
 */
export const SuggestionFeedbackSchema = z.object({
  transactionId: z.string()
    .min(1, 'ID da transação é obrigatório'),
  suggestedCategoryId: z.string()
    .min(1, 'ID da categoria sugerida é obrigatório'),
  actualCategoryId: z.string()
    .min(1, 'ID da categoria real é obrigatório'),
  wasAccepted: z.boolean(),
  confidence: z.number()
    .min(0, 'Confiança deve ser entre 0 e 1')
    .max(1, 'Confiança deve ser entre 0 e 1'),
  matchType: z.enum(['description', 'amount', 'frequency', 'keyword', 'combined']),
  userId: z.string()
    .uuid('ID do usuário deve ser um UUID válido')
    .optional(),
  feedback: z.string()
    .max(500, 'Feedback deve ter no máximo 500 caracteres')
    .optional(),
  timestamp: z.date()
    .optional()
    .default(() => new Date())
});

/**
 * Type definitions derived from schemas
 */
export type CategorySuggestionRequest = z.infer<typeof CategorySuggestionRequestSchema>;
export type CategorySuggestion = z.infer<typeof CategorySuggestionSchema>;
export type CategorySuggestionResponse = z.infer<typeof CategorySuggestionResponseSchema>;
export type BulkCategorySuggestionRequest = z.infer<typeof BulkCategorySuggestionRequestSchema>;
export type BulkCategorySuggestionResponse = z.infer<typeof BulkCategorySuggestionResponseSchema>;
export type SuggestionConfig = z.infer<typeof SuggestionConfigSchema>;
export type SuggestionFeedback = z.infer<typeof SuggestionFeedbackSchema>;

/**
 * Validation helper functions
 */
export const validateCategorySuggestionRequest = (data: unknown) => {
  return CategorySuggestionRequestSchema.safeParse(data);
};

export const validateBulkCategorySuggestionRequest = (data: unknown) => {
  return BulkCategorySuggestionRequestSchema.safeParse(data);
};

export const validateSuggestionConfig = (data: unknown) => {
  return SuggestionConfigSchema.safeParse(data);
};

export const validateSuggestionFeedback = (data: unknown) => {
  return SuggestionFeedbackSchema.safeParse(data);
};
