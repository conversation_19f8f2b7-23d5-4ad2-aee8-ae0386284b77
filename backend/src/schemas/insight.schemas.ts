import { z } from 'zod';

/**
 * Enums for Insight validation
 */
export const InsightTypeEnum = z.enum([
  'SPENDING_PATTERN',
  'BUDGET_ALERT',
  'GOAL_PROGRESS',
  'ANOMALY_DETECTION',
  'TREND_ANALYSIS',
  'CATEGORY_ANALYSIS',
  'SAVINGS_OPPORTUNITY',
  'CASH_FLOW_ANALYSIS',
  'RECURRING_EXPENSE',
  'SEASONAL_PATTERN'
]);

export const InsightPriorityEnum = z.enum([
  'LOW',
  'MEDIUM',
  'HIGH',
  'CRITICAL'
]);

export const InsightStatusEnum = z.enum([
  'NEW',
  'VIEWED',
  'DISMISSED',
  'ACTED_UPON'
]);

/**
 * Schema for creating a new insight
 */
export const CreateInsightSchema = z.object({
  type: InsightTypeEnum,
  priority: InsightPriorityEnum,
  title: z.string()
    .min(5, '<PERSON><PERSON>tu<PERSON> deve ter pelo menos 5 caracteres')
    .max(200, 'Título deve ter no máximo 200 caracteres')
    .trim(),
  description: z.string()
    .min(10, 'Descrição deve ter pelo menos 10 caracteres')
    .max(1000, 'Descrição deve ter no máximo 1000 caracteres')
    .trim(),
  data: z.record(z.any()).optional(),
  categoryId: z.string().cuid('ID da categoria inválido').optional(),
  accountId: z.string().cuid('ID da conta inválido').optional(),
  goalId: z.string().cuid('ID da meta inválido').optional(),
  periodStart: z.string().datetime('Data de início inválida').optional(),
  periodEnd: z.string().datetime('Data de fim inválida').optional(),
  recommendations: z.record(z.any()).optional(),
  expiresAt: z.string().datetime('Data de expiração inválida').optional(),
  relevanceScore: z.number()
    .min(0, 'Score de relevância deve ser maior ou igual a 0')
    .max(10, 'Score de relevância deve ser menor ou igual a 10')
    .optional()
});

/**
 * Schema for updating an insight
 */
export const UpdateInsightSchema = z.object({
  status: InsightStatusEnum.optional(),
  actionTaken: z.boolean().optional(),
  relevanceScore: z.number()
    .min(0, 'Score de relevância deve ser maior ou igual a 0')
    .max(10, 'Score de relevância deve ser menor ou igual a 10')
    .optional()
});

/**
 * Schema for insight query filters
 */
export const InsightFiltersSchema = z.object({
  type: InsightTypeEnum.optional(),
  priority: InsightPriorityEnum.optional(),
  status: InsightStatusEnum.optional(),
  categoryId: z.string().cuid('ID da categoria inválido').optional(),
  accountId: z.string().cuid('ID da conta inválido').optional(),
  goalId: z.string().cuid('ID da meta inválido').optional(),
  fromDate: z.string().datetime('Data de início inválida').optional(),
  toDate: z.string().datetime('Data de fim inválida').optional(),
  includeExpired: z.string()
    .transform(val => val === 'true')
    .optional(),
  minRelevanceScore: z.string()
    .transform(val => parseFloat(val))
    .refine(val => val >= 0 && val <= 10, 'Score mínimo deve ser entre 0 e 10')
    .optional(),
  page: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0, 'Página deve ser maior que 0')
    .optional(),
  limit: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0 && val <= 100, 'Limite deve ser entre 1 e 100')
    .optional()
});

/**
 * Schema for bulk insight operations
 */
export const BulkInsightActionSchema = z.object({
  insightIds: z.array(z.string().cuid('ID do insight inválido'))
    .min(1, 'Pelo menos um insight deve ser selecionado')
    .max(50, 'Máximo de 50 insights por operação'),
  action: z.enum(['MARK_VIEWED', 'DISMISS', 'MARK_ACTED_UPON'])
});

/**
 * Schema for insight generation request
 */
export const GenerateInsightsSchema = z.object({
  types: z.array(InsightTypeEnum).optional(),
  forceRegenerate: z.boolean().optional().default(false),
  periodStart: z.string().datetime('Data de início inválida').optional(),
  periodEnd: z.string().datetime('Data de fim inválida').optional(),
  categoryIds: z.array(z.string().cuid('ID da categoria inválido')).optional(),
  accountIds: z.array(z.string().cuid('ID da conta inválido')).optional(),
  goalIds: z.array(z.string().cuid('ID da meta inválido')).optional()
});

/**
 * Type definitions
 */
export type CreateInsightRequest = z.infer<typeof CreateInsightSchema>;
export type UpdateInsightRequest = z.infer<typeof UpdateInsightSchema>;
export type InsightFilters = z.infer<typeof InsightFiltersSchema>;
export type BulkInsightActionRequest = z.infer<typeof BulkInsightActionSchema>;
export type GenerateInsightsRequest = z.infer<typeof GenerateInsightsSchema>;

/**
 * Insight response type
 */
export interface InsightResponse {
  id: string;
  type: string;
  priority: string;
  status: string;
  title: string;
  description: string;
  data?: Record<string, any>;
  categoryId?: string;
  accountId?: string;
  goalId?: string;
  periodStart?: string;
  periodEnd?: string;
  recommendations?: Record<string, any>;
  actionTaken: boolean;
  expiresAt?: string;
  relevanceScore?: number;
  createdAt: string;
  updatedAt: string;
  version: number;
  // Related data
  category?: {
    id: string;
    name: string;
    color?: string;
  };
  account?: {
    id: string;
    name: string;
    type: string;
  };
  goal?: {
    id: string;
    name: string;
    targetAmount: number;
    currentAmount: number;
  };
}

/**
 * Paginated insights response
 */
export interface PaginatedInsightsResponse {
  data: InsightResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  summary: {
    totalByType: Record<string, number>;
    totalByPriority: Record<string, number>;
    totalByStatus: Record<string, number>;
  };
}

/**
 * Insight generation response
 */
export interface InsightGenerationResponse {
  generated: number;
  updated: number;
  expired: number;
  insights: InsightResponse[];
  processingTime: number;
}

/**
 * Insight analytics response
 */
export interface InsightAnalyticsResponse {
  totalInsights: number;
  newInsights: number;
  actionableInsights: number;
  averageRelevanceScore: number;
  topCategories: Array<{
    categoryId: string;
    categoryName: string;
    insightCount: number;
  }>;
  typeDistribution: Record<string, number>;
  priorityDistribution: Record<string, number>;
  statusDistribution: Record<string, number>;
}
