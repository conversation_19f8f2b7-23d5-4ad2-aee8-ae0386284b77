import { z } from 'zod';

/**
 * Schema for creating a new family member
 */
export const CreateFamilyMemberSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Cor deve estar no formato hexadecimal (#RRGGBB)')
});

/**
 * Schema for updating a family member
 */
export const UpdateFamilyMemberSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim()
    .optional(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Cor deve estar no formato hexadecimal (#RRGGBB)')
    .optional()
});

/**
 * Schema for family member query filters
 */
export const FamilyMemberFiltersSchema = z.object({
  name: z.string().optional(),
  includeArchived: z.string()
    .transform(val => val === 'true')
    .optional(),
  includeDeleted: z.string()
    .transform(val => val === 'true')
    .optional(),
  page: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0, 'Página deve ser maior que 0')
    .optional(),
  limit: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0 && val <= 100, 'Limite deve ser entre 1 e 100')
    .optional()
});

/**
 * Schema for archiving/unarchiving family member
 */
export const ArchiveFamilyMemberSchema = z.object({
  archived: z.boolean()
});

/**
 * Type definitions
 */
export type CreateFamilyMemberRequest = z.infer<typeof CreateFamilyMemberSchema>;
export type UpdateFamilyMemberRequest = z.infer<typeof UpdateFamilyMemberSchema>;
export type FamilyMemberFilters = z.infer<typeof FamilyMemberFiltersSchema>;
export type ArchiveFamilyMemberRequest = z.infer<typeof ArchiveFamilyMemberSchema>;

/**
 * Family member response type
 */
export interface FamilyMemberResponse {
  id: string;
  name: string;
  color: string;
  archived: boolean;
  createdAt: string;
  updatedAt: string;
  version: number;
}

/**
 * Paginated family members response
 */
export interface PaginatedFamilyMembersResponse {
  data: FamilyMemberResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
