import { z } from 'zod';

/**
 * Schema for creating a new tag
 */
export const CreateTagSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(50, 'Nome deve ter no máximo 50 caracteres')
    .trim(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Cor deve estar no formato hexadecimal (#RRGGBB)')
});

/**
 * Schema for updating a tag
 */
export const UpdateTagSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(50, 'Nome deve ter no máximo 50 caracteres')
    .trim()
    .optional(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Cor deve estar no formato hexadecimal (#RRGGBB)')
    .optional()
});

/**
 * Schema for tag filters and search
 */
export const TagFiltersSchema = z.object({
  search: z.string()
    .min(1, 'Busca deve ter pelo menos 1 caracter')
    .optional(),
  includeArchived: z.union([z.boolean(), z.string()])
    .transform(val => val === true || val === 'true')
    .optional()
    .default(false),
  page: z.union([z.number(), z.string()])
    .transform(val => typeof val === 'string' ? parseInt(val, 10) : val)
    .pipe(z.number().int('Página deve ser um número inteiro').min(1, 'Página deve ser maior que 0'))
    .optional()
    .default(1),
  limit: z.union([z.number(), z.string()])
    .transform(val => typeof val === 'string' ? parseInt(val, 10) : val)
    .pipe(z.number().int('Limite deve ser um número inteiro').min(1, 'Limite deve ser maior que 0').max(100, 'Limite deve ser menor ou igual a 100'))
    .optional()
    .default(20),
  sortBy: z.enum(['name', 'usageCount', 'createdAt'])
    .optional()
    .default('name'),
  sortOrder: z.enum(['asc', 'desc'])
    .optional()
    .default('asc')
});

/**
 * Schema for archiving/unarchiving tag
 */
export const ArchiveTagSchema = z.object({
  archived: z.boolean()
});

/**
 * Schema for tag ID validation
 */
export const TagIdSchema = z.object({
  id: z.string().cuid('ID da tag inválido')
});

/**
 * Type definitions for TypeScript
 */
export type CreateTagData = z.infer<typeof CreateTagSchema>;
export type UpdateTagData = z.infer<typeof UpdateTagSchema>;
export type TagFilters = z.infer<typeof TagFiltersSchema>;
export type ArchiveTagData = z.infer<typeof ArchiveTagSchema>;
export type TagIdData = z.infer<typeof TagIdSchema>;

/**
 * Response type for tag operations
 */
export interface TagResponse {
  id: string;
  name: string;
  color: string;
  isArchived: boolean;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

/**
 * Response type for paginated tag list
 */
export interface TagListResponse {
  data: TagResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
