import { z } from 'zod';

/**
 * Schema for creating a new financial goal
 */
export const CreateFinancialGoalSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim(),
  targetAmount: z.number()
    .positive('Valor alvo deve ser positivo')
    .max(999999999.99, 'Valor alvo muito alto')
    .transform(val => Number(val.toFixed(2))),
  currentAmount: z.number()
    .min(0, 'Valor atual deve ser maior ou igual a zero')
    .max(999999999.99, 'Valor atual muito alto')
    .transform(val => Number(val.toFixed(2)))
    .default(0),
  targetDate: z.string()
    .datetime('Data alvo inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  familyMemberIds: z.array(z.string().cuid('ID de membro da família inválido'))
    .max(10, 'Máximo de 10 membros da família por meta')
    .optional()
    .default([])
}).refine((data) => {
  // Validate that target date is in the future
  if (data.targetDate) {
    const now = new Date();
    const targetDate = new Date(data.targetDate);
    return targetDate > now;
  }
  return true;
}, {
  message: 'Data alvo deve ser no futuro',
  path: ['targetDate']
}).refine((data) => {
  // Validate that current amount is not greater than target amount
  return data.currentAmount <= data.targetAmount;
}, {
  message: 'Valor atual não pode ser maior que o valor alvo',
  path: ['currentAmount']
});

/**
 * Schema for updating a financial goal
 */
export const UpdateFinancialGoalSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim()
    .optional(),
  targetAmount: z.number()
    .positive('Valor alvo deve ser positivo')
    .max(999999999.99, 'Valor alvo muito alto')
    .transform(val => Number(val.toFixed(2)))
    .optional(),
  targetDate: z.string()
    .datetime('Data alvo inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  familyMemberIds: z.array(z.string().cuid('ID de membro da família inválido'))
    .min(1, 'Pelo menos um membro da família deve ser associado')
    .max(10, 'Máximo de 10 membros da família por meta')
    .optional()
}).refine((data) => {
  // Validate that target date is in the future
  if (data.targetDate) {
    const now = new Date();
    const targetDate = new Date(data.targetDate);
    return targetDate > now;
  }
  return true;
}, {
  message: 'Data alvo deve ser no futuro',
  path: ['targetDate']
});

/**
 * Schema for updating goal progress
 */
export const UpdateGoalProgressSchema = z.object({
  amount: z.number()
    .min(0, 'Valor deve ser maior ou igual a zero')
    .max(999999999.99, 'Valor muito alto')
    .transform(val => Number(val.toFixed(2))),
  operation: z.enum(['add', 'subtract', 'set'], {
    errorMap: () => ({ message: 'Operação deve ser add, subtract ou set' })
  }).default('add'),
  description: z.string()
    .min(1, 'Descrição é obrigatória')
    .max(255, 'Descrição deve ter no máximo 255 caracteres')
    .trim()
    .optional()
});

/**
 * Schema for creating a goal milestone
 */
export const CreateGoalMilestoneSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim(),
  targetAmount: z.number()
    .positive('Valor alvo deve ser positivo')
    .max(999999999.99, 'Valor alvo muito alto')
    .transform(val => Number(val.toFixed(2))),
  targetDate: z.string()
    .datetime('Data alvo inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
});
// Note: Removed future date validation for creation to allow past dates

/**
 * Schema for updating a goal milestone
 */
export const UpdateGoalMilestoneSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim()
    .optional(),
  targetAmount: z.number()
    .positive('Valor alvo deve ser positivo')
    .max(999999999.99, 'Valor alvo muito alto')
    .transform(val => Number(val.toFixed(2)))
    .optional(),
  targetDate: z.string()
    .datetime('Data alvo inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  isCompleted: z.boolean()
    .optional()
});
// Note: Removed future date validation for updates to allow editing existing milestones

/**
 * Schema for financial goal filters
 */
export const FinancialGoalFiltersSchema = z.object({
  familyMemberId: z.string()
    .cuid('ID do membro da família inválido')
    .optional(),
  status: z.enum(['active', 'completed', 'overdue', 'all'])
    .default('active'),
  minTargetAmount: z.number()
    .positive('Valor mínimo deve ser positivo')
    .optional(),
  maxTargetAmount: z.number()
    .positive('Valor máximo deve ser positivo')
    .optional(),
  targetDateFrom: z.string()
    .datetime('Data inicial inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  targetDateTo: z.string()
    .datetime('Data final inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  page: z.number()
    .int('Página deve ser um número inteiro')
    .min(1, 'Página deve ser maior que 0')
    .default(1),
  limit: z.number()
    .int('Limite deve ser um número inteiro')
    .min(1, 'Limite deve ser maior que 0')
    .max(100, 'Limite deve ser menor ou igual a 100')
    .default(20),
  sortBy: z.enum(['name', 'targetAmount', 'currentAmount', 'targetDate', 'createdAt', 'progress'])
    .default('createdAt'),
  sortOrder: z.enum(['asc', 'desc'])
    .default('desc'),
  includeCompleted: z.boolean()
    .default(false),
  includeMilestones: z.boolean()
    .default(false)
}).refine((data) => {
  // Validate amount range
  if (data.minTargetAmount && data.maxTargetAmount) {
    return data.minTargetAmount <= data.maxTargetAmount;
  }
  return true;
}, {
  message: 'Valor mínimo deve ser menor ou igual ao valor máximo',
  path: ['maxTargetAmount']
}).refine((data) => {
  // Validate date range
  if (data.targetDateFrom && data.targetDateTo) {
    return data.targetDateFrom <= data.targetDateTo;
  }
  return true;
}, {
  message: 'Data inicial deve ser anterior ou igual à data final',
  path: ['targetDateTo']
});

/**
 * Schema for milestone filters
 */
export const MilestoneFiltersSchema = z.object({
  goalId: z.string()
    .cuid('ID da meta inválido'),
  status: z.enum(['pending', 'completed', 'overdue', 'all'])
    .default('all'),
  targetDateFrom: z.string()
    .datetime('Data inicial inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  targetDateTo: z.string()
    .datetime('Data final inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  sortBy: z.enum(['name', 'targetAmount', 'targetDate', 'createdAt'])
    .default('targetDate'),
  sortOrder: z.enum(['asc', 'desc'])
    .default('asc')
}).refine((data) => {
  // Validate date range
  if (data.targetDateFrom && data.targetDateTo) {
    return data.targetDateFrom <= data.targetDateTo;
  }
  return true;
}, {
  message: 'Data inicial deve ser anterior ou igual à data final',
  path: ['targetDateTo']
});

/**
 * Type definitions for request/response
 */
export type CreateFinancialGoalRequest = z.infer<typeof CreateFinancialGoalSchema>;
export type UpdateFinancialGoalRequest = z.infer<typeof UpdateFinancialGoalSchema>;
export type UpdateGoalProgressRequest = z.infer<typeof UpdateGoalProgressSchema>;
export type CreateGoalMilestoneRequest = z.infer<typeof CreateGoalMilestoneSchema>;
export type UpdateGoalMilestoneRequest = z.infer<typeof UpdateGoalMilestoneSchema>;
export type FinancialGoalFilters = z.infer<typeof FinancialGoalFiltersSchema>;
export type MilestoneFilters = z.infer<typeof MilestoneFiltersSchema>;

/**
 * Financial goal response interface
 */
export interface FinancialGoalResponse {
  id: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  targetDate?: string;
  createdAt: string;
  updatedAt: string;
  version: number;
  // Progress calculations
  progress: GoalProgress;
  // Related data
  members: Array<{
    id: string;
    name: string;
    color: string;
  }>;
  milestones?: GoalMilestoneResponse[];
}

/**
 * Goal milestone response interface
 */
export interface GoalMilestoneResponse {
  id: string;
  goalId: string;
  name: string;
  targetAmount: number;
  targetDate: string;
  isCompleted: boolean;
  createdAt: string;
  updatedAt: string;
  version: number;
}

/**
 * Goal progress interface
 */
export interface GoalProgress {
  percentage: number;
  remainingAmount: number;
  isCompleted: boolean;
  isOverdue: boolean;
  daysRemaining?: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue';
  milestoneProgress?: {
    completed: number;
    total: number;
    nextMilestone?: GoalMilestoneResponse;
  };
}

/**
 * Paginated financial goals response
 */
export interface PaginatedFinancialGoalsResponse {
  data: FinancialGoalResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Goal summary interface
 */
export interface GoalSummary {
  totalGoals: number;
  completedGoals: number;
  activeGoals: number;
  overdueGoals: number;
  totalTargetAmount: number;
  totalCurrentAmount: number;
  overallProgress: number;
  averageProgress: number;
}

/**
 * Goal progress update response
 */
export interface GoalProgressUpdateResponse {
  goalId: string;
  previousAmount: number;
  newAmount: number;
  operation: 'add' | 'subtract' | 'set';
  amountChanged: number;
  newProgress: GoalProgress;
  message: string;
}
