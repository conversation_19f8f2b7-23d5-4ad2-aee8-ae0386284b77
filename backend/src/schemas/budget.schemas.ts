import { z } from 'zod';

/**
 * Schema for creating a new budget
 */
export const CreateBudgetSchema = z.object({
  plannedAmount: z.number()
    .positive('Valor planejado deve ser positivo')
    .max(999999999.99, 'Valor planejado muito alto')
    .transform(val => Number(val.toFixed(2))),
  month: z.number()
    .int('Mês deve ser um número inteiro')
    .min(1, 'Mês deve estar entre 1 e 12')
    .max(12, 'Mês deve estar entre 1 e 12'),
  year: z.number()
    .int('Ano deve ser um número inteiro')
    .min(2020, 'Ano deve ser maior ou igual a 2020')
    .max(2050, 'Ano deve ser menor ou igual a 2050'),
  categoryId: z.string()
    .cuid('ID da categoria inválido'),
  familyMemberId: z.string()
    .cuid('ID do membro da família inválido')
    .optional()
}).refine((data) => {
  // Validate that the date is not in the past (more than 1 month ago)
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1; // getMonth() returns 0-11
  
  if (data.year < currentYear) {
    return data.year >= currentYear - 1; // Allow previous year
  }
  
  if (data.year === currentYear) {
    return data.month >= currentMonth - 1; // Allow previous month
  }
  
  return true; // Future dates are always valid
}, {
  message: 'Não é possível criar orçamento para períodos muito antigos',
  path: ['month']
});

/**
 * Schema for updating a budget
 */
export const UpdateBudgetSchema = z.object({
  plannedAmount: z.number()
    .positive('Valor planejado deve ser positivo')
    .max(999999999.99, 'Valor planejado muito alto')
    .transform(val => Number(val.toFixed(2)))
    .optional(),
  month: z.number()
    .int('Mês deve ser um número inteiro')
    .min(1, 'Mês deve estar entre 1 e 12')
    .max(12, 'Mês deve estar entre 1 e 12')
    .optional(),
  year: z.number()
    .int('Ano deve ser um número inteiro')
    .min(2020, 'Ano deve ser maior ou igual a 2020')
    .max(2050, 'Ano deve ser menor ou igual a 2050')
    .optional(),
  categoryId: z.string()
    .cuid('ID da categoria inválido')
    .optional(),
  familyMemberId: z.string()
    .cuid('ID do membro da família inválido')
    .optional()
}).refine((data) => {
  // If both month and year are provided, validate the date
  if (data.month !== undefined && data.year !== undefined) {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    
    if (data.year < currentYear) {
      return data.year >= currentYear - 1;
    }
    
    if (data.year === currentYear) {
      return data.month >= currentMonth - 1;
    }
  }
  
  return true;
}, {
  message: 'Não é possível atualizar orçamento para períodos muito antigos',
  path: ['month']
});

/**
 * Schema for budget filters
 */
export const BudgetFiltersSchema = z.object({
  categoryId: z.string()
    .cuid('ID da categoria inválido')
    .optional(),
  familyMemberId: z.string()
    .cuid('ID do membro da família inválido')
    .optional(),
  month: z.number()
    .int('Mês deve ser um número inteiro')
    .min(1, 'Mês deve estar entre 1 e 12')
    .max(12, 'Mês deve estar entre 1 e 12')
    .optional(),
  year: z.number()
    .int('Ano deve ser um número inteiro')
    .min(2020, 'Ano deve ser maior ou igual a 2020')
    .max(2050, 'Ano deve ser menor ou igual a 2050')
    .optional(),
  page: z.number()
    .int('Página deve ser um número inteiro')
    .min(1, 'Página deve ser maior que 0')
    .default(1),
  limit: z.number()
    .int('Limite deve ser um número inteiro')
    .min(1, 'Limite deve ser maior que 0')
    .max(100, 'Limite deve ser menor ou igual a 100')
    .default(20),
  includeProgress: z.boolean()
    .default(false),
  sortBy: z.enum(['plannedAmount', 'month', 'year', 'createdAt', 'category', 'familyMember'])
    .default('createdAt'),
  sortOrder: z.enum(['asc', 'desc'])
    .default('desc')
});

/**
 * Schema for budget progress filters
 */
export const BudgetProgressFiltersSchema = z.object({
  categoryId: z.string()
    .cuid('ID da categoria inválido')
    .optional(),
  familyMemberId: z.string()
    .cuid('ID do membro da família inválido')
    .optional(),
  month: z.number()
    .int('Mês deve ser um número inteiro')
    .min(1, 'Mês deve estar entre 1 e 12')
    .max(12, 'Mês deve estar entre 1 e 12')
    .optional(),
  year: z.number()
    .int('Ano deve ser um número inteiro')
    .min(2020, 'Ano deve ser maior ou igual a 2020')
    .max(2050, 'Ano deve ser menor ou igual a 2050')
    .optional(),
  startMonth: z.number()
    .int('Mês inicial deve ser um número inteiro')
    .min(1, 'Mês inicial deve estar entre 1 e 12')
    .max(12, 'Mês inicial deve estar entre 1 e 12')
    .optional(),
  startYear: z.number()
    .int('Ano inicial deve ser um número inteiro')
    .min(2020, 'Ano inicial deve ser maior ou igual a 2020')
    .max(2050, 'Ano inicial deve ser menor ou igual a 2050')
    .optional(),
  endMonth: z.number()
    .int('Mês final deve ser um número inteiro')
    .min(1, 'Mês final deve estar entre 1 e 12')
    .max(12, 'Mês final deve estar entre 1 e 12')
    .optional(),
  endYear: z.number()
    .int('Ano final deve ser um número inteiro')
    .min(2020, 'Ano final deve ser maior ou igual a 2020')
    .max(2050, 'Ano final deve ser menor ou igual a 2050')
    .optional(),
  includeSubcategories: z.boolean()
    .default(true),
  alertThreshold: z.number()
    .min(0, 'Limite de alerta deve ser maior ou igual a 0')
    .max(100, 'Limite de alerta deve ser menor ou igual a 100')
    .default(80) // 80% of budget
}).refine((data) => {
  // Validate date range if both start and end are provided
  if (data.startMonth && data.startYear && data.endMonth && data.endYear) {
    const startDate = new Date(data.startYear, data.startMonth - 1);
    const endDate = new Date(data.endYear, data.endMonth - 1);
    return startDate <= endDate;
  }
  return true;
}, {
  message: 'Data inicial deve ser anterior ou igual à data final',
  path: ['endMonth']
});

/**
 * Type definitions for request/response
 */
export type CreateBudgetRequest = z.infer<typeof CreateBudgetSchema>;
export type UpdateBudgetRequest = z.infer<typeof UpdateBudgetSchema>;
export type BudgetFilters = z.infer<typeof BudgetFiltersSchema>;
export type BudgetProgressFilters = z.infer<typeof BudgetProgressFiltersSchema>;

/**
 * Budget response interface
 */
export interface BudgetResponse {
  id: string;
  plannedAmount: number;
  month: number;
  year: number;
  categoryId: string;
  familyMemberId?: string;
  createdAt: string;
  updatedAt: string;
  version: number;
  // Related data
  category: {
    id: string;
    name: string;
    color?: string;
    parent?: {
      id: string;
      name: string;
      color?: string;
    };
  };
  familyMember?: {
    id: string;
    name: string;
    color: string;
  };
  // Progress data (optional)
  progress?: BudgetProgress;
}

/**
 * Budget progress interface
 */
export interface BudgetProgress {
  actualAmount: number;
  remainingAmount: number;
  percentageUsed: number;
  percentageRemaining: number;
  isOverBudget: boolean;
  overBudgetAmount?: number;
  transactionCount: number;
  averageTransactionAmount: number;
  daysInPeriod: number;
  daysRemaining: number;
  dailyBudgetRemaining: number;
  projectedTotal?: number; // Based on current spending rate
  status: 'under_budget' | 'on_track' | 'over_budget' | 'warning';
}

/**
 * Paginated budgets response
 */
export interface PaginatedBudgetsResponse {
  data: BudgetResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Budget summary interface
 */
export interface BudgetSummary {
  totalPlanned: number;
  totalActual: number;
  totalRemaining: number;
  overallPercentageUsed: number;
  budgetCount: number;
  overBudgetCount: number;
  underBudgetCount: number;
  onTrackCount: number;
  warningCount: number;
}

/**
 * Budget report interface
 */
export interface BudgetReport {
  period: {
    month: number;
    year: number;
  };
  summary: BudgetSummary;
  budgets: BudgetResponse[];
  categoryBreakdown: Array<{
    categoryId: string;
    categoryName: string;
    totalPlanned: number;
    totalActual: number;
    percentageUsed: number;
    status: BudgetProgress['status'];
  }>;
  memberBreakdown?: Array<{
    familyMemberId: string;
    familyMemberName: string;
    totalPlanned: number;
    totalActual: number;
    percentageUsed: number;
    budgetCount: number;
  }>;
}
