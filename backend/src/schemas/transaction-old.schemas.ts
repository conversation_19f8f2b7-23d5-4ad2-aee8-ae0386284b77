import { z } from 'zod';
import { TransactionType } from '@prisma/client';



// Schema for updating installments
export const updateInstallmentsSchema = z.object({
  installments: z.array(z.object({
    amount: z.number()
      .positive('Valor da parcela deve ser positivo')
      .max(999999.99, 'Valor da parcela muito alto'),
    transactionDate: z.string()
      .datetime('Data da parcela inválida'),
    description: z.string()
      .min(1, 'Descrição da parcela é obrigatória')
      .max(255, 'Descrição da parcela muito longa')
      .optional()
  }))
  .min(2, 'Mínimo de 2 parcelas')
  .max(60, 'Máximo de 60 parcelas'),
  totalAmount: z.number()
    .positive('Valor total deve ser positivo')
    .max(999999.99, 'Valor total muito alto')
}).refine(data => {
  const sum = data.installments.reduce((acc, inst) => acc + inst.amount, 0)
  // Tolerância de 1 centavo para arredondamento
  return Math.abs(sum - data.totalAmount) < 0.01
}, {
  message: "A soma das parcelas deve ser igual ao valor total da transação",
  path: ["installments"]
})

export type UpdateInstallmentsRequest = z.infer<typeof updateInstallmentsSchema>

/**
 * Schema for creating a transaction
 */
export const CreateTransactionSchema = z.object({
  description: z.string()
    .min(1, 'Descrição é obrigatória')
    .max(255, 'Descrição deve ter no máximo 255 caracteres')
    .trim(),
  amount: z.number()
    .positive('Valor deve ser positivo')
    .max(*********.99, 'Valor muito alto')
    .transform(val => Number(val.toFixed(2))),
  transactionDate: z.string()
    .datetime('Data da transação inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val),
  type: z.nativeEnum(TransactionType),
  accountId: z.string()
    .cuid('ID da conta inválido'),
  categoryId: z.string()
    .cuid('ID da categoria inválido')
    .optional(),
  destinationAccountId: z.string()
    .cuid('ID da conta destino inválido')
    .optional(),
  exchangeRate: z.number()
    .positive('Taxa de câmbio deve ser positiva')
    .max(999999.999999, 'Taxa de câmbio muito alta')
    .optional()
    .transform(val => val ? Number(val.toFixed(6)) : undefined),
  totalInstallments: z.number()
    .int('Total de parcelas deve ser um inteiro')
    .positive('Total de parcelas deve ser positivo')
    .max(60, 'Máximo de 60 parcelas')
    .optional(),
  parentTransactionId: z.string()
    .cuid('ID da transação pai inválido')
    .optional(),
  isFuture: z.boolean().default(false),

  // Advanced transfer fields
  sourceCurrency: z.string()
    .length(3, 'Código da moeda deve ter 3 caracteres')
    .regex(/^[A-Z]{3}$/, 'Código da moeda deve estar em maiúsculas')
    .optional(),
  destinationCurrency: z.string()
    .length(3, 'Código da moeda deve ter 3 caracteres')
    .regex(/^[A-Z]{3}$/, 'Código da moeda deve estar em maiúsculas')
    .optional(),
  sourceAmount: z.number()
    .positive('Valor de origem deve ser positivo')
    .max(*********.99, 'Valor de origem muito alto')
    .transform(val => val ? Number(val.toFixed(2)) : undefined)
    .optional(),
  destinationAmount: z.number()
    .positive('Valor de destino deve ser positivo')
    .max(*********.99, 'Valor de destino muito alto')
    .transform(val => val ? Number(val.toFixed(2)) : undefined)
    .optional(),
  transferReference: z.string()
    .max(255, 'Referência da transferência deve ter no máximo 255 caracteres')
    .trim()
    .optional(),

  tagIds: z.array(z.string().cuid('ID da tag inválido'))
    .max(20, 'Máximo de 20 tags por transação')
    .optional(),
  familyMemberIds: z.array(z.string().cuid('ID de membro da família inválido'))
    .max(10, 'Máximo de 10 membros da família por transação')
    .optional()
}).refine((data) => {
  // Category is required for expenses
  if (data.type === TransactionType.EXPENSE && !data.categoryId) {
    return false;
  }
  return true;
}, {
  message: 'Categoria é obrigatória para despesas',
  path: ['categoryId']
}).refine((data) => {
  // Destination account is required for transfers
  if (data.type === TransactionType.TRANSFER && !data.destinationAccountId) {
    return false;
  }
  return true;
}, {
  message: 'Conta destino é obrigatória para transferências',
  path: ['destinationAccountId']
}).refine((data) => {
  // Destination account should not be the same as source account
  if (data.type === TransactionType.TRANSFER && data.destinationAccountId === data.accountId) {
    return false;
  }
  return true;
}, {
  message: 'Conta destino deve ser diferente da conta origem',
  path: ['destinationAccountId']
}).refine((data) => {
  // Transfer currency validation - if exchange rate is provided, currencies should be different
  if (data.type === TransactionType.TRANSFER && data.exchangeRate &&
      data.sourceCurrency && data.destinationCurrency &&
      data.sourceCurrency === data.destinationCurrency) {
    return false;
  }
  return true;
}, {
  message: 'Moedas de origem e destino devem ser diferentes quando há taxa de câmbio',
  path: ['destinationCurrency']
}).refine((data) => {
  // Transfer amounts validation - if currency conversion, both amounts should be provided
  if (data.type === TransactionType.TRANSFER && data.exchangeRate &&
      (!data.sourceAmount || !data.destinationAmount)) {
    return false;
  }
  return true;
}, {
  message: 'Valores de origem e destino são obrigatórios para conversão de moeda',
  path: ['sourceAmount']
});

/**
 * Schema for updating a transaction
 */
export const UpdateTransactionSchema = z.object({
  description: z.string()
    .min(1, 'Descrição é obrigatória')
    .max(255, 'Descrição deve ter no máximo 255 caracteres')
    .trim()
    .optional(),
  amount: z.number()
    .positive('Valor deve ser positivo')
    .max(*********.99, 'Valor muito alto')
    .transform(val => Number(val.toFixed(2)))
    .optional(),
  transactionDate: z.string()
    .datetime('Data da transação inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  categoryId: z.string()
    .cuid('ID da categoria inválido')
    .optional(),
  destinationAccountId: z.string()
    .cuid('ID da conta destino inválido')
    .optional(),
  exchangeRate: z.number()
    .positive('Taxa de câmbio deve ser positiva')
    .max(999999.999999, 'Taxa de câmbio muito alta')
    .optional()
    .transform(val => val ? Number(val.toFixed(6)) : undefined),
  isFuture: z.boolean().optional(),

  // Advanced transfer fields
  sourceCurrency: z.string()
    .length(3, 'Código da moeda deve ter 3 caracteres')
    .regex(/^[A-Z]{3}$/, 'Código da moeda deve estar em maiúsculas')
    .optional(),
  destinationCurrency: z.string()
    .length(3, 'Código da moeda deve ter 3 caracteres')
    .regex(/^[A-Z]{3}$/, 'Código da moeda deve estar em maiúsculas')
    .optional(),
  sourceAmount: z.number()
    .positive('Valor de origem deve ser positivo')
    .max(*********.99, 'Valor de origem muito alto')
    .transform(val => val ? Number(val.toFixed(2)) : undefined)
    .optional(),
  destinationAmount: z.number()
    .positive('Valor de destino deve ser positivo')
    .max(*********.99, 'Valor de destino muito alto')
    .transform(val => val ? Number(val.toFixed(2)) : undefined)
    .optional(),
  transferReference: z.string()
    .max(255, 'Referência da transferência deve ter no máximo 255 caracteres')
    .trim()
    .optional(),

  tagIds: z.array(z.string().cuid('ID da tag inválido'))
    .max(20, 'Máximo de 20 tags por transação')
    .optional(),
  familyMemberIds: z.array(z.string().cuid('ID de membro da família inválido'))
    .max(10, 'Máximo de 10 membros da família por transação')
    .optional()
});

/**
 * Schema for transaction query filters
 */
export const TransactionFiltersSchema = z.object({
  description: z.string().optional(),
  type: z.nativeEnum(TransactionType).optional(),
  accountId: z.string().cuid('ID da conta inválido').optional(),
  categoryId: z.string().cuid('ID da categoria inválido').optional(),
  familyMemberId: z.string().cuid('ID de membro da família inválido').optional(),
  tagId: z.string().cuid('ID da tag inválido').optional(),
  startDate: z.string()
    .datetime('Data inicial inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  endDate: z.string()
    .datetime('Data final inválida')
    .or(z.date())
    .transform(val => typeof val === 'string' ? new Date(val) : val)
    .optional(),
  minAmount: z.number()
    .positive('Valor mínimo deve ser positivo')
    .optional(),
  maxAmount: z.number()
    .positive('Valor máximo deve ser positivo')
    .optional(),
  isFuture: z.string()
    .transform(val => val === 'true')
    .optional(),
  includeDeleted: z.string()
    .transform(val => val === 'true')
    .optional(),
  page: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0, 'Página deve ser maior que 0')
    .optional(),
  limit: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0 && val <= 100, 'Limite deve ser entre 1 e 100')
    .optional(),
  sortBy: z.enum(['transactionDate', 'amount', 'description', 'createdAt'])
    .default('transactionDate')
    .optional(),
  sortOrder: z.enum(['asc', 'desc'])
    .default('desc')
    .optional()
}).refine((data) => {
  // End date should be after start date
  if (data.startDate && data.endDate && data.endDate <= data.startDate) {
    return false;
  }
  return true;
}, {
  message: 'Data final deve ser posterior à data inicial',
  path: ['endDate']
}).refine((data) => {
  // Max amount should be greater than min amount
  if (data.minAmount && data.maxAmount && data.maxAmount <= data.minAmount) {
    return false;
  }
  return true;
}, {
  message: 'Valor máximo deve ser maior que o valor mínimo',
  path: ['maxAmount']
});

/**
 * Type definitions
 */
export type CreateTransactionRequest = z.infer<typeof CreateTransactionSchema>;
export type UpdateTransactionRequest = z.infer<typeof UpdateTransactionSchema>;
export type TransactionFilters = z.infer<typeof TransactionFiltersSchema>;

/**
 * Transaction response type
 */
export interface TransactionResponse {
  id: string;
  description: string;
  amount: number;
  transactionDate: string;
  type: TransactionType;
  accountId: string;
  categoryId?: string;
  parentTransactionId?: string;
  installmentNumber?: number;
  totalInstallments?: number;
  exchangeRate?: number;
  destinationAccountId?: string;
  isFuture: boolean;

  // Advanced transfer fields
  sourceCurrency?: string;
  destinationCurrency?: string;
  sourceAmount?: number;
  destinationAmount?: number;
  transferReference?: string;

  createdAt: string;
  updatedAt: string;
  version: number;
  // Related data
  account?: {
    id: string;
    name: string;
    type: string;
    currency: string;
  };
  category?: {
    id: string;
    name: string;
    color?: string;
  };
  destinationAccount?: {
    id: string;
    name: string;
    type: string;
    currency: string;
  };
  parentTransaction?: {
    id: string;
    description: string;
  };
  installments?: Array<{
    id: string;
    description: string;
    amount: number;
    transactionDate: string;
    installmentNumber: number;
  }>;
  tags?: Array<{
    id: string;
    name: string;
    color: string;
  }>;
  familyMembers?: Array<{
    id: string;
    name: string;
    color: string;
  }>;
}

/**
 * Paginated transactions response
 */
export interface PaginatedTransactionsResponse {
  data: TransactionResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  summary?: {
    totalIncome: number;
    totalExpenses: number;
    totalTransfers: number;
    netAmount: number;
  };
}

/**
 * Transfer-specific types
 */
export interface TransferRequest extends CreateTransactionRequest {
  type: 'TRANSFER';
  destinationAccountId: string;
  sourceCurrency?: string;
  destinationCurrency?: string;
  sourceAmount?: number;
  destinationAmount?: number;
  exchangeRate?: number;
  transferReference?: string;
}

export interface CurrencyConversionData {
  sourceCurrency: string;
  destinationCurrency: string;
  sourceAmount: number;
  destinationAmount: number;
  exchangeRate: number;
  conversionDate: Date;
}

export interface TransferResponse extends TransactionResponse {
  type: 'TRANSFER';
  destinationAccountId: string;
  destinationAccount: {
    id: string;
    name: string;
    type: string;
    currency: string;
  };
}
