import { z } from 'zod';

/**
 * Schema for creating a new category
 */
export const CreateCategorySchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Cor deve estar no formato hexadecimal (#RRGGBB)')
    .optional(),
  parentId: z.string()
    .cuid('ID da categoria pai inválido')
    .optional()
});

/**
 * Schema for updating a category
 */
export const UpdateCategorySchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .trim()
    .optional(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Cor deve estar no formato hexadecimal (#RRGGBB)')
    .optional(),
  parentId: z.string()
    .cuid('ID da categoria pai inválido')
    .optional()
    .nullable()
});

/**
 * Schema for category query filters
 */
export const CategoryFiltersSchema = z.object({
  name: z.string().optional(),
  parentId: z.string()
    .cuid('ID da categoria pai inválido')
    .optional(),
  includeChildren: z.string()
    .transform(val => val === 'true')
    .optional(),
  onlyParents: z.string()
    .transform(val => val === 'true')
    .optional(),
  onlyChildren: z.string()
    .transform(val => val === 'true')
    .optional(),
  includeArchived: z.string()
    .transform(val => val === 'true')
    .optional(),
  includeDeleted: z.string()
    .transform(val => val === 'true')
    .optional(),
  page: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0, 'Página deve ser maior que 0')
    .optional(),
  limit: z.string()
    .transform(val => parseInt(val, 10))
    .refine(val => val > 0 && val <= 100, 'Limite deve ser entre 1 e 100')
    .optional()
});

/**
 * Schema for archiving/unarchiving category
 */
export const ArchiveCategorySchema = z.object({
  archived: z.boolean()
});

/**
 * Schema for validating category hierarchy
 */
export const CategoryHierarchySchema = z.object({
  categoryId: z.string().cuid('ID da categoria inválido'),
  parentId: z.string().cuid('ID da categoria pai inválido').optional()
});

/**
 * Type definitions
 */
export type CreateCategoryRequest = z.infer<typeof CreateCategorySchema>;
export type UpdateCategoryRequest = z.infer<typeof UpdateCategorySchema>;
export type CategoryFilters = z.infer<typeof CategoryFiltersSchema>;
export type ArchiveCategoryRequest = z.infer<typeof ArchiveCategorySchema>;
export type CategoryHierarchyRequest = z.infer<typeof CategoryHierarchySchema>;

/**
 * Category response type
 */
export interface CategoryResponse {
  id: string;
  name: string;
  color?: string;
  parentId?: string;
  archived: boolean;
  createdAt: string;
  updatedAt: string;
  version: number;
  // Hierarchy information
  level: number; // 0 for parent categories, 1 for subcategories
  hasChildren: boolean;
  // Related data
  parent?: {
    id: string;
    name: string;
    color?: string;
  };
  children?: Array<{
    id: string;
    name: string;
    color?: string;
  }>;
  // Usage statistics
  transactionCount?: number;
  budgetCount?: number;
}

/**
 * Paginated categories response
 */
export interface PaginatedCategoriesResponse {
  data: CategoryResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Category tree response for hierarchical display
 */
export interface CategoryTreeResponse {
  id: string;
  name: string;
  color?: string;
  archived: boolean;
  level: number;
  transactionCount?: number;
  budgetCount?: number;
  children: CategoryTreeResponse[];
}

/**
 * Category hierarchy validation response
 */
export interface CategoryHierarchyValidation {
  isValid: boolean;
  errors: string[];
  maxDepthReached: boolean;
  circularReference: boolean;
}
