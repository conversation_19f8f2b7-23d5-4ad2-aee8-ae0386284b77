import { z } from 'zod';
import { AccountType } from '@prisma/client';
import { SUPPORTED_CURRENCIES } from './account.schemas';

/**
 * Schema for date range filters
 */
export const DateRangeSchema = z.object({
  startDate: z.string()
    .datetime('Data de início deve estar no formato ISO 8601')
    .optional(),
  endDate: z.string()
    .datetime('Data de fim deve estar no formato ISO 8601')
    .optional()
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Data de início deve ser anterior à data de fim',
  path: ['endDate']
});

/**
 * Schema for period filters (month/year)
 */
export const PeriodFilterSchema = z.object({
  month: z.number()
    .int('Mês deve ser um número inteiro')
    .min(1, 'Mês deve ser entre 1 e 12')
    .max(12, 'Mês deve ser entre 1 e 12')
    .optional(),
  year: z.number()
    .int('Ano deve ser um número inteiro')
    .min(2000, 'Ano deve ser maior que 2000')
    .max(2100, 'Ano deve ser menor que 2100')
    .optional()
}).refine((data) => {
  // If month is provided, year must also be provided
  if (data.month && !data.year) {
    return false;
  }
  return true;
}, {
  message: 'Ano é obrigatório quando mês é especificado',
  path: ['year']
});

/**
 * Schema for dashboard filters
 */
export const DashboardFiltersSchema = z.object({
  // Date filters
  dateRange: DateRangeSchema.optional(),
  period: PeriodFilterSchema.optional(),
  
  // Account filters
  accountIds: z.array(z.string().cuid('ID de conta inválido'))
    .max(50, 'Máximo de 50 contas por filtro')
    .optional(),
  accountTypes: z.array(z.nativeEnum(AccountType))
    .max(10, 'Máximo de 10 tipos de conta por filtro')
    .optional(),
  currencies: z.array(z.enum(SUPPORTED_CURRENCIES))
    .max(10, 'Máximo de 10 moedas por filtro')
    .optional(),
  includeInTotal: z.boolean().optional(),
  
  // Category filters
  categoryIds: z.array(z.string().cuid('ID de categoria inválido'))
    .max(100, 'Máximo de 100 categorias por filtro')
    .optional(),
  
  // Family member filters
  familyMemberIds: z.array(z.string().cuid('ID de membro da família inválido'))
    .max(20, 'Máximo de 20 membros da família por filtro')
    .optional(),
    
  // Goal filters
  goalIds: z.array(z.string().cuid('ID de meta inválido'))
    .max(50, 'Máximo de 50 metas por filtro')
    .optional(),
    
  // Budget filters
  budgetIds: z.array(z.string().cuid('ID de orçamento inválido'))
    .max(50, 'Máximo de 50 orçamentos por filtro')
    .optional()
}).refine((data) => {
  // Either dateRange or period should be provided, not both
  if (data.dateRange && data.period) {
    return false;
  }
  return true;
}, {
  message: 'Especifique apenas um tipo de filtro de data: dateRange ou period',
  path: ['period']
});

/**
 * Schema for account balance aggregation response
 */
export const AccountBalanceAggregationSchema = z.object({
  totalBalance: z.number(),
  totalBalanceInBRL: z.number(),
  projectedBalance: z.number(),
  projectedBalanceInBRL: z.number(),
  balanceByType: z.array(z.object({
    type: z.nativeEnum(AccountType),
    balance: z.number(),
    balanceInBRL: z.number(),
    count: z.number()
  })),
  balanceByCurrency: z.array(z.object({
    currency: z.enum(SUPPORTED_CURRENCIES),
    balance: z.number(),
    balanceInBRL: z.number(),
    exchangeRate: z.number().optional(),
    count: z.number()
  }))
});

/**
 * Schema for net worth calculation response
 */
export const NetWorthAggregationSchema = z.object({
  currentNetWorth: z.number(),
  assets: z.object({
    total: z.number(),
    checking: z.number(),
    savings: z.number(),
    investment: z.number(),
    cash: z.number(),
    other: z.number()
  }),
  liabilities: z.object({
    total: z.number(),
    creditCards: z.number(),
    loans: z.number(),
    other: z.number()
  }),
  monthlyHistory: z.array(z.object({
    month: z.string(),
    netWorth: z.number(),
    assets: z.number(),
    liabilities: z.number(),
    change: z.number(),
    changePercentage: z.number()
  })).max(24, 'Máximo de 24 meses de histórico')
});

/**
 * Schema for credit card usage response
 */
export const CreditCardUsageSchema = z.object({
  totalUsage: z.number(),
  totalLimit: z.number(),
  usagePercentage: z.number(),
  availableCredit: z.number(),
  cardDetails: z.array(z.object({
    accountId: z.string(),
    accountName: z.string(),
    currentBalance: z.number(),
    creditLimit: z.number(),
    usagePercentage: z.number(),
    availableCredit: z.number(),
    isNearLimit: z.boolean(),
    currency: z.enum(SUPPORTED_CURRENCIES)
  })),
  alerts: z.array(z.object({
    accountId: z.string(),
    accountName: z.string(),
    alertType: z.enum(['NEAR_LIMIT', 'OVER_LIMIT', 'HIGH_USAGE']),
    message: z.string(),
    severity: z.enum(['LOW', 'MEDIUM', 'HIGH'])
  }))
});

/**
 * Schema for expense breakdown by category response
 */
export const ExpenseByCategorySchema = z.object({
  totalExpenses: z.number(),
  categories: z.array(z.object({
    categoryId: z.string(),
    categoryName: z.string(),
    parentCategoryId: z.string().optional(),
    parentCategoryName: z.string().optional(),
    amount: z.number(),
    percentage: z.number(),
    transactionCount: z.number(),
    subcategories: z.array(z.object({
      categoryId: z.string(),
      categoryName: z.string(),
      amount: z.number(),
      percentage: z.number(),
      transactionCount: z.number()
    })).optional()
  })),
  periodComparison: z.object({
    previousPeriodTotal: z.number(),
    change: z.number(),
    changePercentage: z.number()
  }).optional(),
  trends: z.array(z.object({
    categoryId: z.string(),
    categoryName: z.string(),
    trend: z.enum(['INCREASING', 'DECREASING', 'STABLE']),
    changePercentage: z.number()
  })).optional()
});

/**
 * Schema for expense breakdown by family member response
 */
export const ExpenseByMemberSchema = z.object({
  totalExpenses: z.number(),
  members: z.array(z.object({
    memberId: z.string(),
    memberName: z.string(),
    amount: z.number(),
    percentage: z.number(),
    transactionCount: z.number(),
    averageTransactionAmount: z.number(),
    topCategories: z.array(z.object({
      categoryId: z.string(),
      categoryName: z.string(),
      amount: z.number(),
      percentage: z.number()
    })).max(5, 'Máximo de 5 categorias principais')
  })),
  comparison: z.object({
    highestSpender: z.object({
      memberId: z.string(),
      memberName: z.string(),
      amount: z.number()
    }),
    lowestSpender: z.object({
      memberId: z.string(),
      memberName: z.string(),
      amount: z.number()
    }),
    averagePerMember: z.number()
  })
});

/**
 * Schema for budget vs actual comparison response
 */
export const BudgetComparisonSchema = z.object({
  totalBudgeted: z.number(),
  totalSpent: z.number(),
  totalRemaining: z.number(),
  overallProgress: z.number(),
  categories: z.array(z.object({
    categoryId: z.string(),
    categoryName: z.string(),
    budgeted: z.number(),
    spent: z.number(),
    remaining: z.number(),
    progress: z.number(),
    status: z.enum(['ON_TRACK', 'NEAR_LIMIT', 'OVER_BUDGET']),
    projectedSpend: z.number().optional()
  })),
  alerts: z.array(z.object({
    categoryId: z.string(),
    categoryName: z.string(),
    alertType: z.enum(['NEAR_BUDGET', 'OVER_BUDGET', 'PROJECTED_OVERSPEND']),
    message: z.string(),
    severity: z.enum(['LOW', 'MEDIUM', 'HIGH'])
  }))
});

/**
 * Schema for financial goals progress response
 */
export const GoalProgressSchema = z.object({
  totalGoals: z.number(),
  activeGoals: z.number(),
  completedGoals: z.number(),
  goals: z.array(z.object({
    goalId: z.string(),
    goalName: z.string(),
    targetAmount: z.number(),
    currentAmount: z.number(),
    progress: z.number(),
    remaining: z.number(),
    targetDate: z.string().optional(),
    daysRemaining: z.number().optional(),
    estimatedCompletionDate: z.string().optional(),
    isOnTrack: z.boolean(),
    monthlyRequiredAmount: z.number().optional(),
    status: z.enum(['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'OVERDUE'])
  })),
  summary: z.object({
    totalTargetAmount: z.number(),
    totalCurrentAmount: z.number(),
    overallProgress: z.number(),
    onTrackGoals: z.number(),
    behindScheduleGoals: z.number()
  })
});

/**
 * Type definitions
 */
export type DashboardFilters = z.infer<typeof DashboardFiltersSchema>;
export type DateRange = z.infer<typeof DateRangeSchema>;
export type PeriodFilter = z.infer<typeof PeriodFilterSchema>;
export type AccountBalanceAggregation = z.infer<typeof AccountBalanceAggregationSchema>;
export type NetWorthAggregation = z.infer<typeof NetWorthAggregationSchema>;
export type CreditCardUsage = z.infer<typeof CreditCardUsageSchema>;
export type ExpenseByCategory = z.infer<typeof ExpenseByCategorySchema>;
export type ExpenseByMember = z.infer<typeof ExpenseByMemberSchema>;
export type BudgetComparison = z.infer<typeof BudgetComparisonSchema>;
export type GoalProgress = z.infer<typeof GoalProgressSchema>;

/**
 * Complete dashboard overview response schema
 */
export const DashboardOverviewSchema = z.object({
  accountBalances: AccountBalanceAggregationSchema,
  netWorth: NetWorthAggregationSchema,
  creditCardUsage: CreditCardUsageSchema,
  expensesByCategory: ExpenseByCategorySchema,
  expensesByMember: ExpenseByMemberSchema,
  budgetComparison: BudgetComparisonSchema,
  goalProgress: GoalProgressSchema,
  generatedAt: z.string().datetime(),
  filters: DashboardFiltersSchema
});

export type DashboardOverview = z.infer<typeof DashboardOverviewSchema>;
