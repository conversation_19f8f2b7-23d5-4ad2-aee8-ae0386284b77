import * as schedule from 'node-schedule';

/**
 * Job types for the scheduler
 */
export enum JobType {
  RECURRING_TRANSACTIONS = 'recurring_transactions',
  FUTURE_TRANSACTIONS = 'future_transactions',
  CLEANUP = 'cleanup',
  BACKUP = 'backup',
  INSIGHTS_GENERATION = 'insights_generation'
}

/**
 * Job status enum
 */
export enum JobStatus {
  SCHEDULED = 'scheduled',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

/**
 * Interface for job configuration
 */
export interface JobConfig {
  name: string;
  type: JobType;
  cronPattern: string;
  timezone: string;
  enabled: boolean;
  description?: string;
  retryAttempts?: number;
  retryDelay?: number; // in milliseconds
}

/**
 * Interface for job execution context
 */
export interface JobContext {
  jobId: string;
  name: string;
  type: JobType;
  startTime: Date;
  attempt: number;
  maxAttempts: number;
}

/**
 * Interface for job result
 */
export interface JobResult {
  success: boolean;
  message: string;
  data?: any;
  error?: Error;
  duration: number; // in milliseconds
}

/**
 * Job execution function type
 */
export type JobFunction = (context: JobContext) => Promise<JobResult>;

/**
 * Scheduler configuration
 */
export const SCHEDULER_CONFIG = {
  timezone: 'America/Sao_Paulo',
  defaultRetryAttempts: 3,
  defaultRetryDelay: 5000, // 5 seconds
  gracefulShutdownTimeout: 30000, // 30 seconds
  logLevel: process.env.NODE_ENV === 'production' ? 'info' : 'debug'
};

/**
 * Default job configurations
 */
export const DEFAULT_JOBS: JobConfig[] = [
  {
    name: 'Process Recurring Transactions',
    type: JobType.RECURRING_TRANSACTIONS,
    cronPattern: '1 0 * * *', // Every day at 00:01
    timezone: SCHEDULER_CONFIG.timezone,
    enabled: true,
    description: 'Process all active recurring transactions and generate new transactions',
    retryAttempts: 3,
    retryDelay: 10000 // 10 seconds
  },
  {
    name: 'Process Future Transactions',
    type: JobType.FUTURE_TRANSACTIONS,
    cronPattern: '5 0 * * *', // Every day at 00:05 (after recurring transactions)
    timezone: SCHEDULER_CONFIG.timezone,
    enabled: true,
    description: 'Process future transactions that have reached their execution date',
    retryAttempts: 3,
    retryDelay: 10000 // 10 seconds
  },
  {
    name: 'Daily Cleanup',
    type: JobType.CLEANUP,
    cronPattern: '0 2 * * *', // Every day at 02:00
    timezone: SCHEDULER_CONFIG.timezone,
    enabled: true,
    description: 'Clean up temporary data and logs',
    retryAttempts: 1,
    retryDelay: 5000
  },
  {
    name: 'Generate Insights',
    type: JobType.INSIGHTS_GENERATION,
    cronPattern: '30 6 * * *', // Every day at 06:30
    timezone: SCHEDULER_CONFIG.timezone,
    enabled: true,
    description: 'Generate automatic financial insights based on user data',
    retryAttempts: 2,
    retryDelay: 15000 // 15 seconds
  }
];

/**
 * Scheduler class for managing jobs
 */
export class Scheduler {
  private jobs: Map<string, schedule.Job> = new Map();
  private jobFunctions: Map<JobType, JobFunction> = new Map();
  private isShuttingDown = false;

  /**
   * Register a job function for a specific job type
   */
  registerJobFunction(type: JobType, fn: JobFunction): void {
    this.jobFunctions.set(type, fn);
  }

  /**
   * Schedule a job
   */
  scheduleJob(config: JobConfig): boolean {
    try {
      if (!config.enabled) {
        console.log(`[Scheduler] Job ${config.name} is disabled, skipping...`);
        return false;
      }

      const jobFunction = this.jobFunctions.get(config.type);
      if (!jobFunction) {
        console.error(`[Scheduler] No function registered for job type: ${config.type}`);
        return false;
      }

      const job = schedule.scheduleJob(
        config.name,
        {
          rule: config.cronPattern,
          tz: config.timezone
        },
        async (fireDate: Date) => {
          await this.executeJob(config, jobFunction, fireDate);
        }
      );

      if (job) {
        this.jobs.set(config.name, job);
        console.log(`[Scheduler] Job ${config.name} scheduled with pattern: ${config.cronPattern}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`[Scheduler] Failed to schedule job ${config.name}:`, error);
      return false;
    }
  }

  /**
   * Execute a job with retry logic
   */
  private async executeJob(
    config: JobConfig,
    jobFunction: JobFunction,
    fireDate: Date
  ): Promise<void> {
    if (this.isShuttingDown) {
      console.log(`[Scheduler] Skipping job ${config.name} - scheduler is shutting down`);
      return;
    }

    const jobId = `${config.name}_${Date.now()}`;
    const maxAttempts = config.retryAttempts || SCHEDULER_CONFIG.defaultRetryAttempts;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      const context: JobContext = {
        jobId,
        name: config.name,
        type: config.type,
        startTime: new Date(),
        attempt,
        maxAttempts
      };

      try {
        console.log(`[Scheduler] Executing job ${config.name} (attempt ${attempt}/${maxAttempts})`);
        
        const startTime = Date.now();
        const result = await jobFunction(context);
        const duration = Date.now() - startTime;

        if (result.success) {
          console.log(`[Scheduler] Job ${config.name} completed successfully in ${duration}ms: ${result.message}`);
          return;
        } else {
          console.warn(`[Scheduler] Job ${config.name} failed (attempt ${attempt}/${maxAttempts}): ${result.message}`);
          
          if (attempt === maxAttempts) {
            console.error(`[Scheduler] Job ${config.name} failed after ${maxAttempts} attempts`);
            return;
          }
        }
      } catch (error) {
        console.error(`[Scheduler] Job ${config.name} threw error (attempt ${attempt}/${maxAttempts}):`, error);
        
        if (attempt === maxAttempts) {
          console.error(`[Scheduler] Job ${config.name} failed after ${maxAttempts} attempts`);
          return;
        }
      }

      // Wait before retry
      if (attempt < maxAttempts) {
        const delay = config.retryDelay || SCHEDULER_CONFIG.defaultRetryDelay;
        console.log(`[Scheduler] Retrying job ${config.name} in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Cancel a specific job
   */
  cancelJob(name: string): boolean {
    const job = this.jobs.get(name);
    if (job) {
      job.cancel();
      this.jobs.delete(name);
      console.log(`[Scheduler] Job ${name} cancelled`);
      return true;
    }
    return false;
  }

  /**
   * Get all scheduled jobs
   */
  getScheduledJobs(): string[] {
    return Array.from(this.jobs.keys());
  }

  /**
   * Gracefully shutdown all jobs
   */
  async gracefulShutdown(): Promise<void> {
    console.log('[Scheduler] Starting graceful shutdown...');
    this.isShuttingDown = true;

    const timeout = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Shutdown timeout')), SCHEDULER_CONFIG.gracefulShutdownTimeout);
    });

    try {
      await Promise.race([
        schedule.gracefulShutdown(),
        timeout
      ]);
      console.log('[Scheduler] Graceful shutdown completed');
    } catch (error) {
      console.error('[Scheduler] Graceful shutdown failed:', error);
    }
  }

  /**
   * Initialize scheduler with default jobs
   */
  initialize(): void {
    console.log('[Scheduler] Initializing scheduler...');
    
    // Schedule default jobs
    DEFAULT_JOBS.forEach(jobConfig => {
      this.scheduleJob(jobConfig);
    });

    console.log(`[Scheduler] Initialized with ${this.jobs.size} jobs`);
  }
}

// Export singleton instance
export const scheduler = new Scheduler();
