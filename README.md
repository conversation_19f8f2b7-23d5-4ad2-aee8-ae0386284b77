# 💰 Personal Finance Manager

Um webapp moderno para controle financeiro pessoal e familiar, desenvolvido com React, Node.js, PostgreSQL e Prisma.

## 🎯 Visão Geral

O Personal Finance Manager é uma aplicação completa para gerenciamento de finanças pessoais e familiares, oferecendo:

- 👥 **Gestão de Membros da Família** - Organize gastos por pessoa
- 🏦 **Contas Financeiras** - Corrente, poupança, investimentos, cartões de crédito
- 💳 **Transações Completas** - Despesas, receitas, transferências, parcelamentos
- 📊 **Dashboard Analítico** - Gráficos e insights automáticos
- 🎯 **Orçamentos e Metas** - Planejamento financeiro inteligente
- 🌍 **<PERSON>úl<PERSON><PERSON>** - Suporte a diferentes moedas com conversão
- 🔄 **Transações Recorrentes** - Automatização de lançamentos
- 🏷️ **Categorização Inteligente** - Sugestões automáticas baseadas no histórico

## 🛠️ Stack Tecnológica

### Frontend
- **React 18** - Interface de usuário moderna
- **Vite** - Build tool rápido
- **Tailwind CSS** - Estilização utilitária
- **shadcn/ui** - Biblioteca de componentes
- **React Query** - Gerenciamento de estado servidor
- **React Hook Form** - Formulários performáticos
- **Recharts** - Gráficos e visualizações
- **Zustand** - Gerenciamento de estado local
- **Sonner** - Sistema de notificações

### Backend
- **Node.js** - Runtime JavaScript
- **Express** - Framework web
- **Prisma** - ORM moderno
- **PostgreSQL** - Banco de dados relacional
- **JWT** - Autenticação
- **Zod** - Validação de schemas

### Desenvolvimento
- **TypeScript** - Tipagem estática
- **ESLint** - Linting
- **Jest/Vitest** - Testes
- **TaskMaster-AI** - Gerenciamento de tarefas

## 🚀 Configuração do Ambiente

### Pré-requisitos

- Node.js 18+ 
- npm 9+
- PostgreSQL 14+
- Git

### 1. Clone o Repositório

```bash
git clone <repository-url>
cd personal-finance-manager
```

### 2. Instale as Dependências

```bash
# Instala dependências de todos os workspaces
npm run install:all
```

### 3. Configure o Banco de Dados

```bash
# Instale o PostgreSQL (macOS)
brew install postgresql
brew services start postgresql

# Crie o banco de dados
createdb personal_finance_db
```

### 4. Configure as Variáveis de Ambiente

```bash
# Copie o arquivo de exemplo
cp .env.example .env

# Edite o .env com suas configurações
# Especialmente: DATABASE_URL e JWT_SECRET
```

### 5. Execute as Migrações

```bash
# Gera o cliente Prisma e executa migrações
npm run db:migrate
```

### 6. Popule com Dados de Exemplo (Opcional)

```bash
# Executa o seed do banco
npm run db:seed
```

## 🏃‍♂️ Executando o Projeto

### Desenvolvimento

```bash
# Executa frontend e backend simultaneamente
npm run dev

# Ou execute separadamente:
npm run dev:frontend  # http://localhost:3000
npm run dev:backend   # http://localhost:3001
```

### Produção

```bash
# Build de todos os serviços
npm run build

# Inicia em produção
npm start
```

## 📁 Estrutura do Projeto

```
personal-finance-manager/
├── frontend/           # React + Vite app
│   ├── src/
│   │   ├── components/ # Componentes reutilizáveis
│   │   │   ├── ui/     # shadcn/ui components
│   │   │   ├── accounts/
│   │   │   ├── auth/
│   │   │   ├── categories/
│   │   │   ├── goals/
│   │   │   ├── tags/
│   │   │   └── transactions/
│   │   ├── pages/      # Páginas da aplicação
│   │   ├── hooks/      # Custom hooks (15+)
│   │   ├── lib/        # Utilities e API
│   │   ├── stores/     # Zustand stores
│   │   └── types/      # TypeScript types
│   └── public/
├── backend/            # Node.js + Express API
│   ├── src/
│   │   ├── controllers/# Route controllers (15+)
│   │   ├── middleware/ # Express middleware
│   │   ├── routes/     # API routes (15+)
│   │   ├── services/   # Business logic (15+)
│   │   ├── schemas/    # Zod validations
│   │   ├── utils/      # Utilities
│   │   └── tests/      # Test suites (200+)
│   └── prisma/         # Database schema
├── shared/             # Shared types and utilities
├── docs/               # Documentação técnica
└── .taskmaster/        # TaskMaster-AI configuration
```

## 🧪 Testes

### Suite Completa de Testes
- **Unit Tests** - Jest (backend) + Vitest (frontend)
- **Integration Tests** - API endpoints e database
- **E2E Tests** - Cypress para fluxos críticos
- **Performance Tests** - k6 para load testing
- **Test Coverage** - Relatórios unificados com thresholds

```bash
# Executa todos os testes
npm test

# Testes com watch mode
npm run test:backend -- --watch
npm run test:frontend

# Coverage unificado
npm run test:coverage

# Performance tests
npm run test:performance

# E2E tests
npm run test:e2e

# Gerenciamento de dados de teste
npm run test:data:minimal      # Dataset mínimo
npm run test:data:comprehensive # Dataset completo
npm run test:data:clean        # Limpeza
```

## 🔧 Scripts Úteis

```bash
# Linting
npm run lint
npm run lint:fix

# Banco de dados
npm run db:studio      # Abre Prisma Studio
npm run db:reset       # Reset completo do banco
npm run db:generate    # Regenera o cliente Prisma

# Limpeza
npm run clean          # Remove node_modules
```

## 📊 Status do Projeto

**📈 [Status Completo do Projeto](PROJECT_STATUS.md)** - Progresso detalhado, métricas e roadmap

**Progresso Atual**: 69.39% (34/49 tarefas concluídas)
**Backend**: ✅ 15 módulos implementados e testados + Sistema de Parcelas Refinado
**Frontend**: ✅ 12 módulos implementados (Dashboard, Auth, Accounts, Transactions, Categories, Tags, Goals, etc.)
**Sistema de Transações**: ✅ Completamente refinado com parcelas inteligentes e filtros avançados
**Sistema de Metas Financeiras**: ✅ Frontend completo implementado com progresso visual
**Autenticação**: ✅ Sistema robusto com AuthInitializer funcionando perfeitamente
**Testes**: ✅ Suite completa (Unit, Integration, E2E, Performance, Coverage, CI/CD)
**TaskMaster-AI**: ✅ 100% sincronizado com estado atual (49 tarefas, 201 subtarefas)
**✅ Recém Implementado**: Sistema de metas financeiras frontend completo com cards visuais

### ✅ Módulos Implementados

**Backend (15 módulos):**
- ✅ **Autenticação** - JWT, middleware de segurança
- ✅ **Usuários** - CRUD, perfis, preferências
- ✅ **Contas** - Múltiplos tipos, moedas, saldos
- ✅ **Categorias** - Hierarquia, CRUD, validações
- ✅ **Tags** - Sistema flexível de etiquetas
- ✅ **Transações** - CRUD, filtros avançados, validações
- ✅ **Transferências** - Entre contas, conversão de moedas
- ✅ **Sistema de Parcelas Refinado** - Filtros por data, status individual, badges inteligentes
- ✅ **Transações Recorrentes** - Agendamento automático
- ✅ **Sugestão de Categorias** - IA para categorização
- ✅ **Orçamentos** - Planejamento e acompanhamento
- ✅ **Metas Financeiras** - Objetivos e milestones
- ✅ **Dashboard** - Agregações e métricas
- ✅ **Insights Automáticos** - Análise inteligente
- ✅ **Cache Redis** - Otimização de performance

**Frontend (12 módulos):**
- ✅ **Infraestrutura** - Vite, React, TypeScript, Tailwind
- ✅ **Autenticação Completa** - Login refinado, registro avançado, validações em tempo real
- ✅ **AuthInitializer** - Inicialização automática da autenticação, correção de loops de redirect
- ✅ **Componentes UI** - Biblioteca completa de componentes (shadcn/ui)
- ✅ **Dashboard** - Interface principal com métricas e gráficos
- ✅ **Contas** - Gestão completa de contas com CRUD funcional
- ✅ **Transações Refinadas** - Interface completa com parcelas inteligentes e filtros avançados
- ✅ **Categorias** - **Novo!** Sistema completo de gerenciamento de categorias hierárquicas
- ✅ **Tags** - **Novo!** Sistema completo de gerenciamento de tags
- ✅ **Metas Financeiras** - **Novo!** Interface completa com cards visuais, progresso e marcos
- ✅ **Family Members** - Gestão completa de membros da família
- ✅ **Sistema de Rotas** - Navegação protegida e redirecionamentos corrigidos

**Testes e Qualidade (8 sistemas):**
- ✅ **Cypress E2E** - Testes end-to-end completos
- ✅ **Jest/Vitest** - Testes unitários e integração
- ✅ **Performance Tests** - k6 com quality gates
- ✅ **Coverage Reporting** - Relatórios unificados
- ✅ **Test Data Management** - Factories, seeders, fixtures
- ✅ **CI/CD Pipeline** - GitHub Actions automatizado
- ✅ **Quality Gates** - Coverage, performance, security
- ✅ **Dependabot** - Updates automáticos

## 📚 Documentação dos Módulos

### Módulos Principais
- **[Autenticação](backend/docs/AUTHENTICATION_API.md)** - **Novo!** API de autenticação e autorização
- **[Módulos Core](backend/docs/CORE_MODULES.md)** - Categorias, Tags, Transações, Membros da Família
- **[Contas](backend/docs/ACCOUNTS_MODULE.md)** - Gestão de contas financeiras
- **[Transferências](backend/docs/TRANSFERS_MODULE.md)** - Sistema de transferências avançadas
- **[Sistema de Transações Refinado](backend/docs/TRANSACTIONS_SYSTEM_REFINED.md)** - **Novo!** Parcelas inteligentes e filtros avançados

### Módulos Avançados
- **[Orçamentos](backend/docs/BUDGET_MODULE.md)** - Gestão de orçamentos mensais
- **[Metas Financeiras](backend/docs/FINANCIAL_GOALS_MODULE.md)** - Gestão de metas e marcos financeiros
- **[Transações Recorrentes](backend/docs/RECURRING_TRANSACTIONS_MODULE.md)** - Automatização de transações
- **[Sugestão de Categorias](backend/docs/category-suggestion-system.md)** - IA para categorização
- **[Dashboard Analytics](backend/docs/dashboard-aggregation.md)** - Agregação de dados para dashboard
- **[Insights Automáticos](backend/docs/insights-module.md)** - **Novo!** Sistema de insights financeiros automáticos

### Infraestrutura e Testes
- **[Schema do Banco](backend/docs/database-schema.md)** - Estrutura do banco de dados
- **[Sistema de Testes](docs/TEST-DATA-MANAGEMENT.md)** - **Novo!** Gerenciamento completo de dados de teste
- **[Cobertura de Testes](docs/COVERAGE.md)** - **Novo!** Sistema de cobertura unificada
- **[Pipeline CI/CD](docs/CI-CD-PIPELINE.md)** - **Novo!** Pipeline automatizado com quality gates

### APIs Disponíveis

- `GET /api/v1/health` - Health check
- `POST /api/v1/auth/*` - **[Autenticação e autorização](backend/docs/AUTHENTICATION_API.md)**
- `CRUD /api/v1/accounts` - Gestão de contas
- `CRUD /api/v1/family-members` - Gestão de membros
- `CRUD /api/v1/categories` - Gestão de categorias
- `CRUD /api/v1/tags` - Gestão de tags
- `CRUD /api/v1/transactions` - Gestão de transações
- `CRUD /api/v1/transfers` - Transferências avançadas
- `CRUD /api/v1/budgets` - Gestão de orçamentos
- `CRUD /api/v1/goals` - Gestão de metas financeiras
- `CRUD /api/v1/goals/:goalId/milestones` - Marcos das metas
- `CRUD /api/v1/goals/:goalId/progress-history` - **Novo!** Histórico de progresso das metas
- `CRUD /api/v1/recurring-transactions` - Transações recorrentes
- `POST /api/v1/category-suggestions` - Sugestões inteligentes
- `GET /api/v1/dashboard` - Dados agregados para dashboard
- `CRUD /api/v1/insights` - Sistema de insights automáticos

## 📊 Funcionalidades Principais

### 🏦 Gestão de Contas
- Contas correntes, poupança, investimentos
- Cartões de crédito com controle de limite
- Bens e ativos para patrimônio
- Múltiplas moedas com conversão

### 💰 Sistema de Transações Refinado
- **Parcelas Inteligentes** - Controle individual de status, filtros por data de vencimento
- **Badges Contextuais** - Oculta 1/1 para compras à vista, mostra apenas parcelamentos reais
- **Filtros Avançados** - Busca baseada em datas das parcelas, não da transação
- **Edição Simplificada** - Padrão delete + recreate para transações parceladas
- **Interface Unificada** - Gestão completa de parcelas em uma única tela
- **Cálculos Corretos** - Summary baseado apenas nas parcelas do período filtrado
- **Textos Contextuais** - "Pagamento à vista" vs "Parcela X/Y" conforme apropriado
- **Conversão de Moedas** - Taxa manual para transferências internacionais
- **Transações Recorrentes** - Processamento automático agendado
- **Categorização Inteligente** - Sugestões baseadas em histórico
- **Validação Robusta** - Saldo, limites e integridade de dados
- **Auditoria Completa** - Rastreamento de todas as operações

### 📈 Dashboard e Relatórios
- **Visão geral financeira** com agregação de dados em tempo real
- **Gráficos de gastos** por categoria, membro e período
- **Evolução patrimonial** com tracking histórico
- **Insights automáticos** baseados em análise de padrões
- **Detecção de anomalias** em gastos e comportamentos
- **Análise de tendências** com previsões estatísticas

### 🎯 Planejamento e Metas
- **Orçamentos mensais** com cálculos de progresso em tempo real
- **Transações recorrentes** com processamento automático
- **Metas financeiras** com interface visual completa, marcos e progresso
- **Histórico de progresso** com tracking detalhado de evolução
- **Associação familiar** - vincular metas a membros específicos
- **Atualização manual** de progresso das metas
- **Projeções futuras** baseadas em histórico e recorrências

## 🔐 Segurança

- Autenticação JWT
- Rate limiting
- Validação de inputs
- Soft delete para auditoria
- HTTPS obrigatório em produção

## 🚀 Deploy

O projeto está preparado para deploy em VPS com Docker. Documentação de deploy será adicionada em breve.

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📝 Licença

MIT License - veja o arquivo LICENSE para detalhes.

## 🆘 Suporte

Para dúvidas ou problemas:
1. Verifique a documentação
2. Consulte as issues existentes
3. Abra uma nova issue se necessário

---

Desenvolvido com ❤️ usando TaskMaster-AI
