#!/usr/bin/env node

/**
 * Coverage Combine Script
 * Combines frontend and backend coverage reports into a unified report
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  frontend: {
    coverageDir: path.join(__dirname, '../frontend/coverage'),
    jsonFile: 'coverage-final.json',
    summaryFile: 'coverage-summary.json',
  },
  backend: {
    coverageDir: path.join(__dirname, '../backend/coverage'),
    jsonFile: 'coverage-final.json',
    summaryFile: 'coverage-summary.json',
  },
  output: {
    dir: path.join(__dirname, '../coverage-combined'),
    jsonFile: 'coverage-combined.json',
    summaryFile: 'coverage-summary-combined.json',
    htmlFile: 'index.html',
  },
};

/**
 * Ensure directory exists
 */
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * Read JSON file safely
 */
function readJsonFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(content);
    }
  } catch (error) {
    console.warn(`Warning: Could not read ${filePath}:`, error.message);
  }
  return null;
}

/**
 * Write JSON file safely
 */
function writeJsonFile(filePath, data) {
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error(`Error writing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Combine coverage data from frontend and backend
 */
function combineCoverageData() {
  console.log('🔄 Combining coverage data...');
  
  // Read frontend coverage
  const frontendCoverage = readJsonFile(
    path.join(config.frontend.coverageDir, config.frontend.jsonFile)
  );
  
  // Read backend coverage
  const backendCoverage = readJsonFile(
    path.join(config.backend.coverageDir, config.backend.jsonFile)
  );
  
  if (!frontendCoverage && !backendCoverage) {
    console.error('❌ No coverage data found in frontend or backend');
    return false;
  }
  
  // Combine coverage data
  const combinedCoverage = {
    ...frontendCoverage || {},
    ...backendCoverage || {},
  };
  
  // Ensure output directory exists
  ensureDir(config.output.dir);
  
  // Write combined coverage
  const success = writeJsonFile(
    path.join(config.output.dir, config.output.jsonFile),
    combinedCoverage
  );
  
  if (success) {
    console.log('✅ Combined coverage data written successfully');
    return combinedCoverage;
  }
  
  return false;
}

/**
 * Combine coverage summaries
 */
function combineCoverageSummaries() {
  console.log('🔄 Combining coverage summaries...');
  
  // Read frontend summary
  const frontendSummary = readJsonFile(
    path.join(config.frontend.coverageDir, config.frontend.summaryFile)
  );
  
  // Read backend summary
  const backendSummary = readJsonFile(
    path.join(config.backend.coverageDir, config.backend.summaryFile)
  );
  
  if (!frontendSummary && !backendSummary) {
    console.warn('⚠️ No coverage summary data found');
    return false;
  }
  
  // Calculate combined metrics
  const combinedSummary = {
    total: calculateCombinedMetrics(
      frontendSummary?.total || {},
      backendSummary?.total || {}
    ),
    frontend: frontendSummary?.total || null,
    backend: backendSummary?.total || null,
    timestamp: new Date().toISOString(),
  };
  
  // Write combined summary
  const success = writeJsonFile(
    path.join(config.output.dir, config.output.summaryFile),
    combinedSummary
  );
  
  if (success) {
    console.log('✅ Combined coverage summary written successfully');
    return combinedSummary;
  }
  
  return false;
}

/**
 * Calculate combined metrics from frontend and backend
 */
function calculateCombinedMetrics(frontend, backend) {
  const metrics = ['lines', 'statements', 'functions', 'branches'];
  const combined = {};
  
  metrics.forEach(metric => {
    const frontendMetric = frontend[metric] || { total: 0, covered: 0, skipped: 0, pct: 0 };
    const backendMetric = backend[metric] || { total: 0, covered: 0, skipped: 0, pct: 0 };
    
    const totalCount = frontendMetric.total + backendMetric.total;
    const coveredCount = frontendMetric.covered + backendMetric.covered;
    const skippedCount = frontendMetric.skipped + backendMetric.skipped;
    
    combined[metric] = {
      total: totalCount,
      covered: coveredCount,
      skipped: skippedCount,
      pct: totalCount > 0 ? Math.round((coveredCount / totalCount) * 100 * 100) / 100 : 0,
    };
  });
  
  return combined;
}

/**
 * Generate HTML report
 */
function generateHtmlReport(summary) {
  console.log('🔄 Generating HTML report...');
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Combined Coverage Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .metric-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-details { color: #666; font-size: 0.9em; margin-top: 5px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .coverage-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .coverage-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
        .coverage-text { text-align: center; line-height: 20px; color: white; font-weight: bold; font-size: 0.8em; }
        .timestamp { text-align: center; color: #666; font-size: 0.9em; margin-top: 20px; }
        .good { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Combined Coverage Report</h1>
        
        <div class="summary">
            ${generateMetricCard('Lines', summary.total.lines)}
            ${generateMetricCard('Statements', summary.total.statements)}
            ${generateMetricCard('Functions', summary.total.functions)}
            ${generateMetricCard('Branches', summary.total.branches)}
        </div>
        
        <div class="section">
            <h2>📈 Coverage Overview</h2>
            ${generateCoverageBar('Lines', summary.total.lines)}
            ${generateCoverageBar('Statements', summary.total.statements)}
            ${generateCoverageBar('Functions', summary.total.functions)}
            ${generateCoverageBar('Branches', summary.total.branches)}
        </div>
        
        <div class="section">
            <h2>🎯 Frontend vs Backend</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>Frontend Coverage</h3>
                    ${summary.frontend ? generateSubsectionMetrics(summary.frontend) : '<p>No frontend data available</p>'}
                </div>
                <div>
                    <h3>Backend Coverage</h3>
                    ${summary.backend ? generateSubsectionMetrics(summary.backend) : '<p>No backend data available</p>'}
                </div>
            </div>
        </div>
        
        <div class="timestamp">
            Generated on: ${new Date(summary.timestamp).toLocaleString()}
        </div>
    </div>
</body>
</html>
  `;
  
  const success = fs.writeFileSync(
    path.join(config.output.dir, config.output.htmlFile),
    html.trim()
  );
  
  console.log('✅ HTML report generated successfully');
  return true;
}

function generateMetricCard(title, metric) {
  const percentage = metric.pct || 0;
  const colorClass = percentage >= 80 ? 'good' : percentage >= 60 ? 'warning' : 'danger';
  
  return `
    <div class="metric-card">
        <div class="metric-title">${title}</div>
        <div class="metric-value ${colorClass}">${percentage}%</div>
        <div class="metric-details">
            ${metric.covered}/${metric.total} covered
            ${metric.skipped > 0 ? `(${metric.skipped} skipped)` : ''}
        </div>
    </div>
  `;
}

function generateCoverageBar(title, metric) {
  const percentage = metric.pct || 0;
  
  return `
    <div>
        <strong>${title}:</strong> ${metric.covered}/${metric.total}
        <div class="coverage-bar">
            <div class="coverage-fill" style="width: ${percentage}%">
                <div class="coverage-text">${percentage}%</div>
            </div>
        </div>
    </div>
  `;
}

function generateSubsectionMetrics(metrics) {
  return `
    <ul>
        <li>Lines: ${metrics.lines?.pct || 0}% (${metrics.lines?.covered || 0}/${metrics.lines?.total || 0})</li>
        <li>Statements: ${metrics.statements?.pct || 0}% (${metrics.statements?.covered || 0}/${metrics.statements?.total || 0})</li>
        <li>Functions: ${metrics.functions?.pct || 0}% (${metrics.functions?.covered || 0}/${metrics.functions?.total || 0})</li>
        <li>Branches: ${metrics.branches?.pct || 0}% (${metrics.branches?.covered || 0}/${metrics.branches?.total || 0})</li>
    </ul>
  `;
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting coverage combination process...');
  
  // Combine coverage data
  const combinedData = combineCoverageData();
  if (!combinedData) {
    console.error('❌ Failed to combine coverage data');
    process.exit(1);
  }
  
  // Combine summaries
  const combinedSummary = combineCoverageSummaries();
  if (!combinedSummary) {
    console.error('❌ Failed to combine coverage summaries');
    process.exit(1);
  }
  
  // Generate HTML report
  generateHtmlReport(combinedSummary);
  
  console.log('🎉 Coverage combination complete!');
  console.log(`📁 Combined reports available in: ${config.output.dir}`);
  
  // Print summary to console
  console.log('\n📊 Coverage Summary:');
  console.log(`Lines: ${combinedSummary.total.lines.pct}%`);
  console.log(`Statements: ${combinedSummary.total.statements.pct}%`);
  console.log(`Functions: ${combinedSummary.total.functions.pct}%`);
  console.log(`Branches: ${combinedSummary.total.branches.pct}%`);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  combineCoverageData,
  combineCoverageSummaries,
  generateHtmlReport,
  main,
};
