#!/bin/bash

# Comprehensive Test Coverage Script
# Runs all tests and generates combined coverage reports

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
BACKEND_DIR="$PROJECT_ROOT/backend"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"
COVERAGE_COMBINED_DIR="$PROJECT_ROOT/coverage-combined"

# Coverage thresholds
COVERAGE_THRESHOLD_LINES=75
COVERAGE_THRESHOLD_FUNCTIONS=75
COVERAGE_THRESHOLD_BRANCHES=70
COVERAGE_THRESHOLD_STATEMENTS=75

echo -e "${BLUE}🧪 Comprehensive Test Coverage Suite${NC}"
echo -e "${BLUE}====================================${NC}"
echo -e "Project Root: ${PROJECT_ROOT}"
echo -e "Frontend: ${FRONTEND_DIR}"
echo -e "Backend: ${BACKEND_DIR}"
echo ""

# Function to check if directory exists
check_directory() {
    local dir="$1"
    local name="$2"
    
    if [ ! -d "$dir" ]; then
        echo -e "${RED}❌ ${name} directory not found: ${dir}${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ ${name} directory found${NC}"
    return 0
}

# Function to run frontend tests
run_frontend_tests() {
    echo -e "${YELLOW}🎨 Running Frontend Tests...${NC}"
    
    cd "$FRONTEND_DIR"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ Frontend package.json not found${NC}"
        return 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
        npm install
    fi
    
    # Run tests with coverage
    echo -e "${YELLOW}🧪 Running frontend tests with coverage...${NC}"
    npm run test:coverage
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Frontend tests completed successfully${NC}"
        return 0
    else
        echo -e "${RED}❌ Frontend tests failed${NC}"
        return 1
    fi
}

# Function to run backend tests
run_backend_tests() {
    echo -e "${YELLOW}⚙️ Running Backend Tests...${NC}"
    
    cd "$BACKEND_DIR"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ Backend package.json not found${NC}"
        return 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 Installing backend dependencies...${NC}"
        npm install
    fi
    
    # Run tests with coverage
    echo -e "${YELLOW}🧪 Running backend tests with coverage...${NC}"
    npm run test:coverage
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Backend tests completed successfully${NC}"
        return 0
    else
        echo -e "${RED}❌ Backend tests failed${NC}"
        return 1
    fi
}

# Function to combine coverage reports
combine_coverage_reports() {
    echo -e "${YELLOW}🔄 Combining Coverage Reports...${NC}"
    
    cd "$PROJECT_ROOT"
    
    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js is not installed${NC}"
        return 1
    fi
    
    # Run coverage combination script
    node "$SCRIPTS_DIR/coverage-combine.js"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Coverage reports combined successfully${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to combine coverage reports${NC}"
        return 1
    fi
}

# Function to check coverage thresholds
check_coverage_thresholds() {
    echo -e "${YELLOW}🎯 Checking Coverage Thresholds...${NC}"
    
    local summary_file="$COVERAGE_COMBINED_DIR/coverage-summary-combined.json"
    
    if [ ! -f "$summary_file" ]; then
        echo -e "${RED}❌ Combined coverage summary not found${NC}"
        return 1
    fi
    
    # Extract coverage percentages using Node.js
    local coverage_data=$(node -e "
        const fs = require('fs');
        const data = JSON.parse(fs.readFileSync('$summary_file', 'utf8'));
        const total = data.total;
        console.log(JSON.stringify({
            lines: total.lines.pct,
            functions: total.functions.pct,
            branches: total.branches.pct,
            statements: total.statements.pct
        }));
    ")
    
    local lines=$(echo "$coverage_data" | node -e "console.log(JSON.parse(require('fs').readFileSync(0, 'utf8')).lines)")
    local functions=$(echo "$coverage_data" | node -e "console.log(JSON.parse(require('fs').readFileSync(0, 'utf8')).functions)")
    local branches=$(echo "$coverage_data" | node -e "console.log(JSON.parse(require('fs').readFileSync(0, 'utf8')).branches)")
    local statements=$(echo "$coverage_data" | node -e "console.log(JSON.parse(require('fs').readFileSync(0, 'utf8')).statements)")
    
    echo -e "${BLUE}📊 Coverage Results:${NC}"
    echo -e "Lines: ${lines}% (threshold: ${COVERAGE_THRESHOLD_LINES}%)"
    echo -e "Functions: ${functions}% (threshold: ${COVERAGE_THRESHOLD_FUNCTIONS}%)"
    echo -e "Branches: ${branches}% (threshold: ${COVERAGE_THRESHOLD_BRANCHES}%)"
    echo -e "Statements: ${statements}% (threshold: ${COVERAGE_THRESHOLD_STATEMENTS}%)"
    echo ""
    
    local failed=0
    
    # Check each threshold
    if (( $(echo "$lines < $COVERAGE_THRESHOLD_LINES" | bc -l) )); then
        echo -e "${RED}❌ Lines coverage below threshold: ${lines}% < ${COVERAGE_THRESHOLD_LINES}%${NC}"
        failed=1
    else
        echo -e "${GREEN}✅ Lines coverage meets threshold${NC}"
    fi
    
    if (( $(echo "$functions < $COVERAGE_THRESHOLD_FUNCTIONS" | bc -l) )); then
        echo -e "${RED}❌ Functions coverage below threshold: ${functions}% < ${COVERAGE_THRESHOLD_FUNCTIONS}%${NC}"
        failed=1
    else
        echo -e "${GREEN}✅ Functions coverage meets threshold${NC}"
    fi
    
    if (( $(echo "$branches < $COVERAGE_THRESHOLD_BRANCHES" | bc -l) )); then
        echo -e "${RED}❌ Branches coverage below threshold: ${branches}% < ${COVERAGE_THRESHOLD_BRANCHES}%${NC}"
        failed=1
    else
        echo -e "${GREEN}✅ Branches coverage meets threshold${NC}"
    fi
    
    if (( $(echo "$statements < $COVERAGE_THRESHOLD_STATEMENTS" | bc -l) )); then
        echo -e "${RED}❌ Statements coverage below threshold: ${statements}% < ${COVERAGE_THRESHOLD_STATEMENTS}%${NC}"
        failed=1
    else
        echo -e "${GREEN}✅ Statements coverage meets threshold${NC}"
    fi
    
    return $failed
}

# Function to generate coverage badge
generate_coverage_badge() {
    echo -e "${YELLOW}🏷️ Generating Coverage Badge...${NC}"
    
    local summary_file="$COVERAGE_COMBINED_DIR/coverage-summary-combined.json"
    
    if [ ! -f "$summary_file" ]; then
        echo -e "${RED}❌ Combined coverage summary not found${NC}"
        return 1
    fi
    
    # Extract overall coverage percentage
    local overall_coverage=$(node -e "
        const fs = require('fs');
        const data = JSON.parse(fs.readFileSync('$summary_file', 'utf8'));
        const total = data.total;
        const avg = Math.round((total.lines.pct + total.functions.pct + total.branches.pct + total.statements.pct) / 4);
        console.log(avg);
    ")
    
    # Determine badge color
    local badge_color="red"
    if [ "$overall_coverage" -ge 80 ]; then
        badge_color="brightgreen"
    elif [ "$overall_coverage" -ge 60 ]; then
        badge_color="yellow"
    fi
    
    # Generate badge URL
    local badge_url="https://img.shields.io/badge/coverage-${overall_coverage}%25-${badge_color}"
    
    # Create badge markdown
    local badge_md="![Coverage](${badge_url})"
    
    echo "$badge_md" > "$COVERAGE_COMBINED_DIR/coverage-badge.md"
    echo "$badge_url" > "$COVERAGE_COMBINED_DIR/coverage-badge-url.txt"
    
    echo -e "${GREEN}✅ Coverage badge generated: ${overall_coverage}%${NC}"
    echo -e "Badge URL: ${badge_url}"
    
    return 0
}

# Function to open coverage report
open_coverage_report() {
    local html_report="$COVERAGE_COMBINED_DIR/index.html"
    
    if [ -f "$html_report" ]; then
        echo -e "${YELLOW}🌐 Opening coverage report...${NC}"
        
        # Try to open in browser (cross-platform)
        if command -v open &> /dev/null; then
            open "$html_report"  # macOS
        elif command -v xdg-open &> /dev/null; then
            xdg-open "$html_report"  # Linux
        elif command -v start &> /dev/null; then
            start "$html_report"  # Windows
        else
            echo -e "${YELLOW}📁 Coverage report available at: ${html_report}${NC}"
        fi
    fi
}

# Main execution function
main() {
    local frontend_success=0
    local backend_success=0
    local combine_success=0
    local threshold_success=0
    
    echo -e "${YELLOW}Starting comprehensive test coverage...${NC}"
    echo ""
    
    # Check directories
    check_directory "$FRONTEND_DIR" "Frontend" || exit 1
    check_directory "$BACKEND_DIR" "Backend" || exit 1
    echo ""
    
    # Run frontend tests
    if run_frontend_tests; then
        frontend_success=1
    fi
    echo ""
    
    # Run backend tests
    if run_backend_tests; then
        backend_success=1
    fi
    echo ""
    
    # Combine coverage reports if at least one test suite passed
    if [ $frontend_success -eq 1 ] || [ $backend_success -eq 1 ]; then
        if combine_coverage_reports; then
            combine_success=1
        fi
        echo ""
        
        # Check coverage thresholds
        if [ $combine_success -eq 1 ]; then
            if check_coverage_thresholds; then
                threshold_success=1
            fi
            echo ""
            
            # Generate coverage badge
            generate_coverage_badge
            echo ""
        fi
    fi
    
    # Summary
    echo -e "${BLUE}📋 Test Coverage Summary:${NC}"
    echo -e "Frontend Tests: $([ $frontend_success -eq 1 ] && echo -e "${GREEN}✅ Passed${NC}" || echo -e "${RED}❌ Failed${NC}")"
    echo -e "Backend Tests: $([ $backend_success -eq 1 ] && echo -e "${GREEN}✅ Passed${NC}" || echo -e "${RED}❌ Failed${NC}")"
    echo -e "Coverage Combination: $([ $combine_success -eq 1 ] && echo -e "${GREEN}✅ Success${NC}" || echo -e "${RED}❌ Failed${NC}")"
    echo -e "Coverage Thresholds: $([ $threshold_success -eq 1 ] && echo -e "${GREEN}✅ Met${NC}" || echo -e "${RED}❌ Not Met${NC}")"
    echo ""
    
    if [ $combine_success -eq 1 ]; then
        echo -e "${GREEN}🎉 Coverage reports generated successfully!${NC}"
        echo -e "${BLUE}📁 Reports available in: ${COVERAGE_COMBINED_DIR}${NC}"
        
        # Open coverage report
        open_coverage_report
    fi
    
    # Exit with appropriate code
    if [ $frontend_success -eq 1 ] && [ $backend_success -eq 1 ] && [ $threshold_success -eq 1 ]; then
        exit 0
    else
        exit 1
    fi
}

# Parse command line arguments
case "${1:-all}" in
    "frontend")
        run_frontend_tests
        ;;
    "backend")
        run_backend_tests
        ;;
    "combine")
        combine_coverage_reports
        ;;
    "thresholds")
        check_coverage_thresholds
        ;;
    "badge")
        generate_coverage_badge
        ;;
    "all"|*)
        main
        ;;
esac
