// Base configuration for k6 performance tests
export const baseConfig = {
  // Test execution stages
  stages: [
    { duration: '30s', target: 10 },   // Ramp up to 10 users over 30s
    { duration: '1m', target: 10 },    // Stay at 10 users for 1m
    { duration: '30s', target: 50 },   // Ramp up to 50 users over 30s
    { duration: '2m', target: 50 },    // Stay at 50 users for 2m
    { duration: '30s', target: 100 },  // Ramp up to 100 users over 30s
    { duration: '2m', target: 100 },   // Stay at 100 users for 2m
    { duration: '30s', target: 0 },    // Ramp down to 0 users
  ],

  // Performance thresholds
  thresholds: {
    // HTTP request duration should be below 500ms for 95% of requests
    'http_req_duration': ['p(95)<500'],
    
    // HTTP request duration should be below 200ms for 90% of requests
    'http_req_duration{expected_response:true}': ['p(90)<200'],
    
    // HTTP request failure rate should be below 1%
    'http_req_failed': ['rate<0.01'],
    
    // Checks should pass 99% of the time
    'checks': ['rate>0.99'],
    
    // Data received should be consistent
    'data_received': ['count>0'],
    
    // Data sent should be consistent
    'data_sent': ['count>0'],
  },

  // Test options
  options: {
    // Disable load impact insights
    noConnectionReuse: false,
    
    // User agent
    userAgent: 'k6-performance-test/1.0',
    
    // Batch requests
    batch: 10,
    batchPerHost: 5,
    
    // HTTP configuration
    http: {
      responseCallback: null,
    },
    
    // Tags for metrics
    tags: {
      testType: 'performance',
      environment: 'test',
    },
  },
};

// Load test configuration (higher load)
export const loadTestConfig = {
  stages: [
    { duration: '1m', target: 50 },    // Ramp up to 50 users
    { duration: '3m', target: 100 },   // Ramp up to 100 users
    { duration: '5m', target: 200 },   // Ramp up to 200 users
    { duration: '10m', target: 200 },  // Stay at 200 users for 10m
    { duration: '2m', target: 0 },     // Ramp down
  ],
  
  thresholds: {
    'http_req_duration': ['p(95)<1000'],
    'http_req_duration{expected_response:true}': ['p(90)<500'],
    'http_req_failed': ['rate<0.05'],
    'checks': ['rate>0.95'],
  },
};

// Stress test configuration (breaking point)
export const stressTestConfig = {
  stages: [
    { duration: '2m', target: 100 },   // Ramp up to 100 users
    { duration: '5m', target: 200 },   // Ramp up to 200 users
    { duration: '5m', target: 300 },   // Ramp up to 300 users
    { duration: '5m', target: 400 },   // Ramp up to 400 users
    { duration: '10m', target: 400 },  // Stay at 400 users
    { duration: '3m', target: 0 },     // Ramp down
  ],
  
  thresholds: {
    'http_req_duration': ['p(95)<2000'],
    'http_req_failed': ['rate<0.1'],
    'checks': ['rate>0.90'],
  },
};

// Spike test configuration (sudden load)
export const spikeTestConfig = {
  stages: [
    { duration: '10s', target: 100 },  // Fast ramp up
    { duration: '1m', target: 100 },   // Stay at 100
    { duration: '10s', target: 1000 }, // Spike to 1000
    { duration: '3m', target: 1000 },  // Stay at spike
    { duration: '10s', target: 100 },  // Drop back
    { duration: '3m', target: 100 },   // Recover
    { duration: '10s', target: 0 },    // Ramp down
  ],
  
  thresholds: {
    'http_req_duration': ['p(95)<5000'],
    'http_req_failed': ['rate<0.2'],
  },
};

// Soak test configuration (endurance)
export const soakTestConfig = {
  stages: [
    { duration: '2m', target: 50 },    // Ramp up
    { duration: '30m', target: 50 },   // Stay for 30 minutes
    { duration: '2m', target: 0 },     // Ramp down
  ],
  
  thresholds: {
    'http_req_duration': ['p(95)<500'],
    'http_req_failed': ['rate<0.01'],
    'checks': ['rate>0.99'],
  },
};
