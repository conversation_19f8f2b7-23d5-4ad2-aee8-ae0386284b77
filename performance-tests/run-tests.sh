#!/bin/bash

# Performance Testing Suite Runner
# This script runs all k6 performance tests and generates reports

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL=${BASE_URL:-"http://localhost:3000"}
TEST_USER_EMAIL=${TEST_USER_EMAIL:-"<EMAIL>"}
TEST_USER_PASSWORD=${TEST_USER_PASSWORD:-"password123"}
REPORTS_DIR="./reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create reports directory
mkdir -p "$REPORTS_DIR"

echo -e "${BLUE}🚀 Performance Testing Suite${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "Base URL: ${BASE_URL}"
echo -e "Reports Directory: ${REPORTS_DIR}"
echo -e "Timestamp: ${TIMESTAMP}"
echo ""

# Check if k6 is installed
if ! command -v k6 &> /dev/null; then
    echo -e "${RED}❌ k6 is not installed. Please install k6 first.${NC}"
    echo -e "Visit: https://k6.io/docs/getting-started/installation/"
    exit 1
fi

# Check if API is accessible
echo -e "${YELLOW}🔍 Checking API accessibility...${NC}"
if curl -s -f "${BASE_URL}/api/v1/health" > /dev/null; then
    echo -e "${GREEN}✅ API is accessible${NC}"
else
    echo -e "${RED}❌ API is not accessible at ${BASE_URL}${NC}"
    echo -e "Please ensure the backend server is running."
    exit 1
fi

# Function to run a k6 test
run_test() {
    local test_name="$1"
    local test_file="$2"
    local test_type="$3"
    
    echo -e "${YELLOW}🧪 Running ${test_name}...${NC}"
    
    local report_file="${REPORTS_DIR}/${test_type}_${TIMESTAMP}.json"
    local html_report="${REPORTS_DIR}/${test_type}_${TIMESTAMP}.html"
    
    # Run k6 test with JSON output
    k6 run \
        --env BASE_URL="${BASE_URL}" \
        --env TEST_USER_EMAIL="${TEST_USER_EMAIL}" \
        --env TEST_USER_PASSWORD="${TEST_USER_PASSWORD}" \
        --out json="${report_file}" \
        "${test_file}"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ${test_name} completed successfully${NC}"
        
        # Generate HTML report if possible
        if command -v k6-reporter &> /dev/null; then
            k6-reporter "${report_file}" --output "${html_report}"
            echo -e "${GREEN}📊 HTML report generated: ${html_report}${NC}"
        fi
    else
        echo -e "${RED}❌ ${test_name} failed${NC}"
        return 1
    fi
    
    echo ""
}

# Function to run quick smoke test
run_smoke_test() {
    echo -e "${YELLOW}💨 Running Smoke Test...${NC}"
    
    local smoke_report="${REPORTS_DIR}/smoke_${TIMESTAMP}.json"
    
    # Quick smoke test with minimal load
    k6 run \
        --env BASE_URL="${BASE_URL}" \
        --env TEST_USER_EMAIL="${TEST_USER_EMAIL}" \
        --env TEST_USER_PASSWORD="${TEST_USER_PASSWORD}" \
        --vus 1 \
        --duration 30s \
        --out json="${smoke_report}" \
        ./scripts/api-endpoints.test.js
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Smoke test passed${NC}"
        return 0
    else
        echo -e "${RED}❌ Smoke test failed - skipping other tests${NC}"
        return 1
    fi
}

# Function to generate summary report
generate_summary() {
    echo -e "${BLUE}📊 Generating Performance Summary...${NC}"
    
    local summary_file="${REPORTS_DIR}/performance_summary_${TIMESTAMP}.md"
    
    cat > "${summary_file}" << EOF
# Performance Test Summary

**Date:** $(date)
**Base URL:** ${BASE_URL}
**Test Suite Version:** 1.0

## Test Results

EOF

    # Add results for each test type
    for report in "${REPORTS_DIR}"/*_"${TIMESTAMP}".json; do
        if [ -f "$report" ]; then
            local test_type=$(basename "$report" | cut -d'_' -f1)
            echo "### ${test_type^} Test" >> "${summary_file}"
            echo "" >> "${summary_file}"
            echo "- Report: \`$(basename "$report")\`" >> "${summary_file}"
            echo "- Status: ✅ Completed" >> "${summary_file}"
            echo "" >> "${summary_file}"
        fi
    done
    
    cat >> "${summary_file}" << EOF

## Recommendations

1. Review response time metrics for any endpoints exceeding thresholds
2. Check error rates and investigate any failures
3. Monitor resource usage during peak load
4. Optimize database queries if needed
5. Review cache hit rates and optimize caching strategy

## Next Steps

- Set up automated performance testing in CI/CD
- Establish performance baselines
- Create alerting for performance regressions
- Schedule regular performance reviews

EOF

    echo -e "${GREEN}📋 Summary report generated: ${summary_file}${NC}"
}

# Main execution
main() {
    echo -e "${YELLOW}Starting Performance Test Suite...${NC}"
    echo ""
    
    # Run smoke test first
    if ! run_smoke_test; then
        echo -e "${RED}Smoke test failed. Exiting.${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🎯 Running Full Performance Test Suite...${NC}"
    echo ""
    
    # Run all performance tests
    run_test "API Endpoints Performance Test" "./scripts/api-endpoints.test.js" "api_endpoints"
    run_test "Database Load Test" "./scripts/database-load.test.js" "database_load"
    run_test "Cache Performance Test" "./scripts/cache-performance.test.js" "cache_performance"
    run_test "Stress Test" "./scripts/stress-test.js" "stress_test"
    
    # Generate summary
    generate_summary
    
    echo -e "${GREEN}🎉 Performance Testing Suite Complete!${NC}"
    echo -e "${BLUE}📁 All reports saved to: ${REPORTS_DIR}${NC}"
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "1. Review the generated reports"
    echo -e "2. Analyze performance metrics and bottlenecks"
    echo -e "3. Optimize based on findings"
    echo -e "4. Set up automated performance monitoring"
}

# Parse command line arguments
case "${1:-all}" in
    "smoke")
        run_smoke_test
        ;;
    "api")
        run_test "API Endpoints Test" "./scripts/api-endpoints.test.js" "api_endpoints"
        ;;
    "database")
        run_test "Database Load Test" "./scripts/database-load.test.js" "database_load"
        ;;
    "cache")
        run_test "Cache Performance Test" "./scripts/cache-performance.test.js" "cache_performance"
        ;;
    "stress")
        run_test "Stress Test" "./scripts/stress-test.js" "stress_test"
        ;;
    "all"|*)
        main
        ;;
esac
