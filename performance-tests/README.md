# Performance Testing Suite

This directory contains comprehensive performance tests for the Personal Finance Manager API using k6.

## 📋 Overview

The performance testing suite includes:

- **API Endpoints Test**: Tests all critical API endpoints under normal load
- **Database Load Test**: Focuses on database performance under heavy query load
- **Cache Performance Test**: Validates Redis caching effectiveness
- **Stress Test**: Pushes the system to breaking point to identify limits

## 🛠️ Prerequisites

### 1. Install k6

**macOS:**
```bash
brew install k6
```

**Linux:**
```bash
sudo gpg -k
sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
sudo apt-get update
sudo apt-get install k6
```

**Windows:**
```bash
winget install k6
```

### 2. Ensure Backend is Running

Make sure your backend server is running on `http://localhost:3000` (or set `BASE_URL` environment variable).

### 3. Test User Setup

Ensure you have a test user account:
- Email: `<EMAIL>`
- Password: `password123`

Or set custom credentials via environment variables:
```bash
export TEST_USER_EMAIL="<EMAIL>"
export TEST_USER_PASSWORD="your-password"
```

## 🚀 Running Tests

### Quick Start

Run all performance tests:
```bash
./run-tests.sh
```

### Individual Tests

Run specific test suites:

```bash
# Smoke test (quick validation)
./run-tests.sh smoke

# API endpoints performance
./run-tests.sh api

# Database load testing
./run-tests.sh database

# Cache performance testing
./run-tests.sh cache

# Stress testing
./run-tests.sh stress
```

### Custom Configuration

Set environment variables for custom configuration:

```bash
export BASE_URL="http://localhost:3000"
export TEST_USER_EMAIL="<EMAIL>"
export TEST_USER_PASSWORD="password123"

./run-tests.sh
```

## 📊 Test Scenarios

### 1. API Endpoints Test (`api-endpoints.test.js`)

**Load Pattern:**
- Ramp up: 10 → 50 → 100 users over 2 minutes
- Sustain: 100 users for 2 minutes
- Ramp down: 30 seconds

**Tests:**
- Dashboard workflow
- Account management (CRUD)
- Transaction management (CRUD)
- Category management
- Reports and analytics

**Thresholds:**
- 95% of requests < 500ms
- 90% of requests < 200ms
- Error rate < 1%
- Check success rate > 99%

### 2. Database Load Test (`database-load.test.js`)

**Load Pattern:**
- Progressive load: 50 → 100 → 200 users over 9 minutes
- Sustain: 200 users for 10 minutes

**Tests:**
- Complex transaction queries
- Bulk transaction creation
- Advanced filtering and pagination
- Analytics aggregations

**Thresholds:**
- 95% of requests < 1000ms
- 90% of requests < 500ms
- Error rate < 5%

### 3. Cache Performance Test (`cache-performance.test.js`)

**Load Pattern:**
- Moderate load: 20 → 50 users over 2.5 minutes
- Sustain: 50 users for 3 minutes

**Tests:**
- Dashboard caching
- Transaction list caching
- Analytics caching
- Account data caching

**Thresholds:**
- 95% of requests < 300ms
- Cached requests < 100ms
- Non-cached requests < 500ms
- Cache hit rate > 70%

### 4. Stress Test (`stress-test.js`)

**Load Pattern:**
- Aggressive ramp: 100 → 200 → 300 → 400 users over 17 minutes
- Sustain: 400 users for 10 minutes

**Tests:**
- Critical endpoint stress
- Database write stress
- Concurrent read stress
- Memory-intensive operations

**Thresholds:**
- 95% of requests < 2000ms
- Error rate < 10%
- Check success rate > 90%

## 📈 Metrics and Reports

### Generated Reports

After running tests, reports are saved in the `reports/` directory:

- `{test_type}_{timestamp}.json` - Raw k6 metrics
- `{test_type}_{timestamp}.html` - HTML report (if k6-reporter installed)
- `performance_summary_{timestamp}.md` - Summary report

### Key Metrics

**Response Time Metrics:**
- `http_req_duration` - Request duration
- `http_req_waiting` - Time waiting for response
- `http_req_connecting` - Connection time

**Throughput Metrics:**
- `http_reqs` - Total HTTP requests
- `data_received` - Data received
- `data_sent` - Data sent

**Error Metrics:**
- `http_req_failed` - Failed request rate
- `checks` - Check success rate

**Custom Metrics:**
- `login_duration` - Login operation time
- `dashboard_load_duration` - Dashboard load time
- `transaction_creation_duration` - Transaction creation time
- `cache_hit_rate` - Cache effectiveness

## 🎯 Performance Thresholds

### Response Time Targets

| Operation | Target (95th percentile) | Acceptable (95th percentile) |
|-----------|-------------------------|------------------------------|
| Login | < 200ms | < 500ms |
| Dashboard Load | < 300ms | < 1000ms |
| Transaction List | < 400ms | < 1000ms |
| Transaction Create | < 300ms | < 800ms |
| Analytics | < 1000ms | < 3000ms |

### Error Rate Targets

| Test Type | Target Error Rate | Maximum Acceptable |
|-----------|------------------|-------------------|
| Normal Load | < 0.1% | < 1% |
| High Load | < 1% | < 5% |
| Stress Test | < 5% | < 10% |

### Cache Performance Targets

| Metric | Target | Minimum Acceptable |
|--------|--------|--------------------|
| Cache Hit Rate | > 80% | > 70% |
| Cached Response Time | < 50ms | < 100ms |

## 🔧 Troubleshooting

### Common Issues

**1. Connection Refused**
```
Error: dial tcp 127.0.0.1:3000: connect: connection refused
```
- Ensure backend server is running
- Check BASE_URL configuration

**2. Authentication Failures**
```
Error: login status is 200: false
```
- Verify test user credentials
- Check user exists in database
- Verify JWT configuration

**3. High Error Rates**
```
✗ http_req_failed: rate>0.01
```
- Check server logs for errors
- Verify database connectivity
- Check Redis connectivity
- Review rate limiting configuration

**4. Slow Response Times**
```
✗ http_req_duration: p(95)<500
```
- Check database query performance
- Review cache hit rates
- Monitor server resource usage
- Optimize slow endpoints

### Performance Optimization Tips

1. **Database Optimization**
   - Add indexes for frequently queried fields
   - Optimize complex queries
   - Use connection pooling
   - Consider read replicas

2. **Caching Strategy**
   - Implement Redis caching for expensive operations
   - Use appropriate cache TTL values
   - Cache frequently accessed data
   - Implement cache warming

3. **API Optimization**
   - Implement pagination for large datasets
   - Use compression for responses
   - Optimize JSON serialization
   - Implement rate limiting

4. **Infrastructure Scaling**
   - Scale horizontally with load balancers
   - Use CDN for static assets
   - Implement auto-scaling
   - Monitor resource usage

## 📚 Additional Resources

- [k6 Documentation](https://k6.io/docs/)
- [Performance Testing Best Practices](https://k6.io/docs/testing-guides/performance-testing/)
- [k6 Metrics Reference](https://k6.io/docs/using-k6/metrics/)
- [Load Testing Patterns](https://k6.io/docs/test-types/)
