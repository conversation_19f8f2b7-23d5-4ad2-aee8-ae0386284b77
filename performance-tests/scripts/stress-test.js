import http from 'k6/http';
import { check, sleep } from 'k6';
import { stressTestConfig } from '../configs/base.js';
import { login, getAuthHeaders } from '../utils/auth.js';
import { generateTransaction, generateAccount } from '../utils/data-generators.js';
import { recordApiCall, recordSystemMetrics } from '../utils/metrics.js';

// Stress test configuration - push system to breaking point
export let options = {
  stages: stressTestConfig.stages,
  thresholds: stressTestConfig.thresholds,
  tags: {
    testType: 'stress_test',
    environment: 'test',
  },
};

// Test environment configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const TEST_USER_EMAIL = __ENV.TEST_USER_EMAIL || '<EMAIL>';
const TEST_USER_PASSWORD = __ENV.TEST_USER_PASSWORD || 'password123';

let authToken;

export function setup() {
  console.log('⚡ Starting Stress Tests - Finding Breaking Point');
  console.log(`Base URL: ${BASE_URL}`);
  console.log('⚠️  This test will push the system to its limits');
  
  // Health check
  const healthCheck = http.get(`${BASE_URL}/api/v1/health`);
  if (healthCheck.status !== 200) {
    throw new Error(`API health check failed: ${healthCheck.status}`);
  }
  
  console.log('✅ Stress test setup complete');
  return { baseUrl: BASE_URL };
}

export default function(data) {
  const baseUrl = data.baseUrl;
  
  // Login once per VU
  if (!authToken) {
    authToken = login(baseUrl, {
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD,
    });
    
    if (!authToken) {
      console.error('❌ Login failed for stress test');
      return;
    }
  }
  
  const headers = getAuthHeaders(authToken);
  
  // Record current user count for system metrics
  recordSystemMetrics(__VU, undefined, undefined);
  
  // Stress test scenarios - aggressive load
  stressTestCriticalEndpoints(baseUrl, headers);
  stressTestDatabaseWrites(baseUrl, headers);
  stressTestConcurrentReads(baseUrl, headers);
  stressTestMemoryIntensive(baseUrl, headers);
  
  // Minimal sleep - aggressive testing
  sleep(Math.random() * 0.5);
}

function stressTestCriticalEndpoints(baseUrl, headers) {
  const group = 'Critical Endpoints Stress';
  
  // Rapid-fire requests to critical endpoints
  const criticalEndpoints = [
    '/api/v1/dashboard',
    '/api/v1/accounts',
    '/api/v1/transactions?limit=50',
    '/api/v1/categories',
  ];
  
  // Hit multiple endpoints rapidly
  criticalEndpoints.forEach((endpoint, index) => {
    const requestStart = Date.now();
    const response = http.get(`${baseUrl}${endpoint}`, {
      headers: headers,
      tags: { 
        name: `stress_critical_${index}`, 
        group: group,
        endpoint: endpoint
      },
    });
    const requestDuration = Date.now() - requestStart;
    
    const success = check(response, {
      [`critical endpoint ${endpoint} responds`]: (r) => r.status < 500,
      [`critical endpoint ${endpoint} response time acceptable`]: (r) => r.timings.duration < 5000,
    });
    
    recordApiCall(`stress_critical_${index}`, requestDuration, response.status, success);
    
    // No sleep between requests - maximum stress
  });
}

function stressTestDatabaseWrites(baseUrl, headers) {
  const group = 'Database Write Stress';
  
  // Rapid transaction creation to stress database writes
  const transactionCount = Math.floor(Math.random() * 3) + 1; // 1-3 transactions
  
  for (let i = 0; i < transactionCount; i++) {
    const newTransaction = generateTransaction();
    
    const createStart = Date.now();
    const createResponse = http.post(
      `${baseUrl}/api/v1/transactions`,
      JSON.stringify(newTransaction),
      {
        headers: headers,
        tags: { 
          name: 'stress_db_write', 
          group: group,
          operation: 'create_transaction'
        },
      }
    );
    const createDuration = Date.now() - createStart;
    
    const createSuccess = check(createResponse, {
      'stress db write responds': (r) => r.status < 500,
      'stress db write completes': (r) => r.status === 201 || r.status === 429, // Accept rate limiting
    });
    
    recordApiCall('stress_db_write', createDuration, createResponse.status, createSuccess);
    
    // No sleep - maximum write stress
  }
  
  // Also stress account creation
  if (Math.random() > 0.7) { // 30% chance
    const newAccount = generateAccount();
    
    const accountCreateStart = Date.now();
    const accountCreateResponse = http.post(
      `${baseUrl}/api/v1/accounts`,
      JSON.stringify(newAccount),
      {
        headers: headers,
        tags: { 
          name: 'stress_account_create', 
          group: group,
          operation: 'create_account'
        },
      }
    );
    const accountCreateDuration = Date.now() - accountCreateStart;
    
    const accountCreateSuccess = check(accountCreateResponse, {
      'stress account create responds': (r) => r.status < 500,
    });
    
    recordApiCall('stress_account_create', accountCreateDuration, accountCreateResponse.status, accountCreateSuccess);
  }
}

function stressTestConcurrentReads(baseUrl, headers) {
  const group = 'Concurrent Read Stress';
  
  // Simulate concurrent read operations
  const readOperations = [
    () => http.get(`${baseUrl}/api/v1/transactions?page=${Math.floor(Math.random() * 10) + 1}&limit=100`, { headers }),
    () => http.get(`${baseUrl}/api/v1/analytics/spending-summary`, { headers }),
    () => http.get(`${baseUrl}/api/v1/analytics/monthly-trends`, { headers }),
    () => http.get(`${baseUrl}/api/v1/accounts`, { headers }),
    () => http.get(`${baseUrl}/api/v1/categories`, { headers }),
  ];
  
  // Execute multiple read operations rapidly
  const operationCount = Math.floor(Math.random() * 3) + 2; // 2-4 operations
  
  for (let i = 0; i < operationCount; i++) {
    const randomOperation = readOperations[Math.floor(Math.random() * readOperations.length)];
    
    const readStart = Date.now();
    const readResponse = randomOperation();
    const readDuration = Date.now() - readStart;
    
    const readSuccess = check(readResponse, {
      'stress concurrent read responds': (r) => r.status < 500,
      'stress concurrent read acceptable time': (r) => r.timings.duration < 10000, // 10s max under stress
    });
    
    recordApiCall('stress_concurrent_read', readDuration, readResponse.status, readSuccess);
  }
}

function stressTestMemoryIntensive(baseUrl, headers) {
  const group = 'Memory Intensive Stress';
  
  // Operations that consume more memory/CPU
  const memoryIntensiveOps = [
    // Large transaction lists
    () => http.get(`${baseUrl}/api/v1/transactions?limit=500`, { headers }),
    
    // Complex analytics
    () => http.get(`${baseUrl}/api/v1/analytics/category-breakdown?period=yearly`, { headers }),
    
    // Date range queries
    () => http.get(`${baseUrl}/api/v1/transactions?startDate=2020-01-01&endDate=2024-12-31&limit=1000`, { headers }),
  ];
  
  // Execute memory-intensive operation
  if (Math.random() > 0.5) { // 50% chance
    const randomOp = memoryIntensiveOps[Math.floor(Math.random() * memoryIntensiveOps.length)];
    
    const memoryStart = Date.now();
    const memoryResponse = randomOp();
    const memoryDuration = Date.now() - memoryStart;
    
    const memorySuccess = check(memoryResponse, {
      'stress memory intensive responds': (r) => r.status < 500,
      'stress memory intensive completes': (r) => r.timings.duration < 15000, // 15s max
    });
    
    recordApiCall('stress_memory_intensive', memoryDuration, memoryResponse.status, memorySuccess);
    
    // Record estimated memory usage (simplified)
    const estimatedMemoryUsage = memoryDuration * 0.1; // Rough estimate
    recordSystemMetrics(undefined, estimatedMemoryUsage, undefined);
  }
}

export function teardown(data) {
  console.log('⚡ Stress test complete');
  console.log('📊 Check metrics to identify breaking points and bottlenecks');
  console.log('🔧 Use results to optimize system performance and scaling');
}
