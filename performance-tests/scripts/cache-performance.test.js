import http from 'k6/http';
import { check, sleep } from 'k6';
import { baseConfig } from '../configs/base.js';
import { login, getAuthHeaders } from '../utils/auth.js';
import { recordApiCall, recordCacheMetrics } from '../utils/metrics.js';

// Test configuration for cache performance
export let options = {
  stages: [
    { duration: '30s', target: 20 },   // Ramp up to 20 users
    { duration: '2m', target: 50 },    // Ramp up to 50 users
    { duration: '3m', target: 50 },    // Stay at 50 users
    { duration: '30s', target: 0 },    // Ramp down
  ],
  thresholds: {
    'http_req_duration': ['p(95)<300'],
    'http_req_duration{cached:true}': ['p(95)<100'],  // Cached responses should be faster
    'http_req_duration{cached:false}': ['p(95)<500'], // Non-cached responses
    'http_req_failed': ['rate<0.01'],
    'cache_hit_rate': ['rate>0.7'],  // Expect 70%+ cache hit rate
  },
  tags: {
    testType: 'cache_performance',
    environment: 'test',
  },
};

// Test environment configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const TEST_USER_EMAIL = __ENV.TEST_USER_EMAIL || '<EMAIL>';
const TEST_USER_PASSWORD = __ENV.TEST_USER_PASSWORD || 'password123';

let authToken;

export function setup() {
  console.log('🗄️ Starting Cache Performance Tests');
  console.log(`Base URL: ${BASE_URL}`);
  
  // Health check
  const healthCheck = http.get(`${BASE_URL}/api/v1/health`);
  if (healthCheck.status !== 200) {
    throw new Error(`API health check failed: ${healthCheck.status}`);
  }
  
  console.log('✅ Cache performance test setup complete');
  return { baseUrl: BASE_URL };
}

export default function(data) {
  const baseUrl = data.baseUrl;
  
  // Login once per VU
  if (!authToken) {
    authToken = login(baseUrl, {
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD,
    });
    
    if (!authToken) {
      console.error('❌ Login failed for cache performance test');
      return;
    }
  }
  
  const headers = getAuthHeaders(authToken);
  
  // Cache performance scenarios
  testDashboardCaching(baseUrl, headers);
  testTransactionListCaching(baseUrl, headers);
  testAnalyticsCaching(baseUrl, headers);
  testAccountDataCaching(baseUrl, headers);
  
  // Sleep to simulate user behavior
  sleep(Math.random() * 2 + 1);
}

function testDashboardCaching(baseUrl, headers) {
  const group = 'Dashboard Caching';
  
  // First request - should miss cache and populate it
  const firstRequestStart = Date.now();
  const firstResponse = http.get(`${baseUrl}/api/v1/dashboard`, {
    headers: headers,
    tags: { 
      name: 'dashboard_first_request', 
      group: group,
      cached: 'false'
    },
  });
  const firstRequestDuration = Date.now() - firstRequestStart;
  
  const firstRequestSuccess = check(firstResponse, {
    'dashboard first request status is 200': (r) => r.status === 200,
    'dashboard first request has data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data;
      } catch (e) {
        return false;
      }
    },
  });
  
  recordApiCall('dashboard_cache_miss', firstRequestDuration, firstResponse.status, firstRequestSuccess);
  recordCacheMetrics(false, true); // Cache miss
  
  // Small delay to ensure cache is populated
  sleep(0.1);
  
  // Second request - should hit cache
  const secondRequestStart = Date.now();
  const secondResponse = http.get(`${baseUrl}/api/v1/dashboard`, {
    headers: headers,
    tags: { 
      name: 'dashboard_cached_request', 
      group: group,
      cached: 'true'
    },
  });
  const secondRequestDuration = Date.now() - secondRequestStart;
  
  const secondRequestSuccess = check(secondResponse, {
    'dashboard cached request status is 200': (r) => r.status === 200,
    'dashboard cached request is faster': (r) => r.timings.duration < firstResponse.timings.duration,
    'dashboard cached request has same data structure': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data;
      } catch (e) {
        return false;
      }
    },
  });
  
  recordApiCall('dashboard_cache_hit', secondRequestDuration, secondResponse.status, secondRequestSuccess);
  recordCacheMetrics(true, false); // Cache hit
}

function testTransactionListCaching(baseUrl, headers) {
  const group = 'Transaction List Caching';
  
  // Test caching of transaction lists with different parameters
  const queryParams = [
    '?limit=20',
    '?limit=50',
    '?page=1&limit=20',
    '?type=EXPENSE&limit=20',
    '?startDate=2024-01-01&endDate=2024-12-31&limit=20',
  ];
  
  const randomQuery = queryParams[Math.floor(Math.random() * queryParams.length)];
  
  // First request
  const firstRequestStart = Date.now();
  const firstResponse = http.get(`${baseUrl}/api/v1/transactions${randomQuery}`, {
    headers: headers,
    tags: { 
      name: 'transactions_first_request', 
      group: group,
      cached: 'false'
    },
  });
  const firstRequestDuration = Date.now() - firstRequestStart;
  
  const firstRequestSuccess = check(firstResponse, {
    'transactions first request status is 200': (r) => r.status === 200,
  });
  
  recordApiCall('transactions_cache_miss', firstRequestDuration, firstResponse.status, firstRequestSuccess);
  recordCacheMetrics(false, true);
  
  sleep(0.1);
  
  // Second request (should be cached)
  const secondRequestStart = Date.now();
  const secondResponse = http.get(`${baseUrl}/api/v1/transactions${randomQuery}`, {
    headers: headers,
    tags: { 
      name: 'transactions_cached_request', 
      group: group,
      cached: 'true'
    },
  });
  const secondRequestDuration = Date.now() - secondRequestStart;
  
  const secondRequestSuccess = check(secondResponse, {
    'transactions cached request status is 200': (r) => r.status === 200,
    'transactions cached request is faster': (r) => r.timings.duration < firstResponse.timings.duration,
  });
  
  recordApiCall('transactions_cache_hit', secondRequestDuration, secondResponse.status, secondRequestSuccess);
  recordCacheMetrics(true, false);
}

function testAnalyticsCaching(baseUrl, headers) {
  const group = 'Analytics Caching';
  
  // Analytics endpoints that should be heavily cached due to expensive computations
  const analyticsEndpoints = [
    '/api/v1/analytics/spending-summary',
    '/api/v1/analytics/monthly-trends',
    '/api/v1/analytics/category-breakdown',
  ];
  
  const randomEndpoint = analyticsEndpoints[Math.floor(Math.random() * analyticsEndpoints.length)];
  
  // First request - expensive computation
  const firstRequestStart = Date.now();
  const firstResponse = http.get(`${baseUrl}${randomEndpoint}`, {
    headers: headers,
    tags: { 
      name: 'analytics_first_request', 
      group: group,
      cached: 'false'
    },
  });
  const firstRequestDuration = Date.now() - firstRequestStart;
  
  const firstRequestSuccess = check(firstResponse, {
    'analytics first request status is 200': (r) => r.status === 200,
    'analytics first request has computation time': (r) => r.timings.duration > 100, // Should take some time
  });
  
  recordApiCall('analytics_cache_miss', firstRequestDuration, firstResponse.status, firstRequestSuccess);
  recordCacheMetrics(false, true);
  
  sleep(0.1);
  
  // Second request - should be much faster from cache
  const secondRequestStart = Date.now();
  const secondResponse = http.get(`${baseUrl}${randomEndpoint}`, {
    headers: headers,
    tags: { 
      name: 'analytics_cached_request', 
      group: group,
      cached: 'true'
    },
  });
  const secondRequestDuration = Date.now() - secondRequestStart;
  
  const secondRequestSuccess = check(secondResponse, {
    'analytics cached request status is 200': (r) => r.status === 200,
    'analytics cached request is significantly faster': (r) => {
      return r.timings.duration < (firstResponse.timings.duration * 0.5);
    },
  });
  
  recordApiCall('analytics_cache_hit', secondRequestDuration, secondResponse.status, secondRequestSuccess);
  recordCacheMetrics(true, false);
}

function testAccountDataCaching(baseUrl, headers) {
  const group = 'Account Data Caching';
  
  // Test account list caching
  const firstRequestStart = Date.now();
  const firstResponse = http.get(`${baseUrl}/api/v1/accounts`, {
    headers: headers,
    tags: { 
      name: 'accounts_first_request', 
      group: group,
      cached: 'false'
    },
  });
  const firstRequestDuration = Date.now() - firstRequestStart;
  
  const firstRequestSuccess = check(firstResponse, {
    'accounts first request status is 200': (r) => r.status === 200,
  });
  
  recordApiCall('accounts_cache_miss', firstRequestDuration, firstResponse.status, firstRequestSuccess);
  recordCacheMetrics(false, true);
  
  sleep(0.1);
  
  // Cached request
  const secondRequestStart = Date.now();
  const secondResponse = http.get(`${baseUrl}/api/v1/accounts`, {
    headers: headers,
    tags: { 
      name: 'accounts_cached_request', 
      group: group,
      cached: 'true'
    },
  });
  const secondRequestDuration = Date.now() - secondRequestStart;
  
  const secondRequestSuccess = check(secondResponse, {
    'accounts cached request status is 200': (r) => r.status === 200,
    'accounts cached request is faster': (r) => r.timings.duration < firstResponse.timings.duration,
  });
  
  recordApiCall('accounts_cache_hit', secondRequestDuration, secondResponse.status, secondRequestSuccess);
  recordCacheMetrics(true, false);
}

export function teardown(data) {
  console.log('🧹 Cache performance test cleanup complete');
}
