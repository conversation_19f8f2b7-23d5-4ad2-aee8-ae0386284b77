import http from 'k6/http';
import { check, sleep } from 'k6';
import { baseConfig } from '../configs/base.js';
import { login, getAuthHeaders } from '../utils/auth.js';
import { generateAccount, generateTransaction, generateCategory } from '../utils/data-generators.js';
import { recordLogin, recordApiCall } from '../utils/metrics.js';

// Test configuration
export let options = {
  stages: baseConfig.stages,
  thresholds: baseConfig.thresholds,
  tags: baseConfig.options.tags,
};

// Test environment configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const TEST_USER_EMAIL = __ENV.TEST_USER_EMAIL || '<EMAIL>';
const TEST_USER_PASSWORD = __ENV.TEST_USER_PASSWORD || 'password123';

// Global variables
let authToken;

export function setup() {
  console.log('🚀 Starting API Performance Tests');
  console.log(`Base URL: ${BASE_URL}`);
  
  // Verify API is accessible
  const healthCheck = http.get(`${BASE_URL}/api/v1/health`);
  
  if (healthCheck.status !== 200) {
    throw new Error(`API health check failed: ${healthCheck.status}`);
  }
  
  console.log('✅ API health check passed');
  return { baseUrl: BASE_URL };
}

export default function(data) {
  const baseUrl = data.baseUrl;
  
  // Login phase
  const loginStart = Date.now();
  authToken = login(baseUrl, {
    email: TEST_USER_EMAIL,
    password: TEST_USER_PASSWORD,
  });
  const loginDuration = Date.now() - loginStart;
  
  const loginSuccess = authToken !== null;
  recordLogin(loginDuration, loginSuccess);
  
  if (!loginSuccess) {
    console.error('❌ Login failed, skipping user session');
    return;
  }
  
  const headers = getAuthHeaders(authToken);
  
  // Test scenario: User dashboard workflow
  testDashboardWorkflow(baseUrl, headers);
  
  // Test scenario: Account management
  testAccountManagement(baseUrl, headers);
  
  // Test scenario: Transaction management
  testTransactionManagement(baseUrl, headers);
  
  // Test scenario: Category management
  testCategoryManagement(baseUrl, headers);
  
  // Test scenario: Reports and analytics
  testReportsAndAnalytics(baseUrl, headers);
  
  // Random sleep between 1-3 seconds to simulate user behavior
  sleep(Math.random() * 2 + 1);
}

function testDashboardWorkflow(baseUrl, headers) {
  const group = 'Dashboard Workflow';
  
  // Load dashboard data
  const dashboardStart = Date.now();
  const dashboardResponse = http.get(`${baseUrl}/api/v1/dashboard`, {
    headers: headers,
    tags: { name: 'dashboard_load', group: group },
  });
  const dashboardDuration = Date.now() - dashboardStart;
  
  const dashboardSuccess = check(dashboardResponse, {
    'dashboard status is 200': (r) => r.status === 200,
    'dashboard has data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data;
      } catch (e) {
        return false;
      }
    },
  });
  
  recordApiCall('dashboard_load', dashboardDuration, dashboardResponse.status, dashboardSuccess);
  
  // Load recent transactions
  const recentTransactionsResponse = http.get(`${baseUrl}/api/v1/transactions?limit=10`, {
    headers: headers,
    tags: { name: 'recent_transactions', group: group },
  });
  
  check(recentTransactionsResponse, {
    'recent transactions status is 200': (r) => r.status === 200,
  });
}

function testAccountManagement(baseUrl, headers) {
  const group = 'Account Management';
  
  // List accounts
  const accountsResponse = http.get(`${baseUrl}/api/v1/accounts`, {
    headers: headers,
    tags: { name: 'list_accounts', group: group },
  });
  
  check(accountsResponse, {
    'accounts list status is 200': (r) => r.status === 200,
  });
  
  // Create new account
  const newAccount = generateAccount();
  const createAccountStart = Date.now();
  const createAccountResponse = http.post(
    `${baseUrl}/api/v1/accounts`,
    JSON.stringify(newAccount),
    {
      headers: headers,
      tags: { name: 'create_account', group: group },
    }
  );
  const createAccountDuration = Date.now() - createAccountStart;
  
  const createAccountSuccess = check(createAccountResponse, {
    'create account status is 201': (r) => r.status === 201,
    'create account response has id': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data && body.data.id;
      } catch (e) {
        return false;
      }
    },
  });
  
  recordApiCall('account_creation', createAccountDuration, createAccountResponse.status, createAccountSuccess);
  
  // Get account details (if creation was successful)
  if (createAccountSuccess) {
    try {
      const accountData = JSON.parse(createAccountResponse.body);
      const accountId = accountData.data.id;
      
      const accountDetailsResponse = http.get(`${baseUrl}/api/v1/accounts/${accountId}`, {
        headers: headers,
        tags: { name: 'get_account', group: group },
      });
      
      check(accountDetailsResponse, {
        'account details status is 200': (r) => r.status === 200,
      });
    } catch (e) {
      console.error('Failed to get account details:', e.message);
    }
  }
}

function testTransactionManagement(baseUrl, headers) {
  const group = 'Transaction Management';
  
  // List transactions with pagination
  const transactionsStart = Date.now();
  const transactionsResponse = http.get(`${baseUrl}/api/v1/transactions?page=1&limit=20`, {
    headers: headers,
    tags: { name: 'list_transactions', group: group },
  });
  const transactionsDuration = Date.now() - transactionsStart;
  
  const transactionsSuccess = check(transactionsResponse, {
    'transactions list status is 200': (r) => r.status === 200,
    'transactions response has data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data;
      } catch (e) {
        return false;
      }
    },
  });
  
  recordApiCall('transaction_list', transactionsDuration, transactionsResponse.status, transactionsSuccess);
  
  // Create new transaction
  const newTransaction = generateTransaction();
  const createTransactionStart = Date.now();
  const createTransactionResponse = http.post(
    `${baseUrl}/api/v1/transactions`,
    JSON.stringify(newTransaction),
    {
      headers: headers,
      tags: { name: 'create_transaction', group: group },
    }
  );
  const createTransactionDuration = Date.now() - createTransactionStart;
  
  const createTransactionSuccess = check(createTransactionResponse, {
    'create transaction status is 201': (r) => r.status === 201,
  });
  
  recordApiCall('transaction_creation', createTransactionDuration, createTransactionResponse.status, createTransactionSuccess);
}

function testCategoryManagement(baseUrl, headers) {
  const group = 'Category Management';
  
  // List categories
  const categoriesResponse = http.get(`${baseUrl}/api/v1/categories`, {
    headers: headers,
    tags: { name: 'list_categories', group: group },
  });
  
  check(categoriesResponse, {
    'categories list status is 200': (r) => r.status === 200,
  });
  
  // Create new category
  const newCategory = generateCategory();
  const createCategoryStart = Date.now();
  const createCategoryResponse = http.post(
    `${baseUrl}/api/v1/categories`,
    JSON.stringify(newCategory),
    {
      headers: headers,
      tags: { name: 'create_category', group: group },
    }
  );
  const createCategoryDuration = Date.now() - createCategoryStart;
  
  const createCategorySuccess = check(createCategoryResponse, {
    'create category status is 201': (r) => r.status === 201,
  });
  
  recordApiCall('category_creation', createCategoryDuration, createCategoryResponse.status, createCategorySuccess);
}

function testReportsAndAnalytics(baseUrl, headers) {
  const group = 'Reports & Analytics';
  
  // Get spending summary
  const spendingSummaryResponse = http.get(`${baseUrl}/api/v1/analytics/spending-summary`, {
    headers: headers,
    tags: { name: 'spending_summary', group: group },
  });
  
  check(spendingSummaryResponse, {
    'spending summary status is 200': (r) => r.status === 200,
  });
  
  // Get monthly trends
  const monthlyTrendsResponse = http.get(`${baseUrl}/api/v1/analytics/monthly-trends`, {
    headers: headers,
    tags: { name: 'monthly_trends', group: group },
  });
  
  check(monthlyTrendsResponse, {
    'monthly trends status is 200': (r) => r.status === 200,
  });
}

export function teardown(data) {
  console.log('🧹 Cleaning up API Performance Tests');
}
