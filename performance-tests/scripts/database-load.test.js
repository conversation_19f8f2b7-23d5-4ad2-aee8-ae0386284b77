import http from 'k6/http';
import { check, sleep } from 'k6';
import { loadTestConfig } from '../configs/base.js';
import { login, getAuthHeaders } from '../utils/auth.js';
import { generateTransaction, generateFilters, generatePagination } from '../utils/data-generators.js';
import { recordApiCall, recordDatabaseMetrics } from '../utils/metrics.js';

// Test configuration for database load testing
export let options = {
  stages: loadTestConfig.stages,
  thresholds: {
    ...loadTestConfig.thresholds,
    // Database-specific thresholds
    'db_query_duration': ['p(95)<1000'],
    'transaction_creation_duration': ['p(90)<300'],
    'transaction_list_duration': ['p(90)<500'],
  },
  tags: {
    testType: 'database_load',
    environment: 'test',
  },
};

// Test environment configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const TEST_USER_EMAIL = __ENV.TEST_USER_EMAIL || '<EMAIL>';
const TEST_USER_PASSWORD = __ENV.TEST_USER_PASSWORD || 'password123';

let authToken;

export function setup() {
  console.log('🗄️ Starting Database Load Tests');
  console.log(`Base URL: ${BASE_URL}`);
  
  // Health check
  const healthCheck = http.get(`${BASE_URL}/api/v1/health`);
  if (healthCheck.status !== 200) {
    throw new Error(`API health check failed: ${healthCheck.status}`);
  }
  
  console.log('✅ Database load test setup complete');
  return { baseUrl: BASE_URL };
}

export default function(data) {
  const baseUrl = data.baseUrl;
  
  // Login once per VU
  if (!authToken) {
    authToken = login(baseUrl, {
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD,
    });
    
    if (!authToken) {
      console.error('❌ Login failed for database load test');
      return;
    }
  }
  
  const headers = getAuthHeaders(authToken);
  
  // Database-intensive operations
  testTransactionQueries(baseUrl, headers);
  testBulkTransactionCreation(baseUrl, headers);
  testComplexFiltering(baseUrl, headers);
  testPaginationLoad(baseUrl, headers);
  testAnalyticsQueries(baseUrl, headers);
  
  // Short sleep to prevent overwhelming the database
  sleep(0.5);
}

function testTransactionQueries(baseUrl, headers) {
  const group = 'Transaction Queries';
  
  // Test various transaction query patterns
  const queryPatterns = [
    // Simple list
    '/api/v1/transactions?limit=50',
    
    // Date range queries
    '/api/v1/transactions?startDate=2024-01-01&endDate=2024-12-31',
    
    // Category filtering
    '/api/v1/transactions?categoryId=test-category&limit=100',
    
    // Account filtering
    '/api/v1/transactions?accountId=test-account&limit=100',
    
    // Type filtering
    '/api/v1/transactions?type=EXPENSE&limit=100',
    
    // Combined filters
    '/api/v1/transactions?type=EXPENSE&startDate=2024-01-01&limit=50',
  ];
  
  // Execute random query pattern
  const randomPattern = queryPatterns[Math.floor(Math.random() * queryPatterns.length)];
  
  const queryStart = Date.now();
  const queryResponse = http.get(`${baseUrl}${randomPattern}`, {
    headers: headers,
    tags: { name: 'transaction_query', group: group },
  });
  const queryDuration = Date.now() - queryStart;
  
  const querySuccess = check(queryResponse, {
    'transaction query status is 200': (r) => r.status === 200,
    'transaction query response time < 1s': (r) => r.timings.duration < 1000,
    'transaction query has data structure': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data && Array.isArray(body.data.transactions);
      } catch (e) {
        return false;
      }
    },
  });
  
  recordApiCall('transaction_query', queryDuration, queryResponse.status, querySuccess);
  recordDatabaseMetrics(queryDuration);
}

function testBulkTransactionCreation(baseUrl, headers) {
  const group = 'Bulk Transaction Creation';
  
  // Create multiple transactions in sequence to test database write performance
  const transactionCount = Math.floor(Math.random() * 5) + 1; // 1-5 transactions
  
  for (let i = 0; i < transactionCount; i++) {
    const newTransaction = generateTransaction();
    
    const createStart = Date.now();
    const createResponse = http.post(
      `${baseUrl}/api/v1/transactions`,
      JSON.stringify(newTransaction),
      {
        headers: headers,
        tags: { name: 'bulk_transaction_create', group: group },
      }
    );
    const createDuration = Date.now() - createStart;
    
    const createSuccess = check(createResponse, {
      'bulk transaction create status is 201': (r) => r.status === 201,
      'bulk transaction create response time < 500ms': (r) => r.timings.duration < 500,
    });
    
    recordApiCall('transaction_creation', createDuration, createResponse.status, createSuccess);
    recordDatabaseMetrics(createDuration);
    
    // Small delay between creates
    sleep(0.1);
  }
}

function testComplexFiltering(baseUrl, headers) {
  const group = 'Complex Filtering';
  
  // Generate complex filter combinations
  const filters = generateFilters();
  const pagination = generatePagination();
  
  // Build query string
  const queryParams = new URLSearchParams({
    ...filters,
    ...pagination,
  });
  
  const complexQueryStart = Date.now();
  const complexQueryResponse = http.get(
    `${baseUrl}/api/v1/transactions?${queryParams.toString()}`,
    {
      headers: headers,
      tags: { name: 'complex_filter_query', group: group },
    }
  );
  const complexQueryDuration = Date.now() - complexQueryStart;
  
  const complexQuerySuccess = check(complexQueryResponse, {
    'complex filter query status is 200': (r) => r.status === 200,
    'complex filter query response time < 2s': (r) => r.timings.duration < 2000,
    'complex filter query returns valid pagination': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data && body.data.pagination;
      } catch (e) {
        return false;
      }
    },
  });
  
  recordApiCall('complex_query', complexQueryDuration, complexQueryResponse.status, complexQuerySuccess);
  recordDatabaseMetrics(complexQueryDuration);
}

function testPaginationLoad(baseUrl, headers) {
  const group = 'Pagination Load';
  
  // Test pagination with different page sizes and offsets
  const paginationTests = [
    { page: 1, limit: 10 },
    { page: 1, limit: 50 },
    { page: 1, limit: 100 },
    { page: 5, limit: 20 },
    { page: 10, limit: 10 },
  ];
  
  const randomPagination = paginationTests[Math.floor(Math.random() * paginationTests.length)];
  
  const paginationStart = Date.now();
  const paginationResponse = http.get(
    `${baseUrl}/api/v1/transactions?page=${randomPagination.page}&limit=${randomPagination.limit}`,
    {
      headers: headers,
      tags: { name: 'pagination_query', group: group },
    }
  );
  const paginationDuration = Date.now() - paginationStart;
  
  const paginationSuccess = check(paginationResponse, {
    'pagination query status is 200': (r) => r.status === 200,
    'pagination query response time < 1s': (r) => r.timings.duration < 1000,
    'pagination query returns correct structure': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && 
               body.data && 
               body.data.transactions && 
               body.data.pagination &&
               body.data.pagination.page === randomPagination.page;
      } catch (e) {
        return false;
      }
    },
  });
  
  recordApiCall('pagination_query', paginationDuration, paginationResponse.status, paginationSuccess);
  recordDatabaseMetrics(paginationDuration);
}

function testAnalyticsQueries(baseUrl, headers) {
  const group = 'Analytics Queries';
  
  // Test analytics endpoints that perform complex aggregations
  const analyticsEndpoints = [
    '/api/v1/analytics/spending-summary',
    '/api/v1/analytics/monthly-trends',
    '/api/v1/analytics/category-breakdown',
    '/api/v1/analytics/account-balances',
  ];
  
  const randomEndpoint = analyticsEndpoints[Math.floor(Math.random() * analyticsEndpoints.length)];
  
  const analyticsStart = Date.now();
  const analyticsResponse = http.get(`${baseUrl}${randomEndpoint}`, {
    headers: headers,
    tags: { name: 'analytics_query', group: group },
  });
  const analyticsDuration = Date.now() - analyticsStart;
  
  const analyticsSuccess = check(analyticsResponse, {
    'analytics query status is 200': (r) => r.status === 200,
    'analytics query response time < 3s': (r) => r.timings.duration < 3000,
    'analytics query has data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data;
      } catch (e) {
        return false;
      }
    },
  });
  
  recordApiCall('analytics_query', analyticsDuration, analyticsResponse.status, analyticsSuccess);
  recordDatabaseMetrics(analyticsDuration);
}

export function teardown(data) {
  console.log('🧹 Database load test cleanup complete');
}
