/**
 * Data generators for k6 performance tests
 */

// Sample data arrays
const accountTypes = ['CHECKING', 'SAVINGS', 'CREDIT_CARD', 'INVESTMENT'];
const currencies = ['USD', 'EUR', 'BRL', 'GBP', 'JPY'];
const transactionTypes = ['INCOME', 'EXPENSE', 'TRANSFER'];
const categoryNames = [
  'Food & Dining', 'Transportation', 'Shopping', 'Entertainment',
  'Bills & Utilities', 'Healthcare', 'Travel', 'Education',
  'Business', 'Personal Care', 'Gifts & Donations', 'Investments'
];
const descriptions = [
  'Grocery shopping', 'Gas station', 'Restaurant dinner', 'Coffee shop',
  'Online purchase', 'Utility bill', 'Salary deposit', 'ATM withdrawal',
  'Transfer to savings', 'Investment dividend', 'Medical appointment',
  'Movie tickets', 'Book purchase', 'Gym membership'
];

/**
 * Generate random account data
 */
export function generateAccount() {
  return {
    name: `Test Account ${Math.floor(Math.random() * 1000)}`,
    type: accountTypes[Math.floor(Math.random() * accountTypes.length)],
    currency: currencies[Math.floor(Math.random() * currencies.length)],
    initialBalance: Math.floor(Math.random() * 10000) + 1000,
    creditLimit: Math.floor(Math.random() * 5000) + 1000,
  };
}

/**
 * Generate random transaction data
 */
export function generateTransaction(accountId = null, categoryId = null) {
  const amount = Math.floor(Math.random() * 500) + 10;
  const type = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
  
  return {
    description: descriptions[Math.floor(Math.random() * descriptions.length)],
    amount: type === 'EXPENSE' ? -amount : amount,
    type: type,
    accountId: accountId || `account-${Math.floor(Math.random() * 100)}`,
    categoryId: categoryId || `category-${Math.floor(Math.random() * 20)}`,
    date: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
  };
}

/**
 * Generate random category data
 */
export function generateCategory() {
  const type = Math.random() > 0.7 ? 'INCOME' : 'EXPENSE';
  
  return {
    name: categoryNames[Math.floor(Math.random() * categoryNames.length)],
    type: type,
    color: `#${Math.floor(Math.random()*********).toString(16)}`,
    icon: 'default-icon',
  };
}

/**
 * Generate random budget data
 */
export function generateBudget(categoryId = null) {
  return {
    categoryId: categoryId || `category-${Math.floor(Math.random() * 20)}`,
    amount: Math.floor(Math.random() * 2000) + 500,
    period: 'MONTHLY',
    startDate: new Date().toISOString(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
  };
}

/**
 * Generate random financial goal data
 */
export function generateFinancialGoal() {
  const targetAmount = Math.floor(Math.random() * 50000) + 5000;
  
  return {
    title: `Goal ${Math.floor(Math.random() * 1000)}`,
    description: 'Performance test financial goal',
    targetAmount: targetAmount,
    currentAmount: Math.floor(Math.random() * targetAmount * 0.5),
    targetDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
    category: 'SAVINGS',
  };
}

/**
 * Generate random recurring transaction data
 */
export function generateRecurringTransaction(accountId = null, categoryId = null) {
  return {
    description: `Recurring ${descriptions[Math.floor(Math.random() * descriptions.length)]}`,
    amount: Math.floor(Math.random() * 200) + 50,
    type: 'EXPENSE',
    accountId: accountId || `account-${Math.floor(Math.random() * 100)}`,
    categoryId: categoryId || `category-${Math.floor(Math.random() * 20)}`,
    frequency: 'MONTHLY',
    startDate: new Date().toISOString(),
    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
    isActive: true,
  };
}

/**
 * Generate random date range for filtering
 */
export function generateDateRange() {
  const endDate = new Date();
  const startDate = new Date(endDate.getTime() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000);
  
  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0],
  };
}

/**
 * Generate random pagination parameters
 */
export function generatePagination() {
  return {
    page: Math.floor(Math.random() * 5) + 1,
    limit: [10, 20, 50][Math.floor(Math.random() * 3)],
  };
}

/**
 * Generate random filter parameters
 */
export function generateFilters() {
  const filters = {};
  
  // Random account filter
  if (Math.random() > 0.7) {
    filters.accountId = `account-${Math.floor(Math.random() * 100)}`;
  }
  
  // Random category filter
  if (Math.random() > 0.7) {
    filters.categoryId = `category-${Math.floor(Math.random() * 20)}`;
  }
  
  // Random type filter
  if (Math.random() > 0.8) {
    filters.type = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
  }
  
  // Random date range
  if (Math.random() > 0.6) {
    const dateRange = generateDateRange();
    filters.startDate = dateRange.startDate;
    filters.endDate = dateRange.endDate;
  }
  
  return filters;
}
