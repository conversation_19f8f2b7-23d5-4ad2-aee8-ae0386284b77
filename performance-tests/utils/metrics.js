import { Trend, Counter, Rate, Gauge } from 'k6/metrics';

/**
 * Custom metrics for performance testing
 */

// Response time trends for different operations
export const loginDuration = new Trend('login_duration');
export const accountCreationDuration = new Trend('account_creation_duration');
export const transactionCreationDuration = new Trend('transaction_creation_duration');
export const dashboardLoadDuration = new Trend('dashboard_load_duration');
export const transactionListDuration = new Trend('transaction_list_duration');
export const categoryCreationDuration = new Trend('category_creation_duration');

// Error counters
export const loginErrors = new Counter('login_errors');
export const apiErrors = new Counter('api_errors');
export const authErrors = new Counter('auth_errors');
export const validationErrors = new Counter('validation_errors');
export const serverErrors = new Counter('server_errors');

// Success rates
export const loginSuccessRate = new Rate('login_success_rate');
export const apiSuccessRate = new Rate('api_success_rate');
export const transactionSuccessRate = new Rate('transaction_success_rate');

// Resource usage
export const activeUsers = new Gauge('active_users');
export const memoryUsage = new Gauge('memory_usage');
export const cpuUsage = new Gauge('cpu_usage');

// Database operation metrics
export const dbQueryDuration = new Trend('db_query_duration');
export const dbConnectionCount = new Gauge('db_connection_count');

// Cache metrics
export const cacheHitRate = new Rate('cache_hit_rate');
export const cacheMissRate = new Rate('cache_miss_rate');

/**
 * Record login metrics
 */
export function recordLogin(duration, success) {
  loginDuration.add(duration);
  loginSuccessRate.add(success);
  
  if (!success) {
    loginErrors.add(1);
  }
}

/**
 * Record API call metrics
 */
export function recordApiCall(endpoint, duration, statusCode, success) {
  // Record general API metrics
  apiSuccessRate.add(success);
  
  if (!success) {
    apiErrors.add(1);
    
    // Categorize errors
    if (statusCode === 401 || statusCode === 403) {
      authErrors.add(1);
    } else if (statusCode >= 400 && statusCode < 500) {
      validationErrors.add(1);
    } else if (statusCode >= 500) {
      serverErrors.add(1);
    }
  }
  
  // Record specific endpoint metrics
  switch (endpoint) {
    case 'account_creation':
      accountCreationDuration.add(duration);
      break;
    case 'transaction_creation':
      transactionCreationDuration.add(duration);
      transactionSuccessRate.add(success);
      break;
    case 'dashboard_load':
      dashboardLoadDuration.add(duration);
      break;
    case 'transaction_list':
      transactionListDuration.add(duration);
      break;
    case 'category_creation':
      categoryCreationDuration.add(duration);
      break;
  }
}

/**
 * Record database metrics
 */
export function recordDatabaseMetrics(queryDuration, connectionCount) {
  if (queryDuration !== undefined) {
    dbQueryDuration.add(queryDuration);
  }
  
  if (connectionCount !== undefined) {
    dbConnectionCount.add(connectionCount);
  }
}

/**
 * Record cache metrics
 */
export function recordCacheMetrics(hit, miss) {
  if (hit) {
    cacheHitRate.add(1);
  }
  
  if (miss) {
    cacheMissRate.add(1);
  }
}

/**
 * Record system resource metrics
 */
export function recordSystemMetrics(users, memory, cpu) {
  if (users !== undefined) {
    activeUsers.add(users);
  }
  
  if (memory !== undefined) {
    memoryUsage.add(memory);
  }
  
  if (cpu !== undefined) {
    cpuUsage.add(cpu);
  }
}

/**
 * Get performance summary
 */
export function getPerformanceSummary() {
  return {
    timestamp: new Date().toISOString(),
    metrics: {
      response_times: {
        login: loginDuration,
        account_creation: accountCreationDuration,
        transaction_creation: transactionCreationDuration,
        dashboard_load: dashboardLoadDuration,
        transaction_list: transactionListDuration,
        category_creation: categoryCreationDuration,
      },
      success_rates: {
        login: loginSuccessRate,
        api: apiSuccessRate,
        transactions: transactionSuccessRate,
      },
      error_counts: {
        login: loginErrors,
        api: apiErrors,
        auth: authErrors,
        validation: validationErrors,
        server: serverErrors,
      },
      system: {
        active_users: activeUsers,
        memory_usage: memoryUsage,
        cpu_usage: cpuUsage,
      },
      database: {
        query_duration: dbQueryDuration,
        connection_count: dbConnectionCount,
      },
      cache: {
        hit_rate: cacheHitRate,
        miss_rate: cacheMissRate,
      },
    },
  };
}
