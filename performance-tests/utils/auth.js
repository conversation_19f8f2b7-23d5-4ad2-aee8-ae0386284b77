import http from 'k6/http';
import { check } from 'k6';

/**
 * Authentication utilities for k6 performance tests
 */

// Test user credentials
export const testUsers = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
];

/**
 * Get a random test user
 */
export function getRandomUser() {
  return testUsers[Math.floor(Math.random() * testUsers.length)];
}

/**
 * Login and get JWT token
 */
export function login(baseUrl, user = null) {
  if (!user) {
    user = getRandomUser();
  }

  const loginPayload = {
    email: user.email,
    password: user.password,
  };

  const loginResponse = http.post(
    `${baseUrl}/api/v1/auth/login`,
    JSON.stringify(loginPayload),
    {
      headers: {
        'Content-Type': 'application/json',
      },
      tags: { name: 'login' },
    }
  );

  const loginSuccess = check(loginResponse, {
    'login status is 200': (r) => r.status === 200,
    'login response has token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data && body.data.token;
      } catch (e) {
        return false;
      }
    },
  });

  if (!loginSuccess) {
    console.error(`Login failed for user ${user.email}: ${loginResponse.status} ${loginResponse.body}`);
    return null;
  }

  try {
    const responseBody = JSON.parse(loginResponse.body);
    return responseBody.data.token;
  } catch (e) {
    console.error(`Failed to parse login response: ${e.message}`);
    return null;
  }
}

/**
 * Get authorization headers with JWT token
 */
export function getAuthHeaders(token) {
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
}

/**
 * Create a new test user (for setup)
 */
export function createTestUser(baseUrl, userData) {
  const registerPayload = {
    name: userData.name || 'Test User',
    email: userData.email,
    password: userData.password,
  };

  const registerResponse = http.post(
    `${baseUrl}/api/v1/auth/register`,
    JSON.stringify(registerPayload),
    {
      headers: {
        'Content-Type': 'application/json',
      },
      tags: { name: 'register' },
    }
  );

  return check(registerResponse, {
    'register status is 201': (r) => r.status === 201,
    'register response has user': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data && body.data.user;
      } catch (e) {
        return false;
      }
    },
  });
}

/**
 * Logout user
 */
export function logout(baseUrl, token) {
  const logoutResponse = http.post(
    `${baseUrl}/api/v1/auth/logout`,
    null,
    {
      headers: getAuthHeaders(token),
      tags: { name: 'logout' },
    }
  );

  return check(logoutResponse, {
    'logout status is 200': (r) => r.status === 200,
  });
}
