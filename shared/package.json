{"name": "personal-finance-shared", "version": "1.0.0", "description": "Shared types and utilities for Personal Finance Manager", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"zod": "^3.22.4", "date-fns": "^2.30.0"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.0", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}, "engines": {"node": ">=18.0.0"}}