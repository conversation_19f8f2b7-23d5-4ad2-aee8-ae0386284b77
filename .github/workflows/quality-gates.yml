name: 🛡️ Quality Gates

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'

jobs:
  # Job 1: Coverage Quality Gate
  coverage-gate:
    name: 📊 Coverage Quality Gate
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_finance_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    env:
      DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_finance_db
      NODE_ENV: test
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🗄️ Setup database
        run: |
          cd backend
          npx prisma generate
          npx prisma db push

      - name: 📊 Generate combined coverage
        run: npm run test:coverage:combined

      - name: 🛡️ Check coverage thresholds
        run: |
          echo "🛡️ Checking coverage quality gates..."
          
          # Extract coverage percentages
          BACKEND_COVERAGE=$(cat backend/coverage/coverage-summary.json | jq '.total.lines.pct')
          FRONTEND_COVERAGE=$(cat frontend/coverage/coverage-summary.json | jq '.total.lines.pct')
          
          echo "Backend Coverage: ${BACKEND_COVERAGE}%"
          echo "Frontend Coverage: ${FRONTEND_COVERAGE}%"
          
          # Define thresholds
          BACKEND_THRESHOLD=80
          FRONTEND_THRESHOLD=75
          
          # Check backend threshold
          if (( $(echo "$BACKEND_COVERAGE < $BACKEND_THRESHOLD" | bc -l) )); then
            echo "❌ Backend coverage ($BACKEND_COVERAGE%) is below threshold ($BACKEND_THRESHOLD%)"
            exit 1
          fi
          
          # Check frontend threshold
          if (( $(echo "$FRONTEND_COVERAGE < $FRONTEND_THRESHOLD" | bc -l) )); then
            echo "❌ Frontend coverage ($FRONTEND_COVERAGE%) is below threshold ($FRONTEND_THRESHOLD%)"
            exit 1
          fi
          
          echo "✅ All coverage thresholds passed!"

      - name: 📝 Comment coverage on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            
            // Read coverage data
            const backendCoverage = JSON.parse(fs.readFileSync('backend/coverage/coverage-summary.json', 'utf8'));
            const frontendCoverage = JSON.parse(fs.readFileSync('frontend/coverage/coverage-summary.json', 'utf8'));
            
            const backendPct = backendCoverage.total.lines.pct;
            const frontendPct = frontendCoverage.total.lines.pct;
            
            const comment = `## 📊 Test Coverage Report
            
            | Component | Lines | Functions | Branches | Statements |
            |-----------|-------|-----------|----------|------------|
            | **Backend** | ${backendCoverage.total.lines.pct}% | ${backendCoverage.total.functions.pct}% | ${backendCoverage.total.branches.pct}% | ${backendCoverage.total.statements.pct}% |
            | **Frontend** | ${frontendCoverage.total.lines.pct}% | ${frontendCoverage.total.functions.pct}% | ${frontendCoverage.total.branches.pct}% | ${frontendCoverage.total.statements.pct}% |
            
            ### Quality Gates
            - Backend Coverage: ${backendPct >= 80 ? '✅' : '❌'} ${backendPct}% (threshold: 80%)
            - Frontend Coverage: ${frontendPct >= 75 ? '✅' : '❌'} ${frontendPct}% (threshold: 75%)
            
            ${backendPct >= 80 && frontendPct >= 75 ? '🎉 All quality gates passed!' : '⚠️ Some quality gates failed. Please improve test coverage.'}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  # Job 2: Performance Quality Gate
  performance-gate:
    name: ⚡ Performance Quality Gate
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_finance_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    env:
      DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_finance_db
      NODE_ENV: test
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🗄️ Setup database
        run: |
          cd backend
          npx prisma generate
          npx prisma db push

      - name: 🌱 Seed performance test data
        run: npm run test:data:comprehensive

      - name: 📦 Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: 🚀 Start backend server
        run: |
          cd backend
          npm run build
          npm start &
          sleep 10
        env:
          PORT: 3001

      - name: ⚡ Run performance tests with thresholds
        run: |
          echo "⚡ Running performance quality gates..."
          
          # Run performance tests
          npm run test:performance:gates
          
          # Check if performance results meet thresholds
          if [ -f "backend/performance/results/quality-gate-results.json" ]; then
            RESPONSE_TIME=$(cat backend/performance/results/quality-gate-results.json | jq '.metrics.http_req_duration.avg')
            ERROR_RATE=$(cat backend/performance/results/quality-gate-results.json | jq '.metrics.http_req_failed.rate')
            
            echo "Average Response Time: ${RESPONSE_TIME}ms"
            echo "Error Rate: ${ERROR_RATE}%"
            
            # Define thresholds
            MAX_RESPONSE_TIME=500
            MAX_ERROR_RATE=0.01
            
            # Check response time threshold
            if (( $(echo "$RESPONSE_TIME > $MAX_RESPONSE_TIME" | bc -l) )); then
              echo "❌ Response time (${RESPONSE_TIME}ms) exceeds threshold (${MAX_RESPONSE_TIME}ms)"
              exit 1
            fi
            
            # Check error rate threshold
            if (( $(echo "$ERROR_RATE > $MAX_ERROR_RATE" | bc -l) )); then
              echo "❌ Error rate (${ERROR_RATE}%) exceeds threshold (${MAX_ERROR_RATE}%)"
              exit 1
            fi
            
            echo "✅ All performance thresholds passed!"
          else
            echo "❌ Performance results file not found"
            exit 1
          fi

  # Job 3: Security Quality Gate
  security-gate:
    name: 🔒 Security Quality Gate
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔒 Security audit
        run: |
          echo "🔒 Running security quality gates..."
          
          # Run npm audit
          npm audit --audit-level high --json > security-audit.json || true
          
          # Check for high/critical vulnerabilities
          HIGH_VULNS=$(cat security-audit.json | jq '.metadata.vulnerabilities.high // 0')
          CRITICAL_VULNS=$(cat security-audit.json | jq '.metadata.vulnerabilities.critical // 0')
          
          echo "High vulnerabilities: $HIGH_VULNS"
          echo "Critical vulnerabilities: $CRITICAL_VULNS"
          
          # Fail if critical vulnerabilities found
          if [ "$CRITICAL_VULNS" -gt 0 ]; then
            echo "❌ Critical security vulnerabilities found: $CRITICAL_VULNS"
            exit 1
          fi
          
          # Warn if high vulnerabilities found
          if [ "$HIGH_VULNS" -gt 0 ]; then
            echo "⚠️ High security vulnerabilities found: $HIGH_VULNS"
            echo "Consider updating dependencies"
          fi
          
          echo "✅ Security quality gate passed!"

      - name: 🔍 CodeQL Analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript, typescript

      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  # Job 4: Code Quality Gate
  code-quality-gate:
    name: 📝 Code Quality Gate
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 📝 Run ESLint with quality gates
        run: |
          echo "📝 Running code quality gates..."
          
          # Run ESLint and capture results
          npm run lint:backend -- --format json --output-file backend-lint.json || true
          npm run lint:frontend -- --format json --output-file frontend-lint.json || true
          
          # Count errors and warnings
          BACKEND_ERRORS=$(cat backend-lint.json | jq '[.[] | .errorCount] | add // 0')
          BACKEND_WARNINGS=$(cat backend-lint.json | jq '[.[] | .warningCount] | add // 0')
          FRONTEND_ERRORS=$(cat frontend-lint.json | jq '[.[] | .errorCount] | add // 0')
          FRONTEND_WARNINGS=$(cat frontend-lint.json | jq '[.[] | .warningCount] | add // 0')
          
          echo "Backend - Errors: $BACKEND_ERRORS, Warnings: $BACKEND_WARNINGS"
          echo "Frontend - Errors: $FRONTEND_ERRORS, Warnings: $FRONTEND_WARNINGS"
          
          # Define thresholds
          MAX_ERRORS=0
          MAX_WARNINGS=10
          
          TOTAL_ERRORS=$((BACKEND_ERRORS + FRONTEND_ERRORS))
          TOTAL_WARNINGS=$((BACKEND_WARNINGS + FRONTEND_WARNINGS))
          
          # Check error threshold
          if [ "$TOTAL_ERRORS" -gt "$MAX_ERRORS" ]; then
            echo "❌ Too many linting errors: $TOTAL_ERRORS (max: $MAX_ERRORS)"
            exit 1
          fi
          
          # Check warning threshold
          if [ "$TOTAL_WARNINGS" -gt "$MAX_WARNINGS" ]; then
            echo "⚠️ Too many linting warnings: $TOTAL_WARNINGS (max: $MAX_WARNINGS)"
            echo "Consider fixing warnings before merging"
          fi
          
          echo "✅ Code quality gate passed!"

  # Job 5: Overall Quality Gate
  overall-quality-gate:
    name: 🎯 Overall Quality Gate
    runs-on: ubuntu-latest
    needs: [coverage-gate, performance-gate, security-gate, code-quality-gate]
    if: always()
    
    steps:
      - name: 🎯 Check overall quality
        run: |
          echo "🎯 Checking overall quality gate..."
          
          # Check if all previous jobs passed
          if [ "${{ needs.coverage-gate.result }}" != "success" ]; then
            echo "❌ Coverage quality gate failed"
            exit 1
          fi
          
          if [ "${{ needs.performance-gate.result }}" != "success" ]; then
            echo "❌ Performance quality gate failed"
            exit 1
          fi
          
          if [ "${{ needs.security-gate.result }}" != "success" ]; then
            echo "❌ Security quality gate failed"
            exit 1
          fi
          
          if [ "${{ needs.code-quality-gate.result }}" != "success" ]; then
            echo "❌ Code quality gate failed"
            exit 1
          fi
          
          echo "✅ All quality gates passed! Ready for merge."

      - name: 📝 Update PR status
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const comment = `## 🎯 Quality Gates Summary
            
            | Gate | Status |
            |------|--------|
            | 📊 Coverage | ${{ needs.coverage-gate.result == 'success' && '✅ Passed' || '❌ Failed' }} |
            | ⚡ Performance | ${{ needs.performance-gate.result == 'success' && '✅ Passed' || '❌ Failed' }} |
            | 🔒 Security | ${{ needs.security-gate.result == 'success' && '✅ Passed' || '❌ Failed' }} |
            | 📝 Code Quality | ${{ needs.code-quality-gate.result == 'success' && '✅ Passed' || '❌ Failed' }} |
            
            **Overall Status**: ${{ needs.coverage-gate.result == 'success' && needs.performance-gate.result == 'success' && needs.security-gate.result == 'success' && needs.code-quality-gate.result == 'success' && '🎉 All quality gates passed! Ready for merge.' || '⚠️ Some quality gates failed. Please review and fix issues before merging.' }}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
