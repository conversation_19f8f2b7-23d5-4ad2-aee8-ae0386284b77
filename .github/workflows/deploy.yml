name: 🚀 Deploy

on:
  push:
    branches: [ main ]
  workflow_run:
    workflows: ["🚀 CI/CD Pipeline"]
    types:
      - completed
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  # Job 1: Deploy Staging
  deploy-staging:
    name: 🧪 Deploy to Staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event.workflow_run.conclusion == 'success'
    environment:
      name: staging
      url: https://staging.personal-finance.app
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🏗️ Build application
        run: |
          npm run build:backend
          npm run build:frontend
        env:
          NODE_ENV: production
          VITE_API_URL: https://api-staging.personal-finance.app

      - name: 🧪 Run smoke tests
        run: npm run test:smoke

      - name: 🚀 Deploy to staging
        run: |
          echo "🚀 Deploying to staging environment..."
          # Add your staging deployment commands here
          # Example: rsync, docker push, kubectl apply, etc.

      - name: 🔍 Health check
        run: |
          echo "🔍 Running health checks..."
          # Add health check commands here
          # curl -f https://staging.personal-finance.app/api/v1/health

      - name: 📊 Run staging tests
        run: |
          echo "📊 Running staging validation tests..."
          # Add staging-specific tests here

  # Job 2: Security Scan
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔒 Run security audit
        run: |
          npm audit --audit-level high
          npm run security:scan

      - name: 🔍 SAST scan
        uses: github/super-linter@v4
        env:
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VALIDATE_TYPESCRIPT_ES: true
          VALIDATE_JAVASCRIPT_ES: true

      - name: 🛡️ Dependency check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'Personal Finance Manager'
          path: '.'
          format: 'HTML'

      - name: 📤 Upload security report
        uses: actions/upload-artifact@v3
        with:
          name: security-report
          path: reports/

  # Job 3: Deploy Production
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [deploy-staging, security-scan]
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://personal-finance.app
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🏗️ Build application
        run: |
          npm run build:backend
          npm run build:frontend
        env:
          NODE_ENV: production
          VITE_API_URL: https://api.personal-finance.app

      - name: 🧪 Run pre-deployment tests
        run: npm run test:pre-deploy

      - name: 🚀 Deploy to production
        run: |
          echo "🚀 Deploying to production environment..."
          # Add your production deployment commands here
          # Example: docker push, kubectl apply, terraform apply, etc.

      - name: 🔍 Production health check
        run: |
          echo "🔍 Running production health checks..."
          # Add production health check commands here
          # curl -f https://personal-finance.app/api/v1/health

      - name: 📊 Post-deployment validation
        run: |
          echo "📊 Running post-deployment validation..."
          # Add post-deployment tests here

      - name: 📢 Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()

  # Job 4: Rollback (Manual)
  rollback:
    name: 🔄 Rollback
    runs-on: ubuntu-latest
    if: failure()
    environment:
      name: production
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔄 Rollback deployment
        run: |
          echo "🔄 Rolling back deployment..."
          # Add rollback commands here
          # kubectl rollout undo deployment/app

      - name: 🔍 Verify rollback
        run: |
          echo "🔍 Verifying rollback..."
          # Add rollback verification here

      - name: 📢 Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: 'warning'
          channel: '#deployments'
          text: '🔄 Production rollback completed'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
