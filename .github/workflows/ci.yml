name: 🚀 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '14'

jobs:
  # Job 1: Code Quality and Linting
  quality:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Lint backend
        run: npm run lint:backend

      - name: 🔍 Lint frontend
        run: npm run lint:frontend

      - name: 📝 Check TypeScript
        run: |
          npm run type-check:backend
          npm run type-check:frontend

      - name: 🎨 Check formatting
        run: npm run format:check

  # Job 2: Backend Tests
  backend-tests:
    name: 🧪 Backend Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_finance_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    env:
      DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_finance_db
      REDIS_URL: redis://localhost:6379
      NODE_ENV: test
      JWT_SECRET: test-secret-key-for-ci
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🗄️ Setup database
        run: |
          cd backend
          npx prisma generate
          npx prisma db push

      - name: 🧪 Run unit tests
        run: npm run test:backend:unit

      - name: 🔗 Run integration tests
        run: npm run test:backend:integration

      - name: 📊 Generate coverage report
        run: npm run test:backend:coverage

      - name: 📤 Upload backend coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # Job 3: Frontend Tests
  frontend-tests:
    name: 🎨 Frontend Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run unit tests
        run: npm run test:frontend:unit

      - name: 🔗 Run component tests
        run: npm run test:frontend:components

      - name: 📊 Generate coverage report
        run: npm run test:frontend:coverage

      - name: 📤 Upload frontend coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # Job 4: Performance Tests
  performance-tests:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [backend-tests]
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_finance_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    env:
      DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_finance_db
      REDIS_URL: redis://localhost:6379
      NODE_ENV: test
      JWT_SECRET: test-secret-key-for-ci
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🗄️ Setup database
        run: |
          cd backend
          npx prisma generate
          npx prisma db push

      - name: 🌱 Seed test data
        run: npm run test:data:comprehensive

      - name: 📦 Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: 🚀 Start backend server
        run: |
          cd backend
          npm run build
          npm start &
          sleep 10
        env:
          PORT: 3001

      - name: ⚡ Run performance tests
        run: npm run test:performance

      - name: 📊 Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: backend/performance/results/

  # Job 5: E2E Tests
  e2e-tests:
    name: 🎭 E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [backend-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_finance_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    env:
      DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_finance_db
      REDIS_URL: redis://localhost:6379
      NODE_ENV: test
      JWT_SECRET: test-secret-key-for-ci
      CYPRESS_baseUrl: http://localhost:5173
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🗄️ Setup database
        run: |
          cd backend
          npx prisma generate
          npx prisma db push

      - name: 🌱 Seed test data
        run: npm run test:data:minimal

      - name: 🏗️ Build frontend
        run: npm run build:frontend

      - name: 🚀 Start servers
        run: |
          cd backend && npm run build && npm start &
          cd frontend && npm run preview &
          sleep 15
        env:
          PORT: 3001

      - name: 🎭 Run Cypress E2E tests
        uses: cypress-io/github-action@v6
        with:
          working-directory: frontend
          wait-on: 'http://localhost:5173, http://localhost:3001/api/v1/health'
          wait-on-timeout: 120
          browser: chrome
          record: false

      - name: 📤 Upload E2E artifacts
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots
          path: frontend/cypress/screenshots

      - name: 📤 Upload E2E videos
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cypress-videos
          path: frontend/cypress/videos
