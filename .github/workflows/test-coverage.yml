name: Test Coverage

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'

jobs:
  test-coverage:
    name: Test Coverage
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_finance_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          npm run install:all

      - name: Setup test environment
        run: |
          # Copy test environment files
          cp backend/.env.example backend/.env.test
          
          # Update test environment variables
          echo "DATABASE_URL=postgresql://test:test@localhost:5432/test_finance_db" >> backend/.env.test
          echo "REDIS_URL=redis://localhost:6379" >> backend/.env.test
          echo "NODE_ENV=test" >> backend/.env.test
          echo "JWT_SECRET=test-jwt-secret-for-ci" >> backend/.env.test

      - name: Setup database
        run: |
          cd backend
          npx prisma migrate deploy
          npx prisma db seed

      - name: Run backend tests with coverage
        run: |
          cd backend
          npm run test:coverage
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/test_finance_db
          REDIS_URL: redis://localhost:6379
          NODE_ENV: test
          JWT_SECRET: test-jwt-secret-for-ci

      - name: Run frontend tests with coverage
        run: |
          cd frontend
          npm run test:coverage

      - name: Combine coverage reports
        run: |
          node scripts/coverage-combine.js

      - name: Check coverage thresholds
        run: |
          ./scripts/test-coverage.sh thresholds

      - name: Generate coverage badge
        run: |
          ./scripts/test-coverage.sh badge

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          files: |
            ./backend/coverage/lcov.info,
            ./frontend/coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false
          verbose: true

      - name: Upload combined coverage report
        uses: actions/upload-artifact@v3
        with:
          name: coverage-report
          path: |
            coverage-combined/
            backend/coverage/
            frontend/coverage/
          retention-days: 30

      - name: Comment coverage on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            try {
              const summaryPath = 'coverage-combined/coverage-summary-combined.json';
              if (fs.existsSync(summaryPath)) {
                const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
                const total = summary.total;
                
                const coverageComment = `
            ## 📊 Coverage Report
            
            | Metric | Coverage | Threshold | Status |
            |--------|----------|-----------|--------|
            | Lines | ${total.lines.pct}% | 75% | ${total.lines.pct >= 75 ? '✅' : '❌'} |
            | Functions | ${total.functions.pct}% | 75% | ${total.functions.pct >= 75 ? '✅' : '❌'} |
            | Branches | ${total.branches.pct}% | 70% | ${total.branches.pct >= 70 ? '✅' : '❌'} |
            | Statements | ${total.statements.pct}% | 75% | ${total.statements.pct >= 75 ? '✅' : '❌'} |
            
            ### Frontend Coverage
            ${summary.frontend ? `
            - Lines: ${summary.frontend.lines?.pct || 0}%
            - Functions: ${summary.frontend.functions?.pct || 0}%
            - Branches: ${summary.frontend.branches?.pct || 0}%
            - Statements: ${summary.frontend.statements?.pct || 0}%
            ` : 'No frontend coverage data available'}
            
            ### Backend Coverage
            ${summary.backend ? `
            - Lines: ${summary.backend.lines?.pct || 0}%
            - Functions: ${summary.backend.functions?.pct || 0}%
            - Branches: ${summary.backend.branches?.pct || 0}%
            - Statements: ${summary.backend.statements?.pct || 0}%
            ` : 'No backend coverage data available'}
            
            📁 [View detailed coverage report](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
                `;
                
                github.rest.issues.createComment({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  body: coverageComment
                });
              }
            } catch (error) {
              console.error('Error posting coverage comment:', error);
            }

      - name: Fail if coverage below threshold
        run: |
          if ! ./scripts/test-coverage.sh thresholds; then
            echo "❌ Coverage below threshold"
            exit 1
          fi
          echo "✅ Coverage meets all thresholds"

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: test-coverage
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_finance_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Install dependencies
        run: |
          npm ci
          npm run install:all

      - name: Setup test environment
        run: |
          cp backend/.env.example backend/.env.test
          echo "DATABASE_URL=postgresql://test:test@localhost:5432/test_finance_db" >> backend/.env.test
          echo "REDIS_URL=redis://localhost:6379" >> backend/.env.test

      - name: Setup database
        run: |
          cd backend
          npx prisma migrate deploy
          npx prisma db seed

      - name: Start backend server
        run: |
          cd backend
          npm run build
          npm start &
          sleep 10
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/test_finance_db
          REDIS_URL: redis://localhost:6379
          NODE_ENV: production
          PORT: 3000

      - name: Run performance tests
        run: |
          cd performance-tests
          ./run-tests.sh smoke
        env:
          BASE_URL: http://localhost:3000
          TEST_USER_EMAIL: <EMAIL>
          TEST_USER_PASSWORD: password123

      - name: Upload performance reports
        uses: actions/upload-artifact@v3
        with:
          name: performance-reports
          path: performance-tests/reports/
          retention-days: 30
