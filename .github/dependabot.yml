# Dependabot configuration for automated dependency updates
version: 2

updates:
  # Backend dependencies
  - package-ecosystem: "npm"
    directory: "/backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "lipeeh"
    assignees:
      - "lipeeh"
    commit-message:
      prefix: "backend"
      include: "scope"
    labels:
      - "dependencies"
      - "backend"
    ignore:
      # Ignore major version updates for critical packages
      - dependency-name: "@prisma/client"
        update-types: ["version-update:semver-major"]
      - dependency-name: "express"
        update-types: ["version-update:semver-major"]

  # Frontend dependencies
  - package-ecosystem: "npm"
    directory: "/frontend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "lipeeh"
    assignees:
      - "lipeeh"
    commit-message:
      prefix: "frontend"
      include: "scope"
    labels:
      - "dependencies"
      - "frontend"
    ignore:
      # Ignore major version updates for critical packages
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "vite"
        update-types: ["version-update:semver-major"]

  # Root dependencies
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "lipeeh"
    assignees:
      - "lipeeh"
    commit-message:
      prefix: "root"
      include: "scope"
    labels:
      - "dependencies"
      - "root"

  # Shared dependencies
  - package-ecosystem: "npm"
    directory: "/shared"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "lipeeh"
    assignees:
      - "lipeeh"
    commit-message:
      prefix: "shared"
      include: "scope"
    labels:
      - "dependencies"
      - "shared"

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 2
    reviewers:
      - "lipeeh"
    assignees:
      - "lipeeh"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
