# PRD.md - Personal Finance Manager (v1.0)

## 1. <PERSON><PERSON><PERSON> Geral
O **Personal Finance Manager** é um webapp para controle financeiro pessoal e familiar, projetado para ajudar indivíduos, casais e famílias a gerenciar suas finanças de forma eficiente e intuitiva. A ferramenta oferece funcionalidades como gerenciamento de contas, transações, orçamentos, metas financeiras e dashboards analíticos, tudo suportado por um backend relacional robusto.

### 1.1 Objetivos Principais
- Gerenciar diversas contas financeiras, incluindo contas correntes, poupanças, investimentos, cartões de crédito e bens/ativos.
- Registrar e categorizar transações (despesas, receitas, transferências), incluindo transações parceladas e recorrentes.
- Oferecer sugestões automáticas de categorização de transações com base no histórico.
- Organizar finanças por membros da família.
- Planejar orçamentos mensais e acompanhar metas financeiras.
- Fornecer dashboards analíticos e insights automáticos.
- Suportar múltiplas moedas e integração via API.

### 1.2 Público-Alvo
- Indivíduos, casais e famílias que buscam controle e organização financeira.
- Usuários com conhecimento básico de tecnologia.

---

## 2. Requisitos Funcionais Essenciais

> **Nota:** Todos os módulos que lidam com entidades (Membros, Contas, Categorias, Tags, Orçamentos, Metas) devem permitir Criar, Editar, Listar, Deletar (soft delete) e Arquivar com filtros. Itens arquivados não aparecem em seleções para novas entradas, mas são mantidos para histórico.

### 2.1 Membros da Família
- **RF1.1:** Gerenciar membros da família (Nome, Cor para identificação).
- **User Story:** Como usuário, quero criar membros da família para organizar gastos, contas, orçamentos e metas por pessoa.

### 2.2 Contas
- **RF2.1:** Gerenciar contas (Nome, Tipo: Corrente, Poupança, Investimento, Cartão de Crédito, Carteira/Dinheiro, Bens/Ativos; Moeda; Logomarca opcional).
- **RF2.2:** Opção de incluir/excluir saldo da conta no total geral do dashboard.
- **RF2.3:** Contas podem ser associadas a um ou mais membros da família.
- **RF2.4:** Cartões de crédito possuem campo Limite. O saldo é negativo (dívida), e o Saldo Disponível é calculado como Limite + Saldo Atual (negativo). Apenas o saldo negativo é considerado no total geral.
- **RF2.5:** Contas em moeda estrangeira possuem campo para Taxa de Câmbio manual, usada para exibir o saldo convertido em BRL no dashboard.
- **RF2.6:** Contas do tipo "Bens/Ativos" representam patrimônio (e.g., imóveis, veículos) e podem afetar o patrimônio total no dashboard, mas **não** o saldo corrente.
- **RF2.7:** Logomarca opcional (SVG) para contas, armazenada em 'public/icons/brands/'.
- **User Story:** Como usuário, quero criar uma conta conjunta associada a múltiplos membros da família.
- **User Story:** Como usuário, quero registrar um bem (e.g., imóvel) em uma conta do tipo "Bens/Ativos" para acompanhar meu patrimônio total.
- **User Story:** Como usuário, quero visualizar o patrimônio total da minha família, incluindo contas e bens.

### 2.3 Transações
- **RF3.1:** Registrar transações (Descrição, Valor, Data, Tipo: Despesa, Receita, Transferência).
- **RF3.2:** Associar a Conta. Para transações do tipo "Despesa", a Categoria é obrigatória. Para "Receita" e "Transferência", a Categoria é opcional. Tags e Membros são opcionais para todos os tipos.
- **RF3.3:** Transferências requerem Conta Origem e Conta Destino. Para moedas diferentes, o usuário informa a Taxa de Câmbio no momento da transação, que é armazenada e usada para conversão.
- **RF3.4:** Parcelamento: O usuário informa o número de parcelas. O sistema gera parcelas como transações futuras (baseadas na data futura). A soma das parcelas deve ser igual ao valor original. Ao editar uma parcela, a diferença é ajustada na última parcela em aberto. Parcelas intermediárias não podem ser removidas individualmente.
- **RF3.5:** Transações futuras (baseadas em data futura) são consideradas em saldos projetados, mas não afetam o saldo atual da conta.
- **RF3.6:** Sugestão automática de categorização: O sistema deve sugerir categorias para novas transações com base no histórico de transações do usuário (e.g., descrições similares, beneficiários recorrentes).
- **User Story:** Como usuário, quero transferir USD para uma conta BRL informando a cotação do dia, e que esta cotação seja usada para a transação.
- **User Story:** Como usuário, quero registrar uma compra parcelada em 12x, com valor total de R$1000, e que as parcelas futuras sejam geradas automaticamente.

### 2.4 Transações Recorrentes
- **RF4.1:** Agendar transações recorrentes (Descrição, Valor Fixo ou Percentual sobre saldo da conta, Conta, Categoria, Tipo, Frequência, Data Início/Fim, Membros).
- **RF4.2:** Opção de criar transações futuras ou parceladas.
- **User Story:** Como usuário, quero automatizar o lançamento de uma transação recorrente, seja de valor fixo ou percentual sobre o saldo da conta.

### 2.5 Categorias
- **RF5.1:** Gerenciar categorias e subcategorias (Nome, Categoria Pai opcional, Cor opcional). Hierarquia de dois níveis.
- **User Story:** Como usuário, quero arquivar a categoria "Combustível" sem perder o histórico de transações vinculadas.

### 2.6 Tags
- **RF6.1:** Gerenciar tags (Nome, Cor).
- **User Story:** Como usuário, quero criar tags coloridas para identificar transações específicas (e.g., #viagem2024).

### 2.7 Orçamento
- **RF7.1:** Definir orçamentos mensais por Categoria/Subcategoria e, opcionalmente, por Membro da Família (Valor Planejado, Mês/Ano).
- **RF7.2:** Acompanhamento visual do realizado vs. orçado.
- **User Story:** Como usuário, quero planejar um orçamento mensal de R$800 para "Alimentação" para minha esposa.

### 2.8 Metas Financeiras
- **RF8.1:** Criar metas (Nome, Valor Alvo, Prazo opcional, Membros associados opcional).
- **RF8.2:** Progresso (Valor Atual) atualizado manualmente pelo usuário.
- **RF8.3:** Opcionalmente, definir marcos (milestones) para uma meta, com data e valor parciais, para melhor acompanhamento do progresso.
- **User Story:** Como usuário, quero criar uma meta de R$50.000 para uma viagem em família.
- **User Story:** Como usuário, quero atualizar manualmente o progresso de uma meta para acompanhar o valor atual.
- **User Story:** Como usuário, quero definir marcos para minha meta de longo prazo, para visualizar o progresso em etapas.

### 2.9 Dashboard e Insights
- **RF9.1:** Painel visual com:
  - Saldos: Total (considerando incluir_no_total e taxa de câmbio da conta), Total Geral, Saldo Projetado (com transações futuras).
  - Gráficos: Gastos por Categoria/Membro, Progresso de Metas, Orçamento Mensal, Evolução Patrimonial (total e por conta), Uso de Limite de Cartões (histórico e atual).
- **RF9.2:** Insights automáticos baseados em regras predefinidas (e.g., "Gastos com Restaurantes +20% este mês").
- **RF9.3:** Filtros por período, tipo, entidade e membro.
- **RF9.4:** Consulta de saldo histórico por conta (requer job diário para popular `account_balance_history`).
- **User Story:** Como usuário, quero ver a evolução do uso do limite do meu cartão por período.
- **User Story:** Como usuário, quero visualizar a evolução do meu patrimônio ao longo do tempo.

### 2.10 API para Integração
- **RF10.1:** API RESTful para integrações (e.g., N8N).
- **RF10.2:** Endpoints básicos: Criar Transação, Listar Contas, Listar Categorias.
- **RF10.3:** Autenticação via API Key (bearer token).
- **User Story:** Como usuário, quero que notificações de compras no cartão recebidas via N8N possam criar transações automaticamente no sistema.

---

## 3. Requisitos Não Funcionais Chave
- **Performance:** Interface responsiva, carregamento rápido (<2s), API ágil (<500ms). Lazy loading para listas longas.
- **Confiabilidade:** Alta disponibilidade (99.5%), backups diários automáticos, RTO <4h, RPO <24h.
- **Segurança:** HTTPS, rate limiting, sanitização de inputs, logs de auditoria, rotação de API Keys.
- **Usabilidade:** Interface em português, acessibilidade (WCAG 2.1 AA), suporte a tema escuro (dark mode) e claro, garantindo bom contraste e legibilidade.

---

## 4. Especificações Técnicas Gerais
- **Stack:** Frontend (React, Vite, Tailwind CSS), Backend (Node.js, Express, Prisma), Banco (PostgreSQL). Containerização com Docker.
- **Auditoria:** Tabelas principais terão campos `created_at`, `updated_at`, `deleted_at` (para soft delete), `version`.
- **Validações de Negócio:** Regras robustas para valores de transação, taxas de câmbio, parcelamentos, recorrências, etc.
- **Otimização de Banco:** Uso de índices e particionamento (e.g., `account_balance_history` populada por job diário).

---

## 5. Métricas e Monitoramento
- **Performance:** Tempo de resposta da API, queries de BD, carregamento do dashboard.
- **Negócio:** Volume de transações, contas ativas, uso de orçamento/limites.
- **Erro:** Taxas de erro da API e falhas em transações.
- **Alertas:** Para degradação de performance, erros excessivos, uso de recursos (disco).

---

## 6. Configuração e Testes
- **Ambiente:** Docker e Docker Compose para fácil configuração. Variáveis de ambiente para customização.
- **Testes:** Estratégia de testes unitários, de integração e E2E cobrindo fluxos críticos.

---

## 7. Critérios de Aceitação (Exemplos Chave)
- **CA1 (Cartão):** Criação de cartão com limite, compra debita do limite e atualiza disponível.
- **CA2 (Recorrência Percentual):** Transação recorrente percentual é calculada e gerada corretamente sobre o saldo.
- **CA3 (Transferência Multimoeda):** Transferência entre contas de moedas diferentes usa cotação informada e converte valor corretamente.
- **CA4 (Dashboard):** Dashboard exibe corretamente o histórico de uso de limite de cartão.
- **CA5 (Patrimônio):** Contas do tipo "Bens/Ativos" são listadas e podem ter logomarcas, alteram o patrimônio total (se incluídas).
- **CA6 (Performance):** Dashboard carrega rapidamente mesmo com grande volume de dados (e.g., 5.000 transações <2s).

---

## 8. Glossário Essencial
- **Taxa de Câmbio:** Cotação usada para converter valores entre moedas diferentes. Para transações, é a taxa do momento da transação. Para contas, é uma taxa manual para visualização consolidada.
- **Saldo Projetado:** Saldo futuro estimado, considerando transações futuras agendadas/parceladas.
- **Arquivamento (Soft Delete):** Processo de marcar um item como inativo (`deleted_at`) em vez de removê-lo fisicamente, preservando o histórico.
- **Membro da Família:** Indivíduo associado a contas/transações para organização e filtragem.
- **Bens/Ativos:** Contas que representam patrimônio (e.g., imóveis, veículos) e são usadas para projeções de patrimônio, mas não afetam o saldo corrente.
- **Logomarca (Brand Logo):** Imagem SVG opcional associada a uma conta, usada para identificação visual.

---

## 9. Instruções para Desenvolvimento com Agentes Autônomos de IA

### 9.1 Uso de **context7**
- **context7** será utilizado como repositório central para consulta de documentação atualizada e exemplos de código das tecnologias da stack (React, Vite, Tailwind CSS, Node.js, Express, Prisma, PostgreSQL).
- Agentes autônomos de IA devem consultar **context7** para garantir que o código siga as melhores práticas e padrões de alto nível.
- **Exemplo de Uso:** Antes de implementar uma nova funcionalidade, o agente deve buscar em **context7** exemplos de código similares e diretrizes de implementação.

### 9.2 Uso de **sequential-thinking**
- **sequential-thinking** será utilizado para organizar e raciocinar sobre o escopo de implementação de tarefas ou subtarefas.
- Cada tarefa deve ser decomposta em etapas lógicas e sequenciais, garantindo que todas as dependências e pré-requisitos sejam considerados.
- **Exemplo de Uso:** Para implementar o módulo de transações parceladas, o agente deve:
  1. Definir a lógica de cálculo das parcelas.
  2. Implementar a geração de transações futuras.
  3. Garantir que as parcelas sejam exibidas corretamente no dashboard.
  4. Testar a edição de parcelas e o ajuste na última parcela.

### 9.3 Diretrizes Gerais
- **Documentação:** Todo código deve ser bem documentado, com comentários explicativos e referências a requisitos do PRD.
- **Revisão de Código:** Agentes devem revisar o código gerado por outros agentes para garantir consistência e qualidade.
- **Testes Automatizados:** Implementar testes unitários e de integração para cada funcionalidade, cobrindo os critérios de aceitação.

---

Este PRD finalizado incorpora todas as correções e ajustes necessários, garantindo clareza e consistência para o desenvolvimento com agentes autônomos de IA.