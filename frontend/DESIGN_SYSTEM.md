# 🎨 Design System - Deep Blue Elegance

## Paleta de Cores

### Filosofia
A paleta "Deep Blue Elegance" foi criada para transmitir **sofisticação**, **confiança** e **modernidade**. Baseada em tons de azul profundo (deep blue) com nuances navy e midnight, oferece excelente contraste e legibilidade tanto em modo claro quanto escuro.

### Cores Primárias

#### Primary (Deep Blue)
```css
--primary-50:  #f0f4ff  /* Ultra claro - backgrounds sutis */
--primary-100: #e0e9ff  /* Muito claro - hover states */
--primary-200: #c7d6ff  /* Claro - borders suaves */
--primary-300: #a5b8ff  /* Médio claro - texto secundário */
--primary-400: #8094ff  /* Médio - elementos interativos */
--primary-500: #5d6fff  /* Base - cor principal */
--primary-600: #4c5bff  /* Escuro - hover principal */
--primary-700: #3d47d9  /* <PERSON><PERSON> escuro - texto em fundos claros */
--primary-800: #2d35a6  /* Deep - elementos importantes */
--primary-900: #1e2573  /* Midnight - texto em fundos muito claros */
--primary-950: #0f1240  /* Ultra deep - máximo contraste */
```

#### Secondary (Blue-Gray Sophisticated)
```css
--secondary-50:  #f8fafc  /* Backgrounds muito claros */
--secondary-100: #f1f5f9  /* Backgrounds claros */
--secondary-200: #e2e8f0  /* Borders suaves */
--secondary-300: #cbd5e1  /* Texto terciário */
--secondary-400: #94a3b8  /* Texto secundário */
--secondary-500: #64748b  /* Texto padrão */
--secondary-600: #475569  /* Texto importante */
--secondary-700: #334155  /* Texto escuro */
--secondary-800: #1e293b  /* Backgrounds escuros */
--secondary-900: #0f172a  /* Backgrounds muito escuros */
--secondary-950: #020617  /* Máximo contraste */
```

### Cores de Apoio

#### Success (Emerald)
```css
--success: hsl(158, 64%, 52%)          /* #10b981 */
--success-foreground: hsl(0, 0%, 100%) /* Texto em success */
```

#### Warning (Amber)
```css
--warning: hsl(43, 96%, 56%)           /* #f59e0b */
--warning-foreground: hsl(0, 0%, 100%) /* Texto em warning */
```

#### Info (Blue)
```css
--info: hsl(217, 91%, 60%)             /* #3b82f6 */
--info-foreground: hsl(0, 0%, 100%)    /* Texto em info */
```

#### Destructive (Red)
```css
--destructive: hsl(0, 84%, 60%)        /* #ef4444 */
--destructive-foreground: hsl(0, 0%, 100%) /* Texto em destructive */
```

## Temas

### Light Theme
- **Background**: Branco puro para máxima clareza
- **Foreground**: Deep blue escuro para excelente legibilidade
- **Cards**: Branco com sombras sutis
- **Borders**: Cinza claro com nuances azuladas

### Dark Theme (Padrão)
- **Background**: Deep blue ultra escuro (`hsl(235, 65%, 8%)`)
- **Foreground**: Branco quase puro para máximo contraste
- **Cards**: Deep blue ligeiramente mais claro (`hsl(235, 60%, 10%)`)
- **Borders**: Deep blue médio com transparência

## Sombras Elegantes

### Sombras Customizadas
```css
.shadow-soft     /* Sombra suave com nuances deep blue */
.shadow-medium   /* Sombra média para cards */
.shadow-hard     /* Sombra forte para modais */
.shadow-glow     /* Brilho sutil com cor primária */
.shadow-glow-lg  /* Brilho intenso para elementos especiais */
.shadow-deep     /* Sombra profunda para elevação máxima */
.shadow-elegant  /* Sombra sofisticada combinando primary e deep blue */
```

## Classes Utilitárias

### Gradientes
```css
.text-gradient      /* Gradiente de texto padrão */
.text-gradient-deep /* Gradiente de texto mais intenso */
.bg-gradient-deep   /* Background gradiente principal */
.bg-gradient-subtle /* Background gradiente sutil */
```

### Efeitos Glass
```css
.glass      /* Efeito vidro padrão */
.glass-deep /* Efeito vidro com nuances deep blue */
```

## Tipografia

### Hierarquia de Pesos
- **font-light (300)**: Texto decorativo, subtítulos
- **font-normal (400)**: Texto corpo padrão
- **font-medium (500)**: Texto importante, labels
- **font-semibold (600)**: Títulos secundários, botões
- **font-bold (700)**: Títulos principais
- **font-extrabold (800)**: Títulos de destaque

### Contrastes Otimizados
- **Texto em background claro**: `text-foreground` (deep blue escuro)
- **Texto em background escuro**: `text-foreground` (branco)
- **Texto secundário**: `text-muted-foreground`
- **Texto em primary**: `text-primary-foreground`

## Acessibilidade

### Contraste WCAG AA
- ✅ Todas as combinações de cores atendem ao padrão WCAG AA (4.5:1)
- ✅ Cores de apoio têm contraste adequado com seus foregrounds
- ✅ Estados de foco claramente visíveis com `ring-primary`

### Estados Interativos
- **Hover**: Redução de 10% na opacidade ou mudança para tom mais escuro
- **Focus**: Ring azul com `ring-primary`
- **Active**: Tom mais escuro da cor base
- **Disabled**: 50% de opacidade

## Uso Recomendado

### Botões
```tsx
// Primário
<Button className="bg-primary text-primary-foreground hover:bg-primary/90">

// Secundário  
<Button variant="secondary" className="bg-secondary text-secondary-foreground">

// Outline
<Button variant="outline" className="border-input hover:bg-accent">
```

### Cards
```tsx
<Card className="bg-card border-border shadow-soft">
  <CardHeader className="text-card-foreground">
    <CardTitle className="text-gradient-deep">Título</CardTitle>
  </CardHeader>
</Card>
```

### Estados
```tsx
// Success
<div className="bg-success text-success-foreground">

// Warning  
<div className="bg-warning text-warning-foreground">

// Error
<div className="bg-destructive text-destructive-foreground">
```

---

*Esta paleta foi criada para o Personal Finance Manager com foco em elegância, sofisticação e excelente experiência do usuário.*
