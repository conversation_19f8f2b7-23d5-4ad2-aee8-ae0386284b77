# Arquitetura Frontend - Personal Finance Manager

## 📋 Visão Geral

O frontend é uma Single Page Application (SPA) construída com React e TypeScript, seguindo princípios de arquitetura limpa e componentização.

## 🏗️ Arquitetura

### Camadas da Aplicação

```
┌─────────────────────────────────────┐
│              UI Layer               │
│         (Components/Pages)          │
├─────────────────────────────────────┤
│            State Layer              │
│        (Stores/Contexts)            │
├─────────────────────────────────────┤
│           Service Layer             │
│         (API/Utilities)             │
├─────────────────────────────────────┤
│            Data Layer               │
│      (Types/Validators)             │
└─────────────────────────────────────┘
```

### Fluxo de Dados

1. **UI Components** → Disparam ações
2. **Stores (Zustand)** → Gerenciam estado
3. **API Services** → Comunicam com backend
4. **React Query** → Cache e sincronização
5. **Components** → Reagem às mudanças de estado

## 🧩 Componentes

### Hierarquia de Componentes

```
App
├── Router
├── Providers (Theme, Query, etc.)
└── Pages
    ├── Layout Components
    ├── Feature Components
    └── UI Components (shadcn/ui)
```

### Tipos de Componentes

1. **UI Components** (`/components/ui/`)
   - Componentes base do shadcn/ui
   - Reutilizáveis e sem lógica de negócio
   - Exemplos: Button, Input, Card

2. **Feature Components** (`/components/[feature]/`)
   - Componentes específicos de funcionalidades
   - Contêm lógica de negócio
   - Exemplos: TransactionForm, AccountCard

3. **Layout Components** (`/components/layout/`)
   - Estrutura e navegação
   - Exemplos: Header, Sidebar, Footer

4. **Page Components** (`/pages/`)
   - Componentes de página completa
   - Orquestram feature components

## 🏪 Gerenciamento de Estado

### Estratégia Multi-Store

```typescript
// Estado Global (Zustand)
authStore      // Autenticação
userStore      // Dados do usuário
settingsStore  // Configurações da app

// Estado do Servidor (React Query)
useQuery       // Busca de dados
useMutation    // Mutações
useInfiniteQuery // Paginação

// Estado Local (useState/useReducer)
// Para estado específico de componentes
```

### Fluxo de Autenticação

```
Login → authStore.login() → API Call → Token Storage → User Data → Redirect
```

### Fluxo de Dados

```
Component → Store Action → API Service → React Query → Cache → Component Update
```

## 🔌 Integração com API

### Estrutura da API Client

```typescript
// /lib/api.ts
const api = axios.create({
  baseURL: process.env.VITE_API_URL,
  timeout: 10000,
})

// Interceptors para auth e error handling
api.interceptors.request.use(addAuthToken)
api.interceptors.response.use(handleResponse, handleError)

// Endpoints organizados por domínio
export const authApi = { login, logout, me }
export const transactionsApi = { getAll, create, update, delete }
```

### Padrão de Hooks para API

```typescript
// Custom hook para transações
function useTransactions() {
  return useQuery({
    queryKey: ['transactions'],
    queryFn: () => transactionsApi.getAll(),
    staleTime: 5 * 60 * 1000, // 5 minutos
  })
}

// Mutation para criar transação
function useCreateTransaction() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: transactionsApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries(['transactions'])
    },
  })
}
```

## 🎨 Sistema de Design

### Tema e Estilização

```typescript
// Configuração do tema deepblue
const theme = {
  colors: {
    primary: 'hsl(221.2 83.2% 53.3%)',
    secondary: 'hsl(210 40% 96%)',
    // ...
  },
  spacing: {
    // Baseado em Tailwind
  },
  typography: {
    fontFamily: 'Inter, sans-serif',
  },
}
```

### Componentes Responsivos

```typescript
// Breakpoints Tailwind
sm: '640px'   // Mobile landscape
md: '768px'   // Tablet
lg: '1024px'  // Desktop
xl: '1280px'  // Large desktop
```

## 🧪 Estratégia de Testes

### Pirâmide de Testes

```
        E2E Tests (Cypress)
           /\
          /  \
         /    \
    Integration Tests (Vitest)
       /\      /\
      /  \    /  \
     /    \  /    \
   Unit Tests (Vitest)
```

### Tipos de Testes

1. **Unit Tests**
   - Funções utilitárias
   - Hooks customizados
   - Componentes isolados

2. **Integration Tests**
   - Fluxos de componentes
   - Integração com stores
   - API mocking

3. **E2E Tests** (Futuro)
   - Fluxos críticos do usuário
   - Autenticação
   - Transações

## 📁 Organização de Arquivos

### Estrutura por Feature

```
src/
├── components/
│   ├── ui/              # shadcn/ui components
│   ├── layout/          # Layout components
│   ├── transactions/    # Transaction features
│   ├── accounts/        # Account features
│   └── common/          # Shared components
├── pages/               # Page components
├── hooks/               # Custom hooks
├── stores/              # Zustand stores
├── lib/                 # Utilities & config
├── types/               # TypeScript types
└── contexts/            # React contexts
```

### Convenções de Nomenclatura

- **Componentes**: PascalCase (`TransactionForm.tsx`)
- **Hooks**: camelCase com prefixo `use` (`useTransactions.ts`)
- **Stores**: camelCase com sufixo `Store` (`authStore.ts`)
- **Tipos**: PascalCase (`Transaction.ts`)
- **Utilitários**: camelCase (`formatCurrency.ts`)

## 🔒 Segurança

### Autenticação

- JWT tokens armazenados em localStorage
- Refresh automático de tokens
- Interceptors para adicionar auth headers
- Redirecionamento automático em caso de expiração

### Validação

- Validação client-side com Zod
- Sanitização de inputs
- Validação de tipos TypeScript

## 🚀 Performance

### Otimizações

1. **Code Splitting**
   - Lazy loading de páginas
   - Dynamic imports para componentes pesados

2. **Bundle Optimization**
   - Tree shaking automático (Vite)
   - Minificação em produção
   - Compressão gzip

3. **React Optimizations**
   - React.memo para componentes puros
   - useMemo/useCallback para computações caras
   - Virtualization para listas grandes

### Métricas de Performance

- **Bundle Size**: ~400KB (gzipped: ~125KB)
- **First Contentful Paint**: < 1.5s
- **Time to Interactive**: < 3s
- **Lighthouse Score**: 90+

## 🔄 Fluxos Principais

### Fluxo de Login

```
1. Usuário insere credenciais
2. Validação client-side (Zod)
3. Chamada API de login
4. Armazenamento do token
5. Atualização do authStore
6. Redirecionamento para dashboard
```

### Fluxo de Transação

```
1. Usuário acessa página de transações
2. Carregamento via React Query
3. Exibição em lista/cards
4. Filtros e busca local
5. Criação/edição via modal
6. Invalidação de cache
7. Atualização automática da UI
```

## 📈 Monitoramento

### Métricas Coletadas

- Performance de componentes
- Erros de JavaScript
- Tempo de carregamento de APIs
- Interações do usuário

### Error Boundaries

```typescript
// Captura de erros em componentes
<ErrorBoundary fallback={<ErrorPage />}>
  <App />
</ErrorBoundary>
```

## 🔮 Roadmap Técnico

### Próximas Implementações

1. **PWA** - Service Workers e cache offline
2. **Micro-frontends** - Divisão por domínios
3. **Server-Side Rendering** - Next.js migration
4. **Real-time Updates** - WebSockets
5. **Advanced Testing** - Visual regression tests

### Melhorias Contínuas

- Otimização de bundle size
- Melhoria de acessibilidade
- Implementação de design tokens
- Automação de testes E2E
