@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 235 65% 8%;
    --card: 0 0% 100%;
    --card-foreground: 235 65% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 235 65% 8%;
    --primary: 235 100% 69%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 235 65% 8%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 235 65% 8%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 235 100% 69%;
    --success: 158 64% 52%;
    --success-foreground: 0 0% 100%;
    --warning: 43 96% 56%;
    --warning-foreground: 0 0% 100%;
    --info: 217 91% 60%;
    --info-foreground: 0 0% 100%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 235 65% 8%;
    --foreground: 210 40% 98%;
    --card: 235 60% 10%;
    --card-foreground: 210 40% 98%;
    --popover: 235 60% 10%;
    --popover-foreground: 210 40% 98%;
    --primary: 235 100% 65%;
    --primary-foreground: 235 65% 8%;
    --secondary: 235 40% 15%;
    --secondary-foreground: 210 40% 98%;
    --muted: 235 40% 15%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 235 40% 18%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 50.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 235 40% 15%;
    --input: 235 40% 15%;
    --ring: 235 100% 75%;
    --success: 158 64% 52%;
    --success-foreground: 235 65% 8%;
    --warning: 43 96% 56%;
    --warning-foreground: 235 65% 8%;
    --info: 217 91% 60%;
    --info-foreground: 235 65% 8%;
    --radius: 0.5rem;
  }
}

@layer components {
  /* Custom utility classes for the project */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }

  .text-gradient-deep {
    @apply bg-gradient-to-r from-primary-500 to-primary-700 bg-clip-text text-transparent;
  }

  .glass {
    @apply border border-border/50 bg-background/80 backdrop-blur-md;
  }

  .glass-deep {
    @apply border border-primary-500/20 bg-background/90 backdrop-blur-lg shadow-elegant;
  }

  .bg-gradient-deep {
    @apply bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700;
  }

  .bg-gradient-subtle {
    @apply bg-gradient-to-br from-background via-card to-secondary/5;
  }

  .shadow-elegant {
    @apply shadow-lg shadow-primary-500/10;
  }

  .shadow-glow {
    @apply shadow-xl shadow-primary-500/20;
  }

  .shadow-soft {
    @apply shadow-md shadow-black/10;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) hsl(var(--background));
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply rounded-full bg-muted-foreground;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-foreground;
  }

  /* Custom form and UI classes */
  .btn-primary {
    @apply inline-flex items-center justify-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-outline {
    @apply inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .input {
    @apply flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm;
  }

  .input-error {
    @apply border-destructive focus-visible:ring-destructive;
  }

  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply text-sm font-medium text-foreground;
  }

  .form-error {
    @apply text-sm text-destructive;
  }

  .form-help {
    @apply text-xs text-muted-foreground;
  }

  .card {
    @apply rounded-xl border bg-card p-6 text-card-foreground shadow;
  }

  .modal-overlay {
    @apply fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4;
  }

  .modal-content {
    @apply w-full max-w-lg rounded-lg border bg-background p-6 shadow-lg;
  }

  /* Loading spinner animation */
  .spinner {
    @apply animate-spin rounded-full border-2 border-current border-t-transparent;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
