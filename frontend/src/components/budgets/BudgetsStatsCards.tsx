import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card'
import { Calculator, TrendingUp, TrendingDown, AlertTriangle, Target } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import type { BudgetStats } from '@/types/budget.types'

interface BudgetsStatsCardsProps {
  stats?: BudgetStats
  isLoading?: boolean
}

export function BudgetsStatsCards({ stats, isLoading }: BudgetsStatsCardsProps) {
  const formatNumber = (value: number | undefined) => {
    if (isLoading || value === undefined) return '...'
    return value.toLocaleString('pt-BR')
  }

  const formatCurrencyValue = (value: number | undefined) => {
    if (isLoading || value === undefined) return '...'
    return formatCurrency(value)
  }

  const formatPercentage = (value: number | undefined) => {
    if (isLoading || value === undefined) return '...'
    return `${value.toFixed(1)}%`
  }

  return (
    <div className="grid gap-6 md:grid-cols-4">
      {/* Total Budgets */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">Total de Orçamentos</CardTitle>
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-soft">
            <Calculator className="h-5 w-5 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-foreground">
            {formatNumber(stats?.totalBudgets)}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            orçamentos ativos
          </p>
        </CardContent>
      </Card>

      {/* Total Planned */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">Total Planejado</CardTitle>
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-soft">
            <Target className="h-5 w-5 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-foreground">
            {formatCurrencyValue(stats?.totalPlanned)}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            valor total orçado
          </p>
        </CardContent>
      </Card>

      {/* Total Spent */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">Total Gasto</CardTitle>
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 shadow-soft">
            <TrendingDown className="h-5 w-5 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-foreground">
            {formatCurrencyValue(stats?.totalSpent)}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            valor total gasto
          </p>
        </CardContent>
      </Card>

      {/* Total Remaining */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">Total Restante</CardTitle>
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600 shadow-soft">
            <TrendingUp className="h-5 w-5 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-foreground">
            {formatCurrencyValue(stats?.totalRemaining)}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            valor disponível
          </p>
        </CardContent>
      </Card>

      {/* Progress Overview */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300 md:col-span-2">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">Progresso Geral</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Progresso Médio</span>
              <span className="text-lg font-bold text-foreground">
                {formatPercentage(stats?.averageProgress)}
              </span>
            </div>
            
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-green-600">
                  {formatNumber(stats?.underBudgetCount)}
                </div>
                <div className="text-xs text-muted-foreground">Abaixo do Orçamento</div>
              </div>
              
              <div className="space-y-1">
                <div className="text-2xl font-bold text-blue-600">
                  {formatNumber(stats?.onTrackCount)}
                </div>
                <div className="text-xs text-muted-foreground">No Caminho Certo</div>
              </div>
              
              <div className="space-y-1">
                <div className="text-2xl font-bold text-red-600">
                  {formatNumber(stats?.overBudgetCount)}
                </div>
                <div className="text-xs text-muted-foreground">Acima do Orçamento</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alerts Summary */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300 md:col-span-2">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">Alertas e Atenção</CardTitle>
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-amber-500 to-amber-600 shadow-soft">
            <AlertTriangle className="h-5 w-5 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats?.overBudgetCount && stats.overBudgetCount > 0 ? (
              <div className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800 dark:text-red-200">
                    Orçamentos Estourados
                  </span>
                </div>
                <span className="text-lg font-bold text-red-600">
                  {formatNumber(stats.overBudgetCount)}
                </span>
              </div>
            ) : (
              <div className="flex items-center justify-center p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                <div className="text-center">
                  <div className="text-green-600 text-lg font-semibold">✓ Tudo sob controle!</div>
                  <div className="text-sm text-green-700 dark:text-green-300">
                    Nenhum orçamento estourado
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
