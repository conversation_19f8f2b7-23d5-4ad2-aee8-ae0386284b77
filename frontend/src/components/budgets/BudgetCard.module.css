/* Smooth animations for budget card accordion */
.collapsibleContent {
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.collapsibleContent[data-state="open"] {
  animation: slideDown 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.collapsibleContent[data-state="closed"] {
  animation: slideUp 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideDown {
  from {
    height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    height: var(--radix-collapsible-content-height);
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
    opacity: 1;
    transform: translateY(0);
  }
  to {
    height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* Smooth chevron rotation */
.chevronIcon {
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.chevronIcon[data-state="open"] {
  transform: rotate(180deg);
}

/* Hover effects for child budget items */
.childBudgetItem {
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.childBudgetItem:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Progress bar animation */
.progressBar {
  transition: width 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Badge animations */
.statusBadge {
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.statusBadge:hover {
  transform: scale(1.05);
}

/* Button hover effects */
.actionButton {
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.actionButton:hover {
  transform: scale(1.1);
}

.actionButton:active {
  transform: scale(0.95);
}

/* Focus styles for accessibility */
.collapsibleTrigger:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 6px;
}

.actionButton:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Staggered animation for child items */
.childBudgetItem:nth-child(1) { animation-delay: 0ms; }
.childBudgetItem:nth-child(2) { animation-delay: 50ms; }
.childBudgetItem:nth-child(3) { animation-delay: 100ms; }
.childBudgetItem:nth-child(4) { animation-delay: 150ms; }
.childBudgetItem:nth-child(5) { animation-delay: 200ms; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.childBudgetItem[data-state="open"] {
  animation: fadeInUp 300ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .collapsibleContent {
    padding: 0.5rem;
  }
  
  .childBudgetItem {
    padding: 0.75rem;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .childBudgetItem:hover {
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .collapsibleContent,
  .chevronIcon,
  .childBudgetItem,
  .progressBar,
  .statusBadge,
  .actionButton {
    transition: none;
    animation: none;
  }
}
