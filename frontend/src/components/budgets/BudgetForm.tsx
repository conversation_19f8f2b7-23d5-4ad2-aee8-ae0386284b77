import { useEffect, useState, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Calculator, DollarSign, Calendar, Tags, Users, X, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

import { useCategoriesForForms } from '@/hooks/useCategories'
import { useFamilyMembers } from '@/hooks/useFamilyMembers'
import { useBudgets } from '@/hooks/useBudgets'
import { formatCurrency, parseCurrency } from '@/lib/utils'
import { validateBudgetCreation } from '@/lib/budget-grouping'
import type { Budget, CreateBudgetRequest, UpdateBudgetRequest } from '@/types/budget.types'

// Validation schema
const budgetFormSchema = z.object({
  plannedAmount: z.string()
    .min(1, 'Valor planejado é obrigatório')
    .refine((val) => {
      const amount = parseCurrency(val)
      return amount > 0
    }, 'Valor planejado deve ser maior que zero'),
  month: z.number()
    .int('Mês deve ser um número inteiro')
    .min(1, 'Mês deve estar entre 1 e 12')
    .max(12, 'Mês deve estar entre 1 e 12'),
  year: z.number()
    .int('Ano deve ser um número inteiro')
    .min(2020, 'Ano deve ser maior ou igual a 2020')
    .max(2050, 'Ano deve ser menor ou igual a 2050'),
  categoryId: z.string()
    .min(1, 'Categoria é obrigatória'),
  familyMemberId: z.string()
    .optional(),
})

type BudgetFormData = z.infer<typeof budgetFormSchema>

interface BudgetFormProps {
  mode: 'create' | 'edit'
  budget?: Budget | null
  onSubmit: (data: CreateBudgetRequest | UpdateBudgetRequest) => void
  onCancel: () => void
  isLoading?: boolean
  submitText?: string
}

export function BudgetForm({
  mode,
  budget,
  onSubmit,
  onCancel,
  isLoading = false,
  submitText = 'Salvar'
}: BudgetFormProps) {
  const [validationError, setValidationError] = useState<string | null>(null)

  // Fetch categories and family members
  const { data: categories, isLoading: isLoadingCategories } = useCategoriesForForms()
  const { data: familyMembersData, isLoading: isLoadingMembers } = useFamilyMembers()

  // Fetch existing budgets for validation (only in create mode)
  const { data: budgetsData } = useBudgets()
  const existingBudgets = budgetsData?.data || []

  // Extract data from standardized API response
  const familyMembers = familyMembersData?.data || []

  const form = useForm<BudgetFormData>({
    resolver: zodResolver(budgetFormSchema),
    defaultValues: {
      plannedAmount: '',
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      categoryId: '',
      familyMemberId: 'all',
    },
  })

  // Update form when budget changes (for edit mode)
  useEffect(() => {
    if (mode === 'edit' && budget) {
      form.reset({
        plannedAmount: formatCurrency(budget.plannedAmount),
        month: budget.month,
        year: budget.year,
        categoryId: budget.categoryId,
        familyMemberId: budget.familyMemberId || 'all',
      })
    }
  }, [mode, budget, form])

  // Get selected category details for validation
  const selectedCategory = useMemo(() => {
    const categoryId = form.watch('categoryId')
    return categories?.find(cat => cat.id === categoryId)
  }, [categories, form.watch('categoryId')])

  // Validate budget creation in real-time
  const validationResult = useMemo(() => {
    if (mode === 'edit' || !selectedCategory) return { isValid: true }

    const formData = form.getValues()
    const familyMemberId = formData.familyMemberId === 'all' ? undefined : formData.familyMemberId

    // Find the category with parent info (categories from useCategoriesForForms might not have parent info)
    // For now, we'll assume the category structure is correct
    return validateBudgetCreation(
      selectedCategory.id,
      undefined, // We need to get parent info from a different source
      existingBudgets,
      formData.month,
      formData.year,
      familyMemberId
    )
  }, [mode, selectedCategory, existingBudgets, form.watch()])

  // Update validation error when validation result changes
  useEffect(() => {
    if (!validationResult.isValid) {
      setValidationError(validationResult.error || 'Erro de validação')
    } else {
      setValidationError(null)
    }
  }, [validationResult])

  const handleSubmit = (data: BudgetFormData) => {
    // Final validation before submit
    if (mode === 'create' && !validationResult.isValid) {
      return
    }

    const plannedAmount = parseCurrency(data.plannedAmount)

    const submitData = {
      plannedAmount,
      month: data.month,
      year: data.year,
      categoryId: data.categoryId,
      familyMemberId: data.familyMemberId === 'all' ? undefined : data.familyMemberId || undefined,
    }

    onSubmit(submitData)
  }

  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i)
  const months = [
    { value: 1, label: 'Janeiro' },
    { value: 2, label: 'Fevereiro' },
    { value: 3, label: 'Março' },
    { value: 4, label: 'Abril' },
    { value: 5, label: 'Maio' },
    { value: 6, label: 'Junho' },
    { value: 7, label: 'Julho' },
    { value: 8, label: 'Agosto' },
    { value: 9, label: 'Setembro' },
    { value: 10, label: 'Outubro' },
    { value: 11, label: 'Novembro' },
    { value: 12, label: 'Dezembro' },
  ]

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Validation Error Alert */}
        {validationError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {validationError}
            </AlertDescription>
          </Alert>
        )}

        {/* Planned Amount */}
        <FormField
          control={form.control}
          name="plannedAmount"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2 text-sm font-semibold">
                <DollarSign className="h-4 w-4" />
                Valor Planejado
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="R$ 0,00"
                  className="text-lg"
                  onChange={(e) => {
                    const value = e.target.value
                    const numericValue = value.replace(/[^\d,]/g, '')
                    field.onChange(numericValue)
                  }}
                />
              </FormControl>
              <FormDescription>
                Valor que você planeja gastar nesta categoria no período
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Period Selection */}
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="month"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2 text-sm font-semibold">
                  <Calendar className="h-4 w-4" />
                  Mês
                </FormLabel>
                <Select
                  value={field.value?.toString()}
                  onValueChange={(value) => field.onChange(parseInt(value))}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o mês" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {months.map((month) => (
                      <SelectItem key={month.value} value={month.value.toString()}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="year"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2 text-sm font-semibold">
                  <Calendar className="h-4 w-4" />
                  Ano
                </FormLabel>
                <Select
                  value={field.value?.toString()}
                  onValueChange={(value) => field.onChange(parseInt(value))}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o ano" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Category Selection */}
        <FormField
          control={form.control}
          name="categoryId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2 text-sm font-semibold">
                <Tags className="h-4 w-4" />
                Categoria
              </FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma categoria" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {isLoadingCategories ? (
                    <SelectItem value="loading" disabled>
                      Carregando categorias...
                    </SelectItem>
                  ) : categories && Array.isArray(categories) && categories.length > 0 ? (
                    categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        <div className="flex items-center gap-2">
                          {category.color && (
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: category.color }}
                            />
                          )}
                          {category.name}
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-categories" disabled>
                      Nenhuma categoria cadastrada
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <FormDescription>
                {categories && categories.length === 0 && !isLoadingCategories ? (
                  <span className="text-amber-600">
                    Você precisa criar pelo menos uma categoria antes de criar um orçamento.
                  </span>
                ) : (
                  'Categoria para a qual este orçamento se aplica'
                )}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Family Member Selection (Optional) */}
        <FormField
          control={form.control}
          name="familyMemberId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2 text-sm font-semibold">
                <Users className="h-4 w-4" />
                Membro da Família (Opcional)
              </FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um membro (opcional)" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">Todos os membros</SelectItem>
                  {isLoadingMembers ? (
                    <SelectItem value="loading" disabled>
                      Carregando membros...
                    </SelectItem>
                  ) : familyMembers && Array.isArray(familyMembers) ? (
                    familyMembers.map((member) => (
                      <SelectItem key={member.id} value={member.id} textValue={member.name}>
                        {member.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-members" disabled>
                      Nenhum membro encontrado
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <FormDescription>
                Deixe em branco para aplicar a todos os membros da família
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form Actions */}
        <div className="flex items-center justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="px-6"
          >
            <X className="h-4 w-4 mr-2" />
            Cancelar
          </Button>
          <Button
            type="submit"
            disabled={isLoading || (mode === 'create' && !validationResult.isValid)}
            className="bg-gradient-deep text-white px-6 shadow-glow hover:shadow-glow-lg transition-all duration-200"
          >
            {isLoading ? (
              <>
                <LoadingSpinner className="h-4 w-4 mr-2" />
                Salvando...
              </>
            ) : (
              <>
                <Calculator className="h-4 w-4 mr-2" />
                {submitText}
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}
