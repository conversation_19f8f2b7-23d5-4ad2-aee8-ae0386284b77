import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle, CheckCircle, TrendingUp } from 'lucide-react'
import { formatCurrency, cn } from '@/lib/utils'
import type { Budget } from '@/types/budget.types'

interface BudgetProgressBarProps {
  budget: Budget
  showDetails?: boolean
  className?: string
}

export function BudgetProgressBar({ budget, showDetails = true, className }: BudgetProgressBarProps) {
  const progress = Math.min(budget.progress, 100)
  const isOverBudget = budget.status === 'over_budget'
  const isOnTrack = budget.status === 'on_track'
  const isUnderBudget = budget.status === 'under_budget'

  // Determine progress bar color based on status
  const getProgressColor = () => {
    if (isOverBudget) return 'bg-red-500'
    if (progress >= 80) return 'bg-amber-500'
    if (progress >= 60) return 'bg-blue-500'
    return 'bg-green-500'
  }

  // Get status badge
  const getStatusBadge = () => {
    if (isOverBudget) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          Acima do Orçamento
        </Badge>
      )
    }
    
    if (isOnTrack) {
      return (
        <Badge variant="default" className="flex items-center gap-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          <TrendingUp className="h-3 w-3" />
          No Caminho Certo
        </Badge>
      )
    }
    
    return (
      <Badge variant="secondary" className="flex items-center gap-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
        <CheckCircle className="h-3 w-3" />
        Abaixo do Orçamento
      </Badge>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="font-medium text-foreground">
            Progresso: {progress.toFixed(1)}%
          </span>
          {showDetails && getStatusBadge()}
        </div>
        
        <div className="relative">
          <Progress 
            value={Math.min(progress, 100)} 
            className="h-3"
          />
          {/* Custom progress bar color overlay */}
          <div 
            className={cn(
              'absolute top-0 left-0 h-3 rounded-full transition-all duration-300',
              getProgressColor()
            )}
            style={{ width: `${Math.min(progress, 100)}%` }}
          />
          
          {/* Over-budget indicator */}
          {isOverBudget && progress > 100 && (
            <div className="absolute top-0 right-0 h-3 w-2 bg-red-600 rounded-r-full animate-pulse" />
          )}
        </div>
      </div>

      {/* Details */}
      {showDetails && (
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="font-semibold text-blue-600">
              {formatCurrency(budget.plannedAmount)}
            </div>
            <div className="text-xs text-muted-foreground">Planejado</div>
          </div>
          
          <div className="text-center">
            <div className={cn(
              'font-semibold',
              isOverBudget ? 'text-red-600' : 'text-orange-600'
            )}>
              {formatCurrency(budget.spentAmount)}
            </div>
            <div className="text-xs text-muted-foreground">Gasto</div>
          </div>
          
          <div className="text-center">
            <div className={cn(
              'font-semibold',
              budget.remainingAmount >= 0 ? 'text-green-600' : 'text-red-600'
            )}>
              {formatCurrency(Math.abs(budget.remainingAmount))}
            </div>
            <div className="text-xs text-muted-foreground">
              {budget.remainingAmount >= 0 ? 'Restante' : 'Excedido'}
            </div>
          </div>
        </div>
      )}

      {/* Warning for over-budget */}
      {isOverBudget && showDetails && (
        <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
          <AlertTriangle className="h-4 w-4 text-red-600 flex-shrink-0" />
          <div className="text-sm">
            <div className="font-medium text-red-800 dark:text-red-200">
              Orçamento Estourado
            </div>
            <div className="text-red-700 dark:text-red-300">
              Você gastou {formatCurrency(budget.spentAmount - budget.plannedAmount)} a mais que o planejado.
            </div>
          </div>
        </div>
      )}

      {/* Near limit warning */}
      {isOnTrack && progress >= 80 && progress < 100 && showDetails && (
        <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-950/20 rounded-lg border border-amber-200 dark:border-amber-800">
          <AlertTriangle className="h-4 w-4 text-amber-600 flex-shrink-0" />
          <div className="text-sm">
            <div className="font-medium text-amber-800 dark:text-amber-200">
              Atenção: Próximo do Limite
            </div>
            <div className="text-amber-700 dark:text-amber-300">
              Você já utilizou {progress.toFixed(1)}% do seu orçamento.
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
