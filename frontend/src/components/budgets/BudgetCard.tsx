import { useState, memo } from 'react'
import {
  Calculator,
  Edit,
  Trash2,
  MoreHorizontal,
  Calendar,
  Users,
  TrendingUp,
  AlertTriangle,
  Eye,
  ChevronDown,
  ChevronUp,
  FolderOpen
} from 'lucide-react'
import { formatCurrency, cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { BudgetProgressBar } from './BudgetProgressBar'
import type { Budget, GroupedBudget } from '@/types/budget.types'
import styles from './BudgetCard.module.css'

interface BudgetCardProps {
  budget: GroupedBudget
  onEdit: (budget: Budget) => void
  onDelete: (budgetId: string) => void
  onViewDetails?: (budget: Budget) => void
  className?: string
}

export const BudgetCard = memo(function BudgetCard({
  budget,
  onEdit,
  onDelete,
  onViewDetails,
  className
}: BudgetCardProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  const handleEdit = (budgetToEdit?: Budget) => {
    setIsMenuOpen(false)
    const targetBudget = budgetToEdit || (budget.type === 'individual' ? budget.budget! : budget.childBudgets![0])
    onEdit(targetBudget)
  }

  const handleDelete = (budgetId?: string) => {
    setIsMenuOpen(false)
    const targetId = budgetId || budget.id
    onDelete(targetId)
  }

  const handleViewDetails = (budgetToView?: Budget) => {
    setIsMenuOpen(false)
    const targetBudget = budgetToView || (budget.type === 'individual' ? budget.budget! : budget.childBudgets![0])
    onViewDetails?.(targetBudget)
  }

  const handleMainEdit = () => handleEdit()
  const handleMainDelete = () => handleDelete()
  const handleMainViewDetails = () => handleViewDetails()

  const getMonthName = (month: number) => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ]
    return months[month - 1] || 'Mês Inválido'
  }

  const isOverBudget = budget.status === 'over_budget'
  const isNearLimit = budget.status === 'on_track' && budget.progress >= 80

  return (
    <Card className={cn(
      'glass-deep shadow-elegant hover:shadow-glow transition-all duration-300 group',
      isOverBudget && 'border-red-200 dark:border-red-800',
      className
    )}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={cn(
              'flex h-10 w-10 items-center justify-center rounded-xl shadow-soft',
              isOverBudget
                ? 'bg-gradient-to-br from-red-500 to-red-600'
                : 'bg-gradient-deep'
            )}>
              {budget.type === 'grouped' ? (
                <FolderOpen className="h-5 w-5 text-white" />
              ) : (
                <Calculator className="h-5 w-5 text-white" />
              )}
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg font-bold text-foreground group-hover:text-gradient-deep transition-colors">
                  {budget.category.name}
                </CardTitle>
                {budget.type === 'grouped' && (
                  <Badge variant="outline" className="text-xs">
                    {budget.childBudgets?.length} categorias
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-3 w-3" />
                {getMonthName(budget.month)} {budget.year}
                {budget.category.parent && (
                  <Badge variant="outline" className="text-xs">
                    {budget.category.parent.name}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Status indicators */}
            {isOverBudget && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            {isNearLimit && (
              <TrendingUp className="h-4 w-4 text-amber-500" />
            )}

            {/* Actions menu */}
            <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {onViewDetails && (
                  <>
                    <DropdownMenuItem onClick={handleMainViewDetails}>
                      <Eye className="h-4 w-4 mr-2" />
                      Ver Detalhes
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </>
                )}
                <DropdownMenuItem onClick={handleMainEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleMainDelete}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Family Member */}
        {budget.familyMember && (
          <div className="flex items-center gap-2 mt-2">
            <Users className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              {budget.familyMember.name}
            </span>
            {budget.familyMember.avatar && (
              <img
                src={budget.familyMember.avatar}
                alt={budget.familyMember.name}
                className="w-4 h-4 rounded-full"
              />
            )}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <BudgetProgressBar
          budget={budget.type === 'individual' ? budget.budget! : {
            ...budget,
            categoryId: budget.category.id,
            familyMemberId: budget.familyMember?.id
          } as Budget}
          showDetails={true}
        />

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border/50">
          <div className="text-center">
            <div className="text-xs text-muted-foreground mb-1">Valor Planejado</div>
            <div className="font-semibold text-blue-600">
              {formatCurrency(budget.plannedAmount)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-muted-foreground mb-1">Progresso</div>
            <div className={cn(
              'font-semibold',
              isOverBudget ? 'text-red-600' : 
              budget.progress >= 80 ? 'text-amber-600' : 'text-green-600'
            )}>
              {budget.progress.toFixed(1)}%
            </div>
          </div>
        </div>

        {/* Category Color Indicator */}
        {budget.category.color && (
          <div className="flex items-center gap-2 pt-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: budget.category.color }}
            />
            <span className="text-xs text-muted-foreground">
              Cor da categoria
            </span>
          </div>
        )}

        {/* Grouped Budget Details */}
        {budget.type === 'grouped' && budget.childBudgets && budget.childBudgets.length > 0 && (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-between p-2 h-auto text-sm font-medium text-muted-foreground hover:text-foreground mt-4"
                aria-expanded={isExpanded}
                aria-controls={`budget-details-${budget.id}`}
                aria-label={`${isExpanded ? 'Ocultar' : 'Mostrar'} detalhes das ${budget.childBudgets.length} categorias do orçamento ${budget.category.name}`}
              >
                <span className="flex items-center gap-2">
                  <FolderOpen className="h-4 w-4" aria-hidden="true" />
                  Detalhes das Categorias ({budget.childBudgets.length})
                </span>
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" aria-hidden="true" />
                ) : (
                  <ChevronDown className="h-4 w-4" aria-hidden="true" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent
              className={cn("space-y-3 mt-3", styles.collapsibleContent)}
              id={`budget-details-${budget.id}`}
              role="region"
              aria-label={`Detalhes das categorias do orçamento ${budget.category.name}`}
            >
              {budget.childBudgets.map((childBudget, index) => (
                <div
                  key={childBudget.id}
                  className={cn(
                    "p-3 border rounded-lg hover:bg-muted/20 transition-colors",
                    styles.childBudgetItem
                  )}
                  role="article"
                  aria-label={`Categoria ${childBudget.category.name}, ${index + 1} de ${budget.childBudgets!.length}`}
                  data-state={isExpanded ? "open" : "closed"}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div
                        className="h-3 w-3 rounded-full"
                        style={{ backgroundColor: childBudget.category.color || '#6b7280' }}
                      />
                      <span className="font-medium text-sm">{childBudget.category.name}</span>
                      <Badge
                        variant={
                          childBudget.status === 'over_budget' ? 'destructive' :
                          childBudget.status === 'on_track' ? 'default' : 'secondary'
                        }
                        className={cn("text-xs", styles.statusBadge)}
                      >
                        {childBudget.progress.toFixed(0)}%
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(childBudget)}
                        className={cn("h-6 w-6 p-0", styles.actionButton)}
                        aria-label={`Editar orçamento da categoria ${childBudget.category.name}`}
                        title={`Editar orçamento da categoria ${childBudget.category.name}`}
                      >
                        <Edit className="h-3 w-3" aria-hidden="true" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(childBudget.id)}
                        className={cn("h-6 w-6 p-0 text-red-600 hover:text-red-700", styles.actionButton)}
                        aria-label={`Excluir orçamento da categoria ${childBudget.category.name}`}
                        title={`Excluir orçamento da categoria ${childBudget.category.name}`}
                      >
                        <Trash2 className="h-3 w-3" aria-hidden="true" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Planejado: {formatCurrency(childBudget.plannedAmount)}</span>
                      <span>Gasto: {formatCurrency(childBudget.spentAmount)}</span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-1.5">
                      <div
                        className={cn(
                          "h-1.5 rounded-full transition-all duration-300",
                          styles.progressBar,
                          childBudget.status === 'over_budget'
                            ? 'bg-red-500'
                            : childBudget.status === 'on_track'
                            ? 'bg-blue-500'
                            : 'bg-green-500'
                        )}
                        style={{ width: `${Math.min(childBudget.progress, 100)}%` }}
                      />
                    </div>
                  </div>

                  {childBudget.familyMember && (
                    <div className="flex items-center gap-1 mt-2 text-xs text-muted-foreground">
                      <Users className="h-3 w-3" />
                      <span>{childBudget.familyMember.name}</span>
                    </div>
                  )}
                </div>
              ))}
            </CollapsibleContent>
          </Collapsible>
        )}
      </CardContent>
    </Card>
  )
})

BudgetCard.displayName = 'BudgetCard'
