import { useState, useEffect } from 'react'
import { 
  Filter,
  X,
  Calendar,
  Tags,
  Users,
  TrendingUp,
  TrendingDown,
  AlertTriangle
} from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useCategoriesForForms } from '@/hooks/useCategories'
import { useFamilyMembers } from '@/hooks/useFamilyMembers'
import type { BudgetFilters } from '@/types/budget.types'

interface BudgetsFiltersProps {
  filters: BudgetFilters
  onFiltersChange: (filters: BudgetFilters) => void
  onClearFilters: () => void
}

export function BudgetsFilters({ filters, onFiltersChange, onClearFilters }: BudgetsFiltersProps) {
  const [localFilters, setLocalFilters] = useState<BudgetFilters>(filters)

  // Fetch categories and family members for filter options
  const { data: categoriesData } = useCategoriesForForms()

  const { data: familyMembersResponse } = useFamilyMembers()

  const categories = categoriesData || [] // Categories hook already returns clean array
  const familyMembers = familyMembersResponse?.data || [] // Family members from paginated response

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  const handleFilterChange = (key: keyof BudgetFilters, value: any) => {
    // Convert "all" values to undefined for API
    const processedValue = value === 'all' ? undefined : value
    const newFilters = { ...localFilters, [key]: processedValue }
    setLocalFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const handleClearFilters = () => {
    setLocalFilters({})
    onClearFilters()
  }

  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i)
  const months = [
    { value: 1, label: 'Janeiro' },
    { value: 2, label: 'Fevereiro' },
    { value: 3, label: 'Março' },
    { value: 4, label: 'Abril' },
    { value: 5, label: 'Maio' },
    { value: 6, label: 'Junho' },
    { value: 7, label: 'Julho' },
    { value: 8, label: 'Agosto' },
    { value: 9, label: 'Setembro' },
    { value: 10, label: 'Outubro' },
    { value: 11, label: 'Novembro' },
    { value: 12, label: 'Dezembro' },
  ]

  const statusOptions = [
    { value: 'under_budget', label: 'Abaixo do Orçamento', icon: TrendingDown, color: 'text-green-600' },
    { value: 'on_track', label: 'No Caminho Certo', icon: TrendingUp, color: 'text-blue-600' },
    { value: 'over_budget', label: 'Acima do Orçamento', icon: AlertTriangle, color: 'text-red-600' },
  ]

  const sortOptions = [
    { value: 'createdAt', label: 'Data de Criação' },
    { value: 'plannedAmount', label: 'Valor Planejado' },
    { value: 'month', label: 'Mês' },
    { value: 'year', label: 'Ano' },
    { value: 'category', label: 'Categoria' },
    { value: 'familyMember', label: 'Membro da Família' },
  ]

  const hasActiveFilters = Object.keys(localFilters).some(key => 
    localFilters[key as keyof BudgetFilters] !== undefined && 
    localFilters[key as keyof BudgetFilters] !== ''
  )

  return (
    <Card className="glass-deep shadow-elegant">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg font-bold text-gradient">
            <Filter className="h-5 w-5" />
            Filtros
          </CardTitle>
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilters}
              className="text-xs"
            >
              <X className="h-3 w-3 mr-1" />
              Limpar
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Period Filters */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2 text-sm font-semibold">
            <Calendar className="h-4 w-4" />
            Período
          </Label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs text-muted-foreground">Mês</Label>
              <Select
                value={localFilters.month?.toString() || ''}
                onValueChange={(value) => handleFilterChange('month', value ? parseInt(value) : undefined)}
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os meses</SelectItem>
                  {months.map((month) => (
                    <SelectItem key={month.value} value={month.value.toString()}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs text-muted-foreground">Ano</Label>
              <Select
                value={localFilters.year?.toString() || ''}
                onValueChange={(value) => handleFilterChange('year', value ? parseInt(value) : undefined)}
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os anos</SelectItem>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Separator />

        {/* Category Filter */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2 text-sm font-semibold">
            <Tags className="h-4 w-4" />
            Categoria
          </Label>
          <Select
            value={localFilters.categoryId || ''}
            onValueChange={(value) => handleFilterChange('categoryId', value || undefined)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todas as categorias" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas as categorias</SelectItem>
              {Array.isArray(categories) && categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  <div className="flex items-center gap-2">
                    {category.color && (
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                    )}
                    {category.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Family Member Filter */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2 text-sm font-semibold">
            <Users className="h-4 w-4" />
            Membro da Família
          </Label>
          <Select
            value={localFilters.familyMemberId || ''}
            onValueChange={(value) => handleFilterChange('familyMemberId', value || undefined)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todos os membros" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os membros</SelectItem>
              {Array.isArray(familyMembers) && familyMembers.map((member) => (
                <SelectItem key={member.id} value={member.id}>
                  <div className="flex items-center gap-2">
                    {member.avatar && (
                      <img
                        src={member.avatar}
                        alt={member.name}
                        className="w-4 h-4 rounded-full"
                      />
                    )}
                    {member.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Sorting */}
        <div className="space-y-3">
          <Label className="text-sm font-semibold">Ordenação</Label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs text-muted-foreground">Ordenar por</Label>
              <Select
                value={localFilters.sortBy || 'createdAt'}
                onValueChange={(value) => handleFilterChange('sortBy', value as any)}
              >
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs text-muted-foreground">Ordem</Label>
              <Select
                value={localFilters.sortOrder || 'desc'}
                onValueChange={(value) => handleFilterChange('sortOrder', value as any)}
              >
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">Crescente</SelectItem>
                  <SelectItem value="desc">Decrescente</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label className="text-sm font-semibold">Filtros Ativos</Label>
              <div className="flex flex-wrap gap-2">
                {localFilters.month && (
                  <Badge variant="secondary" className="text-xs">
                    Mês: {months.find(m => m.value === localFilters.month)?.label}
                  </Badge>
                )}
                {localFilters.year && (
                  <Badge variant="secondary" className="text-xs">
                    Ano: {localFilters.year}
                  </Badge>
                )}
                {localFilters.categoryId && (
                  <Badge variant="secondary" className="text-xs">
                    Categoria: {Array.isArray(categories) ? categories.find(c => c.id === localFilters.categoryId)?.name : 'Carregando...'}
                  </Badge>
                )}
                {localFilters.familyMemberId && (
                  <Badge variant="secondary" className="text-xs">
                    Membro: {Array.isArray(familyMembers) ? familyMembers.find(m => m.id === localFilters.familyMemberId)?.name : 'Carregando...'}
                  </Badge>
                )}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
