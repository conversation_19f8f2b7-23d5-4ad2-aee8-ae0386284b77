import { Calculator, X } from 'lucide-react'
import { BudgetForm } from './BudgetForm'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import type { Budget, CreateBudgetRequest, UpdateBudgetRequest } from '@/types/budget.types'

interface BudgetModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: CreateBudgetRequest | UpdateBudgetRequest) => void
  mode: 'create' | 'edit'
  budget?: Budget | null
  isLoading?: boolean
}

export function BudgetModal({ 
  open, 
  onOpenChange, 
  onSubmit, 
  mode, 
  budget, 
  isLoading = false 
}: BudgetModalProps) {
  const title = mode === 'create' ? 'Novo Orçamento' : 'Editar Orçamento'
  const submitText = mode === 'create' ? 'Criar Orçamento' : 'Salvar Alteraçõ<PERSON>'

  const handleSubmit = (data: CreateBudgetRequest | UpdateBudgetRequest) => {
    onSubmit(data)
  }

  const handleClose = () => {
    if (!isLoading) {
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2 text-xl font-bold text-gradient-deep">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-deep shadow-soft">
                <Calculator className="h-4 w-4 text-white" />
              </div>
              {title}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="mt-4">
          <BudgetForm
            mode={mode}
            budget={budget}
            onSubmit={handleSubmit}
            onCancel={handleClose}
            isLoading={isLoading}
            submitText={submitText}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
