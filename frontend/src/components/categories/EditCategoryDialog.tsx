"use client"

import { useState } from "react"
import { Edit } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { CategoryForm } from "./CategoryForm"
import { useUpdateCategory } from "@/hooks/useCategories"
import { type Category } from "@/types/category.types"
import { type CategoryFormData } from "@/lib/validations/category"

interface EditCategoryDialogProps {
  category: Category
  children?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function EditCategoryDialog({ 
  category, 
  children, 
  open: controlledOpen,
  onOpenChange: controlledOnOpenChange 
}: EditCategoryDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const updateMutation = useUpdateCategory()

  // Use controlled state if provided, otherwise use internal state
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen = controlledOnOpenChange || setInternalOpen

  const handleSubmit = async (data: CategoryFormData) => {
    try {
      await updateMutation.mutateAsync({
        id: category.id,
        data: {
          name: data.name,
          color: data.color || null,
          parentId: data.parentId || null,
        },
      })

      setOpen(false)
    } catch (error) {
      console.error("Error updating category:", error)
    }
  }

  const handleCancel = () => {
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {!controlledOpen && (
        <DialogTrigger asChild>
          {children || (
            <Button variant="outline" size="sm" className="gap-2">
              <Edit className="h-4 w-4" />
              Editar
            </Button>
          )}
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Editar Categoria</DialogTitle>
          <DialogDescription>
            Atualize as informações de "{category.name}". Você pode alterar o nome, cor e hierarquia.
          </DialogDescription>
        </DialogHeader>
        <CategoryForm
          category={category}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={updateMutation.isPending}
        />
      </DialogContent>
    </Dialog>
  )
}
