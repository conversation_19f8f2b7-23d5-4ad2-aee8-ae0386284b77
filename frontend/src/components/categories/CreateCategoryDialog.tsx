"use client"

import { useState } from "react"
import { Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { CategoryForm } from "./CategoryForm"
import { useCreateCategory } from "@/hooks/useCategories"
import { type CategoryFormData } from "@/lib/validations/category"

interface CreateCategoryDialogProps {
  children?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  defaultParentId?: string // For creating subcategories
}

export function CreateCategoryDialog({
  children,
  open: controlledOpen,
  onOpenChange,
  defaultParentId
}: CreateCategoryDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false)

  // Use controlled or internal state
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  const createMutation = useCreateCategory()

  const handleSubmit = async (data: CategoryFormData) => {
    try {
      await createMutation.mutateAsync({
        name: data.name,
        color: data.color || undefined,
        parentId: data.parentId || defaultParentId || undefined,
      })

      setOpen(false)
    } catch (error) {
      console.error("Error creating category:", error)
    }
  }

  const handleCancel = () => {
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {!controlledOpen && (
        <DialogTrigger asChild>
          {children || (
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Nova Categoria
            </Button>
          )}
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {defaultParentId ? "Adicionar Subcategoria" : "Adicionar Nova Categoria"}
          </DialogTitle>
          <DialogDescription>
            {defaultParentId 
              ? "Crie uma subcategoria para organizar melhor suas transações."
              : "Crie uma nova categoria para organizar suas transações. Você pode definir uma cor e hierarquia."
            }
          </DialogDescription>
        </DialogHeader>
        <CategoryForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={createMutation.isPending}
        />
      </DialogContent>
    </Dialog>
  )
}
