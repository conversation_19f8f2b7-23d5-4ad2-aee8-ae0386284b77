"use client"

import { useState } from "react"
import { Check, Palette, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { CATEGORY_COLORS, CATEGORY_VALIDATION } from "@/types/category.types"
import { cn } from "@/lib/utils"

interface CategoryColorPickerProps {
  value: string
  onChange: (color: string) => void
  className?: string
}

export function CategoryColorPicker({ value, onChange, className }: CategoryColorPickerProps) {
  const [customColor, setCustomColor] = useState(value)
  const [isOpen, setIsOpen] = useState(false)

  const handlePresetColorSelect = (color: string) => {
    onChange(color)
    setCustomColor(color)
    setIsOpen(false)
  }

  const handleRemoveColor = () => {
    onChange("")
    setCustomColor("")
    setIsOpen(false)
  }

  const handleCustomColorChange = (color: string) => {
    setCustomColor(color)
  }

  const handleCustomColorApply = () => {
    // Validate hex color format
    if (customColor && !CATEGORY_VALIDATION.COLOR_REGEX.test(customColor)) {
      return // Don't apply invalid colors
    }
    onChange(customColor)
    setIsOpen(false)
  }

  const isPresetColor = CATEGORY_COLORS.includes(value as any)
  const isValidCustomColor = value && CATEGORY_VALIDATION.COLOR_REGEX.test(value)

  return (
    <div className={cn("space-y-3", className)}>
      {/* No Color Option */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Sem cor</Label>
        <button
          type="button"
          className={cn(
            "relative h-8 w-8 rounded-full border-2 transition-all hover:scale-110 bg-muted",
            !value
              ? "border-primary scale-110 shadow-md"
              : "border-gray-300"
          )}
          onClick={handleRemoveColor}
          aria-label="Remover cor"
        >
          {!value && (
            <Check className="absolute inset-0 m-auto h-4 w-4 text-foreground" />
          )}
          {value && (
            <X className="absolute inset-0 m-auto h-3 w-3 text-muted-foreground" />
          )}
        </button>
      </div>

      {/* Preset Colors */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Cores predefinidas</Label>
        <div className="flex flex-wrap gap-2">
          {CATEGORY_COLORS.map((color) => (
            <button
              key={color}
              type="button"
              className={cn(
                "relative h-8 w-8 rounded-full border-2 transition-all hover:scale-110",
                value === color
                  ? "border-primary scale-110 shadow-md"
                  : "border-gray-300"
              )}
              style={{ backgroundColor: color }}
              onClick={() => handlePresetColorSelect(color)}
              aria-label={`Selecionar cor ${color}`}
            >
              {value === color && (
                <Check className="absolute inset-0 m-auto h-4 w-4 text-white drop-shadow-sm" />
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Custom Color Picker */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Cor personalizada</Label>
        <div className="flex items-center space-x-2">
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="gap-2 h-8"
              >
                <div
                  className="h-4 w-4 rounded border"
                  style={{ backgroundColor: value || '#f3f4f6' }}
                />
                <Palette className="h-4 w-4" />
                Personalizar
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-4" align="start">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="color-input">Escolher cor</Label>
                  <div className="flex items-center space-x-2">
                    <input
                      id="color-input"
                      type="color"
                      value={customColor || "#000000"}
                      onChange={(e) => handleCustomColorChange(e.target.value)}
                      className="h-10 w-16 rounded border border-input bg-background"
                    />
                    <Input
                      value={customColor || ""}
                      onChange={(e) => handleCustomColorChange(e.target.value)}
                      placeholder="#000000"
                      className="flex-1"
                      pattern="^#[0-9A-Fa-f]{6}$"
                    />
                  </div>
                  {customColor && !CATEGORY_VALIDATION.COLOR_REGEX.test(customColor) && (
                    <p className="text-xs text-destructive">
                      Formato inválido. Use #RRGGBB (ex: #FF5733)
                    </p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label>Preview</Label>
                  <div className="flex items-center space-x-2">
                    <div
                      className="h-8 w-8 rounded-full border-2 border-gray-300"
                      style={{ backgroundColor: customColor || '#f3f4f6' }}
                    />
                    <span className="text-sm text-muted-foreground">
                      {customColor || "Nenhuma cor"}
                    </span>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                  >
                    Cancelar
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleCustomColorApply}
                    disabled={customColor && !CATEGORY_VALIDATION.COLOR_REGEX.test(customColor)}
                  >
                    Aplicar
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          
          {value && !isPresetColor && isValidCustomColor && (
            <span className="text-xs text-muted-foreground">
              Cor personalizada
            </span>
          )}
        </div>
      </div>

      {/* Current Color Display */}
      {value && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Cor atual:</span>
          <div
            className="h-4 w-4 rounded border"
            style={{ backgroundColor: value }}
          />
          <code className="text-xs">{value}</code>
        </div>
      )}
    </div>
  )
}
