import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { LoadingSpinner } from './LoadingSpinner'

describe('LoadingSpinner', () => {
  describe('rendering', () => {
    it('should render with default props', () => {
      render(<LoadingSpinner />)

      const spinner = screen.getByRole('status')
      expect(spinner).toBeInTheDocument()
      expect(spinner).toHaveClass('spinner', 'h-6', 'w-6')
    })

    it('should render with custom className', () => {
      render(<LoadingSpinner className="custom-class" />)

      const spinner = screen.getByRole('status')
      expect(spinner).toHaveClass('spinner', 'h-6', 'w-6', 'custom-class')
    })
  })

  describe('size variants', () => {
    it('should render small size', () => {
      render(<LoadingSpinner size="sm" />)

      const spinner = screen.getByRole('status')
      expect(spinner).toHaveClass('h-4', 'w-4')
      expect(spinner).not.toHaveClass('h-6', 'w-6')
      expect(spinner).not.toHaveClass('h-8', 'w-8')
    })

    it('should render medium size (default)', () => {
      render(<LoadingSpinner size="md" />)

      const spinner = screen.getByRole('status')
      expect(spinner).toHaveClass('h-6', 'w-6')
      expect(spinner).not.toHaveClass('h-4', 'w-4')
      expect(spinner).not.toHaveClass('h-8', 'w-8')
    })

    it('should render large size', () => {
      render(<LoadingSpinner size="lg" />)

      const spinner = screen.getByRole('status')
      expect(spinner).toHaveClass('h-8', 'w-8')
      expect(spinner).not.toHaveClass('h-4', 'w-4')
      expect(spinner).not.toHaveClass('h-6', 'w-6')
    })
  })

  describe('accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<LoadingSpinner />)

      const spinner = screen.getByRole('status')
      expect(spinner).toBeInTheDocument()
      expect(spinner).toHaveAttribute('aria-label', 'Loading')
    })

    it('should be accessible with screen readers', () => {
      render(<LoadingSpinner />)

      // The spinner should be findable by role
      const spinner = screen.getByRole('status')
      expect(spinner).toBeInTheDocument()
    })
  })

  describe('CSS classes', () => {
    it('should always include base spinner class', () => {
      render(<LoadingSpinner />)

      const spinner = screen.getByRole('status')
      expect(spinner).toHaveClass('spinner')
    })

    it('should combine size classes with custom classes', () => {
      render(<LoadingSpinner size="lg" className="text-blue-500 animate-spin" />)

      const spinner = screen.getByRole('status')
      expect(spinner).toHaveClass('spinner', 'h-8', 'w-8', 'text-blue-500', 'animate-spin')
    })

    it('should handle empty className', () => {
      render(<LoadingSpinner className="" />)

      const spinner = screen.getByRole('status')
      expect(spinner).toHaveClass('spinner', 'h-6', 'w-6')
    })

    it('should handle undefined className', () => {
      render(<LoadingSpinner className={undefined} />)

      const spinner = screen.getByRole('status')
      expect(spinner).toHaveClass('spinner', 'h-6', 'w-6')
    })
  })

  describe('props validation', () => {
    it('should handle all valid size props', () => {
      const sizes = ['sm', 'md', 'lg'] as const

      sizes.forEach((size) => {
        const { unmount } = render(<LoadingSpinner size={size} />)
        const spinner = screen.getByRole('status')
        expect(spinner).toBeInTheDocument()
        unmount()
      })
    })

    it('should default to medium size when no size provided', () => {
      render(<LoadingSpinner />)

      const spinner = screen.getByRole('status')
      expect(spinner).toHaveClass('h-6', 'w-6')
    })
  })

  describe('component structure', () => {
    it('should render as a div element', () => {
      render(<LoadingSpinner />)

      const spinner = screen.getByRole('status')
      expect(spinner.tagName).toBe('DIV')
    })

    it('should not have any children', () => {
      render(<LoadingSpinner />)

      const spinner = screen.getByRole('status')
      expect(spinner).toBeEmptyDOMElement()
    })

    it('should be a self-closing component', () => {
      const { container } = render(<LoadingSpinner />)
      
      const spinner = container.firstChild as HTMLElement
      expect(spinner.children.length).toBe(0)
    })
  })
})
