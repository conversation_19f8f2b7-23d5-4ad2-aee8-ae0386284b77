import { useState } from 'react'
import { Target, Search, Filter, SortAsc, SortDesc } from 'lucide-react'
import { GoalCard } from './GoalCard'


import { GoalsFilters } from './GoalsFilters'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useDebounce } from '@/hooks/useDebounce'
import type { FinancialGoal, GoalWithProgress, GoalFilters } from '@/types/goal.types'

interface GoalsListProps {
  goals: FinancialGoal[]
  isLoading?: boolean
  onEdit: (goal: FinancialGoal) => void
  onDelete: (goalId: string) => void
  onUpdateProgress?: (goalId: string, amount: number) => void
  onAddMilestone?: (goalId: string) => void
  onEditMilestone?: (milestone: any) => void
  onDeleteMilestone?: (milestoneId: string) => void
}

interface GoalsListSkeletonProps {
  count?: number
}

function GoalsListSkeleton({ count = 6 }: GoalsListSkeletonProps) {
  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="glass-deep p-6 rounded-xl shadow-elegant">
          {/* Header skeleton */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-xl bg-secondary-800 animate-pulse" />
              <div className="space-y-2">
                <div className="h-5 w-32 bg-secondary-800 rounded animate-pulse" />
                <div className="h-4 w-4 bg-secondary-800 rounded animate-pulse" />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-6 w-16 bg-secondary-800 rounded animate-pulse" />
              <div className="h-8 w-8 bg-secondary-800 rounded animate-pulse" />
            </div>
          </div>

          {/* Progress skeleton */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <div className="h-4 w-16 bg-secondary-800 rounded animate-pulse" />
              <div className="h-4 w-12 bg-secondary-800 rounded animate-pulse" />
            </div>
            <div className="h-2 w-full bg-secondary-800 rounded animate-pulse mb-2" />
            <div className="flex items-center justify-between">
              <div className="h-4 w-24 bg-secondary-800 rounded animate-pulse" />
              <div className="h-4 w-20 bg-secondary-800 rounded animate-pulse" />
            </div>
          </div>

          {/* Details skeleton */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 bg-secondary-800 rounded animate-pulse" />
              <div className="h-4 w-32 bg-secondary-800 rounded animate-pulse" />
            </div>
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 bg-secondary-800 rounded animate-pulse" />
              <div className="h-4 w-24 bg-secondary-800 rounded animate-pulse" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export function GoalsList({
  goals,
  isLoading,
  onEdit,
  onDelete,
  onUpdateProgress,
  onAddMilestone,
  onEditMilestone,
  onDeleteMilestone
}: GoalsListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<GoalFilters>({})
  const [sortBy, setSortBy] = useState<'name' | 'targetDate' | 'targetAmount' | 'progress'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  const debouncedSearchTerm = useDebounce(searchTerm, 300)

  // Filter and sort goals
  const filteredAndSortedGoals = goals
    .filter(goal => {
      // Search filter
      if (debouncedSearchTerm) {
        const searchLower = debouncedSearchTerm.toLowerCase()
        if (!goal.name.toLowerCase().includes(searchLower)) {
          return false
        }
      }

      // Status filter
      if (filters.status && filters.status !== 'all') {
        const goalStatus = goal.progress?.status || 'not_started'
        if (filters.status === 'active' && !['not_started', 'in_progress'].includes(goalStatus)) {
          return false
        }
        if (filters.status === 'completed' && goalStatus !== 'completed') {
          return false
        }
        if (filters.status === 'overdue' && goalStatus !== 'overdue') {
          return false
        }
      }

      // Family member filter
      if (filters.familyMemberId) {
        const hasMembers = goal.members?.some(member => 
          member.familyMemberId === filters.familyMemberId
        )
        if (!hasMembers) {
          return false
        }
      }

      return true
    })
    .sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'targetDate':
          aValue = a.targetDate ? new Date(a.targetDate).getTime() : 0
          bValue = b.targetDate ? new Date(b.targetDate).getTime() : 0
          break
        case 'targetAmount':
          aValue = a.targetAmount
          bValue = b.targetAmount
          break
        case 'progress':
          aValue = a.progress?.percentage || 0
          bValue = b.progress?.percentage || 0
          break
        default:
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

  const toggleSort = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(newSortBy)
      setSortOrder('desc')
    }
  }

  if (isLoading) {
    return <GoalsListSkeleton />
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="glass-deep p-4 rounded-xl shadow-elegant">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar metas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filter Toggle */}
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filtros
          </Button>

          {/* Sort Options */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleSort('name')}
              className="flex items-center gap-1"
            >
              Nome
              {sortBy === 'name' && (
                sortOrder === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleSort('progress')}
              className="flex items-center gap-1"
            >
              Progresso
              {sortBy === 'progress' && (
                sortOrder === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleSort('targetDate')}
              className="flex items-center gap-1"
            >
              Data
              {sortBy === 'targetDate' && (
                sortOrder === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-border">
            <GoalsFilters
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>
        )}
      </div>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          {filteredAndSortedGoals.length} meta{filteredAndSortedGoals.length !== 1 ? 's' : ''} encontrada{filteredAndSortedGoals.length !== 1 ? 's' : ''}
        </p>
      </div>

      {/* Goals Grid */}
      {filteredAndSortedGoals.length === 0 ? (
        <div className="glass-deep p-12 rounded-xl shadow-elegant text-center">
          <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">
            {goals.length === 0 ? 'Nenhuma meta cadastrada' : 'Nenhuma meta encontrada'}
          </h3>
          <p className="text-muted-foreground">
            {goals.length === 0 
              ? 'Comece criando sua primeira meta financeira'
              : 'Tente ajustar os filtros ou termo de busca'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {filteredAndSortedGoals.map((goal) => (
            <GoalCard
              key={goal.id}
              goal={goal as GoalWithProgress}
              onEdit={onEdit}
              onDelete={onDelete}
              onUpdateProgress={onUpdateProgress}
              onAddMilestone={onAddMilestone}
              onEditMilestone={onEditMilestone}
              onDeleteMilestone={onDeleteMilestone}
            />
          ))}
        </div>
      )}
    </div>
  )
}
