import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Target, Calendar, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { formatCurrency, parseCurrency, formatDateForInput } from '@/lib/utils'
import type { GoalMilestone, CreateMilestoneData, UpdateMilestoneData } from '@/types/goal.types'

// Validation schema
const milestoneFormSchema = z.object({
  name: z.string()
    .min(1, 'Nome é obrigatório')
    .min(3, 'Nome deve ter pelo menos 3 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres'),
  targetAmount: z.string()
    .min(1, 'Valor é obrigatório')
    .refine((val) => {
      const amount = parseCurrency(val)
      return amount > 0
    }, 'Valor deve ser maior que zero'),
  targetDate: z.string()
    .min(1, 'Data é obrigatória'),
})

type MilestoneFormData = z.infer<typeof milestoneFormSchema>

interface MilestoneModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateMilestoneData | UpdateMilestoneData) => void
  mode: 'create' | 'edit'
  milestone?: GoalMilestone | null
  goalTargetAmount?: number
  isLoading?: boolean
}

export function MilestoneModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  mode, 
  milestone,
  goalTargetAmount = 0,
  isLoading = false 
}: MilestoneModalProps) {
  const title = mode === 'create' ? 'Novo Marco' : 'Editar Marco'
  const submitText = mode === 'create' ? 'Criar Marco' : 'Salvar Alterações'

  const form = useForm<MilestoneFormData>({
    resolver: zodResolver(milestoneFormSchema),
    defaultValues: {
      name: '',
      targetAmount: '',
      targetDate: '',
    },
  })

  // Load milestone data for editing
  useEffect(() => {
    if (mode === 'edit' && milestone) {
      form.reset({
        name: milestone.name,
        targetAmount: formatCurrency(milestone.targetAmount),
        targetDate: formatDateForInput(milestone.targetDate),
      })
    } else if (mode === 'create') {
      form.reset({
        name: '',
        targetAmount: '',
        targetDate: '',
      })
    }
  }, [mode, milestone, form, isOpen])

  const handleSubmit = (data: MilestoneFormData) => {
    const targetAmount = parseCurrency(data.targetAmount)

    // Validate that milestone amount doesn't exceed goal target amount
    if (goalTargetAmount > 0 && targetAmount > goalTargetAmount) {
      form.setError('targetAmount', {
        type: 'manual',
        message: 'Valor do marco não pode ser maior que o valor da meta'
      })
      return
    }

    const formData = {
      name: data.name.trim(),
      targetAmount,
      targetDate: new Date(data.targetDate + 'T00:00:00.000Z').toISOString(),
    }

    onSubmit(formData)
  }

  const handleClose = () => {
    if (!isLoading) {
      form.reset()
      onClose()
    }
  }

  const formatAmountInput = (value: string) => {
    // Remove non-numeric characters except comma and dot
    const numericValue = value.replace(/[^\d.,]/g, '')
    
    // Convert to number and format as currency
    const number = parseCurrency(numericValue)
    if (isNaN(number) || number === 0) return ''
    
    return formatCurrency(number)
  }

  const handleAmountChange = (field: any, value: string) => {
    field.onChange(value)
  }

  const handleAmountBlur = (field: any, value: string) => {
    const formatted = formatAmountInput(value)
    field.onChange(formatted)
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg font-bold text-gradient-deep">
            <div className="flex h-6 w-6 items-center justify-center rounded-lg bg-gradient-deep shadow-soft">
              <Target className="h-3 w-3 text-white" />
            </div>
            {title}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 mt-4">
            {/* Milestone Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Nome do Marco
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Ex: Primeira parcela"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Target Amount */}
            <FormField
              control={form.control}
              name="targetAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Valor do Marco
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="0,00"
                        {...field}
                        onChange={(e) => handleAmountChange(field, e.target.value)}
                        onBlur={(e) => handleAmountBlur(field, e.target.value)}
                        className="pl-10"
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                  {goalTargetAmount > 0 && (
                    <p className="text-xs text-muted-foreground">
                      Valor máximo: {formatCurrency(goalTargetAmount)}
                    </p>
                  )}
                </FormItem>
              )}
            />

            {/* Target Date */}
            <FormField
              control={form.control}
              name="targetDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Data Alvo
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-gradient-deep text-white"
              >
                {isLoading ? 'Salvando...' : submitText}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
