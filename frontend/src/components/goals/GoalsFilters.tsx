import { X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useFamilyMembers } from '@/hooks/useFamilyMembers'
import type { GoalFilters } from '@/types/goal.types'

interface GoalsFiltersProps {
  filters: GoalFilters
  onFiltersChange: (filters: GoalFilters) => void
}

export function GoalsFilters({ filters, onFiltersChange }: GoalsFiltersProps) {
  const { data: familyMembersData } = useFamilyMembers()
  const familyMembers = familyMembersData?.data || []

  const handleFilterChange = (key: keyof GoalFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value === 'all' ? undefined : value
    })
  }

  const clearFilters = () => {
    onFiltersChange({})
  }

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== null && value !== ''
  )

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-foreground">Filtros</h3>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4 mr-1" />
            Limpar
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Status Filter */}
        <div className="space-y-2">
          <Label htmlFor="status-filter" className="text-sm font-medium">
            Status
          </Label>
          <Select
            value={filters.status || 'all'}
            onValueChange={(value) => handleFilterChange('status', value)}
          >
            <SelectTrigger id="status-filter">
              <SelectValue placeholder="Todos os status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os status</SelectItem>
              <SelectItem value="active">Ativas</SelectItem>
              <SelectItem value="completed">Concluídas</SelectItem>
              <SelectItem value="overdue">Vencidas</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Family Member Filter */}
        <div className="space-y-2">
          <Label htmlFor="member-filter" className="text-sm font-medium">
            Membro da Família
          </Label>
          <Select
            value={filters.familyMemberId || 'all'}
            onValueChange={(value) => handleFilterChange('familyMemberId', value)}
          >
            <SelectTrigger id="member-filter">
              <SelectValue placeholder="Todos os membros" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os membros</SelectItem>
              {familyMembers.map((member) => (
                <SelectItem key={member.id} value={member.id}>
                  <div className="flex items-center gap-2">
                    {member.color && (
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: member.color }}
                      />
                    )}
                    {member.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Target Amount Range */}
        <div className="space-y-2">
          <Label htmlFor="min-amount-filter" className="text-sm font-medium">
            Valor Mínimo
          </Label>
          <Select
            value={filters.minTargetAmount?.toString() || 'all'}
            onValueChange={(value) => handleFilterChange('minTargetAmount', value === 'all' ? undefined : Number(value))}
          >
            <SelectTrigger id="min-amount-filter">
              <SelectValue placeholder="Qualquer valor" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Qualquer valor</SelectItem>
              <SelectItem value="1000">R$ 1.000+</SelectItem>
              <SelectItem value="5000">R$ 5.000+</SelectItem>
              <SelectItem value="10000">R$ 10.000+</SelectItem>
              <SelectItem value="25000">R$ 25.000+</SelectItem>
              <SelectItem value="50000">R$ 50.000+</SelectItem>
              <SelectItem value="100000">R$ 100.000+</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Sort By */}
        <div className="space-y-2">
          <Label htmlFor="sort-filter" className="text-sm font-medium">
            Ordenar por
          </Label>
          <Select
            value={filters.sortBy || 'createdAt'}
            onValueChange={(value) => handleFilterChange('sortBy', value)}
          >
            <SelectTrigger id="sort-filter">
              <SelectValue placeholder="Data de criação" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="createdAt">Data de criação</SelectItem>
              <SelectItem value="name">Nome</SelectItem>
              <SelectItem value="targetDate">Data alvo</SelectItem>
              <SelectItem value="targetAmount">Valor alvo</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Additional Options */}
      <div className="flex flex-wrap gap-4 pt-2 border-t border-border">
        <div className="flex items-center space-x-2">
          <Label htmlFor="include-completed" className="text-sm font-medium">
            Incluir concluídas
          </Label>
          <Select
            value={filters.includeCompleted?.toString() || 'true'}
            onValueChange={(value) => handleFilterChange('includeCompleted', value === 'true')}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Sim</SelectItem>
              <SelectItem value="false">Não</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Label htmlFor="include-milestones" className="text-sm font-medium">
            Incluir marcos
          </Label>
          <Select
            value={filters.includeMilestones?.toString() || 'true'}
            onValueChange={(value) => handleFilterChange('includeMilestones', value === 'true')}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Sim</SelectItem>
              <SelectItem value="false">Não</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Label htmlFor="sort-order" className="text-sm font-medium">
            Ordem
          </Label>
          <Select
            value={filters.sortOrder || 'desc'}
            onValueChange={(value) => handleFilterChange('sortOrder', value)}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="desc">Decrescente</SelectItem>
              <SelectItem value="asc">Crescente</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="pt-2 border-t border-border">
          <div className="flex flex-wrap gap-2">
            {filters.status && filters.status !== 'all' && (
              <div className="flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-xs">
                Status: {filters.status === 'active' ? 'Ativas' : filters.status === 'completed' ? 'Concluídas' : 'Vencidas'}
                <button
                  onClick={() => handleFilterChange('status', undefined)}
                  className="ml-1 hover:text-primary/80"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            
            {filters.familyMemberId && (
              <div className="flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-xs">
                Membro: {familyMembers.find(m => m.id === filters.familyMemberId)?.name}
                <button
                  onClick={() => handleFilterChange('familyMemberId', undefined)}
                  className="ml-1 hover:text-primary/80"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}

            {filters.minTargetAmount && (
              <div className="flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-xs">
                Min: R$ {filters.minTargetAmount.toLocaleString()}+
                <button
                  onClick={() => handleFilterChange('minTargetAmount', undefined)}
                  className="ml-1 hover:text-primary/80"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
