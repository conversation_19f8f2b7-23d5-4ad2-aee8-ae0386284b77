import { Target, X } from 'lucide-react'
import { GoalForm } from './GoalForm'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import type { FinancialGoal, CreateGoalData, UpdateGoalData } from '@/types/goal.types'

interface GoalModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateGoalData | UpdateGoalData) => void
  mode: 'create' | 'edit'
  goal?: FinancialGoal | null
  isLoading?: boolean
}

export function GoalModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  mode, 
  goal, 
  isLoading = false 
}: GoalModalProps) {
  const title = mode === 'create' ? 'Nova Meta Financeira' : 'Editar Meta Financeira'
  const submitText = mode === 'create' ? 'Criar Meta' : 'Salvar Alterações'

  const handleSubmit = (data: CreateGoalData | UpdateGoalData) => {
    onSubmit(data)
  }

  const handleClose = () => {
    if (!isLoading) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2 text-xl font-bold text-gradient-deep">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-deep shadow-soft">
                <Target className="h-4 w-4 text-white" />
              </div>
              {title}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="mt-4">
          <GoalForm
            mode={mode}
            goal={goal}
            onSubmit={handleSubmit}
            onCancel={handleClose}
            isLoading={isLoading}
            submitText={submitText}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
