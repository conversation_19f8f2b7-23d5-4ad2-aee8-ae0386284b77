import { memo } from 'react'
import {
  ChevronDown,
  ChevronRight,
  Target,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  Calendar,
  DollarSign
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { formatCurrency, formatDate, cn } from '@/lib/utils'
import type { GoalMilestone } from '@/types/goal.types'

interface MilestonesListProps {
  milestones: GoalMilestone[]
  isExpanded: boolean
  onToggleExpanded: () => void
  onAddMilestone: () => void
  onEditMilestone: (milestone: GoalMilestone) => void
  onDeleteMilestone: (milestoneId: string) => void
  isLoading?: boolean
}

export const MilestonesList = memo(function MilestonesList({
  milestones,
  isExpanded,
  onToggleExpanded,
  onAddMilestone,
  onEditMilestone,
  onDeleteMilestone,
  isLoading = false
}: MilestonesListProps) {
  const completedMilestones = milestones.filter(m => m.isCompleted).length
  const totalMilestones = milestones.length

  const getMilestoneStatus = (milestone: GoalMilestone) => {
    if (milestone.isCompleted) return 'completed'
    
    const targetDate = new Date(milestone.targetDate)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    if (targetDate < today) return 'overdue'
    return 'pending'
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success" className="text-xs">Concluído</Badge>
      case 'overdue':
        return <Badge variant="destructive" className="text-xs">Vencido</Badge>
      default:
        return <Badge variant="secondary" className="text-xs">Pendente</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-success" />
      case 'overdue':
        return <Clock className="h-4 w-4 text-destructive" />
      default:
        return <Target className="h-4 w-4 text-muted-foreground" />
    }
  }

  if (totalMilestones === 0) {
    return (
      <div className="mt-4 p-4 border border-border rounded-lg bg-muted/20">
        <div className="flex items-center justify-center">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Target className="h-4 w-4" />
            Nenhum marco definido
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="mt-4">
      {/* Header */}
      <div className="flex items-center justify-between p-2 sm:p-3 border border-border rounded-t-lg bg-muted/20">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleExpanded}
          className="flex items-center gap-2 text-sm font-medium min-w-0 flex-1 justify-start"
        >
          {isExpanded ? (
            <ChevronDown className="h-4 w-4 flex-shrink-0" />
          ) : (
            <ChevronRight className="h-4 w-4 flex-shrink-0" />
          )}
          <Target className="h-4 w-4 flex-shrink-0" />
          <span className="truncate">Marcos ({completedMilestones}/{totalMilestones})</span>
        </Button>

        <div className="flex items-center gap-2 flex-shrink-0">
          {totalMilestones > 0 && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Progress
                value={(completedMilestones / totalMilestones) * 100}
                className="w-12 sm:w-16 h-1"
              />
              <span className="hidden sm:inline">{Math.round((completedMilestones / totalMilestones) * 100)}%</span>
            </div>
          )}
        </div>
      </div>

      {/* Milestones List */}
      {isExpanded && (
        <div className="border-x border-b border-border rounded-b-lg bg-card/50">
          {milestones.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              Nenhum marco cadastrado
            </div>
          ) : (
            <div className="divide-y divide-border">
              {milestones.map((milestone) => {
                const status = getMilestoneStatus(milestone)
                
                return (
                  <div key={milestone.id} className="p-3 sm:p-4 hover:bg-muted/30 transition-colors">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2 flex-wrap">
                          {getStatusIcon(status)}
                          <h4 className={cn(
                            "font-medium text-sm flex-1 min-w-0",
                            milestone.isCompleted && "line-through text-muted-foreground"
                          )}>
                            {milestone.name}
                          </h4>
                          <div className="flex-shrink-0">
                            {getStatusBadge(status)}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3 flex-shrink-0" />
                            <span className="truncate">Valor: {formatCurrency(milestone.targetAmount)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3 flex-shrink-0" />
                            <span className="truncate">Data: {formatDate(milestone.targetDate)}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-1 sm:ml-2 self-end sm:self-start">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEditMilestone(milestone)}
                          disabled={isLoading}
                          className="h-7 w-7 p-0"
                          title="Editar marco"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteMilestone(milestone.id)}
                          disabled={isLoading}
                          className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                          title="Excluir marco"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      )}
    </div>
  )
})
