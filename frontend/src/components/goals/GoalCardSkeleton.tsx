export function GoalCardSkeleton() {
  return (
    <div className="glass-deep p-6 rounded-xl shadow-elegant animate-pulse">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 bg-secondary-800 rounded-xl" />
          <div>
            <div className="h-5 w-32 bg-secondary-800 rounded mb-2" />
            <div className="h-4 w-4 bg-secondary-800 rounded" />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="h-6 w-20 bg-secondary-800 rounded-full" />
          <div className="h-8 w-8 bg-secondary-800 rounded" />
        </div>
      </div>

      {/* Progress Section */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <div className="h-4 w-16 bg-secondary-800 rounded" />
          <div className="h-4 w-12 bg-secondary-800 rounded" />
        </div>
        <div className="h-2 w-full bg-secondary-800 rounded mb-2" />
        <div className="flex items-center justify-between">
          <div className="h-3 w-24 bg-secondary-800 rounded" />
          <div className="h-3 w-20 bg-secondary-800 rounded" />
        </div>
      </div>

      {/* Details */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <div className="h-4 w-4 bg-secondary-800 rounded" />
          <div className="h-3 w-16 bg-secondary-800 rounded" />
          <div className="h-3 w-24 bg-secondary-800 rounded" />
        </div>
        
        <div className="flex items-center gap-2">
          <div className="h-4 w-4 bg-secondary-800 rounded" />
          <div className="h-3 w-20 bg-secondary-800 rounded" />
          <div className="flex gap-1">
            <div className="h-5 w-16 bg-secondary-800 rounded-full" />
            <div className="h-5 w-20 bg-secondary-800 rounded-full" />
          </div>
        </div>
      </div>
    </div>
  )
}
