import { Target, CheckCircle, Clock, AlertTriangle, TrendingUp, DollarSign } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import type { GoalsSummary } from '@/types/goal.types'

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  color: string
  textColor: string
  description?: string
  isLoading?: boolean
}

function StatCard({ title, value, icon: Icon, color, textColor, description, isLoading }: StatCardProps) {
  if (isLoading) {
    return (
      <div className="glass-deep p-6 rounded-xl shadow-elegant">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-secondary-800 animate-pulse">
              <div className="h-6 w-6 bg-secondary-700 rounded animate-pulse" />
            </div>
          </div>
          <div className="ml-4 flex-1">
            <div className="h-4 w-24 bg-secondary-800 rounded animate-pulse mb-2" />
            <div className="h-8 w-16 bg-secondary-800 rounded animate-pulse" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="glass-deep p-6 rounded-xl shadow-elegant hover:shadow-glow transition-all duration-300">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`flex h-12 w-12 items-center justify-center rounded-xl ${color} shadow-soft`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-semibold text-muted-foreground">{title}</p>
          <p className={`text-3xl font-bold ${textColor}`}>
            {typeof value === 'number' && (title.includes('Valor') || title.includes('Total'))
              ? formatCurrency(value)
              : value}
          </p>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
      </div>
    </div>
  )
}

interface GoalsStatsCardsProps {
  summary?: GoalsSummary
  isLoading?: boolean
}

export function GoalsStatsCards({ summary, isLoading }: GoalsStatsCardsProps) {
  const stats = [
    {
      title: 'Total de Metas',
      value: summary?.totalGoals || 0,
      icon: Target,
      color: 'bg-gradient-deep',
      textColor: 'text-primary',
      description: 'Metas cadastradas no sistema'
    },
    {
      title: 'Metas Ativas',
      value: summary?.activeGoals || 0,
      icon: Clock,
      color: 'bg-blue-600',
      textColor: 'text-blue-400',
      description: 'Metas em progresso'
    },
    {
      title: 'Metas Concluídas',
      value: summary?.completedGoals || 0,
      icon: CheckCircle,
      color: 'bg-success',
      textColor: 'text-success',
      description: 'Metas já alcançadas'
    },
    {
      title: 'Metas Vencidas',
      value: summary?.overdueGoals || 0,
      icon: AlertTriangle,
      color: 'bg-destructive',
      textColor: 'text-destructive',
      description: 'Metas com prazo vencido'
    },
    {
      title: 'Valor Total das Metas',
      value: summary?.totalTargetAmount || 0,
      icon: DollarSign,
      color: 'bg-purple-600',
      textColor: 'text-purple-400',
      description: 'Soma de todas as metas'
    },
    {
      title: 'Valor Atual',
      value: summary?.totalCurrentAmount || 0,
      icon: TrendingUp,
      color: 'bg-green-600',
      textColor: 'text-green-400',
      description: 'Progresso total acumulado'
    },
    {
      title: 'Progresso Geral',
      value: summary?.overallProgress ? `${summary.overallProgress.toFixed(1)}%` : '0%',
      icon: Target,
      color: 'bg-gradient-deep',
      textColor: 'text-primary',
      description: 'Progresso médio de todas as metas'
    }
  ]

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {stats.map((stat) => (
        <StatCard
          key={stat.title}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          color={stat.color}
          textColor={stat.textColor}
          description={stat.description}
          isLoading={isLoading}
        />
      ))}
    </div>
  )
}
