import { useState } from 'react'
import { 
  History, 
  TrendingUp, 
  TrendingDown, 
  RotateCcw, 
  Calendar, 
  ChevronDown,
  ChevronUp,
  Eye,
  EyeOff
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { cn } from '@/lib/utils'
import { formatCurrency, formatDateTime } from '@/lib/utils'
import { useProgressHistory } from '@/hooks/useProgressHistory'
import type { ProgressHistoryEntry } from '@/types/progress-history.types'

interface GoalProgressHistoryProps {
  goalId: string
  className?: string
}

export function GoalProgressHistory({ goalId, className }: GoalProgressHistoryProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showAll, setShowAll] = useState(false)

  const { data: historyData, isLoading } = useProgressHistory({
    goalId,
    page: 1,
    limit: showAll ? 50 : 5,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  const getOperationIcon = (operation: string) => {
    switch (operation) {
      case 'add':
        return <TrendingUp className="h-3 w-3 text-success" />
      case 'subtract':
        return <TrendingDown className="h-3 w-3 text-destructive" />
      case 'set':
        return <RotateCcw className="h-3 w-3 text-warning" />
      default:
        return <TrendingUp className="h-3 w-3 text-muted-foreground" />
    }
  }

  const getOperationBadge = (operation: string) => {
    const variants = {
      add: 'default',
      subtract: 'destructive', 
      set: 'secondary'
    } as const

    const labels = {
      add: 'Adição',
      subtract: 'Subtração',
      set: 'Definição'
    }

    return (
      <Badge 
        variant={variants[operation as keyof typeof variants] || 'outline'}
        className="text-xs"
      >
        {labels[operation as keyof typeof labels] || operation}
      </Badge>
    )
  }

  const getAmountChangeDisplay = (entry: ProgressHistoryEntry) => {
    const { operation, amountChanged } = entry
    const isPositive = operation === 'add' || (operation === 'set' && amountChanged > 0)
    
    return (
      <span className={cn(
        "font-medium text-xs",
        isPositive ? "text-success" : "text-destructive"
      )}>
        {operation === 'set' ? '' : (isPositive ? '+' : '')}
        {formatCurrency(amountChanged)}
      </span>
    )
  }

  const historyEntries = historyData?.data || []
  const hasHistory = historyEntries.length > 0
  const hasMoreEntries = (historyData?.pagination?.total || 0) > 5

  // Always show the component, even when there's no history
  // if (!hasHistory && !isLoading) {
  //   return null
  // }

  return (
    <div className={cn("mt-4 pt-4 border-t border-border/50", className)}>
      <div className="flex items-center justify-between mb-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="h-auto p-0 text-sm font-medium text-muted-foreground hover:text-foreground"
        >
          <History className="h-4 w-4 mr-2" />
          Histórico de Progresso
          {hasHistory && (
            <Badge variant="outline" className="ml-2 text-xs">
              {historyData?.pagination?.total || historyEntries.length}
            </Badge>
          )}
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 ml-2" />
          ) : (
            <ChevronDown className="h-4 w-4 ml-2" />
          )}
        </Button>
      </div>

      {isExpanded && (
        <div className="space-y-2">
          {isLoading && (
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-3 p-2 border rounded-lg">
                  <Skeleton className="h-6 w-6 rounded-full" />
                  <div className="space-y-1 flex-1">
                    <Skeleton className="h-3 w-1/3" />
                    <Skeleton className="h-2 w-1/2" />
                  </div>
                  <Skeleton className="h-4 w-16" />
                </div>
              ))}
            </div>
          )}

          {historyEntries.map((entry) => (
            <div
              key={entry.id}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/20 transition-colors"
            >
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <div className="flex-shrink-0">
                  {getOperationIcon(entry.operation)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    {getOperationBadge(entry.operation)}
                  </div>
                  
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    <span>{formatDateTime(entry.createdAt)}</span>
                  </div>
                  
                  <div className="text-xs text-muted-foreground mt-1">
                    {formatCurrency(entry.previousAmount)} → {formatCurrency(entry.newAmount)}
                  </div>
                  
                  {entry.description && (
                    <p className="text-xs text-muted-foreground mt-1 truncate">
                      {entry.description}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex-shrink-0">
                {getAmountChangeDisplay(entry)}
              </div>
            </div>
          ))}

          {hasMoreEntries && (
            <div className="flex justify-center pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAll(!showAll)}
                className="text-xs"
              >
                {showAll ? (
                  <>
                    <EyeOff className="h-3 w-3 mr-1" />
                    Mostrar menos
                  </>
                ) : (
                  <>
                    <Eye className="h-3 w-3 mr-1" />
                    Mostrar todos ({historyData?.pagination?.total})
                  </>
                )}
              </Button>
            </div>
          )}

          {historyEntries.length === 0 && !isLoading && (
            <div className="text-center py-4 text-muted-foreground text-sm">
              <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Nenhum histórico de progresso ainda</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
