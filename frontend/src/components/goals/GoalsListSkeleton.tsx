import { GoalCardSkeleton } from './GoalCardSkeleton'

export function GoalsListSkeleton() {
  return (
    <div className="space-y-6">
      {/* Search and Filters Skeleton */}
      <div className="glass-deep p-6 rounded-xl shadow-elegant">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="h-10 w-full bg-secondary-800 rounded animate-pulse" />
          </div>
          <div className="flex gap-2">
            <div className="h-10 w-24 bg-secondary-800 rounded animate-pulse" />
            <div className="h-10 w-32 bg-secondary-800 rounded animate-pulse" />
          </div>
        </div>
      </div>

      {/* Results Count Skeleton */}
      <div className="flex items-center justify-between">
        <div className="h-4 w-32 bg-secondary-800 rounded animate-pulse" />
      </div>

      {/* Goals Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <GoalCardSkeleton key={index} />
        ))}
      </div>
    </div>
  )
}
