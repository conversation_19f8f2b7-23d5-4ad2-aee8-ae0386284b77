"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { Search, Filter, Columns, Users } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { FamilyMember, FamilyMemberFilters } from "@/types/family-member.types"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  isLoading?: boolean
  onBulkArchive?: (ids: string[]) => void
  onBulkDelete?: (ids: string[]) => void
  onFiltersChange?: (filters: FamilyMemberFilters) => void
  currentFilters?: FamilyMemberFilters
}

export function FamilyMembersDataTable<TData, TValue>({
  columns,
  data,
  isLoading = false,
  onBulkArchive,
  onBulkDelete,
  onFiltersChange,
  currentFilters,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [statusFilter, setStatusFilter] = React.useState<string>("all")

  // Optimize filtered data with proper memoization
  const filteredData = React.useMemo(() => {
    if (!Array.isArray(data)) return []

    switch (statusFilter) {
      case "active":
        return data.filter((item: any) => !item.archived)
      case "archived":
        return data.filter((item: any) => item.archived)
      default:
        return data
    }
  }, [data, statusFilter])

  const table = useReactTable({
    data: filteredData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })

  // Handle status filter changes
  React.useEffect(() => {
    if (onFiltersChange) {
      if (statusFilter === "active") {
        onFiltersChange({
          ...currentFilters,
          includeArchived: false,
          includeDeleted: false
        })
      } else if (statusFilter === "archived") {
        onFiltersChange({
          ...currentFilters,
          includeArchived: true,
          includeDeleted: false
        })
      } else {
        onFiltersChange({
          ...currentFilters,
          includeArchived: true,
          includeDeleted: false
        })
      }
    }
  }, [statusFilter, onFiltersChange, currentFilters])

  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length
  const totalRowsCount = table.getFilteredRowModel().rows.length
  const selectedMembers = table.getFilteredSelectedRowModel().rows.map(row => row.original as FamilyMember)
  const selectedIds = selectedMembers.map(member => member.id)

  const handleBulkArchive = () => {
    if (onBulkArchive && selectedIds.length > 0) {
      onBulkArchive(selectedIds)
      table.resetRowSelection()
    }
  }

  const handleBulkDelete = () => {
    if (onBulkDelete && selectedIds.length > 0) {
      onBulkDelete(selectedIds)
      table.resetRowSelection()
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {/* Loading skeleton for filters */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="h-9 w-64 bg-muted animate-pulse rounded-md" />
            <div className="h-9 w-32 bg-muted animate-pulse rounded-md" />
          </div>
          <div className="h-9 w-24 bg-muted animate-pulse rounded-md" />
        </div>
        
        {/* Loading skeleton for table */}
        <div className="rounded-md border">
          <div className="h-12 bg-muted animate-pulse" />
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="h-16 border-t bg-muted/50 animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Filters and Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* Search by name */}
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" aria-hidden="true" />
            <Input
              placeholder="Buscar por nome..."
              value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
              onChange={(event) =>
                table.getColumn("name")?.setFilterValue(event.target.value)
              }
              className="pl-8 max-w-sm"
              aria-label="Buscar membros por nome"
            />
          </div>

          {/* Status filter */}
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32" aria-label="Filtrar por status">
              <Filter className="mr-2 h-4 w-4" aria-hidden="true" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos</SelectItem>
              <SelectItem value="active">Ativos</SelectItem>
              <SelectItem value="archived">Arquivados</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Column visibility */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" aria-label="Configurar visibilidade das colunas">
              <Columns className="mr-2 h-4 w-4" aria-hidden="true" />
              Colunas
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                const columnLabels: Record<string, string> = {
                  name: "Nome",
                  color: "Cor",
                  archived: "Status",
                  createdAt: "Criado em"
                }

                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                  >
                    {columnLabels[column.id] || column.id}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Selection info */}
      {selectedRowsCount > 0 && (
        <div className="flex items-center justify-between rounded-md bg-muted p-3" role="region" aria-label="Ações em massa">
          <div className="text-sm text-muted-foreground">
            <strong>{selectedRowsCount}</strong> de <strong>{totalRowsCount}</strong> membro(s) selecionado(s).
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkArchive}
              disabled={selectedIds.length === 0}
              aria-label={`Arquivar ${selectedRowsCount} membro(s) selecionado(s)`}
            >
              Arquivar selecionados ({selectedRowsCount})
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleBulkDelete}
              disabled={selectedIds.length === 0}
              aria-label={`Excluir permanentemente ${selectedRowsCount} membro(s) selecionado(s)`}
            >
              Excluir selecionados ({selectedRowsCount})
            </Button>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border">
        <Table role="table" aria-label="Lista de membros da família">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} role="row">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} role="columnheader">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  role="row"
                  aria-selected={row.getIsSelected()}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} role="cell">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow role="row">
                <TableCell colSpan={columns.length} className="h-24 text-center" role="cell">
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <Users className="h-8 w-8 text-muted-foreground" aria-hidden="true" />
                    <div className="text-sm text-muted-foreground">
                      Nenhum membro encontrado.
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between space-x-2 py-4" role="navigation" aria-label="Paginação da tabela">
        <div className="text-sm text-muted-foreground" aria-live="polite">
          {totalRowsCount} membro(s) encontrado(s).
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            aria-label="Página anterior"
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            aria-label="Próxima página"
          >
            Próximo
          </Button>
        </div>
      </div>
    </div>
  )
}
