import { useState } from 'react'
import { 
  Filter,
  X,
  Search,
  Users,
  Archive,
  CheckCircle
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

export interface FamilyMemberFilters {
  search?: string
  status?: 'all' | 'active' | 'archived'
  sortBy?: 'name' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
  includeArchived?: boolean
  includeDeleted?: boolean
}

interface FamilyMembersFiltersProps {
  filters: FamilyMemberFilters
  onFiltersChange: (filters: FamilyMemberFilters) => void
  onClearFilters: () => void
}

const sortOptions = [
  { value: 'name', label: 'Nome' },
  { value: 'createdAt', label: 'Data de Criação' },
]

export function FamilyMembersFilters({ filters, onFiltersChange, onClearFilters }: FamilyMembersFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const updateFilter = (key: keyof FamilyMemberFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const hasActiveFilters = Boolean(
    filters.search ||
    (filters.status && filters.status !== 'all') ||
    (filters.sortBy && filters.sortBy !== 'name') ||
    (filters.sortOrder && filters.sortOrder !== 'asc')
  )

  return (
    <div className="card">
      {/* Header com busca e toggle de filtros */}
      <div className="flex items-center gap-4 mb-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-500" />
          <Input
            placeholder="Buscar membros..."
            value={filters.search || ''}
            onChange={(e) => updateFilter('search', e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className={`btn-outline ${hasActiveFilters ? 'border-primary text-primary' : ''}`}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filtros
          {hasActiveFilters && (
            <span className="ml-2 bg-primary text-primary-foreground rounded-full px-2 py-0.5 text-xs">
              {[
                filters.search,
                filters.status && filters.status !== 'all',
                filters.sortBy && filters.sortBy !== 'name',
                filters.sortOrder && filters.sortOrder !== 'asc'
              ].filter(Boolean).length}
            </span>
          )}
        </Button>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-secondary-400 hover:text-white"
          >
            <X className="h-4 w-4 mr-2" />
            Limpar
          </Button>
        )}
      </div>

      {/* Filtros expandidos */}
      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-border">
          {/* Status */}
          <div className="form-group">
            <Label className="form-label">Status</Label>
            <Select
              value={filters.status || 'all'}
              onValueChange={(value) => updateFilter('status', value === 'all' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os status</SelectItem>
                <SelectItem value="active">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-success-400" />
                    Ativos
                  </div>
                </SelectItem>
                <SelectItem value="archived">
                  <div className="flex items-center gap-2">
                    <Archive className="h-4 w-4 text-secondary-500" />
                    Arquivados
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Campo vazio para manter o grid */}
          <div></div>
          <div></div>
        </div>
      )}

      {/* Ordenação */}
      <div className="flex items-center gap-4 mt-4 pt-4 border-t border-border">
        <Label className="form-label text-sm">Ordenar por:</Label>
        
        <Select
          value={filters.sortBy || 'name'}
          onValueChange={(value) => updateFilter('sortBy', value)}
        >
          <SelectTrigger className="w-auto">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => updateFilter('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
          className="flex items-center gap-2"
        >
          {filters.sortOrder === 'desc' ? '↓' : '↑'}
          {filters.sortOrder === 'desc' ? 'Decrescente' : 'Crescente'}
        </Button>
      </div>
    </div>
  )
}
