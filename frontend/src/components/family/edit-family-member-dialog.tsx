"use client"

import { useState, useEffect } from "react"
import { Edit } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { FamilyMemberForm } from "./family-member-form"
import { useUpdateFamilyMember } from "@/hooks/useFamilyMembers"
import { type FamilyMember } from "@/types/family-member.types"
import { type FamilyMemberFormData } from "@/lib/validations/family-member"

interface EditFamilyMemberDialogProps {
  member: FamilyMember
  children?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function EditFamilyMemberDialog({ 
  member, 
  children, 
  open: controlledOpen,
  onOpenChange: controlledOnOpenChange 
}: EditFamilyMemberDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const updateMutation = useUpdateFamilyMember()

  // Use controlled state if provided, otherwise use internal state
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen = controlledOnOpenChange || setInternalOpen

  const handleSubmit = async (data: FamilyMemberFormData) => {
    try {
      // If avatar is a File, we'll need to upload it first
      let avatarUrl: string | undefined

      if (data.avatar instanceof File) {
        // TODO: Implement avatar upload
        // For now, we'll skip the avatar upload
        console.log("Avatar upload not implemented yet")
      } else if (typeof data.avatar === "string") {
        avatarUrl = data.avatar
      }

      await updateMutation.mutateAsync({
        id: member.id,
        data: {
          name: data.name,
          color: data.color,
          avatar: avatarUrl,
        },
      })

      setOpen(false)
    } catch (error) {
      console.error("Error updating family member:", error)
    }
  }

  const handleCancel = () => {
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" size="sm" className="gap-2">
            <Edit className="h-4 w-4" />
            Editar
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Editar Membro</DialogTitle>
          <DialogDescription>
            Atualize as informações de {member.name}. Você pode alterar o nome, cor e foto.
          </DialogDescription>
        </DialogHeader>
        <FamilyMemberForm
          member={member}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={updateMutation.isPending}
        />
      </DialogContent>
    </Dialog>
  )
}
