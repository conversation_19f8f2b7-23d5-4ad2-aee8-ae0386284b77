"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  familyMemberFormSchema,
  type FamilyMemberFormData,
} from "@/lib/validations/family-member"
import { FAMILY_MEMBER_COLORS, type FamilyMember } from "@/types/family-member.types"
// import { AvatarUpload } from "./avatar-upload" // Temporariamente removido
import { ColorPicker } from "./color-picker"
import { useState } from "react"

interface FamilyMemberFormProps {
  member?: FamilyMember
  onSubmit: (data: FamilyMemberFormData) => void
  isLoading?: boolean
  onCancel?: () => void
}

export function FamilyMemberForm({
  member,
  onSubmit,
  isLoading = false,
  onCancel,
}: FamilyMemberFormProps) {
  const form = useForm<FamilyMemberFormData>({
    resolver: zodResolver(familyMemberFormSchema),
    defaultValues: {
      name: member?.name || "",
      color: member?.color || FAMILY_MEMBER_COLORS[0],
      avatar: member?.avatar || undefined,
    },
  })

  // Avatar handlers temporariamente removidos até implementação no backend
  // const handleAvatarChange = (file: File | undefined) => {
  //   form.setValue("avatar", file)
  // }

  // const handleAvatarError = (error: string) => {
  //   form.setError("avatar", { message: error })
  // }

  const handleFormSubmit = (data: FamilyMemberFormData) => {
    onSubmit(data)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Avatar Upload - Temporariamente oculto até implementação no backend */}
        {false && (
          <FormField
            control={form.control}
            name="avatar"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Foto do Membro</FormLabel>
                <FormControl>
                  <AvatarUpload
                    value={field.value}
                    onChange={handleAvatarChange}
                    onError={handleAvatarError}
                    name={form.watch("name")}
                    color={form.watch("color")}
                    size="lg"
                  />
                </FormControl>
                <FormDescription>
                  Adicione uma foto para identificar este membro (opcional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* Name Field */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input placeholder="Digite o nome do membro" {...field} />
              </FormControl>
              <FormDescription>
                Nome do membro da família (2-50 caracteres)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Color Picker */}
        <FormField
          control={form.control}
          name="color"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cor do Membro</FormLabel>
              <FormControl>
                <ColorPicker
                  value={field.value}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormDescription>
                Escolha uma cor para identificar este membro
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form Actions */}
        <div className="flex justify-end space-x-2 pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
          )}
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {member ? "Atualizar" : "Criar"} Membro
          </Button>
        </div>
      </form>
    </Form>
  )
}
