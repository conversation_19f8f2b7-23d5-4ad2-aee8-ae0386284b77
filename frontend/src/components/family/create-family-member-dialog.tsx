"use client"

import { useState } from "react"
import { Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { FamilyMemberForm } from "./family-member-form"
import { useCreateFamilyMember } from "@/hooks/useFamilyMembers"
import { type FamilyMemberFormData } from "@/lib/validations/family-member"

interface CreateFamilyMemberDialogProps {
  children?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function CreateFamilyMemberDialog({
  children,
  open: controlledOpen,
  onOpenChange
}: CreateFamilyMemberDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false)

  // Use controlled or internal state
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  const createMutation = useCreateFamilyMember()

  const handleSubmit = async (data: FamilyMemberFormData) => {
    try {
      // If avatar is a File, we'll need to upload it first
      let avatarUrl: string | undefined

      if (data.avatar instanceof File) {
        // TODO: Implement avatar upload
        // For now, we'll skip the avatar upload
        console.log("Avatar upload not implemented yet")
      } else if (typeof data.avatar === "string") {
        avatarUrl = data.avatar
      }

      await createMutation.mutateAsync({
        name: data.name,
        color: data.color,
        avatar: avatarUrl,
      })

      setOpen(false)
    } catch (error) {
      console.error("Error creating family member:", error)
    }
  }

  const handleCancel = () => {
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {!controlledOpen && (
        <DialogTrigger asChild>
          {children || (
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Adicionar Membro
            </Button>
          )}
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Adicionar Novo Membro</DialogTitle>
          <DialogDescription>
            Adicione um novo membro à sua família. Você pode personalizar a cor e adicionar uma foto.
          </DialogDescription>
        </DialogHeader>
        <FamilyMemberForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={createMutation.isPending}
        />
      </DialogContent>
    </Dialog>
  )
}
