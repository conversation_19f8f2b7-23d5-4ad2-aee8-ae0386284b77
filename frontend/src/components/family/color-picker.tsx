"use client"

import { useState } from "react"
import { Check, Palette } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { FAMILY_MEMBER_COLORS } from "@/types/family-member.types"
import { cn } from "@/lib/utils"

interface ColorPickerProps {
  value: string
  onChange: (color: string) => void
  className?: string
}

export function ColorPicker({ value, onChange, className }: ColorPickerProps) {
  const [customColor, setCustomColor] = useState(value)
  const [isOpen, setIsOpen] = useState(false)

  const handlePresetColorSelect = (color: string) => {
    onChange(color)
    setCustomColor(color)
    setIsOpen(false)
  }

  const handleCustomColorChange = (color: string) => {
    setCustomColor(color)
  }

  const handleCustomColorApply = () => {
    onChange(customColor)
    setIsOpen(false)
  }

  const isPresetColor = FAMILY_MEMBER_COLORS.includes(value as any)

  return (
    <div className={cn("space-y-3", className)}>
      {/* Preset Colors */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Cores predefinidas</Label>
        <div className="flex flex-wrap gap-2">
          {FAMILY_MEMBER_COLORS.map((color) => (
            <button
              key={color}
              type="button"
              className={cn(
                "relative h-8 w-8 rounded-full border-2 transition-all hover:scale-110",
                value === color
                  ? "border-primary scale-110 shadow-md"
                  : "border-gray-300"
              )}
              style={{ backgroundColor: color }}
              onClick={() => handlePresetColorSelect(color)}
              aria-label={`Selecionar cor ${color}`}
            >
              {value === color && (
                <Check className="absolute inset-0 m-auto h-4 w-4 text-white drop-shadow-sm" />
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Custom Color Picker */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Cor personalizada</Label>
        <div className="flex items-center space-x-2">
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="gap-2 h-8"
              >
                <div
                  className="h-4 w-4 rounded border"
                  style={{ backgroundColor: value }}
                />
                <Palette className="h-4 w-4" />
                Personalizar
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-4" align="start">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="color-input">Escolher cor</Label>
                  <div className="flex items-center space-x-2">
                    <input
                      id="color-input"
                      type="color"
                      value={customColor}
                      onChange={(e) => handleCustomColorChange(e.target.value)}
                      className="h-10 w-16 rounded border border-input bg-background"
                    />
                    <Input
                      value={customColor}
                      onChange={(e) => handleCustomColorChange(e.target.value)}
                      placeholder="#000000"
                      className="flex-1"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>Preview</Label>
                  <div className="flex items-center space-x-2">
                    <div
                      className="h-8 w-8 rounded-full border-2 border-gray-300"
                      style={{ backgroundColor: customColor }}
                    />
                    <span className="text-sm text-muted-foreground">
                      {customColor}
                    </span>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                  >
                    Cancelar
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleCustomColorApply}
                  >
                    Aplicar
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          
          {!isPresetColor && (
            <span className="text-xs text-muted-foreground">
              Cor personalizada
            </span>
          )}
        </div>
      </div>
    </div>
  )
}
