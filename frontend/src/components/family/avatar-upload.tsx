"use client"

import { useState, useRef, useCallback } from "react"
import { Upload, X, Camera, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"

interface AvatarUploadProps {
  value?: File | string
  onChange: (file: File | undefined) => void
  onError?: (error: string) => void
  name?: string
  color?: string
  className?: string
  size?: "sm" | "md" | "lg"
}

const sizeClasses = {
  sm: "h-16 w-16",
  md: "h-24 w-24", 
  lg: "h-32 w-32",
}

export function AvatarUpload({
  value,
  onChange,
  onError,
  name = "",
  color = "#3b82f6",
  className,
  size = "md",
}: AvatarUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [preview, setPreview] = useState<string | null>(
    typeof value === "string" ? value : null
  )
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!file.type.startsWith("image/")) {
      return "Por favor, selecione um arquivo de imagem válido."
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return "O arquivo deve ter no máximo 5MB."
    }

    // Check image dimensions (optional)
    return null
  }

  const handleFileSelect = useCallback((file: File) => {
    const error = validateFile(file)
    if (error) {
      onError?.(error)
      return
    }

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)

    onChange(file)
  }, [onChange, onError])

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(false)

    const files = event.dataTransfer.files
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleRemove = () => {
    setPreview(null)
    onChange(undefined)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const avatarSrc = preview || (typeof value === "string" ? value : undefined)
  const initials = name.charAt(0)?.toUpperCase() || "?"

  return (
    <div className={cn("flex flex-col items-center space-y-4", className)}>
      {/* Avatar with Upload Area */}
      <div className="relative">
        <div
          className={cn(
            "relative cursor-pointer transition-all duration-200",
            isDragOver && "scale-105"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <Avatar className={cn(sizeClasses[size], "border-2 border-dashed border-transparent hover:border-primary/50")}>
            <AvatarImage src={avatarSrc} alt={name} />
            <AvatarFallback
              style={{ backgroundColor: color }}
              className="text-white font-semibold"
            >
              {avatarSrc ? (
                <User className="h-1/2 w-1/2" />
              ) : (
                initials
              )}
            </AvatarFallback>
          </Avatar>

          {/* Upload Overlay */}
          <div
            className={cn(
              "absolute inset-0 flex items-center justify-center rounded-full bg-black/50 opacity-0 transition-opacity hover:opacity-100",
              isDragOver && "opacity-100"
            )}
          >
            <Camera className="h-6 w-6 text-white" />
          </div>

          {/* Remove Button */}
          {avatarSrc && (
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
              onClick={(e) => {
                e.stopPropagation()
                handleRemove()
              }}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Drag Overlay */}
        {isDragOver && (
          <div className="absolute inset-0 flex items-center justify-center rounded-full border-2 border-dashed border-primary bg-primary/10">
            <Upload className="h-8 w-8 text-primary" />
          </div>
        )}
      </div>

      {/* Upload Instructions */}
      <div className="text-center space-y-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleClick}
          className="gap-2"
        >
          <Upload className="h-4 w-4" />
          {avatarSrc ? "Alterar foto" : "Adicionar foto"}
        </Button>
        <p className="text-xs text-muted-foreground">
          Clique ou arraste uma imagem
        </p>
        <p className="text-xs text-muted-foreground">
          PNG, JPG ou GIF até 5MB
        </p>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  )
}
