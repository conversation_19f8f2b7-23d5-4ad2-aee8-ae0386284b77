import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

export function FamilyMembersTableSkeleton() {
  return (
    <div className="card">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Membro</TableHead>
            <TableHead>Cor</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Criado em</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 5 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-full bg-secondary-800 animate-pulse" />
                  <div className="space-y-2">
                    <div className="h-4 w-32 bg-secondary-800 rounded animate-pulse" />
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 rounded-full bg-secondary-800 animate-pulse" />
                  <div className="h-4 w-16 bg-secondary-800 rounded animate-pulse" />
                </div>
              </TableCell>
              <TableCell>
                <div className="h-6 w-16 bg-secondary-800 rounded animate-pulse" />
              </TableCell>
              <TableCell>
                <div className="h-4 w-24 bg-secondary-800 rounded animate-pulse" />
              </TableCell>
              <TableCell>
                <div className="h-8 w-8 bg-secondary-800 rounded animate-pulse" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
