import { useEffect } from 'react'
import { useAuthStore } from '@/stores/auth.store'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface AuthInitializerProps {
  children: React.ReactNode
}

export function AuthInitializer({ children }: AuthInitializerProps) {
  const { isLoading, initializeAuth } = useAuthStore()

  useEffect(() => {
    initializeAuth()
  }, [initializeAuth])

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-secondary-950">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return <>{children}</>
}
