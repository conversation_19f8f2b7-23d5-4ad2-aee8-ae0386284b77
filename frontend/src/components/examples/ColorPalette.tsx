import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface ColorSwatch {
  name: string
  value: string
  description: string
  textClass?: string
}

const primaryColors: ColorSwatch[] = [
  { name: 'primary-50', value: '#f0f4ff', description: 'Ultra claro - backgrounds sutis' },
  { name: 'primary-100', value: '#e0e9ff', description: 'Muito claro - hover states' },
  { name: 'primary-200', value: '#c7d6ff', description: 'Claro - borders suaves' },
  { name: 'primary-300', value: '#a5b8ff', description: 'Médio claro - texto secundário' },
  { name: 'primary-400', value: '#8094ff', description: 'Médio - elementos interativos' },
  { name: 'primary-500', value: '#5d6fff', description: 'Base - cor principal', textClass: 'text-white' },
  { name: 'primary-600', value: '#4c5bff', description: 'Escuro - hover principal', textClass: 'text-white' },
  { name: 'primary-700', value: '#3d47d9', description: 'Muito escuro', textClass: 'text-white' },
  { name: 'primary-800', value: '#2d35a6', description: 'Deep - elementos importantes', textClass: 'text-white' },
  { name: 'primary-900', value: '#1e2573', description: 'Midnight', textClass: 'text-white' },
  { name: 'primary-950', value: '#0f1240', description: 'Ultra deep - máximo contraste', textClass: 'text-white' },
]

const secondaryColors: ColorSwatch[] = [
  { name: 'secondary-50', value: '#f8fafc', description: 'Backgrounds muito claros' },
  { name: 'secondary-100', value: '#f1f5f9', description: 'Backgrounds claros' },
  { name: 'secondary-200', value: '#e2e8f0', description: 'Borders suaves' },
  { name: 'secondary-300', value: '#cbd5e1', description: 'Texto terciário' },
  { name: 'secondary-400', value: '#94a3b8', description: 'Texto secundário' },
  { name: 'secondary-500', value: '#64748b', description: 'Texto padrão', textClass: 'text-white' },
  { name: 'secondary-600', value: '#475569', description: 'Texto importante', textClass: 'text-white' },
  { name: 'secondary-700', value: '#334155', description: 'Texto escuro', textClass: 'text-white' },
  { name: 'secondary-800', value: '#1e293b', description: 'Backgrounds escuros', textClass: 'text-white' },
  { name: 'secondary-900', value: '#0f172a', description: 'Backgrounds muito escuros', textClass: 'text-white' },
  { name: 'secondary-950', value: '#020617', description: 'Máximo contraste', textClass: 'text-white' },
]

const supportColors: ColorSwatch[] = [
  { name: 'success', value: '#10b981', description: 'Emerald - Sucesso', textClass: 'text-white' },
  { name: 'warning', value: '#f59e0b', description: 'Amber - Aviso', textClass: 'text-white' },
  { name: 'info', value: '#3b82f6', description: 'Blue - Informação', textClass: 'text-white' },
  { name: 'destructive', value: '#ef4444', description: 'Red - Erro', textClass: 'text-white' },
]

function ColorSwatchGrid({ colors, title }: { colors: ColorSwatch[]; title: string }) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-gradient-deep">{title}</CardTitle>
        <CardDescription>Escala de cores com descrições de uso</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {colors.map((color) => (
            <div
              key={color.name}
              className="flex flex-col rounded-lg border border-border overflow-hidden shadow-soft"
            >
              <div
                className={`h-16 flex items-center justify-center ${color.textClass || 'text-foreground'}`}
                style={{ backgroundColor: color.value }}
              >
                <span className="font-medium text-sm">{color.name}</span>
              </div>
              <div className="p-3 bg-card">
                <div className="font-mono text-xs text-muted-foreground mb-1">
                  {color.value}
                </div>
                <div className="text-sm text-card-foreground">
                  {color.description}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

export function ColorPalette() {
  return (
    <div className="space-y-8 p-6">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gradient-deep">
          Deep Blue Elegance
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Paleta de cores sofisticada criada para transmitir elegância, confiança e modernidade.
          Baseada em tons de deep blue com excelente contraste e acessibilidade.
        </p>
      </div>

      <div className="space-y-8">
        <ColorSwatchGrid colors={primaryColors} title="Cores Primárias (Deep Blue)" />
        <ColorSwatchGrid colors={secondaryColors} title="Cores Secundárias (Blue-Gray)" />
        <ColorSwatchGrid colors={supportColors} title="Cores de Apoio" />
      </div>

      <Card className="glass-deep">
        <CardHeader>
          <CardTitle className="text-gradient-deep">Demonstração de Componentes</CardTitle>
          <CardDescription>Exemplos de como as cores são aplicadas nos componentes</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Botões */}
          <div className="space-y-3">
            <h3 className="font-semibold text-card-foreground">Botões</h3>
            <div className="flex flex-wrap gap-3">
              <Button>Primário</Button>
              <Button variant="secondary">Secundário</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructivo</Button>
            </div>
          </div>

          {/* Badges */}
          <div className="space-y-3">
            <h3 className="font-semibold text-card-foreground">Badges</h3>
            <div className="flex flex-wrap gap-3">
              <Badge>Padrão</Badge>
              <Badge variant="secondary">Secundário</Badge>
              <Badge variant="destructive">Destructivo</Badge>
              <Badge className="bg-success text-success-foreground">Sucesso</Badge>
              <Badge className="bg-warning text-warning-foreground">Aviso</Badge>
              <Badge className="bg-info text-info-foreground">Info</Badge>
            </div>
          </div>

          {/* Gradientes */}
          <div className="space-y-3">
            <h3 className="font-semibold text-card-foreground">Gradientes e Efeitos</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gradient-deep p-6 rounded-lg text-white text-center">
                <span className="font-semibold">Gradiente Deep</span>
              </div>
              <div className="bg-gradient-subtle p-6 rounded-lg text-center border border-border">
                <span className="font-semibold text-foreground">Gradiente Sutil</span>
              </div>
            </div>
          </div>

          {/* Texto com gradiente */}
          <div className="space-y-3">
            <h3 className="font-semibold text-card-foreground">Texto com Gradiente</h3>
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-gradient">Título com Gradiente Padrão</h2>
              <h2 className="text-2xl font-bold text-gradient-deep">Título com Gradiente Deep</h2>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
