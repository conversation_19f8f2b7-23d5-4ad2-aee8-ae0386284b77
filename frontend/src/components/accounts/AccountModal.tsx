import { 
  Credit<PERSON>ard,
  PiggyBank,
  TrendingUp,
  Wallet,
  Banknote,
  X
} from 'lucide-react'
import { ACCOUNT_TYPES } from '@/lib/constants'
import { AccountForm } from './AccountForm'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

interface Account {
  id: string
  name: string
  type: keyof typeof ACCOUNT_TYPES
  currency: string
  creditLimit?: number
  exchangeRate?: number
  includeInTotal: boolean
  logoPath?: string
  archived: boolean
  createdAt: string
  updatedAt: string
}

interface AccountModalProps {
  isOpen: boolean
  onClose: () => void
  account?: Account
  mode: 'create' | 'edit'
}

const accountTypeIcons = {
  CHECKING: Wallet,
  SAVINGS: PiggyBank,
  CREDIT_CARD: CreditCard,
  INVESTMENT: TrendingUp,
  CASH: Banknote,
  ASSETS: TrendingUp,
}

const accountTypeLabels = {
  CHECKING: 'Conta Corrente',
  SAVINGS: 'Poupan<PERSON>',
  CREDIT_CARD: 'Cartão de Crédito',
  INVESTMENT: 'Investimento',
  CASH: 'Dinheiro',
  ASSETS: 'Ativos',
}

export function AccountModal({ isOpen, onClose, account, mode }: AccountModalProps) {
  const isEditing = mode === 'edit'
  const IconComponent = account ? accountTypeIcons[account.type] : CreditCard

  const handleSuccess = () => {
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="modal-content max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="flex items-center gap-3">
            {account && (
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                <IconComponent className="h-5 w-5 text-primary" />
              </div>
            )}
            <div>
              <DialogTitle className="text-xl font-semibold text-white">
                {isEditing ? 'Editar Conta' : 'Nova Conta'}
              </DialogTitle>
              {account && (
                <p className="text-sm text-secondary-400 mt-1">
                  {accountTypeLabels[account.type]} • {account.currency}
                </p>
              )}
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8 text-secondary-400 hover:text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="space-y-6">
          <AccountForm
            account={account}
            onSuccess={handleSuccess}
            onCancel={onClose}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
