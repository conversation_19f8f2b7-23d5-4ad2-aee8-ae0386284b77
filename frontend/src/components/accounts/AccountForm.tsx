import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import {
  CreditCard,
  PiggyBank,
  TrendingUp,
  Wallet,
  Banknote,
  Eye,
  EyeOff
} from 'lucide-react'
import { accountSchema, type AccountData } from '@/lib/validators'
import { ACCOUNT_TYPES, CURRENCIES } from '@/lib/constants'
import { accountsApi } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface Account {
  id: string
  name: string
  type: keyof typeof ACCOUNT_TYPES
  currency: string
  creditLimit?: number
  exchangeRate?: number
  includeInTotal: boolean
  logoPath?: string
  archived: boolean
  createdAt: string
  updatedAt: string
}

interface AccountFormProps {
  account?: Account
  onSuccess?: () => void
  onCancel?: () => void
}

const accountTypeIcons = {
  CHECKING: Wallet,
  SAVINGS: PiggyBank,
  CREDIT_CARD: CreditCard,
  INVESTMENT: TrendingUp,
  CASH: Banknote,
  ASSETS: TrendingUp,
}

const accountTypeLabels = {
  CHECKING: 'Conta Corrente',
  SAVINGS: 'Poupança',
  CREDIT_CARD: 'Cartão de Crédito',
  INVESTMENT: 'Investimento',
  CASH: 'Dinheiro',
  ASSETS: 'Ativos',
}

export function AccountForm({ account, onSuccess, onCancel }: AccountFormProps) {
  const queryClient = useQueryClient()
  const isEditing = !!account



  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<AccountData>({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      name: account?.name || '',
      type: account?.type || 'CHECKING',
      currency: (account?.currency as any) || 'BRL',
      creditLimit: account?.creditLimit || undefined,
      exchangeRate: account?.exchangeRate || undefined,
      includeInTotal: account?.includeInTotal !== undefined ? account.includeInTotal : true,
      logoPath: account?.logoPath || undefined,
    },
    mode: 'onChange'
  })

  const selectedType = watch('type') as keyof typeof ACCOUNT_TYPES
  const includeInTotal = watch('includeInTotal')

  const createMutation = useMutation({
    mutationFn: accountsApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accounts'] })
      toast.success('Conta criada com sucesso!')
      onSuccess?.()
    },
    onError: (error: any) => {
      console.error('Erro ao criar conta:', error)
      console.error('Response data:', error.response?.data)
      const message = error.response?.data?.message ||
                     error.response?.data?.errors?.[0] ||
                     'Erro ao criar conta. Tente novamente.'
      toast.error(message)
    }
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: AccountData }) =>
      accountsApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accounts'] })
      toast.success('Conta atualizada com sucesso!')
      onSuccess?.()
    },
    onError: (error: any) => {
      console.error('Erro ao atualizar conta:', error)
      const message = error.response?.data?.message ||
                     error.response?.data?.errors?.[0] ||
                     'Erro ao atualizar conta. Tente novamente.'
      toast.error(message)
    }
  })

  const onSubmit = (data: AccountData) => {
    // For now, let's create a temporary solution by making familyMemberIds optional in the backend
    // or create a default family member ID
    const payload = { ...data }

    if (!isEditing) {
      // Use a temporary hardcoded family member ID or make it optional
      // This will be handled by the backend validation error for now
      console.log('Creating account without familyMemberIds - backend should handle this')
    }

    if (isEditing && account) {
      updateMutation.mutate({ id: account.id, data: payload })
    } else {
      createMutation.mutate(payload)
    }
  }

  const isLoading = createMutation.isPending || updateMutation.isPending

  const IconComponent = accountTypeIcons[selectedType] || Wallet

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Nome da Conta */}
      <div className="form-group">
        <Label htmlFor="name" className="form-label">
          Nome da Conta *
        </Label>
        <Input
          id="name"
          {...register('name')}
          placeholder="Ex: Conta Corrente Banco do Brasil"
          className={errors.name ? 'input-error' : ''}
          disabled={isLoading}
        />
        {errors.name && (
          <p className="form-error">{errors.name.message}</p>
        )}
      </div>

      {/* Tipo de Conta */}
      <div className="form-group">
        <Label className="form-label">Tipo de Conta *</Label>
        <Select
          value={selectedType}
          onValueChange={(value) => setValue('type', value as keyof typeof ACCOUNT_TYPES)}
          disabled={isLoading}
        >
          <SelectTrigger className={errors.type ? 'input-error' : ''}>
            <SelectValue>
              <div className="flex items-center gap-2">
                <IconComponent className="h-4 w-4" />
                {accountTypeLabels[selectedType]}
              </div>
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {Object.entries(ACCOUNT_TYPES).map(([key]) => {
              const Icon = accountTypeIcons[key as keyof typeof accountTypeIcons]
              return (
                <SelectItem key={key} value={key}>
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    {accountTypeLabels[key as keyof typeof accountTypeLabels]}
                  </div>
                </SelectItem>
              )
            })}
          </SelectContent>
        </Select>
        {errors.type && (
          <p className="form-error">{errors.type.message}</p>
        )}
      </div>

      {/* Moeda */}
      <div className="form-group">
        <Label className="form-label">Moeda *</Label>
        <Select
          value={watch('currency')}
          onValueChange={(value) => setValue('currency', value as any)}
          disabled={isLoading}
        >
          <SelectTrigger className={errors.currency ? 'input-error' : ''}>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {CURRENCIES.map((currency) => (
              <SelectItem key={currency.code} value={currency.code}>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-mono">{currency.code}</span>
                  <span className="text-sm text-secondary-400">{currency.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.currency && (
          <p className="form-error">{errors.currency.message}</p>
        )}
      </div>

      {/* Limite de Crédito (apenas para cartão de crédito) */}
      {selectedType === 'CREDIT_CARD' && (
        <div className="form-group">
          <Label htmlFor="creditLimit" className="form-label">
            Limite de Crédito
          </Label>
          <Input
            id="creditLimit"
            type="number"
            step="0.01"
            min="0"
            max="*********.99"
            {...register('creditLimit', { valueAsNumber: true })}
            placeholder="0,00"
            className={errors.creditLimit ? 'input-error' : ''}
            disabled={isLoading}
          />
          {errors.creditLimit && (
            <p className="form-error">{errors.creditLimit.message}</p>
          )}
          <p className="form-help">
            Deixe em branco se não houver limite ou se for ilimitado
          </p>
        </div>
      )}

      {/* Taxa de Câmbio (para moedas diferentes de BRL) */}
      {watch('currency') !== 'BRL' && (
        <div className="form-group">
          <Label htmlFor="exchangeRate" className="form-label">
            Taxa de Câmbio Manual
          </Label>
          <Input
            id="exchangeRate"
            type="number"
            step="0.000001"
            min="0"
            max="999999.999999"
            {...register('exchangeRate', { valueAsNumber: true })}
            placeholder="Ex: 5.25"
            className={errors.exchangeRate ? 'input-error' : ''}
            disabled={isLoading}
          />
          {errors.exchangeRate && (
            <p className="form-error">{errors.exchangeRate.message}</p>
          )}
          <p className="form-help">
            Taxa de conversão para BRL. Deixe em branco para usar taxa automática.
          </p>
        </div>
      )}

      {/* Incluir no Total */}
      <div className="form-group">
        <div className="flex items-center gap-3">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setValue('includeInTotal', !includeInTotal)}
            className="flex items-center gap-2 p-2"
            disabled={isLoading}
          >
            {includeInTotal ? (
              <Eye className="h-4 w-4 text-success-400" />
            ) : (
              <EyeOff className="h-4 w-4 text-secondary-500" />
            )}
          </Button>
          <div>
            <Label className="form-label">
              {includeInTotal ? 'Incluída no total geral' : 'Oculta do total geral'}
            </Label>
            <p className="form-help">
              {includeInTotal 
                ? 'Esta conta será incluída no cálculo do patrimônio total'
                : 'Esta conta não será incluída no cálculo do patrimônio total'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Botões de Ação */}
      <div className="flex gap-3 pt-4">
        <Button
          type="submit"
          className="btn-primary flex-1"
          disabled={isLoading}
        >
          {isLoading && <LoadingSpinner size="sm" className="mr-2" />}
          {isEditing ? 'Atualizar Conta' : 'Criar Conta'}
        </Button>
        
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
          className="btn-outline"
        >
          Cancelar
        </Button>
      </div>
    </form>
  )
}
