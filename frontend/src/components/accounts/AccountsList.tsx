import { useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  CreditCard,
  PiggyBank,
  TrendingUp,
  Wallet,
  Banknote
} from 'lucide-react'
import { accountsApi } from '@/lib/api'
import { formatCurrency } from '@/lib/utils'
import { ACCOUNT_TYPES } from '@/lib/constants'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { AccountsFilters, type AccountFilters } from './AccountsFilters'
import { AccountsTableSkeleton } from './AccountsTableSkeleton'

interface Account {
  id: string
  name: string
  type: keyof typeof ACCOUNT_TYPES
  currency: string
  currentBalance?: number
  availableBalance?: number
  creditLimit?: number
  includeInTotal: boolean
  archived: boolean
  createdAt: string
  updatedAt: string
}

interface AccountsListProps {
  onEdit?: (account: Account) => void
  onDelete?: (account: Account) => void
  onToggleVisibility?: (account: Account) => void
}

const accountTypeIcons = {
  CHECKING: Wallet,
  SAVINGS: PiggyBank,
  CREDIT_CARD: CreditCard,
  INVESTMENT: TrendingUp,
  CASH: Banknote,
  ASSETS: TrendingUp,
}

const accountTypeLabels = {
  CHECKING: 'Conta Corrente',
  SAVINGS: 'Poupança',
  CREDIT_CARD: 'Cartão de Crédito',
  INVESTMENT: 'Investimento',
  CASH: 'Dinheiro',
  ASSETS: 'Ativos',
}

export function AccountsList({ onEdit, onDelete, onToggleVisibility }: AccountsListProps) {
  const [filters, setFilters] = useState<AccountFilters>({
    sortBy: 'name',
    sortOrder: 'asc'
  })

  const { data: accounts, isLoading, error } = useQuery({
    queryKey: ['accounts'],
    queryFn: accountsApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })

  const handleFiltersChange = (newFilters: AccountFilters) => {
    setFilters(newFilters)
  }

  const handleClearFilters = () => {
    setFilters({
      sortBy: 'name',
      sortOrder: 'asc'
    })
  }

  const handleSort = (field: 'name' | 'balance' | 'type' | 'createdAt') => {
    setFilters(prev => ({
      ...prev,
      sortBy: field,
      sortOrder: prev.sortBy === field && prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }))
  }

  const filteredAndSortedAccounts = useMemo(() => {
    // Now accounts should be the direct data from handleApiResponse
    const accountsData = accounts?.data || []

    if (!Array.isArray(accountsData) || accountsData.length === 0) return []

    let filtered = accountsData.filter((account: Account) => {
      // Filtro de busca
      if (filters.search) {
        const searchLower = filters.search.toLowerCase()
        if (!account.name.toLowerCase().includes(searchLower)) {
          return false
        }
      }

      // Filtro de tipo
      if (filters.type && filters.type !== 'ALL') {
        if (account.type !== filters.type) return false
      }

      // Filtro de moeda
      if (filters.currency && filters.currency !== 'ALL') {
        if (account.currency !== filters.currency) return false
      }

      // Filtro de incluir no total
      if (filters.includeInTotal !== undefined && filters.includeInTotal !== 'ALL') {
        if (account.includeInTotal !== filters.includeInTotal) return false
      }

      // Filtro de arquivada
      if (filters.archived !== undefined && filters.archived !== 'ALL') {
        if (account.archived !== filters.archived) return false
      }

      return true
    })

    // Ordenação
    filtered.sort((a: Account, b: Account) => {
      let aValue: any
      let bValue: any

      switch (filters.sortBy) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'balance':
          aValue = a.currentBalance || 0
          bValue = b.currentBalance || 0
          break
        case 'type':
          aValue = accountTypeLabels[a.type]
          bValue = accountTypeLabels[b.type]
          break
        case 'createdAt':
          aValue = new Date(a.createdAt)
          bValue = new Date(b.createdAt)
          break
        default:
          return 0
      }

      if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [accounts?.data, filters])

  if (isLoading) {
    return (
      <div className="space-y-6">
        <AccountsFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onClearFilters={handleClearFilters}
        />
        <AccountsTableSkeleton />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <AccountsFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onClearFilters={handleClearFilters}
        />
        <div className="glass-deep p-8 rounded-2xl shadow-elegant">
          <div className="py-16 text-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-destructive shadow-glow mx-auto mb-6">
              <CreditCard className="h-8 w-8 text-white" />
            </div>
            <h3 className="mb-4 text-2xl font-bold text-gradient">
              Erro ao carregar contas
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Não foi possível carregar suas contas. Verifique sua conexão e tente novamente.
            </p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="border-primary text-primary hover:bg-primary hover:text-white"
            >
              Tentar Novamente
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (!filteredAndSortedAccounts || filteredAndSortedAccounts.length === 0) {
    return (
      <div className="space-y-6">
        <AccountsFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onClearFilters={handleClearFilters}
        />
        <div className="glass-deep p-8 rounded-2xl shadow-elegant">
          <div className="py-16 text-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-deep shadow-glow mx-auto mb-6">
              <PiggyBank className="h-8 w-8 text-white" />
            </div>
            <h3 className="mb-4 text-2xl font-bold text-gradient">
              Nenhuma conta encontrada
            </h3>
            <p className="text-muted-foreground max-w-md mx-auto">
              {Object.values(filters).some(v => v !== undefined && v !== 'ALL' && v !== '')
                ? 'Nenhuma conta corresponde aos filtros aplicados.'
                : 'Comece criando sua primeira conta financeira.'
              }
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <AccountsFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClearFilters={handleClearFilters}
      />

      <div className="glass-deep rounded-2xl shadow-elegant overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="border-border/50 hover:bg-accent/30">
              <TableHead
                className="cursor-pointer hover:text-primary transition-colors font-semibold"
                onClick={() => handleSort('name')}
              >
                Nome {filters.sortBy === 'name' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
              </TableHead>
              <TableHead
                className="cursor-pointer hover:text-primary transition-colors font-semibold"
                onClick={() => handleSort('type')}
              >
                Tipo {filters.sortBy === 'type' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
              </TableHead>
              <TableHead className="font-semibold">Moeda</TableHead>
              <TableHead
                className="cursor-pointer hover:text-primary transition-colors text-right font-semibold"
                onClick={() => handleSort('balance')}
              >
                Saldo Atual {filters.sortBy === 'balance' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
              </TableHead>
              <TableHead className="text-right font-semibold">Saldo Disponível</TableHead>
              <TableHead className="text-center font-semibold">Status</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedAccounts.map((account: Account) => {
            const IconComponent = accountTypeIcons[account.type] || Wallet
            const isCredit = account.type === 'CREDIT_CARD'
            const balance = account.currentBalance || 0
            const availableBalance = account.availableBalance || balance
            
            return (
              <TableRow key={account.id} className={`border-border/30 hover:bg-accent/20 transition-colors ${account.archived ? 'opacity-50' : ''}`}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-soft">
                      <IconComponent className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <div className="font-semibold text-foreground">{account.name}</div>
                      {account.archived && (
                        <div className="text-xs text-muted-foreground">Arquivada</div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="text-muted-foreground font-medium">
                    {accountTypeLabels[account.type]}
                  </span>
                </TableCell>
                <TableCell>
                  <span className="text-muted-foreground font-medium">{account.currency}</span>
                </TableCell>
                <TableCell className="text-right">
                  <span className={`font-bold ${
                    isCredit
                      ? balance < 0 ? 'text-destructive' : 'text-muted-foreground'
                      : balance >= 0 ? 'text-success' : 'text-destructive'
                  }`}>
                    {formatCurrency(balance, account.currency)}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  {isCredit && account.creditLimit ? (
                    <div className="text-right">
                      <div className={`font-bold ${
                        availableBalance >= 0 ? 'text-success' : 'text-destructive'
                      }`}>
                        {formatCurrency(availableBalance, account.currency)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Limite: {formatCurrency(account.creditLimit, account.currency)}
                      </div>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">—</span>
                  )}
                </TableCell>
                <TableCell className="text-center">
                  <div className="flex items-center justify-center">
                    {account.includeInTotal ? (
                      <Eye className="h-5 w-5 text-success" />
                    ) : (
                      <EyeOff className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onEdit?.(account)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Editar
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onToggleVisibility?.(account)}>
                        {account.includeInTotal ? (
                          <>
                            <EyeOff className="mr-2 h-4 w-4" />
                            Ocultar do Total
                          </>
                        ) : (
                          <>
                            <Eye className="mr-2 h-4 w-4" />
                            Incluir no Total
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => onDelete?.(account)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Excluir
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
    </div>
  )
}
