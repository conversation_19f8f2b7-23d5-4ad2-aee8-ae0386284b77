import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

export function AccountsTableSkeleton() {
  return (
    <div className="card">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Nome</TableHead>
            <TableHead>Tipo</TableHead>
            <TableHead>Moeda</TableHead>
            <TableHead className="text-right">Saldo Atual</TableHead>
            <TableHead className="text-right">Saldo Disponível</TableHead>
            <TableHead className="text-center">Status</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 5 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-full bg-secondary-800 animate-pulse" />
                  <div className="space-y-2">
                    <div className="h-4 w-32 bg-secondary-800 rounded animate-pulse" />
                    <div className="h-3 w-20 bg-secondary-800 rounded animate-pulse" />
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="h-4 w-24 bg-secondary-800 rounded animate-pulse" />
              </TableCell>
              <TableCell>
                <div className="h-4 w-12 bg-secondary-800 rounded animate-pulse" />
              </TableCell>
              <TableCell className="text-right">
                <div className="h-4 w-20 bg-secondary-800 rounded animate-pulse ml-auto" />
              </TableCell>
              <TableCell className="text-right">
                <div className="h-4 w-20 bg-secondary-800 rounded animate-pulse ml-auto" />
              </TableCell>
              <TableCell className="text-center">
                <div className="h-4 w-4 bg-secondary-800 rounded animate-pulse mx-auto" />
              </TableCell>
              <TableCell>
                <div className="h-8 w-8 bg-secondary-800 rounded animate-pulse" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
