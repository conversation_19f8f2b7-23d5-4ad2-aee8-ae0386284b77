import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { futureTransactionsApi } from '@/lib/api'
import { formatCurrency, formatDate, formatRelativeTime } from '@/lib/utils'
import {
  Calendar,
  Edit,
  Trash2,
  AlertTriangle,
  Clock,
  TrendingUp,
  TrendingDown,
  ArrowRightLeft,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'
import toast from 'react-hot-toast'

interface Transaction {
  id: string
  description: string
  amount: number
  transactionDate: string
  type: 'INCOME' | 'EXPENSE' | 'TRANSFER'
  account: {
    id: string
    name: string
    currency: string
  }
  destinationAccount?: {
    id: string
    name: string
    currency: string
  }
  category?: {
    id: string
    name: string
  }
  isFuture: boolean
  createdAt: string
}

interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

interface FutureTransactionsListProps {
  transactions: Transaction[]
  pagination?: Pagination
  onPageChange: (_page: number) => void
  onRefresh: () => void
}

export function FutureTransactionsList({
  transactions,
  pagination,
  onPageChange,
  onRefresh,
}: FutureTransactionsListProps) {
  const [selectedTransaction, setSelectedTransaction] = useState<string | null>(
    null
  )
  const queryClient = useQueryClient()

  const cancelMutation = useMutation({
    mutationFn: futureTransactionsApi.cancel,
    onSuccess: () => {
      toast.success('Transação futura cancelada com sucesso!')
      queryClient.invalidateQueries({ queryKey: ['future-transactions'] })
      onRefresh()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao cancelar transação')
    },
  })

  const handleCancelTransaction = (id: string) => {
    if (
      // eslint-disable-next-line no-alert
      window.confirm('Tem certeza que deseja cancelar esta transação futura?')
    ) {
      cancelMutation.mutate(id)
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'INCOME':
        return <TrendingUp className="h-4 w-4 text-success-400" />
      case 'EXPENSE':
        return <TrendingDown className="h-4 w-4 text-error-400" />
      case 'TRANSFER':
        return <ArrowRightLeft className="h-4 w-4 text-primary-400" />
      default:
        return <Calendar className="h-4 w-4 text-secondary-400" />
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'INCOME':
        return 'text-success-400'
      case 'EXPENSE':
        return 'text-error-400'
      case 'TRANSFER':
        return 'text-primary-400'
      default:
        return 'text-white'
    }
  }

  const isOverdue = (transactionDate: string) => {
    return new Date(transactionDate) < new Date()
  }

  const isDueToday = (transactionDate: string) => {
    const today = new Date()
    const transDate = new Date(transactionDate)
    return (
      transDate.getDate() === today.getDate() &&
      transDate.getMonth() === today.getMonth() &&
      transDate.getFullYear() === today.getFullYear()
    )
  }

  if (transactions.length === 0) {
    return (
      <div className="card">
        <div className="py-12 text-center">
          <Calendar className="mx-auto mb-4 h-12 w-12 text-secondary-600" />
          <h3 className="mb-2 text-lg font-medium text-white">
            Nenhuma transação futura encontrada
          </h3>
          <p className="text-secondary-400">
            Crie sua primeira transação futura para começar a planejar suas
            finanças.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Transactions List */}
      <div className="card">
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div
              key={transaction.id}
              className={`rounded-lg border p-4 transition-colors ${
                selectedTransaction === transaction.id
                  ? 'border-primary-500 bg-primary-900/20'
                  : 'border-secondary-700 bg-secondary-800 hover:bg-secondary-700'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Transaction Icon */}
                  <div className="flex-shrink-0">
                    {getTransactionIcon(transaction.type)}
                  </div>

                  {/* Transaction Details */}
                  <div className="min-w-0 flex-1">
                    <div className="mb-1 flex items-center gap-2">
                      <h4 className="truncate text-sm font-medium text-white">
                        {transaction.description}
                      </h4>

                      {/* Status Badges */}
                      {isOverdue(transaction.transactionDate) && (
                        <span className="inline-flex items-center gap-1 rounded-full bg-error-900 px-2 py-1 text-xs text-error-300">
                          <AlertTriangle className="h-3 w-3" />
                          Em atraso
                        </span>
                      )}

                      {isDueToday(transaction.transactionDate) && (
                        <span className="inline-flex items-center gap-1 rounded-full bg-warning-900 px-2 py-1 text-xs text-warning-300">
                          <Clock className="h-3 w-3" />
                          Vence hoje
                        </span>
                      )}
                    </div>

                    <div className="flex items-center gap-4 text-sm text-secondary-400">
                      <span>{transaction.account.name}</span>
                      {transaction.destinationAccount && (
                        <>
                          <ArrowRightLeft className="h-3 w-3" />
                          <span>{transaction.destinationAccount.name}</span>
                        </>
                      )}
                      {transaction.category && (
                        <span className="badge badge-secondary">
                          {transaction.category.name}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Amount and Date */}
                <div className="text-right">
                  <p
                    className={`text-lg font-semibold ${getTransactionColor(transaction.type)}`}
                  >
                    {transaction.type === 'INCOME'
                      ? '+'
                      : transaction.type === 'EXPENSE'
                        ? '-'
                        : ''}
                    {formatCurrency(
                      transaction.amount,
                      transaction.account.currency
                    )}
                  </p>
                  <p className="text-sm text-secondary-400">
                    {formatDate(transaction.transactionDate)}
                  </p>
                  <p className="text-xs text-secondary-500">
                    {formatRelativeTime(transaction.transactionDate)}
                  </p>
                </div>

                {/* Actions */}
                <div className="ml-4 flex items-center gap-2">
                  <button
                    onClick={() =>
                      setSelectedTransaction(
                        selectedTransaction === transaction.id
                          ? null
                          : transaction.id
                      )
                    }
                    className="btn-ghost btn-sm"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleCancelTransaction(transaction.id)}
                    disabled={cancelMutation.isPending}
                    className="btn-ghost btn-sm text-error-400 hover:text-error-300"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Expanded Details */}
              {selectedTransaction === transaction.id && (
                <div className="mt-4 border-t border-secondary-600 pt-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-secondary-400">Criado em:</span>
                      <span className="ml-2 text-white">
                        {formatDate(transaction.createdAt)}
                      </span>
                    </div>
                    <div>
                      <span className="text-secondary-400">Tipo:</span>
                      <span className="ml-2 text-white">
                        {transaction.type === 'INCOME'
                          ? 'Receita'
                          : transaction.type === 'EXPENSE'
                            ? 'Despesa'
                            : 'Transferência'}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-secondary-400">
            Mostrando {(pagination.page - 1) * pagination.limit + 1} a{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} de{' '}
            {pagination.total} transações
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="btn-outline btn-sm"
            >
              <ChevronLeft className="h-4 w-4" />
              Anterior
            </button>

            <span className="text-sm text-secondary-300">
              Página {pagination.page} de {pagination.totalPages}
            </span>

            <button
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
              className="btn-outline btn-sm"
            >
              Próxima
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
