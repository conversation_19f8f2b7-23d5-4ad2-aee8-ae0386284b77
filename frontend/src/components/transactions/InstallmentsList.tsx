import React, { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Checkbox } from '../ui/checkbox'
import { 
  Calendar, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  CreditCard,
  TrendingUp,
  MoreHorizontal
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { useUpdateInstallmentStatus } from '@/hooks/useTransactions'
import { 
  Transaction, 
  Installment, 
  getInstallmentStatus, 
  calculateTransactionProgress 
} from '@/types/transaction.types'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'

interface InstallmentsListProps {
  transaction: Transaction
  showActions?: boolean
  compact?: boolean
}

export const InstallmentsList: React.FC<InstallmentsListProps> = ({
  transaction,
  showActions = true,
  compact = false
}) => {
  const [selectedInstallments, setSelectedInstallments] = useState<Set<number>>(new Set())
  
  const updateInstallmentMutation = useUpdateInstallmentStatus()
  // Note: Bulk update removed - can be implemented later

  const progress = calculateTransactionProgress(transaction)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const getStatusColor = (installment: Installment) => {
    const status = getInstallmentStatus(installment)
    switch (status) {
      case 'paid':
        return 'bg-green-500 text-white'
      case 'overdue':
        return 'bg-red-500 text-white'
      case 'pending':
        return 'bg-yellow-500 text-white'
      default:
        return 'bg-gray-500 text-white'
    }
  }

  const getStatusIcon = (installment: Installment) => {
    const status = getInstallmentStatus(installment)
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />
      case 'overdue':
        return <AlertTriangle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusLabel = (installment: Installment) => {
    const status = getInstallmentStatus(installment)
    switch (status) {
      case 'paid':
        return 'Paga'
      case 'overdue':
        return 'Vencida'
      case 'pending':
        return 'Pendente'
      default:
        return 'Pendente'
    }
  }

  const handleInstallmentToggle = async (installmentNumber: number, isPaid: boolean) => {
    try {
      await updateInstallmentMutation.mutateAsync({
        transactionId: transaction.id,
        installmentNumber,
        isPaid
      })
    } catch (error) {
      console.error('Error updating installment:', error)
    }
  }

  const handleSelectInstallment = (installmentNumber: number, checked: boolean) => {
    const newSelected = new Set(selectedInstallments)
    if (checked) {
      newSelected.add(installmentNumber)
    } else {
      newSelected.delete(installmentNumber)
    }
    setSelectedInstallments(newSelected)
  }

  const handleBulkMarkAsPaid = async () => {
    // Temporarily disabled - bulk operations can be implemented later
    console.log('Bulk mark as paid - feature not implemented yet')
    setSelectedInstallments(new Set())
  }

  const handleBulkMarkAsUnpaid = async () => {
    // Temporarily disabled - bulk operations can be implemented later
    console.log('Bulk mark as unpaid - feature not implemented yet')
    setSelectedInstallments(new Set())
  }

  const selectAll = () => {
    setSelectedInstallments(new Set(transaction.installments.map(i => i.installmentNumber)))
  }

  const clearSelection = () => {
    setSelectedInstallments(new Set())
  }

  if (compact) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">
            {transaction.totalInstallments > 1
              ? `${progress.paidInstallments}/${transaction.totalInstallments} parcelas pagas`
              : progress.paidInstallments > 0 ? 'Pagamento realizado' : 'Pagamento pendente'
            }
          </span>
          <span className="text-sm text-muted-foreground">
            {formatCurrency(progress.paidAmount)} / {formatCurrency(transaction.totalAmount)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-green-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress.percentage}%` }}
          />
        </div>
      </div>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Parcelas da Transação
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <TrendingUp className="h-4 w-4" />
            {progress.percentage.toFixed(1)}% concluído
          </div>
        </CardTitle>
        
        {/* Progress Summary */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>
              {transaction.totalInstallments > 1
                ? `Progresso: ${progress.paidInstallments}/${transaction.totalInstallments}`
                : progress.paidInstallments > 0 ? 'Status: Pago' : 'Status: Pendente'
              }
            </span>
            <span>{formatCurrency(progress.paidAmount)} / {formatCurrency(transaction.totalAmount)}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress.percentage}%` }}
            />
          </div>
        </div>

        {/* Bulk Actions */}
        {showActions && selectedInstallments.size > 0 && (
          <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200">
            <span className="text-sm text-blue-700 dark:text-blue-300">
              {selectedInstallments.size} parcela(s) selecionada(s)
            </span>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleBulkMarkAsUnpaid}
                disabled={false}
              >
                Marcar como Não Paga
              </Button>
              <Button
                size="sm"
                onClick={handleBulkMarkAsPaid}
                disabled={false}
                className="bg-green-600 hover:bg-green-700"
              >
                Marcar como Paga
              </Button>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {/* Select All Controls */}
        {showActions && (
          <div className="flex items-center justify-between mb-4 pb-2 border-b">
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selectedInstallments.size === transaction.installments.length}
                onCheckedChange={(checked) => checked ? selectAll() : clearSelection()}
              />
              <span className="text-sm">Selecionar todas</span>
            </div>
            {selectedInstallments.size > 0 && (
              <Button
                size="sm"
                variant="ghost"
                onClick={clearSelection}
              >
                Limpar seleção
              </Button>
            )}
          </div>
        )}

        {/* Installments List */}
        <div className="space-y-3">
          {transaction.installments.map((installment) => (
            <div
              key={installment.id}
              className="flex items-center justify-between p-4 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center gap-3">
                {showActions && (
                  <Checkbox
                    checked={selectedInstallments.has(installment.installmentNumber)}
                    onCheckedChange={(checked) => 
                      handleSelectInstallment(installment.installmentNumber, checked as boolean)
                    }
                  />
                )}
                
                <div className="flex items-center gap-2">
                  {getStatusIcon(installment)}
                  <span className="font-medium">
                    {transaction.totalInstallments > 1
                      ? `Parcela ${installment.installmentNumber}/${transaction.totalInstallments}`
                      : 'Pagamento à vista'
                    }
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="text-right">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{formatDate(installment.dueDate)}</span>
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <span className="font-semibold">{formatCurrency(installment.amount)}</span>
                  </div>
                </div>

                <Badge className={getStatusColor(installment)}>
                  {getStatusLabel(installment)}
                </Badge>

                {showActions && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => handleInstallmentToggle(
                          installment.installmentNumber, 
                          !installment.isPaid
                        )}
                        disabled={updateInstallmentMutation.isPending}
                      >
                        {installment.isPaid ? 'Marcar como Não Paga' : 'Marcar como Paga'}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Valor Pago:</span>
              <span className="ml-2 font-semibold text-green-600">
                {formatCurrency(progress.paidAmount)}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Valor Restante:</span>
              <span className="ml-2 font-semibold text-orange-600">
                {formatCurrency(progress.remainingAmount)}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
