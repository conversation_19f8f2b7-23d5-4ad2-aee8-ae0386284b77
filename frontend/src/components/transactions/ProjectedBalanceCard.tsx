import { TrendingUp, TrendingDown, DollarSign, Calendar } from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

interface ProjectedBalanceCardProps {
  projectedBalance?: {
    currentBalance: number
    currentBalanceInBRL: number
    projectedBalance: number
    projectedBalanceInBRL: number
    projectionDate: string
    includeRecurring: boolean
    balanceByType: {
      CHECKING: number
      SAVINGS: number
      INVESTMENT: number
      CREDIT_CARD: number
    }
    balanceByCurrency: Record<string, number>
  }
}

export function ProjectedBalanceCard({
  projectedBalance,
}: ProjectedBalanceCardProps) {
  if (!projectedBalance) {
    return (
      <div className="card animate-pulse">
        <div className="mb-4 h-6 rounded bg-secondary-700" />
        <div className="space-y-3">
          <div className="h-8 rounded bg-secondary-700" />
          <div className="h-4 w-3/4 rounded bg-secondary-700" />
          <div className="h-4 w-1/2 rounded bg-secondary-700" />
        </div>
      </div>
    )
  }

  const balanceChange =
    projectedBalance.projectedBalanceInBRL -
    projectedBalance.currentBalanceInBRL
  const isPositiveChange = balanceChange >= 0
  const changePercentage =
    projectedBalance.currentBalanceInBRL !== 0
      ? (balanceChange / Math.abs(projectedBalance.currentBalanceInBRL)) * 100
      : 0

  return (
    <div className="card">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="card-title flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Saldo Projetado
        </h3>
        <div className="flex items-center gap-1 text-xs text-secondary-400">
          <Calendar className="h-3 w-3" />
          {projectedBalance.projectionDate !== 'indefinite'
            ? formatDate(projectedBalance.projectionDate)
            : 'Indefinido'}
        </div>
      </div>

      <div className="space-y-4">
        {/* Current Balance */}
        <div>
          <p className="mb-1 text-sm text-secondary-400">Saldo Atual</p>
          <p className="text-xl font-semibold text-white">
            {formatCurrency(projectedBalance.currentBalanceInBRL)}
          </p>
        </div>

        {/* Projected Balance */}
        <div>
          <p className="mb-1 text-sm text-secondary-400">Saldo Projetado</p>
          <p className="text-2xl font-bold text-white">
            {formatCurrency(projectedBalance.projectedBalanceInBRL)}
          </p>
        </div>

        {/* Balance Change */}
        <div className="flex items-center justify-between rounded-lg bg-secondary-700 p-3">
          <div className="flex items-center gap-2">
            {isPositiveChange ? (
              <TrendingUp className="h-4 w-4 text-success-400" />
            ) : (
              <TrendingDown className="h-4 w-4 text-error-400" />
            )}
            <span className="text-sm text-secondary-300">Variação</span>
          </div>
          <div className="text-right">
            <p
              className={`font-medium ${isPositiveChange ? 'text-success-400' : 'text-error-400'}`}
            >
              {isPositiveChange ? '+' : ''}
              {formatCurrency(balanceChange)}
            </p>
            <p
              className={`text-xs ${isPositiveChange ? 'text-success-400' : 'text-error-400'}`}
            >
              {isPositiveChange ? '+' : ''}
              {changePercentage.toFixed(1)}%
            </p>
          </div>
        </div>

        {/* Balance by Account Type */}
        {projectedBalance.balanceByType && (
          <div>
            <p className="mb-2 text-sm text-secondary-400">
              Saldo por Tipo de Conta
            </p>
            <div className="space-y-2">
              {Object.entries(projectedBalance.balanceByType).map(
                ([type, balance]) => {
                const typeLabels: Record<string, string> = {
                  CHECKING: 'Conta Corrente',
                  SAVINGS: 'Poupança',
                  INVESTMENT: 'Investimentos',
                  CREDIT_CARD: 'Cartão de Crédito',
                }

                const typeColors: Record<string, string> = {
                  CHECKING: 'text-primary-400',
                  SAVINGS: 'text-success-400',
                  INVESTMENT: 'text-warning-400',
                  CREDIT_CARD: 'text-error-400',
                }

                if (balance === 0) {
                  return null
                }

                return (
                  <div key={type} className="flex items-center justify-between">
                    <span className="text-sm text-secondary-300">
                      {typeLabels[type] || type}
                    </span>
                    <span
                      className={`text-sm font-medium ${typeColors[type] || 'text-white'}`}
                    >
                      {formatCurrency(balance)}
                    </span>
                  </div>
                )
                }
              )}
            </div>
          </div>
        )}

        {/* Multi-currency balances */}
        {projectedBalance.balanceByCurrency && Object.keys(projectedBalance.balanceByCurrency).length > 1 && (
          <div>
            <p className="mb-2 text-sm text-secondary-400">Saldo por Moeda</p>
            <div className="space-y-2">
              {Object.entries(projectedBalance.balanceByCurrency).map(
                ([currency, balance]) => {
                  if (balance === 0) {
                    return null
                  }

                  return (
                    <div
                      key={currency}
                      className="flex items-center justify-between"
                    >
                      <span className="text-sm text-secondary-300">
                        {currency}
                      </span>
                      <span className="text-sm font-medium text-white">
                        {formatCurrency(balance, currency)}
                      </span>
                    </div>
                  )
                }
              )}
            </div>
          </div>
        )}

        {/* Info about recurring transactions */}
        {projectedBalance.includeRecurring && (
          <div className="rounded bg-secondary-800 p-2 text-xs text-secondary-400">
            * Inclui transações recorrentes na projeção
          </div>
        )}
      </div>
    </div>
  )
}
