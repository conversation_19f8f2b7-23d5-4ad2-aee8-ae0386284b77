import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useMutation, useQuery } from '@tanstack/react-query'
import { z } from 'zod'
import { X, Calendar, DollarSign, ArrowRightLeft } from 'lucide-react'
import { transactionsApi, accountsApi } from '@/lib/api'
import { useCategoriesForForms } from '@/hooks/useCategories'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import toast from 'react-hot-toast'

const futureTransactionSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória'),
  amount: z.number().positive('Valor deve ser positivo'),
  transactionDate: z.string().min(1, 'Data é obrigatória'),
  type: z.enum(['INCOME', 'EXPENSE', 'TRANSFER']),
  accountId: z.string().min(1, 'Conta é obrigatória'),
  categoryId: z.string().optional(),
  destinationAccountId: z.string().optional(),
  exchangeRate: z.number().optional(),
  sourceCurrency: z.string().optional(),
  destinationCurrency: z.string().optional(),
  sourceAmount: z.number().optional(),
  destinationAmount: z.number().optional(),
})

type FutureTransactionFormData = z.infer<typeof futureTransactionSchema>

interface FutureTransactionFormProps {
  onClose: () => void
  onSuccess: () => void
  transaction?: any // For editing existing transactions
}

export function FutureTransactionForm({
  onClose,
  onSuccess,
  transaction,
}: FutureTransactionFormProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [selectedType, setSelectedType] = useState<
    'INCOME' | 'EXPENSE' | 'TRANSFER'
  >('EXPENSE')

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FutureTransactionFormData>({
    defaultValues: {
      type: 'EXPENSE',
      transactionDate: new Date().toISOString().split('T')[0],
      ...transaction,
    },
  })

  const watchedType = watch('type')
  const watchedAccountId = watch('accountId')

  // Fetch accounts
  const { data: accounts } = useQuery({
    queryKey: ['accounts'],
    queryFn: accountsApi.getAll,
  })

  // Fetch categories
  const { data: categories } = useCategoriesForForms()

  // Create/Update mutation
  const createMutation = useMutation({
    mutationFn: (data: any) => {
      if (transaction) {
        return transactionsApi.update(transaction.id, data)
      }
      return transactionsApi.create({ ...data, isFuture: true })
    },
    onSuccess: () => {
      toast.success(
        transaction
          ? 'Transação futura atualizada com sucesso!'
          : 'Transação futura criada com sucesso!'
      )
      onSuccess()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao salvar transação')
    },
  })

  useEffect(() => {
    setSelectedType(watchedType)
  }, [watchedType])

  const onSubmit = (data: FutureTransactionFormData) => {
    // Validate future date
    const transactionDate = new Date(data.transactionDate)
    const now = new Date()

    if (transactionDate <= now) {
      toast.error('A data da transação deve ser no futuro')
      return
    }

    // Validate transfer requirements
    if (data.type === 'TRANSFER' && !data.destinationAccountId) {
      toast.error('Conta de destino é obrigatória para transferências')
      return
    }

    if (
      data.type === 'TRANSFER' &&
      data.accountId === data.destinationAccountId
    ) {
      toast.error('Conta de origem e destino devem ser diferentes')
      return
    }

    createMutation.mutate(data)
  }

  const selectedAccount = accounts?.data?.find(
    (acc: any) => acc.id === watchedAccountId
  )
  const availableDestinationAccounts = accounts?.data?.filter(
    (acc: any) => acc.id !== watchedAccountId
  )

  return (
    <div className="modal-overlay">
      <div className="modal-content max-w-2xl">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="flex items-center gap-2 text-xl font-semibold text-white">
            <Calendar className="h-5 w-5" />
            {transaction ? 'Editar' : 'Nova'} Transação Futura
          </h2>
          <button
            onClick={onClose}
            className="text-secondary-400 hover:text-white"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Transaction Type */}
          <div className="form-group">
            <label className="form-label">Tipo de Transação</label>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: 'INCOME', label: 'Receita', icon: '💰' },
                { value: 'EXPENSE', label: 'Despesa', icon: '💸' },
                { value: 'TRANSFER', label: 'Transferência', icon: '🔄' },
              ].map((type) => (
                <button
                  key={type.value}
                  type="button"
                  onClick={() => setValue('type', type.value as any)}
                  className={`rounded-lg border p-3 text-center transition-colors ${
                    selectedType === type.value
                      ? 'border-primary-500 bg-primary-900/20 text-primary-300'
                      : 'border-secondary-600 bg-secondary-800 text-secondary-300 hover:border-secondary-500'
                  }`}
                >
                  <div className="mb-1 text-lg">{type.icon}</div>
                  <div className="text-sm font-medium">{type.label}</div>
                </button>
              ))}
            </div>
            <input type="hidden" {...register('type')} />
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="form-group">
              <label className="form-label">Descrição</label>
              <input
                {...register('description')}
                className={`input ${errors.description ? 'input-error' : ''}`}
                placeholder="Ex: Salário, Aluguel, Compras..."
              />
              {errors.description && (
                <p className="form-error">{errors.description.message}</p>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">Data da Transação</label>
              <input
                {...register('transactionDate')}
                type="date"
                min={new Date().toISOString().split('T')[0]}
                className={`input ${errors.transactionDate ? 'input-error' : ''}`}
              />
              {errors.transactionDate && (
                <p className="form-error">{errors.transactionDate.message}</p>
              )}
            </div>
          </div>

          {/* Amount and Account */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="form-group">
              <label className="form-label">Valor</label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-secondary-400" />
                <input
                  {...register('amount', { valueAsNumber: true })}
                  type="number"
                  step="0.01"
                  min="0"
                  className={`input pl-10 ${errors.amount ? 'input-error' : ''}`}
                  placeholder="0,00"
                />
              </div>
              {errors.amount && (
                <p className="form-error">{errors.amount.message}</p>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">
                {selectedType === 'TRANSFER' ? 'Conta de Origem' : 'Conta'}
              </label>
              <select
                {...register('accountId')}
                className={`input ${errors.accountId ? 'input-error' : ''}`}
              >
                <option value="">Selecione uma conta</option>
                {accounts?.data?.map((account: any) => (
                  <option key={account.id} value={account.id}>
                    {account.name} ({account.currency})
                  </option>
                ))}
              </select>
              {errors.accountId && (
                <p className="form-error">{errors.accountId.message}</p>
              )}
            </div>
          </div>

          {/* Transfer Destination */}
          {selectedType === 'TRANSFER' && (
            <div className="form-group">
              <label className="form-label flex items-center gap-2">
                <ArrowRightLeft className="h-4 w-4" />
                Conta de Destino
              </label>
              <select
                {...register('destinationAccountId')}
                className={`input ${errors.destinationAccountId ? 'input-error' : ''}`}
              >
                <option value="">Selecione a conta de destino</option>
                {availableDestinationAccounts?.map((account: any) => (
                  <option key={account.id} value={account.id}>
                    {account.name} ({account.currency})
                  </option>
                ))}
              </select>
              {errors.destinationAccountId && (
                <p className="form-error">
                  {errors.destinationAccountId.message}
                </p>
              )}
            </div>
          )}

          {/* Category */}
          {selectedType !== 'TRANSFER' && (
            <div className="form-group">
              <label className="form-label">Categoria (Opcional)</label>
              <select {...register('categoryId')} className="input">
                <option value="">Selecione uma categoria</option>
                {categories?.map((category: any) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Advanced Options Toggle */}
          <div>
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm text-primary-400 hover:text-primary-300"
            >
              {showAdvanced ? 'Ocultar' : 'Mostrar'} opções avançadas
            </button>
          </div>

          {/* Advanced Options */}
          {showAdvanced && (
            <div className="space-y-4 rounded-lg bg-secondary-800 p-4">
              <h4 className="text-sm font-medium text-white">
                Opções Avançadas
              </h4>

              {/* Multi-currency for transfers */}
              {selectedType === 'TRANSFER' && selectedAccount && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="form-group">
                    <label className="form-label">Taxa de Câmbio</label>
                    <input
                      {...register('exchangeRate', { valueAsNumber: true })}
                      type="number"
                      step="0.0001"
                      min="0"
                      className="input"
                      placeholder="1.0000"
                    />
                    <p className="form-help">
                      Deixe vazio para usar taxa atual
                    </p>
                  </div>

                  <div className="form-group">
                    <label className="form-label">Valor de Destino</label>
                    <input
                      {...register('destinationAmount', {
                        valueAsNumber: true,
                      })}
                      type="number"
                      step="0.01"
                      min="0"
                      className="input"
                      placeholder="Calculado automaticamente"
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end gap-3 border-t border-secondary-700 pt-4">
            <button type="button" onClick={onClose} className="btn-outline">
              Cancelar
            </button>
            <button
              type="submit"
              disabled={createMutation.isPending}
              className="btn-primary flex items-center gap-2"
            >
              {createMutation.isPending && <LoadingSpinner size="sm" />}
              {transaction ? 'Atualizar' : 'Criar'} Transação
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
