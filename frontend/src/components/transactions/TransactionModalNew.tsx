import { useQuery } from '@tanstack/react-query'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { CreditCard } from 'lucide-react'
import { accountsApi, tagsApi } from '@/lib/api'
import { useCreateTransaction, useUpdateTransaction } from '@/hooks/useTransactions'
import { useCategoriesForForms } from '@/hooks/useCategories'
import { useFamilyMembers } from '@/hooks/useFamilyMembers'
import { TransactionForm } from './TransactionForm'
import type { Transaction, CreateTransactionData, UpdateTransactionData } from '@/types/transaction.types'

interface TransactionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  transaction?: Transaction | null
  mode: 'create' | 'edit'
}

export function TransactionModal({
  open,
  onOpenChange,
  transaction,
  mode
}: TransactionModalProps) {
  const createMutation = useCreateTransaction()
  const updateMutation = useUpdateTransaction()

  // Fetch data for form options
  const { data: accounts, isLoading: isLoadingAccounts } = useQuery({
    queryKey: ['accounts'],
    queryFn: () => accountsApi.getAll(),
  })

  const { data: categories, isLoading: isLoadingCategories } = useCategoriesForForms()

  const { data: familyMembersData, isLoading: isLoadingFamilyMembers } = useFamilyMembers()

  const { data: tags, isLoading: isLoadingTags } = useQuery({
    queryKey: ['tags'],
    queryFn: () => tagsApi.getAll(),
  })

  const isLoading = isLoadingAccounts || isLoadingCategories || isLoadingFamilyMembers || isLoadingTags

  // Extract data from API responses with better handling
  const extractData = (response: any) => {
    if (!response) return []

    // If it's already an array, return it
    if (Array.isArray(response)) return response

    // If it has a data property that's an array, return that
    if (response.data && Array.isArray(response.data)) return response.data

    // If it has nested data.data structure
    if (response.data?.data && Array.isArray(response.data.data)) return response.data.data

    // Default to empty array
    return []
  }

  const accountsList = extractData(accounts)
  const categoriesList = categories || [] // Categories hook already returns clean array
  const familyMembersList = familyMembersData?.data || [] // Family members from paginated response
  const tagsList = extractData(tags)

  // Debug logs
  console.log('Modal data extraction:', {
    accounts,
    categories,
    accountsList,
    categoriesList
  })

  const handleSubmit = async (data: CreateTransactionData | UpdateTransactionData) => {
    try {
      if (mode === 'create') {
        await createMutation.mutateAsync(data as CreateTransactionData)
      } else if (transaction) {
        await updateMutation.mutateAsync({
          id: transaction.id,
          data: data as UpdateTransactionData
        })
      }
      onOpenChange(false)
    } catch (error) {
      console.error('Error submitting transaction:', error)
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  const isSubmitting = createMutation.isPending || updateMutation.isPending

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-center p-8">
            <LoadingSpinner />
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {mode === 'create' ? 'Nova Transação' : 'Editar Transação'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? 'Preencha os dados para criar uma nova transação com suporte a parcelas'
              : 'Edite os dados da transação'
            }
          </DialogDescription>
        </DialogHeader>

        <TransactionForm
          initialData={transaction}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={isSubmitting}
          accounts={accountsList}
          categories={categoriesList}
          familyMembers={familyMembersList}
          tags={tagsList}
        />
      </DialogContent>
    </Dialog>
  )
}
