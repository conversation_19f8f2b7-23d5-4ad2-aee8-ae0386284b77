import { Calendar, Clock, AlertTriangle, CheckCircle } from 'lucide-react'

interface FutureTransactionStatsProps {
  stats?: {
    totalPending: number
    dueToday: number
    dueThisWeek: number
    dueThisMonth: number
    overdue: number
  }
}

export function FutureTransactionStats({ stats }: FutureTransactionStatsProps) {
  if (!stats) {
    return (
      <div className="col-span-2 grid grid-cols-2 gap-4 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="card animate-pulse">
            <div className="mb-2 h-4 rounded bg-secondary-700" />
            <div className="h-8 rounded bg-secondary-700" />
          </div>
        ))}
      </div>
    )
  }

  const statItems = [
    {
      label: 'Total Pendente',
      value: stats.totalPending,
      icon: Calendar,
      color: 'bg-primary-600',
      textColor: 'text-primary-400',
    },
    {
      label: 'V<PERSON> Hoje',
      value: stats.dueToday,
      icon: Clock,
      color: 'bg-warning-600',
      textColor: 'text-warning-400',
    },
    {
      label: 'Esta Semana',
      value: stats.dueThisWeek,
      icon: CheckCircle,
      color: 'bg-success-600',
      textColor: 'text-success-400',
    },
    {
      label: 'Em Atraso',
      value: stats.overdue,
      icon: AlertTriangle,
      color: 'bg-error-600',
      textColor: 'text-error-400',
    },
  ]

  return (
    <div className="col-span-2 grid grid-cols-2 gap-4 lg:grid-cols-4">
      {statItems.map((item) => (
        <div key={item.label} className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-lg ${item.color}`}
              >
                <item.icon className="h-5 w-5 text-white" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-secondary-400">
                {item.label}
              </p>
              <p className={`text-2xl font-semibold ${item.textColor}`}>
                {item.value}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
