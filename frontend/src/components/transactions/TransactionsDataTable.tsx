"use client"

import { useState } from 'react'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
} from "@tanstack/react-table"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Copy,
  Eye,
  TrendingUp,
  TrendingDown,
  ArrowRightLeft,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Check,
  Clock,
  AlertCircle
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  getInstallmentStatus,
  getInstallmentStatusColor,
  getInstallmentStatusLabel
} from '@/lib/transaction-utils'
import { TRANSACTION_TYPE_COLORS } from '@/types/transaction.types'
import type { Transaction, ExpandedTransactionRow } from '@/types/transaction.types'

interface TransactionsDataTableProps {
  data: ExpandedTransactionRow[]
  isLoading?: boolean
  onEdit?: (transactionId: string) => void
  onDelete?: (transactionId: string) => void
  onDuplicate?: (transactionId: string) => void
  onView?: (transactionId: string) => void
  onToggleInstallmentStatus?: (transactionId: string, installmentNumber: number, isPaid: boolean) => void
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  onPageChange?: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void
}

export function TransactionsDataTable({
  data,
  isLoading = false,
  onEdit,
  onDelete,
  onDuplicate,
  onView,
  onToggleInstallmentStatus,
  pagination,
  onPageChange,
  onPageSizeChange
}: TransactionsDataTableProps) {
  const [sorting, setSorting] = useState<SortingState>([])

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case 'INCOME':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'EXPENSE':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'TRANSFER':
        return <ArrowRightLeft className="h-4 w-4 text-blue-600" />
      default:
        return null
    }
  }

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'INCOME':
        return 'Receita'
      case 'EXPENSE':
        return 'Despesa'
      case 'TRANSFER':
        return 'Transferência'
      default:
        return type
    }
  }

  const columns: ColumnDef<ExpandedTransactionRow>[] = [
    {
      accessorKey: "installmentDueDate",
      header: "Data Vencimento",
      cell: ({ row }) => {
        const date = new Date(row.original.installmentDueDate)
        return (
          <div className="font-medium">
            {formatDate(date)}
          </div>
        )
      },
    },
    {
      accessorKey: "description",
      header: "Descrição",
      cell: ({ row }) => {
        const rowData = row.original

        return (
          <div className="max-w-[250px]">
            <div className="font-medium truncate">{rowData.description}</div>
            {/* Only show installment badge for actual installments (more than 1) */}
            {rowData.totalInstallments > 1 && (
              <div className="mt-1">
                <Badge
                  variant="outline"
                  className="text-xs px-1.5 py-0.5"
                >
                  {rowData.installmentNumber}/{rowData.totalInstallments}
                </Badge>
              </div>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: "type",
      header: "Tipo",
      cell: ({ row }) => {
        const type = row.getValue("type") as string
        return (
          <Badge variant="outline" className="flex items-center gap-1 w-fit">
            {getTransactionTypeIcon(type)}
            {getTransactionTypeLabel(type)}
          </Badge>
        )
      },
    },
    {
      accessorKey: "installmentAmount",
      header: "Valor Parcela",
      cell: ({ row }) => {
        const amount = row.original.installmentAmount
        const type = row.original.type
        const colorClass = TRANSACTION_TYPE_COLORS[type as keyof typeof TRANSACTION_TYPE_COLORS]

        return (
          <div>
            <div className={`font-semibold ${colorClass}`}>
              {formatCurrency(amount, row.original.account.currency)}
            </div>
            <div className="text-xs text-muted-foreground">
              de {formatCurrency(row.original.totalAmount, row.original.account.currency)}
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: "account",
      header: "Conta",
      cell: ({ row }) => {
        const account = row.original.account
        return (
          <div>
            <div className="font-medium">{account.name}</div>
            <div className="text-sm text-muted-foreground">{account.type}</div>
          </div>
        )
      },
    },
    {
      accessorKey: "category",
      header: "Categoria",
      cell: ({ row }) => {
        const category = row.original.category
        if (!category) {
          return <span className="text-muted-foreground">-</span>
        }
        return (
          <div className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: category.color }}
            />
            <span className="font-medium">{category.name}</span>
          </div>
        )
      },
    },
    {
      accessorKey: "familyMembers",
      header: "Membros",
      cell: ({ row }) => {
        const members = row.original.familyMembers
        if (!members || members.length === 0) {
          return <span className="text-muted-foreground">-</span>
        }

        if (members.length === 1) {
          return (
            <div className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: members[0].color }}
              />
              <span className="text-sm">{members[0].name}</span>
            </div>
          )
        }

        return (
          <div className="flex items-center gap-1">
            {members.slice(0, 2).map((member, index) => (
              <div
                key={member.id}
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: member.color }}
                title={member.name}
              />
            ))}
            {members.length > 2 && (
              <span className="text-xs text-muted-foreground ml-1">
                +{members.length - 2}
              </span>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: "tags",
      header: "Tags",
      cell: ({ row }) => {
        const tags = row.original.tags
        if (!tags || tags.length === 0) {
          return <span className="text-muted-foreground">-</span>
        }
        
        return (
          <div className="flex flex-wrap gap-1">
            {tags.slice(0, 2).map((tag) => (
              <Badge 
                key={tag.id} 
                variant="secondary" 
                className="text-xs"
                style={{ backgroundColor: `${tag.color}20`, color: tag.color }}
              >
                {tag.name}
              </Badge>
            ))}
            {tags.length > 2 && (
              <Badge variant="secondary" className="text-xs">
                +{tags.length - 2}
              </Badge>
            )}
          </div>
        )
      },
    },
    {
      id: "status",
      header: "Status",
      cell: ({ row }) => {
        const rowData = row.original
        const status = getInstallmentStatus({
          isPaid: rowData.installmentIsPaid,
          dueDate: rowData.installmentDueDate
        })

        const getStatusIcon = (status: string) => {
          switch (status) {
            case 'paid':
              return <Check className="h-4 w-4" />
            case 'overdue':
              return <AlertCircle className="h-4 w-4" />
            default:
              return <Clock className="h-4 w-4" />
          }
        }

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className={`h-8 px-2 ${getInstallmentStatusColor(status)}`}
              onClick={() => onToggleInstallmentStatus?.(
                rowData.transactionId,
                rowData.installmentNumber,
                !rowData.installmentIsPaid
              )}
            >
              {getStatusIcon(status)}
              <span className="ml-1 text-xs">
                {getInstallmentStatusLabel(status)}
              </span>
            </Button>
          </div>
        )
      },
    },
    {
      id: "actions",
      header: "Ações",
      cell: ({ row }) => {
        const rowData = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Abrir menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Ações</DropdownMenuLabel>
              {onView && (
                <DropdownMenuItem onClick={() => onView(rowData.transactionId)}>
                  <Eye className="mr-2 h-4 w-4" />
                  Ver Transação
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(rowData.transactionId)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Editar Transação
                </DropdownMenuItem>
              )}
              {onDuplicate && (
                <DropdownMenuItem onClick={() => onDuplicate(rowData.transactionId)}>
                  <Copy className="mr-2 h-4 w-4" />
                  Duplicar Transação
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onToggleInstallmentStatus && (
                <DropdownMenuItem
                  onClick={() => onToggleInstallmentStatus(
                    rowData.transactionId,
                    rowData.installmentNumber,
                    !rowData.installmentIsPaid
                  )}
                >
                  {rowData.installmentIsPaid ? (
                    <>
                      <Clock className="mr-2 h-4 w-4" />
                      Marcar como Não Pago
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Marcar como Pago
                    </>
                  )}
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onDelete && (
                <DropdownMenuItem
                  onClick={() => onDelete(rowData.transactionId)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Deletar Transação
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
    manualPagination: true,
    pageCount: pagination?.totalPages || 0,
  })

  if (isLoading) {
    return (
      <div className="rounded-md border glass-deep">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                {columns.map((_, cellIndex) => (
                  <TableCell key={cellIndex}>
                    <div className="h-4 bg-muted animate-pulse rounded" />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border glass-deep">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="hover:bg-muted/50"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center py-8">
                    <div className="text-muted-foreground mb-2">Nenhuma transação encontrada</div>
                    <div className="text-sm text-muted-foreground">
                      Tente ajustar os filtros ou criar uma nova transação
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Mostrando {((pagination.page - 1) * pagination.limit) + 1} a{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} de{' '}
            {pagination.total} transações
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(1)}
              disabled={pagination.page === 1}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex items-center space-x-1">
              <span className="text-sm">Página</span>
              <span className="text-sm font-medium">{pagination.page}</span>
              <span className="text-sm">de</span>
              <span className="text-sm font-medium">{pagination.totalPages}</span>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(pagination.totalPages)}
              disabled={pagination.page === pagination.totalPages}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
