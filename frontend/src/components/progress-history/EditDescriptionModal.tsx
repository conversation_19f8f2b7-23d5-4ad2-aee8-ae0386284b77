import { useState, useEffect } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Edit } from 'lucide-react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form'
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import { useUpdateProgressHistoryDescription } from '../../hooks/useProgressHistory'
import type { ProgressHistoryEntry } from '../../types/progress-history.types'

const editDescriptionSchema = z.object({
  description: z.string()
    .min(1, 'Descrição é obrigatória')
    .max(500, 'Descrição muito longa (máximo 500 caracteres)')
})

type EditDescriptionFormData = z.infer<typeof editDescriptionSchema>

interface EditDescriptionModalProps {
  entry: ProgressHistoryEntry
  isOpen: boolean
  onClose: () => void
}

export function EditDescriptionModal({ entry, isOpen, onClose }: EditDescriptionModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const updateDescriptionMutation = useUpdateProgressHistoryDescription()

  const form = useForm<EditDescriptionFormData>({
    resolver: zodResolver(editDescriptionSchema),
    defaultValues: {
      description: entry.description || ''
    }
  })

  // Reset form when entry changes
  useEffect(() => {
    form.reset({
      description: entry.description || ''
    })
  }, [entry, form])

  const handleClose = () => {
    if (!isLoading) {
      form.reset()
      onClose()
    }
  }

  const handleSubmit = async (data: EditDescriptionFormData) => {
    try {
      setIsLoading(true)
      
      await updateDescriptionMutation.mutateAsync({
        id: entry.id,
        data: {
          description: data.description
        }
      })
      
      handleClose()
    } catch (error) {
      console.error('Erro ao atualizar descrição:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg font-bold text-gradient-deep">
            <div className="flex h-6 w-6 items-center justify-center rounded-lg bg-gradient-deep shadow-soft">
              <Edit className="h-3 w-3 text-white" />
            </div>
            Editar Descrição
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Entry Info */}
            <div className="p-3 bg-muted/30 rounded-lg text-sm">
              <div className="font-medium">{entry.goalName}</div>
              <div className="text-muted-foreground">
                {new Date(entry.createdAt).toLocaleDateString('pt-BR', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            </div>

            {/* Description Field */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2 text-sm font-medium">
                    <Edit className="h-4 w-4" />
                    Descrição
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Digite uma descrição para esta atualização..."
                      disabled={isLoading}
                      maxLength={500}
                    />
                  </FormControl>
                  <FormMessage />
                  <div className="text-xs text-muted-foreground text-right">
                    {field.value?.length || 0}/500 caracteres
                  </div>
                </FormItem>
              )}
            />

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-gradient-deep hover:bg-gradient-deep/90"
              >
                {isLoading ? 'Salvando...' : 'Salvar'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
