import { useState } from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  RotateCcw, 
  Calendar, 
  DollarSign, 
  Edit, 
  Trash2,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Skeleton } from '../ui/skeleton'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select'
import { cn } from '../../lib/utils'
import { formatCurrency, formatDate, formatDateTime } from '../../lib/utils'
import { useProgressHistory, useDeleteProgressHistory } from '../../hooks/useProgressHistory'
import type { ProgressHistoryFilters, ProgressHistoryEntry } from '../../types/progress-history.types'
import { EditDescriptionModal } from './EditDescriptionModal'

interface ProgressHistoryListProps {
  goalId?: string
  className?: string
}

export function ProgressHistoryList({ goalId, className }: ProgressHistoryListProps) {
  const [filters, setFilters] = useState<ProgressHistoryFilters>({
    goalId,
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })
  const [searchQuery, setSearchQuery] = useState('')
  const [editingEntry, setEditingEntry] = useState<ProgressHistoryEntry | null>(null)

  const { data: historyData, isLoading, error } = useProgressHistory(filters)
  const deleteHistoryMutation = useDeleteProgressHistory()

  const handleFilterChange = (key: keyof ProgressHistoryFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }))
  }

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }))
  }

  const handleDeleteEntry = async (id: string) => {
    if (window.confirm('Tem certeza que deseja deletar esta entrada do histórico?')) {
      await deleteHistoryMutation.mutateAsync(id)
    }
  }

  const getOperationIcon = (operation: string) => {
    switch (operation) {
      case 'add':
        return <TrendingUp className="h-4 w-4 text-success" />
      case 'subtract':
        return <TrendingDown className="h-4 w-4 text-destructive" />
      case 'set':
        return <RotateCcw className="h-4 w-4 text-warning" />
      default:
        return <DollarSign className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getOperationBadge = (operation: string) => {
    const variants = {
      add: 'default',
      subtract: 'destructive',
      set: 'secondary'
    } as const

    const labels = {
      add: 'Adição',
      subtract: 'Subtração',
      set: 'Definição'
    }

    return (
      <Badge variant={variants[operation as keyof typeof variants] || 'outline'}>
        {labels[operation as keyof typeof labels] || operation}
      </Badge>
    )
  }

  const getAmountChangeDisplay = (entry: ProgressHistoryEntry) => {
    const { operation, amountChanged } = entry
    const isPositive = operation === 'add' || (operation === 'set' && amountChanged > 0)
    
    return (
      <span className={cn(
        "font-medium",
        isPositive ? "text-success" : "text-destructive"
      )}>
        {operation === 'set' ? '' : (isPositive ? '+' : '')}
        {formatCurrency(amountChanged)}
      </span>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-destructive">
            Erro ao carregar histórico de progresso
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Histórico de Progresso
            {historyData?.pagination && (
              <Badge variant="outline">
                {historyData.pagination.total} entradas
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por meta ou descrição..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Select
                value={filters.operation || 'all'}
                onValueChange={(value) => handleFilterChange('operation', value === 'all' ? undefined : value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Operação" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  <SelectItem value="add">Adição</SelectItem>
                  <SelectItem value="subtract">Subtração</SelectItem>
                  <SelectItem value="set">Definição</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={`${filters.sortBy}-${filters.sortOrder}`}
                onValueChange={(value) => {
                  const [sortBy, sortOrder] = value.split('-')
                  handleFilterChange('sortBy', sortBy)
                  handleFilterChange('sortOrder', sortOrder)
                }}
              >
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Ordenar" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt-desc">Mais recente</SelectItem>
                  <SelectItem value="createdAt-asc">Mais antigo</SelectItem>
                  <SelectItem value="amountChanged-desc">Maior valor</SelectItem>
                  <SelectItem value="amountChanged-asc">Menor valor</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-1/3" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-20" />
                </div>
              ))}
            </div>
          )}

          {/* History Entries */}
          {historyData?.data && historyData.data.length > 0 && (
            <div className="space-y-3">
              {historyData.data
                .filter(entry => 
                  searchQuery === '' || 
                  entry.goalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  (entry.description && entry.description.toLowerCase().includes(searchQuery.toLowerCase()))
                )
                .map((entry) => (
                  <div
                    key={entry.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                  >
                    <div className="flex items-center gap-4 flex-1 min-w-0">
                      <div className="flex-shrink-0">
                        {getOperationIcon(entry.operation)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-sm truncate">{entry.goalName}</h4>
                          {getOperationBadge(entry.operation)}
                        </div>
                        
                        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDateTime(entry.createdAt)}</span>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <span>
                              {formatCurrency(entry.previousAmount)} → {formatCurrency(entry.newAmount)}
                            </span>
                          </div>
                        </div>
                        
                        {entry.description && (
                          <p className="text-xs text-muted-foreground mt-1 truncate">
                            {entry.description}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 flex-shrink-0">
                      <div className="text-right">
                        {getAmountChangeDisplay(entry)}
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingEntry(entry)}
                          className="h-7 w-7 p-0"
                          title="Editar descrição"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteEntry(entry.id)}
                          disabled={deleteHistoryMutation.isPending}
                          className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                          title="Deletar entrada"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}

          {/* Empty State */}
          {historyData?.data && historyData.data.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Nenhum histórico de progresso encontrado</p>
            </div>
          )}

          {/* Pagination */}
          {historyData?.pagination && historyData.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between pt-4">
              <div className="text-sm text-muted-foreground">
                Página {historyData.pagination.page} de {historyData.pagination.totalPages}
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(historyData.pagination.page - 1)}
                  disabled={historyData.pagination.page <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Anterior
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(historyData.pagination.page + 1)}
                  disabled={historyData.pagination.page >= historyData.pagination.totalPages}
                >
                  Próxima
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Description Modal */}
      {editingEntry && (
        <EditDescriptionModal
          entry={editingEntry}
          isOpen={!!editingEntry}
          onClose={() => setEditingEntry(null)}
        />
      )}
    </div>
  )
}
