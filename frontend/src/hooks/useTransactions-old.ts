import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { transactionsApi } from '@/lib/api/transactions.api'
import type {
  TransactionFilters,
  CreateTransactionData,
  UpdateTransactionData
} from '@/types/transaction.types'

// Query keys
export const transactionKeys = {
  all: ['transactions'] as const,
  lists: () => [...transactionKeys.all, 'list'] as const,
  list: (filters: TransactionFilters) => [...transactionKeys.lists(), filters] as const,
  details: () => [...transactionKeys.all, 'detail'] as const,
  detail: (id: string) => [...transactionKeys.details(), id] as const,
  stats: () => [...transactionKeys.all, 'stats'] as const,
  statsFiltered: (filters: Partial<TransactionFilters>) => [...transactionKeys.stats(), filters] as const,
  recent: (limit: number) => [...transactionKeys.all, 'recent', limit] as const,
  byAccount: (accountId: string) => [...transactionKeys.all, 'byAccount', accountId] as const,
  byCategory: (categoryId: string) => [...transactionKeys.all, 'byCategory', categoryId] as const,
  byFamilyMember: (familyMemberId: string) => [...transactionKeys.all, 'byFamilyMember', familyMemberId] as const,
  future: () => [...transactionKeys.all, 'future'] as const,
  search: (query: string) => [...transactionKeys.all, 'search', query] as const,
}

/**
 * Hook to fetch all transactions with filters
 */
export function useTransactions(filters: TransactionFilters = {}) {
  return useQuery({
    queryKey: transactionKeys.list(filters),
    queryFn: () => transactionsApi.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch transaction by ID
 */
export function useTransaction(id: string, includeDeleted = false) {
  return useQuery({
    queryKey: transactionKeys.detail(id),
    queryFn: () => transactionsApi.getById(id, includeDeleted),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch transaction statistics
 */
export function useTransactionStats(filters: Partial<TransactionFilters> = {}) {
  return useQuery({
    queryKey: transactionKeys.statsFiltered(filters),
    queryFn: () => transactionsApi.getStats(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch recent transactions
 */
export function useRecentTransactions(limit = 10) {
  return useQuery({
    queryKey: transactionKeys.recent(limit),
    queryFn: () => transactionsApi.getRecent(limit),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to fetch transactions by account
 */
export function useTransactionsByAccount(accountId: string, filters: Omit<TransactionFilters, 'accountId'> = {}) {
  return useQuery({
    queryKey: [...transactionKeys.byAccount(accountId), filters],
    queryFn: () => transactionsApi.getByAccount(accountId, filters),
    enabled: !!accountId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch transactions by category
 */
export function useTransactionsByCategory(categoryId: string, filters: Omit<TransactionFilters, 'categoryId'> = {}) {
  return useQuery({
    queryKey: [...transactionKeys.byCategory(categoryId), filters],
    queryFn: () => transactionsApi.getByCategory(categoryId, filters),
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch future transactions
 */
export function useFutureTransactions(filters: Omit<TransactionFilters, 'isFuture'> = {}) {
  return useQuery({
    queryKey: [...transactionKeys.future(), filters],
    queryFn: () => transactionsApi.getFuture(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to search transactions
 */
export function useSearchTransactions(query: string, filters: Omit<TransactionFilters, 'description'> = {}) {
  return useQuery({
    queryKey: [...transactionKeys.search(query), filters],
    queryFn: () => transactionsApi.search(query, filters),
    enabled: query.length >= 2, // Only search if query has at least 2 characters
    staleTime: 30 * 1000, // 30 seconds for search results
  })
}

/**
 * Hook to create a new transaction
 */
export function useCreateTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateTransactionData) => transactionsApi.create(data),
    onSuccess: (newTransaction) => {
      // Invalidate and refetch transactions list
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.recent(10) })
      
      // Add to cache
      queryClient.setQueryData(
        transactionKeys.detail(newTransaction.id),
        newTransaction
      )

      toast.success('Transação criada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao criar transação'
      toast.error(message)
    },
  })
}

/**
 * Hook to update a transaction
 */
export function useUpdateTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTransactionData }) =>
      transactionsApi.update(id, data),
    onSuccess: (updatedTransaction) => {
      // Update cache
      queryClient.setQueryData(
        transactionKeys.detail(updatedTransaction.id),
        updatedTransaction
      )

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })

      toast.success('Transação atualizada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao atualizar transação'
      toast.error(message)
    },
  })
}

/**
 * Hook to update installments of a transaction
 */
export function useUpdateInstallments() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ transactionId, data }: {
      transactionId: string;
      data: {
        installments: Array<{
          amount: number;
          transactionDate: string;
          description?: string;
        }>;
        totalAmount: number;
      }
    }) => transactionsApi.updateInstallments(transactionId, data),
    onSuccess: (result, variables) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.recent(10) })
      queryClient.invalidateQueries({ queryKey: ['installments', variables.transactionId] })

      // Update parent transaction in cache if it exists
      if (result.parentTransaction?.id) {
        queryClient.setQueryData(
          transactionKeys.detail(result.parentTransaction.id),
          result.parentTransaction
        )
      }

      toast.success('Parcelas atualizadas com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao atualizar parcelas'
      toast.error(message)
    },
  })
}

/**
 * Hook to delete a transaction
 */
export function useDeleteTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => transactionsApi.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: transactionKeys.detail(deletedId) })

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })

      toast.success('Transação deletada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao deletar transação'
      toast.error(message)
    },
  })
}

/**
 * Hook to duplicate a transaction
 */
export function useDuplicateTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => transactionsApi.duplicate(id),
    onSuccess: (duplicatedTransaction) => {
      // Add to cache
      queryClient.setQueryData(
        transactionKeys.detail(duplicatedTransaction.id),
        duplicatedTransaction
      )

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })

      toast.success('Transação duplicada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao duplicar transação'
      toast.error(message)
    },
  })
}
