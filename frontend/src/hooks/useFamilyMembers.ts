import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { familyMemberService } from '@/services/family-member.service'
import type {
  CreateFamilyMemberRequest,
  UpdateFamilyMemberRequest,
  FamilyMemberFilters,
} from '@/types/family-member.types'

// Query keys
export const familyMemberKeys = {
  all: ['family-members'] as const,
  lists: () => [...familyMemberKeys.all, 'list'] as const,
  list: (filters: FamilyMemberFilters) => [...familyMemberKeys.lists(), filters] as const,
  details: () => [...familyMemberKeys.all, 'detail'] as const,
  detail: (id: string) => [...familyMemberKeys.details(), id] as const,
  stats: () => [...familyMemberKeys.all, 'stats'] as const,
}

/**
 * Hook to fetch all family members with filters
 */
export function useFamilyMembers(filters: FamilyMemberFilters = {}) {
  return useQuery({
    queryKey: familyMemberKeys.list(filters),
    queryFn: () => familyMemberService.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch a single family member by ID
 */
export function useFamilyMember(id: string) {
  return useQuery({
    queryKey: familyMemberKeys.detail(id),
    queryFn: () => familyMemberService.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch family members statistics
 */
export function useFamilyMemberStats() {
  return useQuery({
    queryKey: familyMemberKeys.stats(),
    queryFn: () => familyMemberService.getStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to create a new family member
 */
export function useCreateFamilyMember() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateFamilyMemberRequest) => familyMemberService.create(data),
    onSuccess: (newMember) => {
      // Invalidate and refetch family members lists
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.lists() })
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.stats() })
      
      // Add the new member to the cache
      queryClient.setQueryData(familyMemberKeys.detail(newMember.id), newMember)
      
      toast.success('Membro da família criado com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao criar membro da família')
    },
  })
}

/**
 * Hook to update an existing family member
 */
export function useUpdateFamilyMember() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateFamilyMemberRequest }) =>
      familyMemberService.update(id, data),
    onSuccess: (updatedMember) => {
      // Update the member in the cache
      queryClient.setQueryData(familyMemberKeys.detail(updatedMember.id), updatedMember)
      
      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.lists() })
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.stats() })
      
      toast.success('Membro da família atualizado com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao atualizar membro da família')
    },
  })
}

/**
 * Hook to archive a family member
 */
export function useArchiveFamilyMember() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => familyMemberService.archive(id),
    onSuccess: (_, id) => {
      // Invalidate queries to reflect the archived state
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.lists() })
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.stats() })
      
      toast.success('Membro da família arquivado com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao arquivar membro da família')
    },
  })
}

/**
 * Hook to restore an archived family member
 */
export function useRestoreFamilyMember() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => familyMemberService.restore(id),
    onSuccess: (restoredMember) => {
      // Update the member in the cache
      queryClient.setQueryData(familyMemberKeys.detail(restoredMember.id), restoredMember)
      
      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.lists() })
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.stats() })
      
      toast.success('Membro da família restaurado com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao restaurar membro da família')
    },
  })
}

/**
 * Hook to permanently delete a family member
 */
export function useDeleteFamilyMember() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => familyMemberService.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: familyMemberKeys.detail(id) })
      
      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.lists() })
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.stats() })
      
      toast.success('Membro da família excluído permanentemente!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao excluir membro da família')
    },
  })
}

/**
 * Hook to upload avatar for family member
 */
export function useUploadFamilyMemberAvatar() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, file }: { id: string; file: File }) =>
      familyMemberService.uploadAvatar(id, file),
    onSuccess: (_, { id }) => {
      // Invalidate the member detail to refetch with new avatar
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.lists() })
      
      toast.success('Avatar atualizado com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao fazer upload do avatar')
    },
  })
}

/**
 * Hook to remove avatar from family member
 */
export function useRemoveFamilyMemberAvatar() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => familyMemberService.removeAvatar(id),
    onSuccess: (_, id) => {
      // Invalidate the member detail to refetch without avatar
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: familyMemberKeys.lists() })
      
      toast.success('Avatar removido com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao remover avatar')
    },
  })
}
