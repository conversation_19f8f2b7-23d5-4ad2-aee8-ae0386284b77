import { useQuery } from '@tanstack/react-query'
import { transactionsApi } from '@/lib/api'

export const useInstallments = (transactionId: string | null) => {
  return useQuery({
    queryKey: ['installments', transactionId],
    queryFn: async () => {
      if (!transactionId) return null

      try {
        // Get transaction with installments (new architecture)
        const transaction = await transactionsApi.getById(transactionId)

        if (!transaction) {
          return {
            parentTransaction: null,
            allInstallments: [],
            totalInstallments: 0,
            currentTransaction: null
          }
        }

        // Convert string amounts to numbers for installments
        const processedInstallments = (transaction.installments || []).map((installment: any) => ({
          ...installment,
          amount: typeof installment.amount === 'string' ? parseFloat(installment.amount) : installment.amount
        }))

        return {
          parentTransaction: transaction,
          allInstallments: processedInstallments,
          totalInstallments: transaction.totalInstallments || processedInstallments.length,
          currentTransaction: transaction
        }
      } catch (error) {
        console.error('Error fetching transaction with installments:', error)

        // Return empty data if everything fails
        return {
          parentTransaction: null,
          allInstallments: [],
          totalInstallments: 0,
          currentTransaction: null
        }
      }
    },
    enabled: !!transactionId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}
