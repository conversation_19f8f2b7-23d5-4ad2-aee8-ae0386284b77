import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useDebounce } from './useDebounce'

describe('useDebounce', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('basic functionality', () => {
    it('should return initial value immediately', () => {
      const { result } = renderHook(() => useDebounce('initial', 500))
      
      expect(result.current).toBe('initial')
    })

    it('should debounce value changes', () => {
      const { result, rerender } = renderHook(
        ({ value, delay }) => useDebounce(value, delay),
        {
          initialProps: { value: 'initial', delay: 500 }
        }
      )
      
      expect(result.current).toBe('initial')
      
      // Update value
      rerender({ value: 'updated', delay: 500 })
      
      // Value should not change immediately
      expect(result.current).toBe('initial')
      
      // Fast-forward time by 499ms (just before delay)
      act(() => {
        vi.advanceTimersByTime(499)
      })
      
      expect(result.current).toBe('initial')
      
      // Fast-forward time by 1ms more (completing the delay)
      act(() => {
        vi.advanceTimersByTime(1)
      })
      
      expect(result.current).toBe('updated')
    })

    it('should reset timer on rapid value changes', () => {
      const { result, rerender } = renderHook(
        ({ value, delay }) => useDebounce(value, delay),
        {
          initialProps: { value: 'initial', delay: 500 }
        }
      )
      
      // First update
      rerender({ value: 'first', delay: 500 })
      
      // Advance time partially
      act(() => {
        vi.advanceTimersByTime(300)
      })
      
      expect(result.current).toBe('initial')
      
      // Second update before first completes
      rerender({ value: 'second', delay: 500 })
      
      // Advance time by the original remaining time
      act(() => {
        vi.advanceTimersByTime(200)
      })
      
      // Should still be initial because timer was reset
      expect(result.current).toBe('initial')
      
      // Complete the new timer
      act(() => {
        vi.advanceTimersByTime(300)
      })
      
      expect(result.current).toBe('second')
    })

    it('should handle delay changes', () => {
      const { result, rerender } = renderHook(
        ({ value, delay }) => useDebounce(value, delay),
        {
          initialProps: { value: 'initial', delay: 500 }
        }
      )
      
      // Update value and delay
      rerender({ value: 'updated', delay: 1000 })
      
      // Advance by original delay time
      act(() => {
        vi.advanceTimersByTime(500)
      })
      
      // Should not have updated yet due to new delay
      expect(result.current).toBe('initial')
      
      // Complete the new delay
      act(() => {
        vi.advanceTimersByTime(500)
      })
      
      expect(result.current).toBe('updated')
    })
  })

  describe('different data types', () => {
    it('should work with strings', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 100),
        {
          initialProps: { value: 'hello' }
        }
      )
      
      rerender({ value: 'world' })
      
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      expect(result.current).toBe('world')
    })

    it('should work with numbers', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 100),
        {
          initialProps: { value: 0 }
        }
      )
      
      rerender({ value: 42 })
      
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      expect(result.current).toBe(42)
    })

    it('should work with booleans', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 100),
        {
          initialProps: { value: false }
        }
      )
      
      rerender({ value: true })
      
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      expect(result.current).toBe(true)
    })

    it('should work with objects', () => {
      const initialObj = { name: 'John', age: 30 }
      const updatedObj = { name: 'Jane', age: 25 }
      
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 100),
        {
          initialProps: { value: initialObj }
        }
      )
      
      rerender({ value: updatedObj })
      
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      expect(result.current).toEqual(updatedObj)
    })

    it('should work with arrays', () => {
      const initialArray = [1, 2, 3]
      const updatedArray = [4, 5, 6]
      
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 100),
        {
          initialProps: { value: initialArray }
        }
      )
      
      rerender({ value: updatedArray })
      
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      expect(result.current).toEqual(updatedArray)
    })

    it('should work with null and undefined', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 100),
        {
          initialProps: { value: null as string | null }
        }
      )
      
      rerender({ value: undefined as string | null | undefined })
      
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      expect(result.current).toBeUndefined()
    })
  })

  describe('edge cases', () => {
    it('should handle zero delay', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 0),
        {
          initialProps: { value: 'initial' }
        }
      )
      
      rerender({ value: 'updated' })
      
      // Even with 0 delay, it should still use setTimeout
      expect(result.current).toBe('initial')
      
      act(() => {
        vi.advanceTimersByTime(0)
      })
      
      expect(result.current).toBe('updated')
    })

    it('should handle negative delay', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, -100),
        {
          initialProps: { value: 'initial' }
        }
      )
      
      rerender({ value: 'updated' })
      
      // Negative delay should be treated as 0
      act(() => {
        vi.advanceTimersByTime(0)
      })
      
      expect(result.current).toBe('updated')
    })

    it('should handle very large delays', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 999999),
        {
          initialProps: { value: 'initial' }
        }
      )
      
      rerender({ value: 'updated' })
      
      act(() => {
        vi.advanceTimersByTime(999998)
      })
      
      expect(result.current).toBe('initial')
      
      act(() => {
        vi.advanceTimersByTime(1)
      })
      
      expect(result.current).toBe('updated')
    })

    it('should handle multiple rapid updates', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 100),
        {
          initialProps: { value: 'initial' }
        }
      )
      
      // Rapid updates
      rerender({ value: 'update1' })
      rerender({ value: 'update2' })
      rerender({ value: 'update3' })
      rerender({ value: 'final' })
      
      // Should still be initial
      expect(result.current).toBe('initial')
      
      // Complete the debounce
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      // Should have the final value
      expect(result.current).toBe('final')
    })

    it('should cleanup timer on unmount', () => {
      const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout')
      
      const { unmount, rerender } = renderHook(
        ({ value }) => useDebounce(value, 100),
        {
          initialProps: { value: 'initial' }
        }
      )
      
      rerender({ value: 'updated' })
      
      unmount()
      
      expect(clearTimeoutSpy).toHaveBeenCalled()
      
      clearTimeoutSpy.mockRestore()
    })
  })

  describe('performance', () => {
    it('should not create new timers if value and delay are the same', () => {
      const setTimeoutSpy = vi.spyOn(global, 'setTimeout')
      const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout')
      
      const { rerender } = renderHook(
        ({ value, delay }) => useDebounce(value, delay),
        {
          initialProps: { value: 'same', delay: 100 }
        }
      )
      
      const initialSetTimeoutCalls = setTimeoutSpy.mock.calls.length
      const initialClearTimeoutCalls = clearTimeoutSpy.mock.calls.length
      
      // Re-render with same props
      rerender({ value: 'same', delay: 100 })
      
      // Should not create new timers
      expect(setTimeoutSpy.mock.calls.length).toBe(initialSetTimeoutCalls)
      expect(clearTimeoutSpy.mock.calls.length).toBe(initialClearTimeoutCalls)
      
      setTimeoutSpy.mockRestore()
      clearTimeoutSpy.mockRestore()
    })
  })
})
