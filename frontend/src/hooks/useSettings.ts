import { useSettingsStore } from '@/stores/settings.store'
import { useCallback } from 'react'

export function useSettings() {
  const {
    theme,
    currency,
    language,
    notifications,
    display,
    setTheme,
    setCurrency,
    setLanguage,
    updateNotifications,
    updateDisplay,
    resetSettings,
  } = useSettingsStore()

  const toggleTheme = useCallback(() => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }, [theme, setTheme])

  const toggleCompactMode = useCallback(() => {
    updateDisplay({ compactMode: !display.compactMode })
  }, [display.compactMode, updateDisplay])

  const toggleShowDecimals = useCallback(() => {
    updateDisplay({ showDecimals: !display.showDecimals })
  }, [display.showDecimals, updateDisplay])

  const toggleGroupTransactions = useCallback(() => {
    updateDisplay({ groupTransactions: !display.groupTransactions })
  }, [display.groupTransactions, updateDisplay])

  const toggleNotification = useCallback((key: keyof typeof notifications) => {
    updateNotifications({ [key]: !notifications[key] })
  }, [notifications, updateNotifications])

  return {
    // Current settings
    theme,
    currency,
    language,
    notifications,
    display,
    
    // Theme helpers
    isDark: theme === 'dark',
    isLight: theme === 'light',
    isSystem: theme === 'system',
    
    // Actions
    setTheme,
    setCurrency,
    setLanguage,
    updateNotifications,
    updateDisplay,
    resetSettings,
    
    // Convenience methods
    toggleTheme,
    toggleCompactMode,
    toggleShowDecimals,
    toggleGroupTransactions,
    toggleNotification,
  }
}
