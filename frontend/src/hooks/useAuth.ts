import { useAuthStore } from '@/stores/auth.store'
import { useUserStore } from '@/stores/user.store'
import { useCallback, useEffect } from 'react'
import type { LoginData } from '@/lib/validators'

export function useAuth() {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    login: loginAction,
    logout: logoutAction,
    checkAuth,
    setLoading,
  } = useAuthStore()

  const userStore = useUserStore()

  // Auto-fetch user data when authenticated (only once)
  useEffect(() => {
    if (isAuthenticated && user && !userStore.profile && !userStore.isLoading) {
      userStore.fetchProfile().catch(console.error)
      userStore.fetchStats().catch(console.error)
    }
  }, [isAuthenticated, user, userStore.profile, userStore.isLoading])

  // Clear user data on logout
  useEffect(() => {
    if (!isAuthenticated) {
      userStore.clearUserData()
    }
  }, [isAuthenticated])

  const login = useCallback(
    async (credentials: LoginData) => {
      return loginAction(credentials.email, credentials.password)
    },
    [loginAction]
  )

  const logout = useCallback(() => {
    logoutAction()
    userStore.clearUserData()
  }, [logoutAction, userStore])

  const refreshAuth = useCallback(async () => {
    return checkAuth()
  }, [checkAuth])

  return {
    // Auth State
    user,
    token,
    isAuthenticated,
    isLoading,

    // User Profile
    profile: userStore.profile,
    stats: userStore.stats,
    preferences: userStore.preferences,

    // Actions
    login,
    logout,
    refreshAuth,
    setLoading,
    updateProfile: userStore.updateProfile,
    updatePreferences: userStore.updatePreferences,
    fetchStats: userStore.fetchStats,

    // Computed
    isLoggedIn: isAuthenticated && !!user,
    hasToken: !!token,
  }
}
