import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useSettings } from './useSettings'

// Mock the settings store
const mockSettingsStore = {
  theme: 'system' as const,
  currency: 'BRL' as const,
  language: 'pt-BR' as const,
  notifications: {
    email: true,
    push: true,
    transactionAlerts: true,
    budgetAlerts: true,
    monthlyReports: true,
  },
  display: {
    compactMode: false,
    showDecimals: true,
    groupTransactions: false,
    defaultView: 'dashboard' as const,
  },
  setTheme: vi.fn(),
  setCurrency: vi.fn(),
  setLanguage: vi.fn(),
  updateNotifications: vi.fn(),
  updateDisplay: vi.fn(),
  resetSettings: vi.fn(),
}

vi.mock('@/stores/settings.store', () => ({
  useSettingsStore: () => mockSettingsStore,
}))

describe('useSettings', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Reset mock store to default state
    Object.assign(mockSettingsStore, {
      theme: 'system',
      currency: 'BRL',
      language: 'pt-BR',
      notifications: {
        email: true,
        push: true,
        transactionAlerts: true,
        budgetAlerts: true,
        monthlyReports: true,
      },
      display: {
        compactMode: false,
        showDecimals: true,
        groupTransactions: false,
        defaultView: 'dashboard',
      },
    })
  })

  describe('initial state', () => {
    it('should return correct initial state', () => {
      const { result } = renderHook(() => useSettings())

      expect(result.current.theme).toBe('system')
      expect(result.current.currency).toBe('BRL')
      expect(result.current.language).toBe('pt-BR')
      expect(result.current.notifications).toEqual({
        email: true,
        push: true,
        transactionAlerts: true,
        budgetAlerts: true,
        monthlyReports: true,
      })
      expect(result.current.display).toEqual({
        compactMode: false,
        showDecimals: true,
        groupTransactions: false,
        defaultView: 'dashboard',
      })
    })

    it('should return correct theme helpers', () => {
      const { result } = renderHook(() => useSettings())

      expect(result.current.isDark).toBe(false)
      expect(result.current.isLight).toBe(false)
      expect(result.current.isSystem).toBe(true)
    })

    it('should return correct theme helpers for dark theme', () => {
      mockSettingsStore.theme = 'dark'

      const { result } = renderHook(() => useSettings())

      expect(result.current.isDark).toBe(true)
      expect(result.current.isLight).toBe(false)
      expect(result.current.isSystem).toBe(false)
    })

    it('should return correct theme helpers for light theme', () => {
      mockSettingsStore.theme = 'light'

      const { result } = renderHook(() => useSettings())

      expect(result.current.isDark).toBe(false)
      expect(result.current.isLight).toBe(true)
      expect(result.current.isSystem).toBe(false)
    })
  })

  describe('basic actions', () => {
    it('should call setTheme', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.setTheme('dark')
      })

      expect(mockSettingsStore.setTheme).toHaveBeenCalledWith('dark')
    })

    it('should call setCurrency', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.setCurrency('USD')
      })

      expect(mockSettingsStore.setCurrency).toHaveBeenCalledWith('USD')
    })

    it('should call setLanguage', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.setLanguage('en-US')
      })

      expect(mockSettingsStore.setLanguage).toHaveBeenCalledWith('en-US')
    })

    it('should call updateNotifications', () => {
      const { result } = renderHook(() => useSettings())

      const updates = { email: false, push: true }

      act(() => {
        result.current.updateNotifications(updates)
      })

      expect(mockSettingsStore.updateNotifications).toHaveBeenCalledWith(updates)
    })

    it('should call updateDisplay', () => {
      const { result } = renderHook(() => useSettings())

      const updates = { compactMode: true, showDecimals: false }

      act(() => {
        result.current.updateDisplay(updates)
      })

      expect(mockSettingsStore.updateDisplay).toHaveBeenCalledWith(updates)
    })

    it('should call resetSettings', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.resetSettings()
      })

      expect(mockSettingsStore.resetSettings).toHaveBeenCalled()
    })
  })

  describe('toggleTheme', () => {
    it('should toggle from light to dark', () => {
      mockSettingsStore.theme = 'light'

      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleTheme()
      })

      expect(mockSettingsStore.setTheme).toHaveBeenCalledWith('dark')
    })

    it('should toggle from dark to light', () => {
      mockSettingsStore.theme = 'dark'

      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleTheme()
      })

      expect(mockSettingsStore.setTheme).toHaveBeenCalledWith('light')
    })

    it('should toggle from system to light', () => {
      mockSettingsStore.theme = 'system'

      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleTheme()
      })

      expect(mockSettingsStore.setTheme).toHaveBeenCalledWith('light')
    })
  })

  describe('display toggles', () => {
    it('should toggle compact mode', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleCompactMode()
      })

      expect(mockSettingsStore.updateDisplay).toHaveBeenCalledWith({
        compactMode: true
      })
    })

    it('should toggle compact mode when already enabled', () => {
      mockSettingsStore.display.compactMode = true

      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleCompactMode()
      })

      expect(mockSettingsStore.updateDisplay).toHaveBeenCalledWith({
        compactMode: false
      })
    })

    it('should toggle show decimals', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleShowDecimals()
      })

      expect(mockSettingsStore.updateDisplay).toHaveBeenCalledWith({
        showDecimals: false
      })
    })

    it('should toggle show decimals when already disabled', () => {
      mockSettingsStore.display.showDecimals = false

      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleShowDecimals()
      })

      expect(mockSettingsStore.updateDisplay).toHaveBeenCalledWith({
        showDecimals: true
      })
    })

    it('should toggle group transactions', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleGroupTransactions()
      })

      expect(mockSettingsStore.updateDisplay).toHaveBeenCalledWith({
        groupTransactions: true
      })
    })

    it('should toggle group transactions when already enabled', () => {
      mockSettingsStore.display.groupTransactions = true

      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleGroupTransactions()
      })

      expect(mockSettingsStore.updateDisplay).toHaveBeenCalledWith({
        groupTransactions: false
      })
    })
  })

  describe('notification toggles', () => {
    it('should toggle email notifications', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleNotification('email')
      })

      expect(mockSettingsStore.updateNotifications).toHaveBeenCalledWith({
        email: false
      })
    })

    it('should toggle push notifications', () => {
      mockSettingsStore.notifications.push = false

      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleNotification('push')
      })

      expect(mockSettingsStore.updateNotifications).toHaveBeenCalledWith({
        push: true
      })
    })

    it('should toggle transaction alerts', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleNotification('transactionAlerts')
      })

      expect(mockSettingsStore.updateNotifications).toHaveBeenCalledWith({
        transactionAlerts: false
      })
    })

    it('should toggle budget alerts', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleNotification('budgetAlerts')
      })

      expect(mockSettingsStore.updateNotifications).toHaveBeenCalledWith({
        budgetAlerts: false
      })
    })

    it('should toggle monthly reports', () => {
      const { result } = renderHook(() => useSettings())

      act(() => {
        result.current.toggleNotification('monthlyReports')
      })

      expect(mockSettingsStore.updateNotifications).toHaveBeenCalledWith({
        monthlyReports: false
      })
    })
  })

  describe('memoization', () => {
    it('should memoize toggleTheme function', () => {
      const { result, rerender } = renderHook(() => useSettings())

      const firstToggleTheme = result.current.toggleTheme
      
      rerender()
      
      const secondToggleTheme = result.current.toggleTheme

      expect(firstToggleTheme).toBe(secondToggleTheme)
    })

    it('should update memoized functions when dependencies change', () => {
      const { result, rerender } = renderHook(() => useSettings())

      const firstToggleTheme = result.current.toggleTheme

      // Change the theme to trigger dependency change
      mockSettingsStore.theme = 'dark'
      
      rerender()
      
      const secondToggleTheme = result.current.toggleTheme

      // Functions should be different due to dependency change
      expect(firstToggleTheme).not.toBe(secondToggleTheme)
    })

    it('should memoize display toggle functions', () => {
      const { result, rerender } = renderHook(() => useSettings())

      const firstToggleCompact = result.current.toggleCompactMode
      const firstToggleDecimals = result.current.toggleShowDecimals
      const firstToggleGroup = result.current.toggleGroupTransactions
      
      rerender()
      
      const secondToggleCompact = result.current.toggleCompactMode
      const secondToggleDecimals = result.current.toggleShowDecimals
      const secondToggleGroup = result.current.toggleGroupTransactions

      expect(firstToggleCompact).toBe(secondToggleCompact)
      expect(firstToggleDecimals).toBe(secondToggleDecimals)
      expect(firstToggleGroup).toBe(secondToggleGroup)
    })

    it('should memoize notification toggle function', () => {
      const { result, rerender } = renderHook(() => useSettings())

      const firstToggleNotification = result.current.toggleNotification
      
      rerender()
      
      const secondToggleNotification = result.current.toggleNotification

      expect(firstToggleNotification).toBe(secondToggleNotification)
    })
  })
})
