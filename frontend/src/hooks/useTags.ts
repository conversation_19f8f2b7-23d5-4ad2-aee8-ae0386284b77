import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { tagsApi } from '@/lib/api'
import type { Tag, TagFilters, CreateTagData, UpdateTagData } from '@/types/tag.types'

// Query keys
export const tagKeys = {
  all: ['tags'] as const,
  lists: () => [...tagKeys.all, 'list'] as const,
  list: (filters: TagFilters) => [...tagKeys.lists(), filters] as const,
  details: () => [...tagKeys.all, 'detail'] as const,
  detail: (id: string) => [...tagKeys.details(), id] as const,
  stats: () => [...tagKeys.all, 'stats'] as const,
}

/**
 * Hook to fetch all tags with filters
 */
export function useTags(filters: TagFilters = {}) {
  return useQuery({
    queryKey: tagKeys.list(filters),
    queryFn: () => tagsApi.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch a single tag by ID
 */
export function useTag(id: string, includeArchived = false) {
  return useQuery({
    queryKey: tagKeys.detail(id),
    queryFn: () => tagsApi.getById(id, includeArchived),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch tags statistics
 */
export function useTagStats() {
  return useQuery({
    queryKey: tagKeys.stats(),
    queryFn: () => tagsApi.getStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to create a new tag
 */
export function useCreateTag() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateTagData) => tagsApi.create(data),
    onSuccess: (newTag) => {
      // Add the new tag to the cache
      queryClient.setQueryData(tagKeys.detail(newTag.data.id), newTag)

      // Invalidate lists to include the new tag
      queryClient.invalidateQueries({ queryKey: tagKeys.lists() })
      queryClient.invalidateQueries({ queryKey: tagKeys.stats() })

      toast.success('Tag criada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao criar tag'
      toast.error(message)
    },
  })
}

/**
 * Hook to update an existing tag
 */
export function useUpdateTag() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTagData }) =>
      tagsApi.update(id, data),
    onSuccess: (updatedTag, { id }) => {
      // Update the tag in the cache
      queryClient.setQueryData(tagKeys.detail(id), updatedTag)

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: tagKeys.lists() })
      queryClient.invalidateQueries({ queryKey: tagKeys.stats() })

      toast.success('Tag atualizada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao atualizar tag'
      toast.error(message)
    },
  })
}

/**
 * Hook to archive/unarchive a tag
 */
export function useArchiveTag() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, archived }: { id: string; archived: boolean }) =>
      tagsApi.archive(id, archived),
    onSuccess: (updatedTag, { archived }) => {
      // Update the tag in the cache
      queryClient.setQueryData(tagKeys.detail(updatedTag.data.id), updatedTag)

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: tagKeys.lists() })
      queryClient.invalidateQueries({ queryKey: tagKeys.stats() })

      const action = archived ? 'arquivada' : 'restaurada'
      toast.success(`Tag ${action} com sucesso!`)
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao arquivar tag'
      toast.error(message)
    },
  })
}

/**
 * Hook to delete a tag permanently
 */
export function useDeleteTag() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => tagsApi.delete(id),
    onSuccess: (_, id) => {
      // Remove the tag from the cache
      queryClient.removeQueries({ queryKey: tagKeys.detail(id) })

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: tagKeys.lists() })
      queryClient.invalidateQueries({ queryKey: tagKeys.stats() })

      toast.success('Tag excluída com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Erro ao excluir tag'
      toast.error(message)
    },
  })
}
