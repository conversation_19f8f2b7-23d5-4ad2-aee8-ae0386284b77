import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useLocalStorage } from './useLocalStorage'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock console.warn to avoid noise in tests
const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

describe('useLocalStorage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    consoleWarnSpy.mockClear()
  })

  describe('initialization', () => {
    it('should return initial value when localStorage is empty', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial-value'))
      
      expect(result.current[0]).toBe('initial-value')
      expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key')
    })

    it('should return stored value when localStorage has data', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify('stored-value'))
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial-value'))
      
      expect(result.current[0]).toBe('stored-value')
      expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key')
    })

    it('should handle complex objects', () => {
      const complexObject = { name: 'John', age: 30, preferences: { theme: 'dark' } }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(complexObject))
      
      const { result } = renderHook(() => useLocalStorage('user-data', {}))
      
      expect(result.current[0]).toEqual(complexObject)
    })

    it('should handle arrays', () => {
      const arrayData = [1, 2, 3, 'test', { nested: true }]
      localStorageMock.getItem.mockReturnValue(JSON.stringify(arrayData))
      
      const { result } = renderHook(() => useLocalStorage('array-data', []))
      
      expect(result.current[0]).toEqual(arrayData)
    })

    it('should return initial value when JSON parsing fails', () => {
      localStorageMock.getItem.mockReturnValue('invalid-json')
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'fallback'))
      
      expect(result.current[0]).toBe('fallback')
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Error reading localStorage key "test-key":',
        expect.any(Error)
      )
    })
  })

  describe('setValue', () => {
    it('should update value and localStorage', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
      
      act(() => {
        result.current[1]('new-value')
      })
      
      expect(result.current[0]).toBe('new-value')
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'test-key',
        JSON.stringify('new-value')
      )
    })

    it('should handle function updates', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify(5))
      
      const { result } = renderHook(() => useLocalStorage('counter', 0))
      
      act(() => {
        result.current[1]((prev) => prev + 1)
      })
      
      expect(result.current[0]).toBe(6)
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'counter',
        JSON.stringify(6)
      )
    })

    it('should handle complex object updates', () => {
      const initialUser = { name: 'John', age: 30 }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(initialUser))
      
      const { result } = renderHook(() => useLocalStorage('user', {}))
      
      const updatedUser = { ...initialUser, age: 31 }
      
      act(() => {
        result.current[1](updatedUser)
      })
      
      expect(result.current[0]).toEqual(updatedUser)
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'user',
        JSON.stringify(updatedUser)
      )
    })

    it('should handle localStorage errors gracefully', () => {
      localStorageMock.getItem.mockReturnValue(null)
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded')
      })
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
      
      act(() => {
        result.current[1]('new-value')
      })
      
      // Value should still be updated in state
      expect(result.current[0]).toBe('new-value')
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Error setting localStorage key "test-key":',
        expect.any(Error)
      )
    })
  })

  describe('removeValue', () => {
    it('should remove value from localStorage and reset to initial', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify('stored-value'))
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
      
      expect(result.current[0]).toBe('stored-value')
      
      act(() => {
        result.current[2]() // removeValue
      })
      
      expect(result.current[0]).toBe('initial')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-key')
    })

    it('should handle localStorage remove errors gracefully', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify('stored-value'))
      localStorageMock.removeItem.mockImplementation(() => {
        throw new Error('Remove failed')
      })
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
      
      act(() => {
        result.current[2]() // removeValue
      })
      
      // Value should still be reset in state
      expect(result.current[0]).toBe('initial')
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Error removing localStorage key "test-key":',
        expect.any(Error)
      )
    })
  })

  describe('storage event handling', () => {
    it('should update value when storage event is fired', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify('initial'))
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'default'))
      
      expect(result.current[0]).toBe('initial')
      
      // Simulate storage event from another tab
      const storageEvent = new StorageEvent('storage', {
        key: 'test-key',
        newValue: JSON.stringify('updated-from-another-tab'),
        oldValue: JSON.stringify('initial'),
      })
      
      act(() => {
        window.dispatchEvent(storageEvent)
      })
      
      expect(result.current[0]).toBe('updated-from-another-tab')
    })

    it('should ignore storage events for different keys', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify('initial'))
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'default'))
      
      const storageEvent = new StorageEvent('storage', {
        key: 'different-key',
        newValue: JSON.stringify('should-not-update'),
        oldValue: JSON.stringify('old'),
      })
      
      act(() => {
        window.dispatchEvent(storageEvent)
      })
      
      expect(result.current[0]).toBe('initial')
    })

    it('should handle invalid JSON in storage events', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify('initial'))
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'default'))
      
      const storageEvent = new StorageEvent('storage', {
        key: 'test-key',
        newValue: 'invalid-json',
        oldValue: JSON.stringify('initial'),
      })
      
      act(() => {
        window.dispatchEvent(storageEvent)
      })
      
      expect(result.current[0]).toBe('initial')
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Error parsing localStorage key "test-key":',
        expect.any(Error)
      )
    })

    it('should ignore storage events with null newValue', () => {
      localStorageMock.getItem.mockReturnValue(JSON.stringify('initial'))
      
      const { result } = renderHook(() => useLocalStorage('test-key', 'default'))
      
      const storageEvent = new StorageEvent('storage', {
        key: 'test-key',
        newValue: null,
        oldValue: JSON.stringify('initial'),
      })
      
      act(() => {
        window.dispatchEvent(storageEvent)
      })
      
      expect(result.current[0]).toBe('initial')
    })
  })

  describe('type safety', () => {
    it('should work with string types', () => {
      const { result } = renderHook(() => useLocalStorage('string-key', 'default'))
      
      act(() => {
        result.current[1]('new string')
      })
      
      expect(result.current[0]).toBe('new string')
    })

    it('should work with number types', () => {
      const { result } = renderHook(() => useLocalStorage('number-key', 42))
      
      act(() => {
        result.current[1](100)
      })
      
      expect(result.current[0]).toBe(100)
    })

    it('should work with boolean types', () => {
      const { result } = renderHook(() => useLocalStorage('boolean-key', false))
      
      act(() => {
        result.current[1](true)
      })
      
      expect(result.current[0]).toBe(true)
    })

    it('should work with object types', () => {
      interface User {
        name: string
        age: number
      }
      
      const { result } = renderHook(() => 
        useLocalStorage<User>('user-key', { name: 'Default', age: 0 })
      )
      
      act(() => {
        result.current[1]({ name: 'John', age: 30 })
      })
      
      expect(result.current[0]).toEqual({ name: 'John', age: 30 })
    })
  })
})
