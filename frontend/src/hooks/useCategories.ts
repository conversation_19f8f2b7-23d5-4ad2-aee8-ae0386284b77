import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { categoryService } from '@/services/category.service'
import type {
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryFilters,
  BulkCategoryOperation,
} from '@/types/category.types'

// Query keys
export const categoryKeys = {
  all: ['categories'] as const,
  lists: () => [...categoryKeys.all, 'list'] as const,
  list: (filters: CategoryFilters) => [...categoryKeys.lists(), filters] as const,
  details: () => [...categoryKeys.all, 'detail'] as const,
  detail: (id: string) => [...categoryKeys.details(), id] as const,
  tree: () => [...categoryKeys.all, 'tree'] as const,
  treeWithArchived: (includeArchived: boolean) => [...categoryKeys.tree(), includeArchived] as const,
  stats: () => [...categoryKeys.all, 'stats'] as const,
  selection: () => [...categoryKeys.all, 'selection'] as const,
  search: (query: string) => [...categoryKeys.all, 'search', query] as const,
  validation: () => [...categoryKeys.all, 'validation'] as const,
}

/**
 * Hook to fetch all categories with filters
 */
export function useCategories(filters: CategoryFilters = {}) {
  return useQuery({
    queryKey: categoryKeys.list(filters),
    queryFn: () => categoryService.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch category tree for hierarchical display
 */
export function useCategoryTree(includeArchived = false) {
  return useQuery({
    queryKey: categoryKeys.treeWithArchived(includeArchived),
    queryFn: () => categoryService.getCategoryTree(includeArchived),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch a single category by ID
 */
export function useCategory(id: string, includeArchived = false) {
  return useQuery({
    queryKey: categoryKeys.detail(id),
    queryFn: () => categoryService.getById(id, includeArchived),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch categories statistics
 */
export function useCategoryStats() {
  return useQuery({
    queryKey: categoryKeys.stats(),
    queryFn: () => categoryService.getStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to fetch categories for selection dropdown
 */
export function useCategoriesForSelection(excludeId?: string) {
  return useQuery({
    queryKey: [...categoryKeys.selection(), excludeId],
    queryFn: () => categoryService.getForSelection(excludeId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch categories for CRUD forms (simplified)
 * This hook is specifically designed for use in transaction, budget, and goal forms
 * Uses the main categories endpoint to ensure all data (including colors) is available
 */
export function useCategoriesForForms() {
  return useQuery({
    queryKey: categoryKeys.lists(),
    queryFn: () => categoryService.getAll({ includeChildren: false, includeArchived: false, page: 1, limit: 100 }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => {
      // Transform paginated response to simple array format expected by forms
      const categories = data?.data || []
      return categories.map(category => ({
        id: category.id,
        name: category.name,
        color: category.color
      }))
    },
  })
}

/**
 * Hook to search categories by name
 */
export function useSearchCategories(query: string, limit = 10) {
  return useQuery({
    queryKey: categoryKeys.search(query),
    queryFn: () => categoryService.search(query, limit),
    enabled: query.length >= 2, // Only search if query has at least 2 characters
    staleTime: 30 * 1000, // 30 seconds for search results
  })
}

/**
 * Hook to create a new category
 */
export function useCreateCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateCategoryRequest) => categoryService.create(data),
    onSuccess: (newCategory) => {
      // Invalidate and refetch categories lists
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.stats() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.selection() })
      
      // Add the new category to the cache
      queryClient.setQueryData(categoryKeys.detail(newCategory.id), newCategory)
      
      toast.success('Categoria criada com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao criar categoria')
    },
  })
}

/**
 * Hook to update an existing category
 */
export function useUpdateCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCategoryRequest }) =>
      categoryService.update(id, data),
    onSuccess: (updatedCategory) => {
      // Update the category in the cache
      queryClient.setQueryData(categoryKeys.detail(updatedCategory.id), updatedCategory)
      
      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.stats() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.selection() })
      
      toast.success('Categoria atualizada com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao atualizar categoria')
    },
  })
}



/**
 * Hook to archive/unarchive a category
 */
export function useArchiveCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, archived }: { id: string; archived: boolean }) =>
      categoryService.archive(id, archived),
    onSuccess: (updatedCategory, { archived }) => {
      // Update the category in the cache
      queryClient.setQueryData(categoryKeys.detail(updatedCategory.id), updatedCategory)

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.stats() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.selection() })

      const action = archived ? 'arquivada' : 'restaurada'
      toast.success(`Categoria ${action} com sucesso!`)
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao arquivar categoria')
    },
  })
}

/**
 * Hook to restore an archived category
 */
export function useRestoreCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => categoryService.restore(id),
    onSuccess: (restoredCategory) => {
      // Update the category in the cache
      queryClient.setQueryData(categoryKeys.detail(restoredCategory.id), restoredCategory)

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.stats() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.selection() })

      toast.success('Categoria restaurada com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao restaurar categoria')
    },
  })
}

/**
 * Hook to permanently delete a category
 */
export function useDeleteCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => categoryService.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: categoryKeys.detail(id) })
      
      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.stats() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.selection() })
      
      toast.success('Categoria excluída permanentemente!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao excluir categoria')
    },
  })
}

/**
 * Hook to validate category hierarchy
 */
export function useValidateCategoryHierarchy() {
  return useMutation({
    mutationFn: ({ parentId, categoryId }: { parentId: string; categoryId?: string }) =>
      categoryService.validateHierarchy(parentId, categoryId),
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao validar hierarquia da categoria')
    },
  })
}

/**
 * Hook to perform bulk operations on categories
 */
export function useBulkCategoryOperation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (operation: BulkCategoryOperation) => categoryService.bulkOperation(operation),
    onSuccess: (result) => {
      // Invalidate all category-related queries
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.stats() })
      queryClient.invalidateQueries({ queryKey: categoryKeys.selection() })
      
      const { processed, failed, operation } = result
      const operationText = operation === 'archive' ? 'arquivadas' : 
                           operation === 'unarchive' ? 'restauradas' : 'excluídas'
      
      if (failed > 0) {
        toast.success(`${processed} categorias ${operationText} com sucesso. ${failed} falharam.`)
      } else {
        toast.success(`${processed} categorias ${operationText} com sucesso!`)
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao executar operação em lote')
    },
  })
}
