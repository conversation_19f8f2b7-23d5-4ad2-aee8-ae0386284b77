import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { transactionService } from '@/services/transaction.service'
import type {
  TransactionFilters,
  CreateTransactionData,
  UpdateTransactionData,
  InstallmentFilters
} from '@/types/transaction.types'

// Query keys for new architecture
export const transactionKeys = {
  all: ['transactions'] as const,
  lists: () => [...transactionKeys.all, 'list'] as const,
  list: (filters: TransactionFilters) => [...transactionKeys.lists(), filters] as const,
  details: () => [...transactionKeys.all, 'detail'] as const,
  detail: (id: string) => [...transactionKeys.details(), id] as const,
  stats: () => [...transactionKeys.all, 'stats'] as const,
  insights: (period: string) => [...transactionKeys.all, 'insights', period] as const,
  installments: () => ['installments'] as const,
  installmentsList: (filters: InstallmentFilters) => [...transactionKeys.installments(), 'list', filters] as const,
  upcomingInstallments: (days: number) => [...transactionKeys.installments(), 'upcoming', days] as const,
  overdueInstallments: () => [...transactionKeys.installments(), 'overdue'] as const,
}

/**
 * Hook to fetch all transactions with filters and pagination
 */
export function useTransactions(filters: TransactionFilters = {}) {
  return useQuery({
    queryKey: transactionKeys.list(filters),
    queryFn: () => transactionService.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch transaction by ID with all installments
 */
export function useTransaction(id: string) {
  return useQuery({
    queryKey: transactionKeys.detail(id),
    queryFn: () => transactionService.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch transaction statistics
 */
export function useTransactionStats() {
  return useQuery({
    queryKey: transactionKeys.stats(),
    queryFn: () => transactionService.getStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch transaction insights
 */
export function useTransactionInsights(period: 'week' | 'month' | 'quarter' | 'year' = 'month') {
  return useQuery({
    queryKey: transactionKeys.insights(period),
    queryFn: () => transactionService.getInsights(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Note: useInstallments hook moved to separate file: hooks/useInstallments.ts

// Note: Upcoming and overdue installments hooks removed
// These can be implemented later when the backend endpoints are available

/**
 * Hook to create a new transaction with installments
 */
export function useCreateTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateTransactionData) => transactionService.create(data),
    onSuccess: (newTransaction) => {
      // Invalidate and refetch transactions list
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.stats() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.installments() })
      
      // Add to cache
      queryClient.setQueryData(
        transactionKeys.detail(newTransaction.id),
        newTransaction
      )

      toast.success('Transação criada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao criar transação'
      toast.error(message)
    },
  })
}

/**
 * Hook to update a transaction and its installments
 */
export function useUpdateTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTransactionData }) =>
      transactionService.update(id, data),
    onSuccess: (updatedTransaction) => {
      // Update cache
      queryClient.setQueryData(
        transactionKeys.detail(updatedTransaction.id),
        updatedTransaction
      )

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.stats() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.installments() })

      toast.success('Transação atualizada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao atualizar transação'
      toast.error(message)
    },
  })
}

/**
 * Hook to delete a transaction and all installments
 */
export function useDeleteTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => transactionService.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: transactionKeys.detail(deletedId) })

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.stats() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.installments() })

      toast.success('Transação deletada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao deletar transação'
      toast.error(message)
    },
  })
}

/**
 * Hook to update installment status (paid/unpaid)
 */
export function useUpdateInstallmentStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      transactionId, 
      installmentNumber, 
      isPaid 
    }: { 
      transactionId: string
      installmentNumber: number
      isPaid: boolean 
    }) => transactionService.updateInstallmentStatus(transactionId, installmentNumber, isPaid),
    onSuccess: (updatedTransaction) => {
      // Update transaction in cache
      queryClient.setQueryData(
        transactionKeys.detail(updatedTransaction.id),
        updatedTransaction
      )

      // Invalidate all relevant queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.installments() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.stats() })

      toast.success('Status da parcela atualizado!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao atualizar parcela'
      toast.error(message)
    },
  })
}

// Note: Bulk update installments hook removed
// This can be implemented later when the backend endpoint is available

/**
 * Hook to duplicate a transaction
 */
export function useDuplicateTransaction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, newDate }: { id: string; newDate?: string }) => 
      transactionService.duplicate(id, newDate),
    onSuccess: (duplicatedTransaction) => {
      // Add to cache
      queryClient.setQueryData(
        transactionKeys.detail(duplicatedTransaction.id),
        duplicatedTransaction
      )

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: transactionKeys.lists() })
      queryClient.invalidateQueries({ queryKey: transactionKeys.stats() })

      toast.success('Transação duplicada com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao duplicar transação'
      toast.error(message)
    },
  })
}

/**
 * Hook to export transactions
 */
export function useExportTransactions() {
  return useMutation({
    mutationFn: ({ 
      format, 
      filters 
    }: { 
      format: 'csv' | 'excel'
      filters?: TransactionFilters 
    }) => transactionService.exportTransactions(format, filters),
    onSuccess: (blob, variables) => {
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `transactions.${variables.format === 'csv' ? 'csv' : 'xlsx'}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success('Transações exportadas com sucesso!')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao exportar transações'
      toast.error(message)
    },
  })
}

/**
 * Hook to import transactions
 */
export function useImportTransactions() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (file: File) => transactionService.importTransactions(file),
    onSuccess: (result) => {
      // Invalidate all transaction queries
      queryClient.invalidateQueries({ queryKey: transactionKeys.all })
      queryClient.invalidateQueries({ queryKey: transactionKeys.installments() })

      if (result.errors.length > 0) {
        toast.success(`${result.success} transações importadas. ${result.errors.length} erros encontrados.`)
      } else {
        toast.success(`${result.success} transações importadas com sucesso!`)
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error?.message || 'Erro ao importar transações'
      toast.error(message)
    },
  })
}
