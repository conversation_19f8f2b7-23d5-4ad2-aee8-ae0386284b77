import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useAuth } from './useAuth'

// Mock the stores
const mockAuthStore = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  login: vi.fn(),
  logout: vi.fn(),
  checkAuth: vi.fn(),
  setLoading: vi.fn(),
}

const mockUserStore = {
  profile: null,
  stats: null,
  preferences: null,
  fetchProfile: vi.fn(),
  fetchStats: vi.fn(),
  clearUserData: vi.fn(),
  updateProfile: vi.fn(),
  updatePreferences: vi.fn(),
}

vi.mock('@/stores/auth.store', () => ({
  useAuthStore: () => mockAuthStore,
}))

vi.mock('@/stores/user.store', () => ({
  useUserStore: () => mockUserStore,
}))

describe('useAuth', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Reset mock store states
    Object.assign(mockAuthStore, {
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
    })
    
    Object.assign(mockUserStore, {
      profile: null,
      stats: null,
      preferences: null,
    })
  })

  describe('initial state', () => {
    it('should return correct initial state', () => {
      const { result } = renderHook(() => useAuth())

      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.profile).toBeNull()
      expect(result.current.stats).toBeNull()
      expect(result.current.preferences).toBeNull()
      expect(result.current.isLoggedIn).toBe(false)
      expect(result.current.hasToken).toBe(false)
    })

    it('should return correct computed values when authenticated', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: '2024-01-01T00:00:00.000Z'
      }

      Object.assign(mockAuthStore, {
        user: mockUser,
        token: 'mock-token',
        isAuthenticated: true,
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.isLoggedIn).toBe(true)
      expect(result.current.hasToken).toBe(true)
    })
  })

  describe('login', () => {
    it('should call auth store login with correct credentials', async () => {
      mockAuthStore.login.mockResolvedValue(undefined)

      const { result } = renderHook(() => useAuth())

      const credentials = {
        email: '<EMAIL>',
        password: 'password123'
      }

      await act(async () => {
        await result.current.login(credentials)
      })

      expect(mockAuthStore.login).toHaveBeenCalledWith('<EMAIL>', 'password123')
    })

    it('should handle login errors', async () => {
      const mockError = new Error('Login failed')
      mockAuthStore.login.mockRejectedValue(mockError)

      const { result } = renderHook(() => useAuth())

      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      await expect(
        act(async () => {
          await result.current.login(credentials)
        })
      ).rejects.toThrow('Login failed')
    })
  })

  describe('logout', () => {
    it('should call auth store logout and clear user data', () => {
      const { result } = renderHook(() => useAuth())

      act(() => {
        result.current.logout()
      })

      expect(mockAuthStore.logout).toHaveBeenCalled()
      expect(mockUserStore.clearUserData).toHaveBeenCalled()
    })
  })

  describe('refreshAuth', () => {
    it('should call auth store checkAuth', async () => {
      mockAuthStore.checkAuth.mockResolvedValue(undefined)

      const { result } = renderHook(() => useAuth())

      await act(async () => {
        await result.current.refreshAuth()
      })

      expect(mockAuthStore.checkAuth).toHaveBeenCalled()
    })
  })

  describe('user data fetching', () => {
    it('should fetch user profile and stats when authenticated', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: '2024-01-01T00:00:00.000Z'
      }

      mockUserStore.fetchProfile.mockResolvedValue(undefined)
      mockUserStore.fetchStats.mockResolvedValue(undefined)

      // Set authenticated state
      Object.assign(mockAuthStore, {
        user: mockUser,
        isAuthenticated: true,
      })

      renderHook(() => useAuth())

      expect(mockUserStore.fetchProfile).toHaveBeenCalled()
      expect(mockUserStore.fetchStats).toHaveBeenCalled()
    })

    it('should not fetch user data when not authenticated', () => {
      renderHook(() => useAuth())

      expect(mockUserStore.fetchProfile).not.toHaveBeenCalled()
      expect(mockUserStore.fetchStats).not.toHaveBeenCalled()
    })

    it('should not fetch user data when profile already exists', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: '2024-01-01T00:00:00.000Z'
      }

      const mockProfile = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
      }

      Object.assign(mockAuthStore, {
        user: mockUser,
        isAuthenticated: true,
      })

      Object.assign(mockUserStore, {
        profile: mockProfile,
      })

      renderHook(() => useAuth())

      expect(mockUserStore.fetchProfile).not.toHaveBeenCalled()
      expect(mockUserStore.fetchStats).not.toHaveBeenCalled()
    })

    it('should handle fetch errors gracefully', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: '2024-01-01T00:00:00.000Z'
      }

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      mockUserStore.fetchProfile.mockRejectedValue(new Error('Fetch failed'))
      mockUserStore.fetchStats.mockRejectedValue(new Error('Fetch failed'))

      Object.assign(mockAuthStore, {
        user: mockUser,
        isAuthenticated: true,
      })

      renderHook(() => useAuth())

      expect(mockUserStore.fetchProfile).toHaveBeenCalled()
      expect(mockUserStore.fetchStats).toHaveBeenCalled()

      consoleErrorSpy.mockRestore()
    })
  })

  describe('user data clearing', () => {
    it('should clear user data when not authenticated', () => {
      // Start with authenticated state
      Object.assign(mockAuthStore, {
        isAuthenticated: false,
      })

      renderHook(() => useAuth())

      expect(mockUserStore.clearUserData).toHaveBeenCalled()
    })

    it('should not clear user data when authenticated', () => {
      Object.assign(mockAuthStore, {
        isAuthenticated: true,
      })

      renderHook(() => useAuth())

      expect(mockUserStore.clearUserData).not.toHaveBeenCalled()
    })
  })

  describe('exposed methods', () => {
    it('should expose setLoading method', () => {
      const { result } = renderHook(() => useAuth())

      act(() => {
        result.current.setLoading(true)
      })

      expect(mockAuthStore.setLoading).toHaveBeenCalledWith(true)
    })

    it('should expose updateProfile method', async () => {
      mockUserStore.updateProfile.mockResolvedValue(undefined)

      const { result } = renderHook(() => useAuth())

      const profileData = { name: 'Updated Name' }

      await act(async () => {
        await result.current.updateProfile(profileData)
      })

      expect(mockUserStore.updateProfile).toHaveBeenCalledWith(profileData)
    })

    it('should expose updatePreferences method', async () => {
      mockUserStore.updatePreferences.mockResolvedValue(undefined)

      const { result } = renderHook(() => useAuth())

      const preferences = { theme: 'dark' }

      await act(async () => {
        await result.current.updatePreferences(preferences)
      })

      expect(mockUserStore.updatePreferences).toHaveBeenCalledWith(preferences)
    })

    it('should expose fetchStats method', async () => {
      mockUserStore.fetchStats.mockResolvedValue(undefined)

      const { result } = renderHook(() => useAuth())

      await act(async () => {
        await result.current.fetchStats()
      })

      expect(mockUserStore.fetchStats).toHaveBeenCalled()
    })
  })

  describe('memoization', () => {
    it('should memoize login function', () => {
      const { result, rerender } = renderHook(() => useAuth())

      const firstLogin = result.current.login
      
      rerender()
      
      const secondLogin = result.current.login

      expect(firstLogin).toBe(secondLogin)
    })

    it('should memoize logout function', () => {
      const { result, rerender } = renderHook(() => useAuth())

      const firstLogout = result.current.logout
      
      rerender()
      
      const secondLogout = result.current.logout

      expect(firstLogout).toBe(secondLogout)
    })

    it('should memoize refreshAuth function', () => {
      const { result, rerender } = renderHook(() => useAuth())

      const firstRefreshAuth = result.current.refreshAuth
      
      rerender()
      
      const secondRefreshAuth = result.current.refreshAuth

      expect(firstRefreshAuth).toBe(secondRefreshAuth)
    })

    it('should update memoized functions when dependencies change', () => {
      const { result, rerender } = renderHook(() => useAuth())

      const firstLogout = result.current.logout

      // Change the logout function in the mock
      const newLogoutFn = vi.fn()
      mockAuthStore.logout = newLogoutFn
      
      rerender()
      
      const secondLogout = result.current.logout

      // Functions should be different due to dependency change
      expect(firstLogout).not.toBe(secondLogout)
    })
  })
})
