import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'

interface UserProfile {
  id: string
  email: string
  name: string
  avatar?: string
  phone?: string
  dateOfBirth?: string
  occupation?: string
  monthlyIncome?: number
  createdAt: string
  updatedAt: string
}

interface UserPreferences {
  budgetCategories: string[]
  favoriteTransactionTypes: string[]
  defaultAccount?: string
  quickAmounts: number[]
}

interface UserStats {
  totalTransactions: number
  totalAccounts: number
  totalBudgets: number
  currentBalance: number
  monthlyExpenses: number
  monthlyIncome: number
}

interface UserState {
  profile: UserProfile | null
  preferences: UserPreferences
  stats: UserStats | null
  isLoading: boolean
  
  // Actions
  fetchProfile: () => Promise<void>
  updateProfile: (data: Partial<UserProfile>) => Promise<void>
  updatePreferences: (preferences: Partial<UserPreferences>) => void
  fetchStats: () => Promise<void>
  setLoading: (loading: boolean) => void
  clearUserData: () => void
}

const defaultPreferences: UserPreferences = {
  budgetCategories: ['Alimentação', 'Transporte', 'Moradia', 'Lazer', 'Saúde'],
  favoriteTransactionTypes: ['Receita', 'Despesa'],
  quickAmounts: [10, 25, 50, 100, 250, 500],
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      profile: null,
      preferences: defaultPreferences,
      stats: null,
      isLoading: false,

      fetchProfile: async () => {
        try {
          set({ isLoading: true })
          
          const response = await api.get('/user/profile')
          const profile = response.data.data
          
          set({ profile, isLoading: false })
        } catch (error: any) {
          set({ isLoading: false })
          
          const message = error.response?.data?.error || 'Erro ao carregar perfil'
          toast.error(message)
          
          throw error
        }
      },

      updateProfile: async (data: Partial<UserProfile>) => {
        try {
          set({ isLoading: true })
          
          const response = await api.put('/user/profile', data)
          const updatedProfile = response.data.data
          
          set({ profile: updatedProfile, isLoading: false })
          toast.success('Perfil atualizado com sucesso!')
        } catch (error: any) {
          set({ isLoading: false })
          
          const message = error.response?.data?.error || 'Erro ao atualizar perfil'
          toast.error(message)
          
          throw error
        }
      },

      updatePreferences: (preferences: Partial<UserPreferences>) => {
        set((state) => ({
          preferences: { ...state.preferences, ...preferences }
        }))
      },

      fetchStats: async () => {
        try {
          const response = await api.get('/user/stats')
          const stats = response.data.data
          
          set({ stats })
        } catch (error: any) {
          const message = error.response?.data?.error || 'Erro ao carregar estatísticas'
          console.error('Error fetching user stats:', message)
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      clearUserData: () => {
        set({
          profile: null,
          preferences: defaultPreferences,
          stats: null,
          isLoading: false,
        })
      },
    }),
    {
      name: 'user-storage',
      partialize: (state) => ({
        preferences: state.preferences,
      }),
    }
  )
)
