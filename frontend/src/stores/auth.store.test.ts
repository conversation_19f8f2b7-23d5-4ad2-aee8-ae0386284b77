import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useAuthStore } from './auth.store'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'

// Mock dependencies
vi.mock('@/lib/api', () => ({
  api: {
    post: vi.fn(),
    get: vi.fn(),
    defaults: {
      headers: {
        common: {}
      }
    }
  }
}))

vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  }
}))

// Mock localStorage for Zustand persist
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('useAuthStore', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    
    // Reset store state
    useAuthStore.setState({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useAuthStore())
      
      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('login', () => {
    it('should login successfully', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: '2024-01-01T00:00:00.000Z'
      }
      const mockToken = 'mock-jwt-token'

      const mockResponse = {
        data: {
          data: {
            user: mockUser,
            token: mockToken
          }
        }
      }

      vi.mocked(api.post).mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login('<EMAIL>', 'password123')
      })

      expect(api.post).toHaveBeenCalledWith('/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      })

      expect(result.current.user).toEqual(mockUser)
      expect(result.current.token).toBe(mockToken)
      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.isLoading).toBe(false)
      expect(api.defaults.headers.common['Authorization']).toBe(`Bearer ${mockToken}`)
      expect(toast.success).toHaveBeenCalledWith('Login realizado com sucesso!')
    })

    it('should handle login failure', async () => {
      const mockError = {
        response: {
          data: {
            error: 'Invalid credentials'
          }
        }
      }

      vi.mocked(api.post).mockRejectedValue(mockError)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        try {
          await result.current.login('<EMAIL>', 'wrongpassword')
        } catch (error) {
          // Expected to throw
        }
      })

      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(toast.error).toHaveBeenCalledWith('Invalid credentials')
    })

    it('should handle login failure with generic error', async () => {
      const mockError = new Error('Network error')

      vi.mocked(api.post).mockRejectedValue(mockError)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        try {
          await result.current.login('<EMAIL>', 'password123')
        } catch (error) {
          // Expected to throw
        }
      })

      expect(toast.error).toHaveBeenCalledWith('Erro ao fazer login')
    })

    it('should set loading state during login', async () => {
      let resolveLogin: (value: any) => void
      const loginPromise = new Promise((resolve) => {
        resolveLogin = resolve
      })

      vi.mocked(api.post).mockReturnValue(loginPromise)

      const { result } = renderHook(() => useAuthStore())

      // Start login
      act(() => {
        result.current.login('<EMAIL>', 'password123')
      })

      expect(result.current.isLoading).toBe(true)

      // Resolve login
      await act(async () => {
        resolveLogin!({
          data: {
            data: {
              user: { id: '1', email: '<EMAIL>', name: 'Test', createdAt: '2024-01-01' },
              token: 'token'
            }
          }
        })
        await loginPromise
      })

      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('logout', () => {
    it('should logout successfully', () => {
      const { result } = renderHook(() => useAuthStore())

      // Set initial authenticated state
      act(() => {
        useAuthStore.setState({
          user: { id: '1', email: '<EMAIL>', name: 'Test', createdAt: '2024-01-01' },
          token: 'mock-token',
          isAuthenticated: true,
        })
        api.defaults.headers.common['Authorization'] = 'Bearer mock-token'
      })

      act(() => {
        result.current.logout()
      })

      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(api.defaults.headers.common['Authorization']).toBeUndefined()
      expect(toast.success).toHaveBeenCalledWith('Logout realizado com sucesso!')
    })
  })

  describe('checkAuth', () => {
    it('should verify valid token', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: '2024-01-01T00:00:00.000Z'
      }

      const mockResponse = {
        data: {
          data: mockUser
        }
      }

      vi.mocked(api.get).mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      // Set token first
      act(() => {
        useAuthStore.setState({ token: 'valid-token' })
      })

      await act(async () => {
        await result.current.checkAuth()
      })

      expect(api.get).toHaveBeenCalledWith('/auth/me')
      expect(api.defaults.headers.common['Authorization']).toBe('Bearer valid-token')
      expect(result.current.user).toEqual(mockUser)
      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.isLoading).toBe(false)
    })

    it('should handle invalid token', async () => {
      const mockError = new Error('Unauthorized')

      vi.mocked(api.get).mockRejectedValue(mockError)

      const { result } = renderHook(() => useAuthStore())

      // Set initial state with token
      act(() => {
        useAuthStore.setState({
          token: 'invalid-token',
          user: { id: '1', email: '<EMAIL>', name: 'Test', createdAt: '2024-01-01' },
          isAuthenticated: true,
        })
      })

      await act(async () => {
        await result.current.checkAuth()
      })

      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(api.defaults.headers.common['Authorization']).toBeUndefined()
    })

    it('should not check auth when no token exists', async () => {
      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.checkAuth()
      })

      expect(api.get).not.toHaveBeenCalled()
    })

    it('should set loading state during auth check', async () => {
      let resolveAuth: (value: any) => void
      const authPromise = new Promise((resolve) => {
        resolveAuth = resolve
      })

      vi.mocked(api.get).mockReturnValue(authPromise)

      const { result } = renderHook(() => useAuthStore())

      // Set token first
      act(() => {
        useAuthStore.setState({ token: 'valid-token' })
      })

      // Start auth check
      act(() => {
        result.current.checkAuth()
      })

      expect(result.current.isLoading).toBe(true)

      // Resolve auth check
      await act(async () => {
        resolveAuth!({
          data: {
            data: { id: '1', email: '<EMAIL>', name: 'Test', createdAt: '2024-01-01' }
          }
        })
        await authPromise
      })

      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('setLoading', () => {
    it('should update loading state', () => {
      const { result } = renderHook(() => useAuthStore())

      act(() => {
        result.current.setLoading(true)
      })

      expect(result.current.isLoading).toBe(true)

      act(() => {
        result.current.setLoading(false)
      })

      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('persistence', () => {
    it('should persist auth state to localStorage', () => {
      const { result } = renderHook(() => useAuthStore())

      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: '2024-01-01T00:00:00.000Z'
      }

      act(() => {
        useAuthStore.setState({
          user: mockUser,
          token: 'mock-token',
          isAuthenticated: true,
        })
      })

      // Zustand persist should call localStorage.setItem
      expect(localStorageMock.setItem).toHaveBeenCalled()
    })

    it('should restore auth state from localStorage', () => {
      const persistedState = {
        user: {
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          createdAt: '2024-01-01T00:00:00.000Z'
        },
        token: 'persisted-token',
        isAuthenticated: true,
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        state: persistedState,
        version: 0
      }))

      // Create new store instance to trigger rehydration
      const { result } = renderHook(() => useAuthStore())

      // Note: In real tests, you might need to wait for rehydration
      // This is a simplified test
      expect(localStorageMock.getItem).toHaveBeenCalledWith('auth-storage')
    })
  })
})
