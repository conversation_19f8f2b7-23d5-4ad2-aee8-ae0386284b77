import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'

interface User {
  id: string
  email: string
  name: string
  createdAt: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  login: (_email: string, _password: string) => Promise<void>
  register: (_email: string, _password: string, _name: string) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
  initializeAuth: () => Promise<void>
  setLoading: (_loading: boolean) => void
  clearError: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true, // Start with loading true
      error: null,

      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null })

          const response = await api.post('/auth/login', {
            email,
            password,
          })

          const { token, user } = response.data.data

          // Set token in API headers
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })

          toast.success('Login realizado com sucesso!')
        } catch (error: any) {
          const message = error.response?.data?.error?.message || error.response?.data?.message || 'Erro ao fazer login'

          set({
            isLoading: false,
            error: message,
          })

          toast.error(message)
          throw error
        }
      },

      register: async (email: string, password: string, name: string) => {
        try {
          set({ isLoading: true, error: null })

          const response = await api.post('/auth/register', {
            email,
            password,
            name,
          })

          const { token, user } = response.data.data

          // Set token in API headers
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })

          toast.success('Conta criada com sucesso!')
        } catch (error: any) {
          const message = error.response?.data?.error?.message || error.response?.data?.message || 'Erro ao criar conta'

          set({
            isLoading: false,
            error: message,
          })

          toast.error(message)
          throw error
        }
      },

      logout: () => {
        // Remove token from API headers
        delete api.defaults.headers.common['Authorization']

        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        })

        toast.success('Logout realizado com sucesso!')
      },

      checkAuth: async () => {
        const { token } = get()

        if (!token) {
          set({ isLoading: false })
          return
        }

        try {
          set({ isLoading: true })

          // Set token in API headers
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`

          // Verify token with backend
          const response = await api.get('/auth/profile')
          const user = response.data.data

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
          })
        } catch (error) {
          // Token is invalid, clear auth state
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          })

          delete api.defaults.headers.common['Authorization']
        }
      },

      initializeAuth: async () => {
        const { token } = get()

        if (token) {
          // Set token in API headers immediately
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`

          // Check if token is still valid
          await get().checkAuth()
        } else {
          // No token, set loading to false
          set({ isLoading: false })
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      clearError: () => {
        set({ error: null })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
