import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type Theme = 'light' | 'dark' | 'system'
export type Currency = 'BRL' | 'USD' | 'EUR'
export type Language = 'pt-BR' | 'en-US'

interface NotificationSettings {
  email: boolean
  push: boolean
  transactionAlerts: boolean
  budgetAlerts: boolean
  monthlyReports: boolean
}

interface DisplaySettings {
  compactMode: boolean
  showDecimals: boolean
  groupTransactions: boolean
  defaultView: 'dashboard' | 'transactions' | 'budget'
}

interface SettingsState {
  // Theme and appearance
  theme: Theme
  currency: Currency
  language: Language
  
  // Notifications
  notifications: NotificationSettings
  
  // Display preferences
  display: DisplaySettings
  
  // Actions
  setTheme: (theme: Theme) => void
  setCurrency: (currency: Currency) => void
  setLanguage: (language: Language) => void
  updateNotifications: (notifications: Partial<NotificationSettings>) => void
  updateDisplay: (display: Partial<DisplaySettings>) => void
  resetSettings: () => void
}

const defaultNotifications: NotificationSettings = {
  email: true,
  push: true,
  transactionAlerts: true,
  budgetAlerts: true,
  monthlyReports: true,
}

const defaultDisplay: DisplaySettings = {
  compactMode: false,
  showDecimals: true,
  groupTransactions: false,
  defaultView: 'dashboard',
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      theme: 'system',
      currency: 'BRL',
      language: 'pt-BR',
      notifications: defaultNotifications,
      display: defaultDisplay,

      setTheme: (theme: Theme) => {
        set({ theme })
        
        // Apply theme to document
        const root = document.documentElement
        if (theme === 'dark') {
          root.classList.add('dark')
        } else if (theme === 'light') {
          root.classList.remove('dark')
        } else {
          // System theme
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
          if (prefersDark) {
            root.classList.add('dark')
          } else {
            root.classList.remove('dark')
          }
        }
      },

      setCurrency: (currency: Currency) => {
        set({ currency })
      },

      setLanguage: (language: Language) => {
        set({ language })
      },

      updateNotifications: (notifications: Partial<NotificationSettings>) => {
        set((state) => ({
          notifications: { ...state.notifications, ...notifications }
        }))
      },

      updateDisplay: (display: Partial<DisplaySettings>) => {
        set((state) => ({
          display: { ...state.display, ...display }
        }))
      },

      resetSettings: () => {
        set({
          theme: 'system',
          currency: 'BRL',
          language: 'pt-BR',
          notifications: defaultNotifications,
          display: defaultDisplay,
        })
      },
    }),
    {
      name: 'settings-storage',
      partialize: (state) => ({
        theme: state.theme,
        currency: state.currency,
        language: state.language,
        notifications: state.notifications,
        display: state.display,
      }),
    }
  )
)
