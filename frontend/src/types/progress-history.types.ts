export interface ProgressHistoryEntry {
  id: string
  goalId: string
  goalName: string
  previousAmount: number
  newAmount: number
  amountChanged: number
  operation: 'add' | 'subtract' | 'set'
  description?: string
  createdAt: string
}

export interface ProgressHistoryFilters {
  goalId?: string
  operation?: 'add' | 'subtract' | 'set'
  fromDate?: string
  toDate?: string
  page?: number
  limit?: number
  sortBy?: 'createdAt' | 'amountChanged'
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedProgressHistoryResponse {
  data: ProgressHistoryEntry[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface UpdateProgressHistoryData {
  description: string
}

export interface ProgressHistoryResponse {
  success: boolean
  data: ProgressHistoryEntry
  message: string
}

export interface ProgressHistoryListResponse {
  success: boolean
  data: ProgressHistoryEntry[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  message: string
}
