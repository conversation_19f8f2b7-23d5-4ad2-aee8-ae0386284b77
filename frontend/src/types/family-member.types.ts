// Base Family Member interface
export interface FamilyMember {
  id: string
  name: string
  color: string
  avatar?: string
  archived: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string | null
}

// Create Family Member request
export interface CreateFamilyMemberRequest {
  name: string
  color: string
  avatar?: string
}

// Update Family Member request
export interface UpdateFamilyMemberRequest {
  name?: string
  color?: string
  avatar?: string
}

// Family Member filters for listing
export interface FamilyMemberFilters {
  name?: string
  includeArchived?: boolean
  includeDeleted?: boolean
  page?: number
  limit?: number
}

// Paginated response
export interface PaginatedFamilyMembersResponse {
  data: FamilyMember[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Single Family Member response
export interface FamilyMemberResponse {
  success: boolean
  data: FamilyMember
  message?: string
}

// Family Members list response
export interface FamilyMembersListResponse {
  success: boolean
  data: PaginatedFamilyMembersResponse
  message?: string
}

// Family Member stats
export interface FamilyMemberStats {
  totalMembers: number
  activeMembers: number
  archivedMembers: number
}

// Family Member with stats
export interface FamilyMemberWithStats extends FamilyMember {
  stats?: {
    totalTransactions?: number
    totalExpenses?: number
    totalIncome?: number
    averageMonthlyExpenses?: number
  }
}

// Error response
export interface FamilyMemberError {
  success: false
  error: {
    message: string
    code: string
    details?: any
  }
}

// API Response types
export type FamilyMemberApiResponse = FamilyMemberResponse | FamilyMemberError
export type FamilyMembersApiResponse = FamilyMembersListResponse | FamilyMemberError

// Form data for family member
export interface FamilyMemberFormData {
  name: string
  color: string
  avatar?: File | string
}

// Color options for family members
export const FAMILY_MEMBER_COLORS = [
  '#ef4444', // red
  '#f97316', // orange
  '#eab308', // yellow
  '#22c55e', // green
  '#06b6d4', // cyan
  '#3b82f6', // blue
  '#8b5cf6', // violet
  '#ec4899', // pink
  '#64748b', // slate
  '#78716c', // stone
] as const

export type FamilyMemberColor = typeof FAMILY_MEMBER_COLORS[number]
