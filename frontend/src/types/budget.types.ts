// Base Budget interface
export interface Budget {
  id: string
  plannedAmount: number
  spentAmount: number
  remainingAmount: number
  month: number
  year: number
  categoryId: string
  familyMemberId?: string | null
  progress: number
  status: 'under_budget' | 'on_track' | 'over_budget'
  createdAt: string
  updatedAt: string
  deletedAt?: string | null
  // Related data
  category: {
    id: string
    name: string
    color?: string
    parent?: {
      id: string
      name: string
      color?: string
    }
  }
  familyMember?: {
    id: string
    name: string
    avatar?: string
  } | null
}

// Create Budget request
export interface CreateBudgetRequest {
  plannedAmount: number
  month: number
  year: number
  categoryId: string
  familyMemberId?: string
}

// Update Budget request
export interface UpdateBudgetRequest {
  plannedAmount?: number
  month?: number
  year?: number
  categoryId?: string
  familyMemberId?: string | null
}

// Budget filters for listing
export interface BudgetFilters {
  categoryId?: string
  familyMemberId?: string
  month?: number
  year?: number
  page?: number
  limit?: number
  includeProgress?: boolean
  sortBy?: 'plannedAmount' | 'month' | 'year' | 'createdAt' | 'category' | 'familyMember'
  sortOrder?: 'asc' | 'desc'
}

// Budget stats
export interface BudgetStats {
  totalBudgets: number
  totalPlanned: number
  totalSpent: number
  totalRemaining: number
  overBudgetCount: number
  onTrackCount: number
  underBudgetCount: number
  averageProgress: number
}

// Budget progress details
export interface BudgetProgress {
  budgetId: string
  plannedAmount: number
  spentAmount: number
  remainingAmount: number
  progress: number
  status: 'under_budget' | 'on_track' | 'over_budget'
  daysRemaining: number
  dailyBudgetRemaining: number
  projectedTotal: number
  isOverBudget: boolean
  alertLevel: 'low' | 'medium' | 'high' | null
}

// Budget report data
export interface BudgetReport {
  totalBudgeted: number
  totalSpent: number
  totalRemaining: number
  overallProgress: number
  categories: Array<{
    categoryId: string
    categoryName: string
    budgeted: number
    spent: number
    remaining: number
    progress: number
    status: 'under_budget' | 'on_track' | 'over_budget'
  }>
  alerts: Array<{
    type: 'OVER_BUDGET' | 'NEAR_LIMIT'
    categoryId: string
    categoryName: string
    message: string
    severity: 'low' | 'medium' | 'high'
  }>
  monthlyComparison?: {
    currentMonth: {
      month: number
      year: number
      totalBudgeted: number
      totalSpent: number
    }
    previousMonth: {
      month: number
      year: number
      totalBudgeted: number
      totalSpent: number
    }
    variance: {
      budgetedVariance: number
      spentVariance: number
      percentageChange: number
    }
  }
}

// API Response types
export interface BudgetResponse {
  success: boolean
  data: Budget
  message?: string
}

export interface BudgetsResponse {
  success: boolean
  data: Budget[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  message?: string
}

export interface BudgetStatsResponse {
  success: boolean
  data: BudgetStats
  message?: string
}

export interface BudgetReportResponse {
  success: boolean
  data: BudgetReport
  message?: string
}

export interface BudgetProgressResponse {
  success: boolean
  data: BudgetProgress
  message?: string
}

// Form Data Types for React Hook Form
export interface BudgetFormData {
  plannedAmount: string // String for form input, converted to number
  month: number
  year: number
  categoryId: string
  familyMemberId: string
}

// Utility Types
export type BudgetWithProgress = Budget & {
  progressDetails: BudgetProgress
}

export type BudgetStatus = 'under_budget' | 'on_track' | 'over_budget'

export type BudgetSortField = 'plannedAmount' | 'month' | 'year' | 'createdAt' | 'category' | 'familyMember'

export type BudgetSortOrder = 'asc' | 'desc'

// Grouped Budget types for parent category aggregation
export interface GroupedBudget {
  id: string // For grouped: parent category ID, for individual: budget ID
  type: 'individual' | 'grouped'
  // Common fields (aggregated for grouped budgets)
  plannedAmount: number
  spentAmount: number
  remainingAmount: number
  month: number
  year: number
  progress: number
  status: 'under_budget' | 'on_track' | 'over_budget'
  // Category information
  category: {
    id: string
    name: string
    color?: string
    parent?: {
      id: string
      name: string
      color?: string
    }
  }
  // Family member (null for grouped budgets with mixed members)
  familyMember?: {
    id: string
    name: string
    avatar?: string
  } | null
  // Individual budget data (only for type: 'individual')
  budget?: Budget
  // Child budgets data (only for type: 'grouped')
  childBudgets?: Budget[]
  // Metadata
  createdAt: string
  updatedAt: string
}

// Budget alert types
export interface BudgetAlert {
  id: string
  budgetId: string
  type: 'OVER_BUDGET' | 'NEAR_LIMIT' | 'MONTHLY_SUMMARY'
  severity: 'low' | 'medium' | 'high'
  message: string
  threshold: number
  currentValue: number
  isRead: boolean
  createdAt: string
}

// Pagination helper
export interface PaginatedBudgetsResponse {
  data: Budget[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}
