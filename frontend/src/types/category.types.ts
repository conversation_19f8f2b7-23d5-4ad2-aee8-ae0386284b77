// Base Category interface
export interface Category {
  id: string
  name: string
  color?: string
  parentId?: string
  archived: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string | null
  version: number
  // Hierarchy information
  level: number // 0 for parent categories, 1 for subcategories
  hasChildren: boolean
  // Related data
  parent?: {
    id: string
    name: string
    color?: string
  }
  children?: Array<{
    id: string
    name: string
    color?: string
  }>
  // Usage statistics
  transactionCount?: number
  budgetCount?: number
}

// Create Category request
export interface CreateCategoryRequest {
  name: string
  color?: string
  parentId?: string
}

// Update Category request
export interface UpdateCategoryRequest {
  name?: string
  color?: string
  parentId?: string | null
}

// Archive Category request
export interface ArchiveCategoryRequest {
  archived: boolean
}

// Category filters for listing
export interface CategoryFilters {
  name?: string
  parentId?: string | null
  includeChildren?: boolean
  onlyParents?: boolean
  onlyChildren?: boolean
  includeArchived?: boolean
  includeDeleted?: boolean
  page?: number
  limit?: number
}

// Category tree node for hierarchical display
export interface CategoryTreeNode {
  id: string
  name: string
  color?: string
  archived: boolean
  level: number
  transactionCount?: number
  budgetCount?: number
  children: CategoryTreeNode[]
  // UI state for tree view
  expanded?: boolean
  selected?: boolean
}

// Paginated response
export interface PaginatedCategoriesResponse {
  data: Category[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Single Category response
export interface CategoryResponse {
  success: boolean
  data: Category
  message?: string
}

// Categories list response
export interface CategoriesListResponse {
  success: boolean
  data: PaginatedCategoriesResponse
  message?: string
}

// Category tree response
export interface CategoryTreeResponse {
  success: boolean
  data: CategoryTreeNode[]
  message?: string
}

// Category stats
export interface CategoryStats {
  totalCategories: number
  parentCategories: number
  subcategories: number
  archivedCategories: number
  categoriesWithTransactions: number
  categoriesWithBudgets: number
}

// Category with extended stats
export interface CategoryWithStats extends Category {
  stats?: {
    totalTransactions?: number
    totalBudgets?: number
    monthlyTransactionAverage?: number
    lastTransactionDate?: string
    totalSpent?: number
    budgetUtilization?: number
  }
}

// Category hierarchy validation
export interface CategoryHierarchyValidation {
  isValid: boolean
  errors: string[]
  maxDepthReached: boolean
  circularReference: boolean
}

// Error response
export interface CategoryError {
  success: false
  error: {
    message: string
    code: string
    details?: any
  }
}

// API Response types
export type CategoryApiResponse = CategoryResponse | CategoryError
export type CategoriesApiResponse = CategoriesListResponse | CategoryError
export type CategoryTreeApiResponse = CategoryTreeResponse | CategoryError

// Form data for category
export interface CategoryFormData {
  name: string
  color?: string
  parentId?: string
}

// Category selector option
export interface CategoryOption {
  value: string
  label: string
  color?: string
  level: number
  disabled?: boolean
}

// Bulk operations
export interface BulkCategoryOperation {
  categoryIds: string[]
  operation: 'archive' | 'unarchive' | 'delete'
}

export interface BulkCategoryResponse {
  success: boolean
  processed: number
  failed: number
  errors?: Array<{
    categoryId: string
    error: string
  }>
  message?: string
}

// Category view modes
export type CategoryViewMode = 'table' | 'tree' | 'grid'

// Category sort options
export type CategorySortField = 'name' | 'createdAt' | 'updatedAt' | 'transactionCount'
export type CategorySortOrder = 'asc' | 'desc'

export interface CategorySort {
  field: CategorySortField
  order: CategorySortOrder
}

// Color options for categories (extended palette)
export const CATEGORY_COLORS = [
  '#ef4444', // red
  '#f97316', // orange
  '#eab308', // yellow
  '#22c55e', // green
  '#06b6d4', // cyan
  '#3b82f6', // blue
  '#8b5cf6', // violet
  '#ec4899', // pink
  '#64748b', // slate
  '#78716c', // stone
  '#dc2626', // red-600
  '#ea580c', // orange-600
  '#ca8a04', // yellow-600
  '#16a34a', // green-600
  '#0891b2', // cyan-600
  '#2563eb', // blue-600
  '#7c3aed', // violet-600
  '#db2777', // pink-600
  '#475569', // slate-600
  '#57534e', // stone-600
] as const

export type CategoryColor = typeof CATEGORY_COLORS[number]

// Default category icons by common category names
export const CATEGORY_ICONS: Record<string, string> = {
  'Alimentação': '🍽️',
  'Transporte': '🚗',
  'Moradia': '🏠',
  'Saúde': '🏥',
  'Educação': '📚',
  'Lazer': '🎮',
  'Vestuário': '👕',
  'Investimentos': '📈',
  'Salário': '💰',
  'Freelance': '💻',
  'Outros': '📦',
} as const

// Category validation rules
export const CATEGORY_VALIDATION = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 100,
  MAX_HIERARCHY_DEPTH: 2, // Only 2 levels: parent and child
  COLOR_REGEX: /^#[0-9A-Fa-f]{6}$/,
} as const
