// Base types
export interface BaseEntity {
  id: string
  createdAt: string
  updatedAt: string
}

// User types
export interface User extends BaseEntity {
  name: string
  email: string
  avatar?: string
  preferences: UserPreferences
}

export interface UserPreferences {
  currency: string
  locale: string
  theme: 'light' | 'dark' | 'system'
  dateFormat: string
  notifications: NotificationSettings
}

export interface NotificationSettings {
  email: boolean
  push: boolean
  transactions: boolean
  budgets: boolean
  reports: boolean
}

// Account types
export interface Account extends BaseEntity {
  name: string
  type: AccountType
  currency: string
  balance: number
  initialBalance: number
  description?: string
  isActive: boolean
  userId: string
}

export type AccountType = 'CHECKING' | 'SAVINGS' | 'CREDIT_CARD' | 'INVESTMENT' | 'CASH'

// Category types
export interface Category extends BaseEntity {
  name: string
  type: CategoryType
  color?: string
  description?: string
  parentId?: string
  parent?: Category
  children?: Category[]
  userId: string
}

export type CategoryType = 'INCOME' | 'EXPENSE'

// Transaction types
export interface Transaction extends BaseEntity {
  description: string
  amount: number
  type: TransactionType
  transactionDate: string
  notes?: string
  accountId: string
  account: Account
  destinationAccountId?: string
  destinationAccount?: Account
  categoryId?: string
  category?: Category
  userId: string
  status: TransactionStatus
}

export type TransactionType = 'INCOME' | 'EXPENSE' | 'TRANSFER'
export type TransactionStatus = 'PENDING' | 'COMPLETED' | 'CANCELLED' | 'FAILED'

// Future Transaction types
export interface FutureTransaction extends BaseEntity {
  description: string
  amount: number
  type: TransactionType
  transactionDate: string
  notes?: string
  accountId: string
  account: Account
  destinationAccountId?: string
  destinationAccount?: Account
  categoryId?: string
  category?: Category
  userId: string
  recurrenceRule?: string
  endDate?: string
  isProcessed: boolean
}

// Recurring Transaction types
export interface RecurringTransaction extends BaseEntity {
  description: string
  amount: number
  type: TransactionType
  frequency: RecurrenceFrequency
  startDate: string
  endDate?: string
  notes?: string
  accountId: string
  account: Account
  destinationAccountId?: string
  destinationAccount?: Account
  categoryId?: string
  category?: Category
  userId: string
  isActive: boolean
  lastProcessedDate?: string
  nextProcessDate: string
}

export type RecurrenceFrequency = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY'

// API types
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  errors?: string[]
}

export interface PaginatedResponse<T = any> {
  success: boolean
  data: {
    items: T[]
    pagination: Pagination
  }
  message?: string
  errors?: string[]
}

export interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// Filter types
export interface TransactionFilters {
  startDate?: string
  endDate?: string
  accountId?: string
  categoryId?: string
  type?: TransactionType
  minAmount?: number
  maxAmount?: number
  search?: string
  page?: number
  limit?: number
}

export interface DateRange {
  startDate: string
  endDate: string
}

// Dashboard types
export interface DashboardStats {
  totalBalance: number
  monthlyIncome: number
  monthlyExpenses: number
  monthlyBalance: number
  accountsCount: number
  transactionsCount: number
  categoriesCount: number
}

export interface MonthlyStats {
  month: string
  income: number
  expenses: number
  balance: number
}

export interface CategoryStats {
  categoryId: string
  categoryName: string
  amount: number
  percentage: number
  color?: string
}

export interface AccountBalance {
  accountId: string
  accountName: string
  balance: number
  currency: string
  type: AccountType
}

// Chart types
export interface ChartData {
  name: string
  value: number
  color?: string
}

export interface TimeSeriesData {
  date: string
  income: number
  expenses: number
  balance: number
}

// Form types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea'
  placeholder?: string
  required?: boolean
  options?: { value: string; label: string }[]
  validation?: any
}

// UI types
export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

export interface TabItem {
  id: string
  label: string
  content: React.ReactNode
  disabled?: boolean
}

export interface MenuItem {
  id: string
  label: string
  icon?: React.ReactNode
  href?: string
  onClick?: () => void
  disabled?: boolean
  children?: MenuItem[]
}

// Theme types
export interface ThemeColors {
  primary: string
  secondary: string
  success: string
  warning: string
  error: string
  info: string
}

// Error types
export interface AppError {
  code: string
  message: string
  details?: any
}

// Loading states
export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

// Modal types
export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

// Toast types
export interface ToastMessage {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
}

// File types
export interface FileUpload {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

// Search types
export interface SearchResult<T = any> {
  items: T[]
  total: number
  query: string
  filters?: Record<string, any>
}

// Export/Import types
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf'
  dateRange?: DateRange
  accounts?: string[]
  categories?: string[]
  includeHeaders?: boolean
}

export interface ImportResult {
  success: boolean
  imported: number
  failed: number
  errors?: string[]
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// Event types
export interface AppEvent {
  type: string
  payload?: any
  timestamp: number
}

// Storage types
export interface StorageItem<T = any> {
  value: T
  timestamp: number
  expiry?: number
}
