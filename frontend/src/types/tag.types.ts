/**
 * Tag-related type definitions
 * Based on backend Tag model and API responses
 */

export interface Tag {
  id: string
  name: string
  color: string
  isArchived: boolean
  usageCount: number
  createdAt: string
  updatedAt: string
  version: number
}

export interface CreateTagData {
  name: string
  color: string
}

export interface UpdateTagData {
  name?: string
  color?: string
}

export interface TagFilters {
  search?: string
  includeArchived?: boolean
  page?: number
  limit?: number
  sortBy?: 'name' | 'usageCount' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface TagListResponse {
  data: Tag[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface TagStats {
  total: number
  active: number
  archived: number
  mostUsed: Tag[]
}

// Form validation types
export interface TagFormData {
  name: string
  color: string
}

// UI state types
export interface TagModalState {
  isOpen: boolean
  mode: 'create' | 'edit'
  tag?: Tag
}
