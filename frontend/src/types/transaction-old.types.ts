export interface Transaction {
  id: string
  description: string
  amount: number
  transactionDate: string
  type: TransactionType
  accountId: string
  categoryId?: string
  parentTransactionId?: string
  installmentNumber?: number
  totalInstallments?: number
  exchangeRate?: number
  destinationAccountId?: string
  isFuture: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string

  // Related data
  account: {
    id: string
    name: string
    type: string
    currency: string
  }
  category?: {
    id: string
    name: string
    color: string
  }
  destinationAccount?: {
    id: string
    name: string
    type: string
    currency: string
  }
  parentTransaction?: {
    id: string
    description: string
  }
  installments: {
    id: string
    description: string
    amount: number
    transactionDate: string
    installmentNumber: number
  }[]
  tags?: {
    id: string
    name: string
    color: string
  }[]
  members?: {
    id: string
    name: string
    color: string
  }[]
}

export type TransactionType = 'INCOME' | 'EXPENSE' | 'TRANSFER'

export interface CreateTransactionData {
  description: string
  amount: number
  transactionDate: string | Date
  type: TransactionType
  accountId: string
  categoryId?: string
  destinationAccountId?: string
  exchangeRate?: number
  installmentNumber?: number
  totalInstallments?: number
  parentTransactionId?: string
  isFuture?: boolean
  tagIds?: string[]
  familyMemberIds: string[]
}

export interface UpdateTransactionData {
  description?: string
  amount?: number
  transactionDate?: string | Date
  categoryId?: string
  destinationAccountId?: string
  exchangeRate?: number
  isFuture?: boolean
  tagIds?: string[]
  familyMemberIds?: string[]
}

export interface TransactionFilters {
  description?: string
  type?: TransactionType
  accountId?: string
  categoryId?: string
  familyMemberId?: string
  tagId?: string
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
  isFuture?: boolean
  includeDeleted?: boolean
  page?: number
  limit?: number
  sortBy?: 'transactionDate' | 'amount' | 'description' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface TransactionSummary {
  totalIncome: number
  totalExpense: number
  totalTransfer: number
  netAmount: number
  transactionCount: number
}

export interface PaginatedTransactionsResponse {
  data: Transaction[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  summary: TransactionSummary
}

export interface TransactionStats {
  totalTransactions: number
  totalIncome: number
  totalExpense: number
  netAmount: number
  averageTransaction: number
  thisMonthTransactions: number
  lastMonthTransactions: number
  growthPercentage: number
}

// Form validation types
export interface TransactionFormData {
  description: string
  amount: string
  transactionDate: string
  type: TransactionType
  accountId: string
  categoryId: string
  destinationAccountId: string
  exchangeRate: string
  installmentNumber: string
  totalInstallments: string
  isFuture: boolean
  tagIds: string[]
  familyMemberIds: string[]
}

// Constants
export const TRANSACTION_TYPES = {
  INCOME: 'INCOME',
  EXPENSE: 'EXPENSE',
  TRANSFER: 'TRANSFER'
} as const

export const TRANSACTION_TYPE_LABELS = {
  INCOME: 'Receita',
  EXPENSE: 'Despesa',
  TRANSFER: 'Transferência'
} as const

export const TRANSACTION_TYPE_COLORS = {
  INCOME: 'text-green-600',
  EXPENSE: 'text-red-600',
  TRANSFER: 'text-blue-600'
} as const

export const SORT_OPTIONS = [
  { value: 'transactionDate', label: 'Data da Transação' },
  { value: 'amount', label: 'Valor' },
  { value: 'description', label: 'Descrição' },
  { value: 'createdAt', label: 'Data de Criação' }
] as const

export const SORT_ORDER_OPTIONS = [
  { value: 'desc', label: 'Decrescente' },
  { value: 'asc', label: 'Crescente' }
] as const
