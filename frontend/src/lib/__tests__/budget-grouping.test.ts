import { describe, it, expect } from 'vitest'
import { groupBudgetsByParentCategory, validateBudgetCreation } from '../budget-grouping'
import type { Budget } from '@/types/budget.types'

// Mock budget data for testing
const createMockBudget = (
  id: string,
  categoryName: string,
  parentCategory?: { id: string; name: string },
  plannedAmount = 1000,
  spentAmount = 500,
  familyMemberId?: string
): Budget => ({
  id,
  plannedAmount,
  spentAmount,
  remainingAmount: plannedAmount - spentAmount,
  month: 1,
  year: 2024,
  categoryId: `cat-${id}`,
  familyMemberId,
  progress: (spentAmount / plannedAmount) * 100,
  status: spentAmount > plannedAmount ? 'over_budget' : spentAmount >= plannedAmount * 0.8 ? 'on_track' : 'under_budget',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  category: {
    id: `cat-${id}`,
    name: categoryName,
    parent: parentCategory
  },
  familyMember: familyMemberId ? {
    id: familyMemberId,
    name: `Member ${familyMemberId}`
  } : null
})

describe('groupBudgetsByParentCategory', () => {
  it('should return empty array for empty input', () => {
    const result = groupBudgetsByParentCategory([])
    expect(result).toEqual([])
  })

  it('should keep individual parent category budgets as individual', () => {
    const budgets = [
      createMockBudget('1', 'Transportes'),
      createMockBudget('2', 'Alimentação')
    ]

    const result = groupBudgetsByParentCategory(budgets)

    expect(result).toHaveLength(2)
    expect(result[0].type).toBe('individual')
    expect(result[1].type).toBe('individual')
    // Results are sorted alphabetically, so Alimentação comes first
    expect(result[0].category.name).toBe('Alimentação')
    expect(result[1].category.name).toBe('Transportes')
  })

  it('should keep single child category budget as individual', () => {
    const budgets = [
      createMockBudget('1', 'Combustível', { id: 'parent-1', name: 'Transportes' })
    ]

    const result = groupBudgetsByParentCategory(budgets)

    expect(result).toHaveLength(1)
    expect(result[0].type).toBe('individual')
    expect(result[0].category.name).toBe('Combustível')
  })

  it('should group multiple child category budgets under same parent', () => {
    const parentCategory = { id: 'parent-1', name: 'Transportes' }
    const budgets = [
      createMockBudget('1', 'Combustível', parentCategory, 800, 400),
      createMockBudget('2', 'Uber', parentCategory, 200, 150)
    ]

    const result = groupBudgetsByParentCategory(budgets)

    expect(result).toHaveLength(1)
    expect(result[0].type).toBe('grouped')
    expect(result[0].category.name).toBe('Transportes')
    expect(result[0].plannedAmount).toBe(1000) // 800 + 200
    expect(result[0].spentAmount).toBe(550) // 400 + 150
    expect(result[0].childBudgets).toHaveLength(2)
  })

  it('should calculate correct status for grouped budgets', () => {
    const parentCategory = { id: 'parent-1', name: 'Transportes' }

    // Test over budget scenario - need higher total spent amount
    const overBudgetBudgets = [
      createMockBudget('1', 'Combustível', parentCategory, 500, 600), // Over budget individually
      createMockBudget('2', 'Uber', parentCategory, 300, 350) // Over budget individually
    ]

    const overResult = groupBudgetsByParentCategory(overBudgetBudgets)
    expect(overResult[0].status).toBe('over_budget')

    // Test on track scenario
    const onTrackBudgets = [
      createMockBudget('3', 'Combustível', parentCategory, 500, 400), // 80%
      createMockBudget('4', 'Uber', parentCategory, 300, 250) // 83%
    ]

    const onTrackResult = groupBudgetsByParentCategory(onTrackBudgets)
    expect(onTrackResult[0].status).toBe('on_track')

    // Test under budget scenario
    const underBudgetBudgets = [
      createMockBudget('5', 'Combustível', parentCategory, 500, 200), // 40%
      createMockBudget('6', 'Uber', parentCategory, 300, 100) // 33%
    ]

    const underResult = groupBudgetsByParentCategory(underBudgetBudgets)
    expect(underResult[0].status).toBe('under_budget')
  })

  it('should handle mixed parent and child budgets correctly', () => {
    const parentCategory = { id: 'parent-1', name: 'Transportes' }
    const budgets = [
      createMockBudget('1', 'Alimentação'), // Parent category
      createMockBudget('2', 'Combustível', parentCategory), // Child category (single)
      createMockBudget('3', 'Uber', parentCategory), // Child category (will be grouped)
      createMockBudget('4', 'Lazer') // Another parent category
    ]

    const result = groupBudgetsByParentCategory(budgets)

    expect(result).toHaveLength(3) // Alimentação, Transportes (grouped), Lazer
    
    const groupedTransport = result.find(b => b.category.name === 'Transportes')
    expect(groupedTransport?.type).toBe('grouped')
    expect(groupedTransport?.childBudgets).toHaveLength(2)

    const individualBudgets = result.filter(b => b.type === 'individual')
    expect(individualBudgets).toHaveLength(2)
  })

  it('should sort results by status priority', () => {
    const parentCategory = { id: 'parent-1', name: 'Transportes' }
    const budgets = [
      createMockBudget('1', 'Alimentação', undefined, 1000, 200), // under_budget
      createMockBudget('2', 'Combustível', parentCategory, 500, 600), // over_budget individually
      createMockBudget('3', 'Uber', parentCategory, 300, 350), // over_budget individually (grouped total will be over)
      createMockBudget('4', 'Lazer', undefined, 800, 650) // on_track
    ]

    const result = groupBudgetsByParentCategory(budgets)

    expect(result[0].status).toBe('over_budget') // Transportes grouped
    expect(result[1].status).toBe('on_track') // Lazer
    expect(result[2].status).toBe('under_budget') // Alimentação
  })
})

describe('validateBudgetCreation', () => {
  const existingBudgets = [
    createMockBudget('1', 'Combustível', { id: 'parent-1', name: 'Transportes' }),
    createMockBudget('2', 'Alimentação')
  ]

  it('should allow creating budget for new category', () => {
    const result = validateBudgetCreation('new-category', undefined, existingBudgets, 1, 2024)
    expect(result.isValid).toBe(true)
  })

  it('should prevent creating parent budget when child budgets exist', () => {
    const result = validateBudgetCreation('parent-1', undefined, existingBudgets, 1, 2024)
    expect(result.isValid).toBe(false)
    expect(result.error).toContain('categorias filhas')
  })

  it('should prevent creating child budget when parent budget exists', () => {
    const budgetsWithParent = [
      createMockBudget('1', 'Transportes') // Parent category budget with categoryId 'cat-1'
    ]

    // Try to create child budget for parent category 'cat-1'
    const result = validateBudgetCreation('child-category', 'cat-1', budgetsWithParent, 1, 2024)
    expect(result.isValid).toBe(false)
    expect(result.error).toContain('categoria pai')
  })

  it('should allow creating additional child budgets when other child budgets exist', () => {
    const result = validateBudgetCreation('new-child', 'parent-1', existingBudgets, 1, 2024)
    expect(result.isValid).toBe(true)
  })

  it('should consider family member when validating', () => {
    const budgetsWithMember = [
      createMockBudget('1', 'Transportes', undefined, 1000, 500, 'member-1') // categoryId will be 'cat-1'
    ]

    // Different family member should be allowed
    const result1 = validateBudgetCreation('child-category', 'cat-1', budgetsWithMember, 1, 2024, 'member-2')
    expect(result1.isValid).toBe(true)

    // Same family member should be blocked
    const result2 = validateBudgetCreation('child-category', 'cat-1', budgetsWithMember, 1, 2024, 'member-1')
    expect(result2.isValid).toBe(false)
  })

  it('should consider period when validating', () => {
    // Different month should be allowed
    const result1 = validateBudgetCreation('child-category', 'parent-1', existingBudgets, 2, 2024)
    expect(result1.isValid).toBe(true)
    
    // Different year should be allowed
    const result2 = validateBudgetCreation('child-category', 'parent-1', existingBudgets, 1, 2025)
    expect(result2.isValid).toBe(true)
  })
})
