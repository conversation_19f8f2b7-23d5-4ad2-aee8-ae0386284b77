# Budget Grouping System

## Overview

The Budget Grouping System automatically organizes budgets by parent categories when multiple child category budgets exist for the same parent category. This provides a cleaner, more organized view of budget data while maintaining the ability to manage individual category budgets.

## Core Concepts

### Budget Types

- **Individual Budget**: A budget for a single category (either parent or child)
- **Grouped Budget**: An aggregated view of multiple child category budgets under the same parent category

### Grouping Logic

1. **Single Child Category**: Remains as individual budget
2. **Multiple Child Categories**: Automatically grouped under parent category
3. **Parent Category Budgets**: Always remain individual
4. **Mixed Scenarios**: Handled intelligently with proper separation

## API Reference

### `groupBudgetsByParentCategory(budgets: Budget[]): GroupedBudget[]`

Main function that processes an array of budgets and returns grouped results.

**Parameters:**
- `budgets`: Array of Budget objects to be processed

**Returns:**
- Array of GroupedBudget objects (individual or grouped)

**Performance:**
- Optimized for datasets up to 1000+ budgets
- Uses Map-based grouping for O(n) complexity
- Automatic algorithm selection based on dataset size

### `validateBudgetCreation(categoryId, parentId, existingBudgets, month, year, familyMemberId?)`

Validates if creating a new budget would conflict with existing budgets.

**Parameters:**
- `categoryId`: ID of the category for the new budget
- `parentId`: ID of the parent category (if applicable)
- `existingBudgets`: Array of existing budgets
- `month`: Budget month
- `year`: Budget year
- `familyMemberId`: Optional family member ID

**Returns:**
- `{ isValid: boolean, error?: string }`

**Validation Rules:**
1. Cannot create parent budget when child budgets exist
2. Cannot create child budget when parent budget exists
3. Multiple child budgets for same parent are allowed
4. Different periods (month/year) are allowed
5. Different family members are allowed

## Component Integration

### BudgetCard Component

The `BudgetCard` component automatically detects budget type and renders accordingly:

```tsx
// Individual Budget
<BudgetCard budget={individualBudget} />

// Grouped Budget (with accordion)
<BudgetCard budget={groupedBudget} />
```

**Features:**
- **Visual Indicators**: Different icons for individual vs grouped budgets
- **Accordion Interface**: Expandable details for grouped budgets
- **Individual Actions**: Edit/delete actions for each child budget
- **Accessibility**: Full ARIA support and keyboard navigation
- **Animations**: Smooth expand/collapse transitions

### BudgetsList Component

Automatically processes budgets through grouping logic:

```tsx
const processedBudgets = useMemo(() => {
  const groupedBudgets = groupBudgetsByParentCategory(budgets)
  // Apply search filters
  return filteredResults
}, [budgets, searchTerm])
```

## Data Structures

### GroupedBudget Interface

```typescript
interface GroupedBudget {
  id: string                    // Budget ID or parent category ID
  type: 'individual' | 'grouped'
  plannedAmount: number         // Sum for grouped budgets
  spentAmount: number          // Sum for grouped budgets
  remainingAmount: number      // Calculated remaining
  progress: number             // Overall progress percentage
  status: BudgetStatus         // Calculated overall status
  category: CategoryInfo       // Parent category for grouped
  familyMember?: FamilyMember  // Common member or null
  budget?: Budget              // Original budget (individual only)
  childBudgets?: Budget[]      // Child budgets (grouped only)
  createdAt: string
  updatedAt: string
}
```

### Status Calculation

For grouped budgets, status is calculated based on overall progress:

- **over_budget**: Progress > 100%
- **on_track**: Progress >= 80% and <= 100%
- **under_budget**: Progress < 80%

## Usage Examples

### Basic Grouping

```typescript
import { groupBudgetsByParentCategory } from '@/lib/budget-grouping'

const budgets = [
  { category: { name: 'Combustível', parent: { name: 'Transportes' } } },
  { category: { name: 'Uber', parent: { name: 'Transportes' } } },
  { category: { name: 'Alimentação' } }
]

const grouped = groupBudgetsByParentCategory(budgets)
// Result: [
//   { type: 'grouped', category: { name: 'Transportes' }, childBudgets: [...] },
//   { type: 'individual', category: { name: 'Alimentação' } }
// ]
```

### Validation

```typescript
import { validateBudgetCreation } from '@/lib/budget-grouping'

const validation = validateBudgetCreation(
  'new-category-id',
  'parent-category-id',
  existingBudgets,
  1, // January
  2024,
  'family-member-id'
)

if (!validation.isValid) {
  console.error(validation.error)
}
```

## Performance Considerations

### Algorithm Selection

- **Small datasets (≤10 budgets)**: Simple algorithm with minimal overhead
- **Large datasets (>10 budgets)**: Optimized algorithm with:
  - Single-pass processing
  - Pre-allocated arrays
  - Map-based grouping
  - Optimized sorting

### Memory Usage

- Efficient Map-based grouping reduces memory allocation
- Reuses existing budget objects where possible
- Minimal object creation for grouped budgets

### Caching

React Query integration ensures:
- Automatic cache invalidation on budget changes
- Optimistic updates for better UX
- Background refetching for data consistency

## Accessibility Features

### ARIA Support

- **Accordion**: Proper `aria-expanded`, `aria-controls` attributes
- **Regions**: Semantic regions for screen readers
- **Labels**: Descriptive labels for all interactive elements
- **Live Regions**: Status updates announced to screen readers

### Keyboard Navigation

- **Tab Navigation**: All interactive elements are focusable
- **Enter/Space**: Activate accordion toggles
- **Escape**: Close expanded accordions
- **Arrow Keys**: Navigate between child budget actions

### Visual Indicators

- **Focus Styles**: Clear focus indicators for keyboard users
- **High Contrast**: Sufficient color contrast ratios
- **Motion Preferences**: Respects `prefers-reduced-motion`

## Testing

### Unit Tests

Located in `src/lib/__tests__/budget-grouping.test.ts`:

- Grouping logic validation
- Edge case handling
- Performance benchmarks
- Validation rule testing

### Integration Tests

- Component rendering with grouped budgets
- User interaction flows
- Accessibility compliance
- Performance under load

## Troubleshooting

### Common Issues

1. **Budgets not grouping**: Check parent category relationships
2. **Validation errors**: Verify existing budget conflicts
3. **Performance issues**: Monitor dataset size and algorithm selection
4. **Cache issues**: Ensure proper React Query invalidation

### Debug Mode

Enable debug logging:

```typescript
// Add to development environment
if (process.env.NODE_ENV === 'development') {
  console.log('Grouped budgets:', groupedBudgets)
}
```

## Future Enhancements

### Planned Features

1. **Custom Grouping Rules**: User-defined grouping criteria
2. **Bulk Operations**: Actions on entire budget groups
3. **Advanced Filtering**: Group-aware filter options
4. **Export/Import**: Preserve grouping in data exports
5. **Analytics**: Group-level insights and reporting

### Performance Optimizations

1. **Virtual Scrolling**: For very large budget lists
2. **Lazy Loading**: Load child budgets on demand
3. **Memoization**: Cache expensive calculations
4. **Web Workers**: Background processing for large datasets

## Contributing

When modifying the budget grouping system:

1. **Run Tests**: Ensure all tests pass
2. **Performance**: Benchmark changes with large datasets
3. **Accessibility**: Test with screen readers
4. **Documentation**: Update this documentation
5. **Type Safety**: Maintain strict TypeScript types

## Related Files

- `src/lib/budget-grouping.ts` - Core logic
- `src/components/budgets/BudgetCard.tsx` - UI component
- `src/components/budgets/BudgetsList.tsx` - List component
- `src/types/budget.types.ts` - Type definitions
- `src/hooks/useBudgets.ts` - React Query integration
