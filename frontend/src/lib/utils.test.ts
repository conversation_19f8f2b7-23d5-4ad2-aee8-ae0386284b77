import { describe, it, expect, vi } from 'vitest'
import {
  cn,
  formatCurrency,
  formatDate,
  debounce,
  throttle,
  truncateText
} from './utils'

describe('utils', () => {
  describe('cn', () => {
    it('should merge class names correctly', () => {
      expect(cn('px-2 py-1', 'bg-blue-500')).toBe('px-2 py-1 bg-blue-500')
    })

    it('should handle conditional classes', () => {
      expect(cn('px-2', true && 'py-1', false && 'bg-red-500')).toBe('px-2 py-1')
    })

    it('should handle undefined and null values', () => {
      expect(cn('px-2', undefined, null, 'py-1')).toBe('px-2 py-1')
    })
  })

  describe('formatCurrency', () => {
    it('should format positive numbers correctly', () => {
      const result = formatCurrency(1234.56)
      expect(result).toContain('1.234,56')
      expect(result).toContain('R$')
    })

    it('should format negative numbers correctly', () => {
      const result = formatCurrency(-1234.56)
      expect(result).toContain('1.234,56')
      expect(result).toContain('R$')
      expect(result).toContain('-')
    })

    it('should format zero correctly', () => {
      const result = formatCurrency(0)
      expect(result).toContain('0,00')
      expect(result).toContain('R$')
    })

    it('should handle different currencies', () => {
      const result = formatCurrency(1234.56, 'USD')
      expect(result).toContain('1.234,56')
      expect(result).toContain('US$')
    })
  })

  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = new Date('2023-12-25T10:30:00')
      const result = formatDate(date)
      expect(result).toContain('25')
      expect(result).toContain('12')
      expect(result).toContain('2023')
    })

    it('should format date with long format', () => {
      const date = new Date('2023-12-25T10:30:00')
      const result = formatDate(date, 'long')
      expect(result).toContain('25')
      expect(result).toContain('dezembro')
      expect(result).toContain('2023')
    })

    it('should handle string dates', () => {
      const result = formatDate('2023-12-25')
      expect(result).toContain('12')
      expect(result).toContain('2023')
    })
  })

  describe('debounce', () => {
    it('should debounce function calls', async () => {
      const mockFn = vi.fn()
      const debouncedFn = debounce(mockFn, 100)

      debouncedFn('test1')
      debouncedFn('test2')
      debouncedFn('test3')

      expect(mockFn).not.toHaveBeenCalled()

      await new Promise((resolve) => setTimeout(resolve, 150))

      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('test3')
    })
  })

  describe('throttle', () => {
    it('should throttle function calls', async () => {
      const mockFn = vi.fn()
      const throttledFn = throttle(mockFn, 100)

      throttledFn('test1')
      throttledFn('test2')
      throttledFn('test3')

      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('test1')

      await new Promise((resolve) => setTimeout(resolve, 150))

      throttledFn('test4')
      expect(mockFn).toHaveBeenCalledTimes(2)
      expect(mockFn).toHaveBeenCalledWith('test4')
    })
  })

  describe('truncateText', () => {
    it('should truncate long text', () => {
      const longText = 'This is a very long text that should be truncated'
      expect(truncateText(longText, 20)).toBe('This is a very lo...')
    })

    it('should not truncate short text', () => {
      const shortText = 'Short text'
      expect(truncateText(shortText, 20)).toBe('Short text')
    })

    it('should handle exact length', () => {
      const text = 'Exactly twenty chars'
      expect(truncateText(text, 20)).toBe('Exactly twenty chars')
    })
  })
})
