import type { Budget, GroupedBudget } from '@/types/budget.types'

/**
 * Groups budgets by parent category when there are multiple child category budgets
 * for the same parent category. Individual budgets (parent categories or single child
 * categories) remain as individual items.
 *
 * Performance optimized for large datasets using Map-based grouping and minimal iterations.
 */
export function groupBudgetsByParentCategory(budgets: Budget[]): GroupedBudget[] {
  if (!budgets || budgets.length === 0) {
    return []
  }

  // Early return for small datasets - no need for complex optimization
  if (budgets.length <= 10) {
    return groupBudgetsSimple(budgets)
  }

  // Use optimized algorithm for larger datasets
  return groupBudgetsOptimized(budgets)
}

/**
 * Creates an individual grouped budget from a single budget
 */
function createIndividualGroupedBudget(budget: Budget): GroupedBudget {
  return {
    id: budget.id,
    type: 'individual',
    plannedAmount: budget.plannedAmount,
    spentAmount: budget.spentAmount,
    remainingAmount: budget.remainingAmount,
    month: budget.month,
    year: budget.year,
    progress: budget.progress,
    status: budget.status,
    category: budget.category,
    familyMember: budget.familyMember,
    budget: budget,
    createdAt: budget.createdAt,
    updatedAt: budget.updatedAt,
  }
}

/**
 * Creates a grouped budget from multiple child category budgets
 */
function createGroupedBudget(childBudgets: Budget[], parentId: string): GroupedBudget | null {
  if (childBudgets.length === 0) return null

  // Get parent category info from the first child budget
  const firstChild = childBudgets[0]
  const parentCategory = firstChild.category.parent
  
  if (!parentCategory) return null

  // Calculate aggregated values
  const totalPlanned = childBudgets.reduce((sum, budget) => sum + budget.plannedAmount, 0)
  const totalSpent = childBudgets.reduce((sum, budget) => sum + budget.spentAmount, 0)
  const totalRemaining = totalPlanned - totalSpent
  const overallProgress = totalPlanned > 0 ? (totalSpent / totalPlanned) * 100 : 0

  // Determine overall status
  let overallStatus: 'under_budget' | 'on_track' | 'over_budget' = 'under_budget'
  if (overallProgress > 100) {
    overallStatus = 'over_budget'
  } else if (overallProgress >= 80) {
    overallStatus = 'on_track'
  }

  // Check if all budgets have the same family member
  const familyMembers = childBudgets.map(b => b.familyMember?.id).filter(Boolean)
  const uniqueFamilyMembers = [...new Set(familyMembers)]
  const commonFamilyMember = uniqueFamilyMembers.length === 1 && childBudgets[0].familyMember
    ? childBudgets[0].familyMember
    : null

  // Use the earliest created date and latest updated date
  const createdDates = childBudgets.map(b => new Date(b.createdAt))
  const updatedDates = childBudgets.map(b => new Date(b.updatedAt))
  const earliestCreated = new Date(Math.min(...createdDates.map(d => d.getTime())))
  const latestUpdated = new Date(Math.max(...updatedDates.map(d => d.getTime())))

  return {
    id: parentId,
    type: 'grouped',
    plannedAmount: totalPlanned,
    spentAmount: totalSpent,
    remainingAmount: totalRemaining,
    month: firstChild.month,
    year: firstChild.year,
    progress: overallProgress,
    status: overallStatus,
    category: {
      id: parentCategory.id,
      name: parentCategory.name,
      color: parentCategory.color,
    },
    familyMember: commonFamilyMember,
    childBudgets: childBudgets.sort((a, b) => a.category.name.localeCompare(b.category.name)),
    createdAt: earliestCreated.toISOString(),
    updatedAt: latestUpdated.toISOString(),
  }
}

/**
 * Validates if creating a budget would conflict with existing budgets
 * (prevents creating parent budget when child budgets exist and vice versa)
 */
export function validateBudgetCreation(
  categoryId: string,
  categoryParentId: string | undefined,
  existingBudgets: Budget[],
  month: number,
  year: number,
  familyMemberId?: string
): { isValid: boolean; error?: string } {
  const samePeriodBudgets = existingBudgets.filter(
    b => b.month === month && b.year === year
  )

  // Filter by family member if specified
  const relevantBudgets = familyMemberId
    ? samePeriodBudgets.filter(b => b.familyMemberId === familyMemberId)
    : samePeriodBudgets.filter(b => !b.familyMemberId)

  if (categoryParentId) {
    // Creating a child category budget
    // Check if parent category already has a budget
    const parentBudgetExists = relevantBudgets.some(
      b => b.categoryId === categoryParentId
    )

    if (parentBudgetExists) {
      return {
        isValid: false,
        error: 'Não é possível criar orçamento para categoria filha quando já existe orçamento para a categoria pai no mesmo período.'
      }
    }
  } else {
    // Creating a parent category budget
    // Check if any child categories already have budgets
    const childBudgetsExist = relevantBudgets.some(
      b => b.category.parent?.id === categoryId
    )

    if (childBudgetsExist) {
      return {
        isValid: false,
        error: 'Não é possível criar orçamento para categoria pai quando já existem orçamentos para categorias filhas no mesmo período.'
      }
    }
  }

  return { isValid: true }
}

/**
 * Simple grouping algorithm for small datasets (≤10 budgets)
 * Uses the original algorithm without optimization overhead
 */
function groupBudgetsSimple(budgets: Budget[]): GroupedBudget[] {
  const budgetsByParent = new Map<string, Budget[]>()
  const parentCategoryBudgets: Budget[] = []

  budgets.forEach(budget => {
    if (budget.category.parent) {
      const parentId = budget.category.parent.id
      if (!budgetsByParent.has(parentId)) {
        budgetsByParent.set(parentId, [])
      }
      budgetsByParent.get(parentId)!.push(budget)
    } else {
      parentCategoryBudgets.push(budget)
    }
  })

  const groupedBudgets: GroupedBudget[] = []

  // Process parent category budgets
  parentCategoryBudgets.forEach(budget => {
    groupedBudgets.push(createIndividualGroupedBudget(budget))
  })

  // Process child category budgets
  budgetsByParent.forEach((childBudgets, parentId) => {
    if (childBudgets.length === 1) {
      groupedBudgets.push(createIndividualGroupedBudget(childBudgets[0]))
    } else {
      const groupedBudget = createGroupedBudget(childBudgets, parentId)
      if (groupedBudget) {
        groupedBudgets.push(groupedBudget)
      }
    }
  })

  return sortGroupedBudgets(groupedBudgets)
}

/**
 * Optimized grouping algorithm for larger datasets
 * Uses single-pass processing and optimized sorting
 */
function groupBudgetsOptimized(budgets: Budget[]): GroupedBudget[] {
  const budgetsByParent = new Map<string, Budget[]>()
  const parentCategoryBudgets: Budget[] = []
  const groupedBudgets: GroupedBudget[] = []

  // Single pass to categorize budgets
  for (const budget of budgets) {
    if (budget.category.parent) {
      const parentId = budget.category.parent.id
      const existing = budgetsByParent.get(parentId)
      if (existing) {
        existing.push(budget)
      } else {
        budgetsByParent.set(parentId, [budget])
      }
    } else {
      parentCategoryBudgets.push(budget)
    }
  }

  // Process parent category budgets (pre-allocate array size)
  groupedBudgets.length = parentCategoryBudgets.length + budgetsByParent.size
  let index = 0

  for (const budget of parentCategoryBudgets) {
    groupedBudgets[index++] = createIndividualGroupedBudget(budget)
  }

  // Process child category budgets
  for (const [parentId, childBudgets] of budgetsByParent) {
    if (childBudgets.length === 1) {
      groupedBudgets[index++] = createIndividualGroupedBudget(childBudgets[0])
    } else {
      const groupedBudget = createGroupedBudget(childBudgets, parentId)
      if (groupedBudget) {
        groupedBudgets[index++] = groupedBudget
      }
    }
  }

  // Trim array to actual size and sort
  groupedBudgets.length = index
  return sortGroupedBudgets(groupedBudgets)
}

/**
 * Optimized sorting function
 */
function sortGroupedBudgets(budgets: GroupedBudget[]): GroupedBudget[] {
  // Use a more efficient sorting approach for larger datasets
  if (budgets.length <= 20) {
    return budgets.sort((a, b) => {
      const statusPriority = { over_budget: 3, on_track: 2, under_budget: 1 }
      const aPriority = statusPriority[a.status]
      const bPriority = statusPriority[b.status]

      if (aPriority !== bPriority) {
        return bPriority - aPriority
      }

      return a.category.name.localeCompare(b.category.name)
    })
  }

  // For larger datasets, use a more optimized approach
  const overBudget: GroupedBudget[] = []
  const onTrack: GroupedBudget[] = []
  const underBudget: GroupedBudget[] = []

  for (const budget of budgets) {
    switch (budget.status) {
      case 'over_budget':
        overBudget.push(budget)
        break
      case 'on_track':
        onTrack.push(budget)
        break
      case 'under_budget':
        underBudget.push(budget)
        break
    }
  }

  // Sort each group by name
  const sortByName = (a: GroupedBudget, b: GroupedBudget) =>
    a.category.name.localeCompare(b.category.name)

  overBudget.sort(sortByName)
  onTrack.sort(sortByName)
  underBudget.sort(sortByName)

  return [...overBudget, ...onTrack, ...underBudget]
}
