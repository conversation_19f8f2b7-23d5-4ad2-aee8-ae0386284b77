// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api/v1',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
} as const

// Application Configuration
export const APP_CONFIG = {
  NAME: 'Personal Finance Manager',
  VERSION: '1.0.0',
  DESCRIPTION: 'Gerencie suas finanças pessoais de forma inteligente',
  AUTHOR: 'Personal Finance Team',
} as const

// Theme Configuration
export const THEME_CONFIG = {
  DEFAULT_THEME: 'dark',
  STORAGE_KEY: 'pf-theme',
} as const

// Pagination Configuration
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'dd/MM/yyyy',
  DISPLAY_WITH_TIME: 'dd/MM/yyyy HH:mm',
  API: 'yyyy-MM-dd',
  API_WITH_TIME: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
  RELATIVE_TIME_THRESHOLD: 7, // days
} as const

// Currency Configuration
export const CURRENCY_CONFIG = {
  DEFAULT_CURRENCY: 'BRL',
  DEFAULT_LOCALE: 'pt-BR',
  SUPPORTED_CURRENCIES: ['BRL', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF'] as const,
} as const

// Currencies with labels for UI
export const CURRENCIES = [
  { code: 'BRL', name: 'Real Brasileiro' },
  { code: 'USD', name: 'Dólar Americano' },
  { code: 'EUR', name: 'Euro' },
  { code: 'GBP', name: 'Libra Esterlina' },
  { code: 'JPY', name: 'Iene Japonês' },
  { code: 'CAD', name: 'Dólar Canadense' },
  { code: 'AUD', name: 'Dólar Australiano' },
  { code: 'CHF', name: 'Franco Suíço' },
] as const

// Transaction Types
export const TRANSACTION_TYPES = {
  INCOME: 'INCOME',
  EXPENSE: 'EXPENSE',
  TRANSFER: 'TRANSFER',
} as const

// Transaction Status
export const TRANSACTION_STATUS = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  FAILED: 'FAILED',
} as const

// Account Types
export const ACCOUNT_TYPES = {
  CHECKING: 'CHECKING',
  SAVINGS: 'SAVINGS',
  CREDIT_CARD: 'CREDIT_CARD',
  INVESTMENT: 'INVESTMENT',
  CASH: 'CASH',
  ASSETS: 'ASSETS',
} as const

// Category Types
export const CATEGORY_TYPES = {
  INCOME: 'INCOME',
  EXPENSE: 'EXPENSE',
} as const

// Validation Rules
export const VALIDATION_RULES = {
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
  EMAIL_MAX_LENGTH: 255,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 100,
  DESCRIPTION_MAX_LENGTH: 500,
  AMOUNT_MIN: 0.01,
  AMOUNT_MAX: *********.99,
} as const

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'pf-auth-token',
  USER_PREFERENCES: 'pf-user-preferences',
  THEME: 'pf-theme',
  SIDEBAR_COLLAPSED: 'pf-sidebar-collapsed',
  LAST_VISITED_PAGE: 'pf-last-visited-page',
} as const

// Query Keys for React Query
export const QUERY_KEYS = {
  AUTH: ['auth'],
  USER: ['user'],
  ACCOUNTS: ['accounts'],
  CATEGORIES: ['categories'],
  TRANSACTIONS: ['transactions'],
  FUTURE_TRANSACTIONS: ['future-transactions'],
  RECURRING_TRANSACTIONS: ['recurring-transactions'],
  DASHBOARD: ['dashboard'],
  STATS: ['stats'],
} as const

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Erro de conexão. Verifique sua internet.',
  UNAUTHORIZED: 'Sessão expirada. Faça login novamente.',
  FORBIDDEN: 'Você não tem permissão para esta ação.',
  NOT_FOUND: 'Recurso não encontrado.',
  VALIDATION_ERROR: 'Dados inválidos. Verifique os campos.',
  SERVER_ERROR: 'Erro interno do servidor. Tente novamente.',
  UNKNOWN_ERROR: 'Erro desconhecido. Tente novamente.',
} as const

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN: 'Login realizado com sucesso!',
  LOGOUT: 'Logout realizado com sucesso!',
  CREATED: 'Criado com sucesso!',
  UPDATED: 'Atualizado com sucesso!',
  DELETED: 'Excluído com sucesso!',
  SAVED: 'Salvo com sucesso!',
} as const

// Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  TRANSACTIONS: '/transactions',
  FUTURE_TRANSACTIONS: '/future-transactions',
  RECURRING_TRANSACTIONS: '/recurring',
  ACCOUNTS: '/accounts',
  CATEGORIES: '/categories',
  FAMILY_MEMBERS: '/family-members',
  SETTINGS: '/settings',
  COMPONENTS: '/components',
} as const

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const

// Animation Durations (in milliseconds)
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 1000,
} as const

// Z-Index Layers
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
} as const
