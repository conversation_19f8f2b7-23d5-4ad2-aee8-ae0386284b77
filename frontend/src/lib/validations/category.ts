import { z } from "zod"
import { CATEGORY_COLORS, CATEGORY_VALIDATION } from "@/types/category.types"

export const categoryFormSchema = z.object({
  name: z
    .string()
    .min(CATEGORY_VALIDATION.NAME_MIN_LENGTH, {
      message: `Nome deve ter pelo menos ${CATEGORY_VALIDATION.NAME_MIN_LENGTH} caracteres.`,
    })
    .max(CATEGORY_VALIDATION.NAME_MAX_LENGTH, {
      message: `Nome deve ter no máximo ${CATEGORY_VALIDATION.NAME_MAX_LENGTH} caracteres.`,
    })
    .regex(/^[a-zA-ZÀ-ÿ0-9\s\-_&()]+$/, {
      message: "Nome deve conter apenas letras, números, espaços e caracteres especiais básicos.",
    }),
  color: z
    .string()
    .optional()
    .refine((color) => {
      if (!color) return true // Color is optional
      return CATEGORY_COLORS.includes(color as any) || CATEGORY_VALIDATION.COLOR_REGEX.test(color)
    }, {
      message: "Cor deve ser uma das opções disponíveis ou um código hexadecimal válido.",
    }),
  parentId: z
    .string()
    .optional()
    .nullable()
    .transform((val) => {
      if (val === "" || val === null) return undefined
      return val
    }),
})

export type CategoryFormData = z.infer<typeof categoryFormSchema>

// Schema for creating a category (API request)
export const createCategorySchema = z.object({
  name: z.string().min(CATEGORY_VALIDATION.NAME_MIN_LENGTH).max(CATEGORY_VALIDATION.NAME_MAX_LENGTH),
  color: z.string().optional(),
  parentId: z.string().optional(),
})

// Schema for updating a category (API request)
export const updateCategorySchema = z.object({
  name: z.string().min(CATEGORY_VALIDATION.NAME_MIN_LENGTH).max(CATEGORY_VALIDATION.NAME_MAX_LENGTH).optional(),
  color: z.string().optional().nullable(),
  parentId: z.string().optional().nullable(),
})

// Schema for category hierarchy validation
export const categoryHierarchySchema = z.object({
  parentId: z.string(),
  categoryId: z.string().optional(),
})

// Schema for bulk operations
export const bulkCategoryOperationSchema = z.object({
  categoryIds: z.array(z.string()).min(1, "Selecione pelo menos uma categoria"),
  operation: z.enum(['archive', 'unarchive', 'delete'], {
    required_error: "Operação é obrigatória",
  }),
})

export type CreateCategoryData = z.infer<typeof createCategorySchema>
export type UpdateCategoryData = z.infer<typeof updateCategorySchema>
export type CategoryHierarchyData = z.infer<typeof categoryHierarchySchema>
export type BulkCategoryOperationData = z.infer<typeof bulkCategoryOperationSchema>
