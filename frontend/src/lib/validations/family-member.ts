import { z } from "zod"
import { FAMILY_MEMBER_COLORS } from "@/types/family-member.types"

export const familyMemberFormSchema = z.object({
  name: z
    .string()
    .min(2, {
      message: "Nome deve ter pelo menos 2 caracteres.",
    })
    .max(50, {
      message: "Nome deve ter no máximo 50 caracteres.",
    })
    .regex(/^[a-zA-ZÀ-ÿ\s]+$/, {
      message: "Nome deve conter apenas letras e espaços.",
    }),
  color: z
    .string()
    .refine((color) => FAMILY_MEMBER_COLORS.includes(color as any), {
      message: "Cor deve ser uma das opções disponíveis.",
    }),
  avatar: z
    .union([
      z.instanceof(File),
      z.string().url().optional(),
      z.string().length(0),
    ])
    .optional()
    .transform((val) => {
      if (val === "") return undefined
      return val
    }),
})

export type FamilyMemberFormData = z.infer<typeof familyMemberFormSchema>

// Schema for creating a family member (API request)
export const createFamilyMemberSchema = z.object({
  name: z.string().min(2).max(50),
  color: z.string(),
  avatar: z.string().url().optional(),
})

// Schema for updating a family member (API request)
export const updateFamilyMemberSchema = z.object({
  name: z.string().min(2).max(50).optional(),
  color: z.string().optional(),
  avatar: z.string().url().optional(),
})

export type CreateFamilyMemberData = z.infer<typeof createFamilyMemberSchema>
export type UpdateFamilyMemberData = z.infer<typeof updateFamilyMemberSchema>
