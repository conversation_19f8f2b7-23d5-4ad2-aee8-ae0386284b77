import { z } from 'zod'
import { VALIDATION_RULES, TRANSACTION_TYPES, ACCOUNT_TYPES, CATEGORY_TYPES } from './constants'

// Base schemas
export const emailSchema = z
  .string()
  .email('Email inválido')
  .max(VALIDATION_RULES.EMAIL_MAX_LENGTH, 'Email muito longo')

export const passwordSchema = z
  .string()
  .min(VALIDATION_RULES.PASSWORD_MIN_LENGTH, 'Senha deve ter pelo menos 8 caracteres')
  .max(VALIDATION_RULES.PASSWORD_MAX_LENGTH, 'Senha muito longa')
  .regex(/[A-Z]/, 'Senha deve conter pelo menos uma letra maiúscula')
  .regex(/[a-z]/, 'Senha deve conter pelo menos uma letra minúscula')
  .regex(/[0-9]/, 'Senha deve conter pelo menos um número')
  .regex(/[^A-Za-z0-9]/, 'Senha deve conter pelo menos um caractere especial')

export const nameSchema = z
  .string()
  .min(VALIDATION_RULES.NAME_MIN_LENGTH, 'Nome muito curto')
  .max(VALIDATION_RULES.NAME_MAX_LENGTH, 'Nome muito longo')
  .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços')

export const descriptionSchema = z
  .string()
  .max(VALIDATION_RULES.DESCRIPTION_MAX_LENGTH, 'Descrição muito longa')
  .optional()

export const amountSchema = z
  .number()
  .min(VALIDATION_RULES.AMOUNT_MIN, 'Valor deve ser maior que zero')
  .max(VALIDATION_RULES.AMOUNT_MAX, 'Valor muito alto')

export const dateSchema = z
  .string()
  .datetime('Data inválida')
  .or(z.date())

export const currencySchema = z.enum(['BRL', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF'], {
  errorMap: () => ({ message: 'Moeda inválida' }),
})

// Auth schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Senha é obrigatória'),
})

export const registerSchema = z
  .object({
    name: nameSchema,
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Senhas não coincidem',
    path: ['confirmPassword'],
  })

export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Senha atual é obrigatória'),
    newPassword: passwordSchema,
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Senhas não coincidem',
    path: ['confirmPassword'],
  })

// Account schemas
export const accountSchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100, 'Nome muito longo'),
  type: z.enum(Object.values(ACCOUNT_TYPES) as [string, ...string[]], {
    errorMap: () => ({ message: 'Tipo de conta inválido' }),
  }),
  currency: currencySchema,
  creditLimit: z.number()
    .positive('Limite de crédito deve ser positivo')
    .max(*********.99, 'Limite de crédito muito alto')
    .optional(),
  exchangeRate: z.number()
    .positive('Taxa de câmbio deve ser positiva')
    .max(999999.999999, 'Taxa de câmbio muito alta')
    .optional(),
  includeInTotal: z.boolean().optional(),
  logoPath: z.string().max(255, 'Caminho do logo muito longo').optional(),
  familyMemberIds: z.array(z.string()).min(1, 'Pelo menos um membro da família deve ser selecionado').optional(),
})

// Category schemas
export const categorySchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório').max(100, 'Nome muito longo'),
  type: z.enum(Object.values(CATEGORY_TYPES) as [string, ...string[]], {
    errorMap: () => ({ message: 'Tipo de categoria inválido' }),
  }),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Cor inválida').optional(),
  description: descriptionSchema,
  parentId: z.string().uuid().optional(),
})

// Transaction schemas
export const transactionSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória').max(200, 'Descrição muito longa'),
  amount: amountSchema,
  type: z.enum(Object.values(TRANSACTION_TYPES) as [string, ...string[]], {
    errorMap: () => ({ message: 'Tipo de transação inválido' }),
  }),
  accountId: z.string().uuid('ID da conta inválido'),
  destinationAccountId: z.string().uuid().optional(),
  categoryId: z.string().uuid().optional(),
  transactionDate: dateSchema,
  notes: descriptionSchema,
})

export const futureTransactionSchema = transactionSchema.extend({
  recurrenceRule: z.string().optional(),
  endDate: dateSchema.optional(),
})

export const recurringTransactionSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória').max(200, 'Descrição muito longa'),
  amount: amountSchema,
  type: z.enum(Object.values(TRANSACTION_TYPES) as [string, ...string[]], {
    errorMap: () => ({ message: 'Tipo de transação inválido' }),
  }),
  accountId: z.string().uuid('ID da conta inválido'),
  destinationAccountId: z.string().uuid().optional(),
  categoryId: z.string().uuid().optional(),
  frequency: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY'], {
    errorMap: () => ({ message: 'Frequência inválida' }),
  }),
  startDate: dateSchema,
  endDate: dateSchema.optional(),
  notes: descriptionSchema,
})

// Filter schemas
export const transactionFiltersSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  accountId: z.string().uuid().optional(),
  categoryId: z.string().uuid().optional(),
  type: z.enum(Object.values(TRANSACTION_TYPES) as [string, ...string[]]).optional(),
  minAmount: z.number().min(0).optional(),
  maxAmount: z.number().min(0).optional(),
  search: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const dateRangeSchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
})

// Settings schemas
export const userPreferencesSchema = z.object({
  currency: currencySchema,
  locale: z.string().default('pt-BR'),
  theme: z.enum(['light', 'dark', 'system']).default('dark'),
  dateFormat: z.string().default('dd/MM/yyyy'),
  notifications: z.object({
    email: z.boolean().default(true),
    push: z.boolean().default(true),
    transactions: z.boolean().default(true),
    budgets: z.boolean().default(true),
    reports: z.boolean().default(false),
  }).default({}),
})

// API Response schemas
export const apiResponseSchema = <T>(dataSchema: z.ZodSchema<T>) =>
  z.object({
    success: z.boolean(),
    data: dataSchema,
    message: z.string().optional(),
    errors: z.array(z.string()).optional(),
  })

export const paginationSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  totalPages: z.number(),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
})

export const paginatedResponseSchema = <T>(dataSchema: z.ZodSchema<T>) =>
  z.object({
    success: z.boolean(),
    data: z.object({
      items: z.array(dataSchema),
      pagination: paginationSchema,
    }),
    message: z.string().optional(),
    errors: z.array(z.string()).optional(),
  })

// Type exports
export type LoginData = z.infer<typeof loginSchema>
export type RegisterData = z.infer<typeof registerSchema>
export type ChangePasswordData = z.infer<typeof changePasswordSchema>
export type AccountData = z.infer<typeof accountSchema>
export type CategoryData = z.infer<typeof categorySchema>
export type TransactionData = z.infer<typeof transactionSchema>
export type FutureTransactionData = z.infer<typeof futureTransactionSchema>
export type RecurringTransactionData = z.infer<typeof recurringTransactionSchema>
export type TransactionFilters = z.infer<typeof transactionFiltersSchema>
export type DateRange = z.infer<typeof dateRangeSchema>
export type UserPreferences = z.infer<typeof userPreferencesSchema>
export type Pagination = z.infer<typeof paginationSchema>

// Validation helper functions
export const validateEmail = (email: string): boolean => {
  return emailSchema.safeParse(email).success
}

export const validatePassword = (password: string): boolean => {
  return passwordSchema.safeParse(password).success
}

export const validateAmount = (amount: number): boolean => {
  return amountSchema.safeParse(amount).success
}

export const getValidationErrors = (error: z.ZodError): Record<string, string> => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    errors[path] = err.message
  })
  
  return errors
}
