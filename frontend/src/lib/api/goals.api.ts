import { api } from '../api'
import type {
  FinancialGoal,
  GoalMilestone,
  CreateGoalData,
  UpdateGoalData,
  UpdateProgressData,
  CreateMilestoneData,
  UpdateMilestoneData,
  GoalFilters,
  MilestoneFilters,
  GoalsResponse,
  GoalResponse,
  MilestonesResponse,
  MilestoneResponse,
  GoalsSummaryResponse
} from '@/types/goal.types'

// Helper function to handle API responses
const handleApiResponse = (promise: Promise<any>) => {
  return promise.then((response) => response.data)
}

export const goalsApi = {
  /**
   * Get all financial goals with filters and pagination
   */
  async getAll(filters: GoalFilters = {}, includeMilestones = true): Promise<GoalsResponse> {
    const params = new URLSearchParams()

    // Add filters to params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value))
      }
    })

    // Include milestones by default
    if (includeMilestones) {
      params.append('includeMilestones', 'true')
    }

    return handleApiResponse(api.get(`/goals?${params.toString()}`))
  },

  /**
   * Get financial goal by ID
   */
  async getById(id: string, includeMilestones = true): Promise<GoalResponse> {
    const params = includeMilestones ? '?includeMilestones=true' : ''
    return handleApiResponse(api.get(`/goals/${id}${params}`))
  },

  /**
   * Create a new financial goal
   */
  async create(data: CreateGoalData): Promise<GoalResponse> {
    return handleApiResponse(api.post('/goals', data))
  },

  /**
   * Update financial goal
   */
  async update(id: string, data: UpdateGoalData): Promise<GoalResponse> {
    return handleApiResponse(api.put(`/goals/${id}`, data))
  },

  /**
   * Delete financial goal (soft delete)
   */
  async delete(id: string): Promise<void> {
    return handleApiResponse(api.delete(`/goals/${id}`))
  },

  /**
   * Update goal progress manually
   */
  async updateProgress(id: string, data: UpdateProgressData): Promise<GoalResponse> {
    return handleApiResponse(api.post(`/goals/${id}/progress`, data))
  },

  /**
   * Get goals summary/statistics
   */
  async getSummary(): Promise<GoalsSummaryResponse> {
    return handleApiResponse(api.get('/goals/summary'))
  },

  /**
   * Get goals by family member
   */
  async getByFamilyMember(familyMemberId: string, filters: Omit<GoalFilters, 'familyMemberId'> = {}, includeMilestones = true): Promise<GoalsResponse> {
    return this.getAll({ ...filters, familyMemberId }, includeMilestones)
  },

  /**
   * Get goals by status
   */
  async getByStatus(status: 'active' | 'completed' | 'overdue', filters: Omit<GoalFilters, 'status'> = {}, includeMilestones = true): Promise<GoalsResponse> {
    return this.getAll({ ...filters, status }, includeMilestones)
  },

  /**
   * Search goals by name
   */
  async search(query: string, filters: Omit<GoalFilters, 'search'> = {}, includeMilestones = true): Promise<GoalsResponse> {
    return this.getAll({ ...filters, search: query }, includeMilestones)
  },

  /**
   * Get recent goals
   */
  async getRecent(limit = 5, includeMilestones = true): Promise<FinancialGoal[]> {
    const response = await this.getAll({
      limit,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }, includeMilestones)
    return response.data
  },

  /**
   * Get active goals (not completed or overdue)
   */
  async getActive(filters: Omit<GoalFilters, 'status'> = {}, includeMilestones = true): Promise<GoalsResponse> {
    return this.getAll({ ...filters, status: 'active' }, includeMilestones)
  },

  /**
   * Get completed goals
   */
  async getCompleted(filters: Omit<GoalFilters, 'status'> = {}, includeMilestones = true): Promise<GoalsResponse> {
    return this.getAll({ ...filters, status: 'completed' }, includeMilestones)
  },

  /**
   * Get overdue goals
   */
  async getOverdue(filters: Omit<GoalFilters, 'status'> = {}, includeMilestones = true): Promise<GoalsResponse> {
    return this.getAll({ ...filters, status: 'overdue' }, includeMilestones)
  }
}

export const milestonesApi = {
  /**
   * Get all milestones for a goal
   */
  async getByGoal(goalId: string, filters: Omit<MilestoneFilters, 'goalId'> = {}): Promise<MilestonesResponse> {
    const params = new URLSearchParams()
    
    // Add goalId and filters to params
    params.append('goalId', goalId)
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value))
      }
    })

    return handleApiResponse(api.get(`/goals/${goalId}/milestones?${params.toString()}`))
  },

  /**
   * Get milestone by ID
   */
  async getById(id: string): Promise<MilestoneResponse> {
    return handleApiResponse(api.get(`/milestones/${id}`))
  },

  /**
   * Create a new milestone
   */
  async create(goalId: string, data: CreateMilestoneData): Promise<MilestoneResponse> {
    return handleApiResponse(api.post(`/goals/${goalId}/milestones`, data))
  },

  /**
   * Update milestone
   */
  async update(id: string, data: UpdateMilestoneData): Promise<MilestoneResponse> {
    return handleApiResponse(api.put(`/milestones/${id}`, data))
  },

  /**
   * Delete milestone
   */
  async delete(id: string): Promise<void> {
    return handleApiResponse(api.delete(`/milestones/${id}`))
  },

  /**
   * Mark milestone as completed
   */
  async markCompleted(id: string): Promise<MilestoneResponse> {
    return handleApiResponse(api.post(`/milestones/${id}/complete`))
  },

  /**
   * Mark milestone as pending
   */
  async markPending(id: string): Promise<MilestoneResponse> {
    return handleApiResponse(api.post(`/milestones/${id}/pending`))
  },

  /**
   * Get pending milestones for a goal
   */
  async getPending(goalId: string): Promise<GoalMilestone[]> {
    const response = await this.getByGoal(goalId, { status: 'pending' })
    return response.data
  },

  /**
   * Get completed milestones for a goal
   */
  async getCompleted(goalId: string): Promise<GoalMilestone[]> {
    const response = await this.getByGoal(goalId, { status: 'completed' })
    return response.data
  },

  /**
   * Get upcoming milestones (due soon)
   */
  async getUpcoming(goalId: string, days = 30): Promise<GoalMilestone[]> {
    const targetDateTo = new Date()
    targetDateTo.setDate(targetDateTo.getDate() + days)
    
    const response = await this.getByGoal(goalId, {
      status: 'pending',
      targetDateTo: targetDateTo.toISOString(),
      sortBy: 'targetDate',
      sortOrder: 'asc'
    })
    return response.data
  }
}

export default { goalsApi, milestonesApi }
