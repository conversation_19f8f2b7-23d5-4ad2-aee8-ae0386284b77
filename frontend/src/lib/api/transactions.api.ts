import { api } from '../api'
import type {
  Transaction,
  CreateTransactionData,
  UpdateTransactionData,
  TransactionFilters,
  PaginatedTransactionsResponse,
  TransactionStats
} from '@/types/transaction.types'

export const transactionsApi = {
  /**
   * Get all transactions with filters and pagination
   */
  async getAll(filters: TransactionFilters = {}): Promise<PaginatedTransactionsResponse> {
    const params = new URLSearchParams()
    
    // Add filters to params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value))
      }
    })

    const response = await api.get(`/transactions?${params.toString()}`)
    return response.data
  },

  /**
   * Get transaction by ID
   */
  async getById(id: string, includeDeleted = false): Promise<Transaction> {
    const params = includeDeleted ? '?includeDeleted=true' : ''
    const response = await api.get(`/transactions/${id}${params}`)
    return response.data
  },

  /**
   * Create a new transaction
   */
  async create(data: CreateTransactionData): Promise<Transaction> {
    const response = await api.post('/transactions', data)
    return response.data
  },

  /**
   * Update transaction
   */
  async update(id: string, data: UpdateTransactionData): Promise<Transaction> {
    const response = await api.put(`/transactions/${id}`, data)
    return response.data
  },

  /**
   * Get installments of a transaction
   */
  async getInstallments(id: string): Promise<{
    parentTransaction: Transaction;
    allInstallments: Transaction[];
    totalInstallments: number;
  }> {
    const response = await api.get(`/transactions/${id}/installments`)
    return response.data
  },

  /**
   * Update installments of a transaction
   */
  async updateInstallments(id: string, data: {
    installments: Array<{
      amount: number;
      transactionDate: string;
      description?: string;
    }>;
    totalAmount: number;
  }): Promise<{
    parentTransaction: Transaction;
    installments: Transaction[];
    summary: any;
    deletedInstallments: string[];
  }> {
    const response = await api.put(`/transactions/${id}/installments`, data)
    return response.data
  },

  /**
   * Delete transaction (soft delete)
   */
  async delete(id: string): Promise<void> {
    await api.delete(`/transactions/${id}`)
  },

  /**
   * Get transaction statistics
   */
  async getStats(filters: Partial<TransactionFilters> = {}): Promise<TransactionStats> {
    const params = new URLSearchParams()
    
    // Add filters to params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value))
      }
    })

    const response = await api.get(`/transactions/stats?${params.toString()}`)
    return response.data
  },

  /**
   * Get transactions for a specific account
   */
  async getByAccount(accountId: string, filters: Omit<TransactionFilters, 'accountId'> = {}): Promise<PaginatedTransactionsResponse> {
    return this.getAll({ ...filters, accountId })
  },

  /**
   * Get transactions for a specific category
   */
  async getByCategory(categoryId: string, filters: Omit<TransactionFilters, 'categoryId'> = {}): Promise<PaginatedTransactionsResponse> {
    return this.getAll({ ...filters, categoryId })
  },

  /**
   * Get transactions for a specific family member
   */
  async getByFamilyMember(familyMemberId: string, filters: Omit<TransactionFilters, 'familyMemberId'> = {}): Promise<PaginatedTransactionsResponse> {
    return this.getAll({ ...filters, familyMemberId })
  },

  /**
   * Get transactions by date range
   */
  async getByDateRange(startDate: string, endDate: string, filters: Omit<TransactionFilters, 'startDate' | 'endDate'> = {}): Promise<PaginatedTransactionsResponse> {
    return this.getAll({ ...filters, startDate, endDate })
  },

  /**
   * Get future transactions
   */
  async getFuture(filters: Omit<TransactionFilters, 'isFuture'> = {}): Promise<PaginatedTransactionsResponse> {
    return this.getAll({ ...filters, isFuture: true })
  },

  /**
   * Get transactions by type
   */
  async getByType(type: 'INCOME' | 'EXPENSE' | 'TRANSFER', filters: Omit<TransactionFilters, 'type'> = {}): Promise<PaginatedTransactionsResponse> {
    return this.getAll({ ...filters, type })
  },

  /**
   * Search transactions by description
   */
  async search(query: string, filters: Omit<TransactionFilters, 'description'> = {}): Promise<PaginatedTransactionsResponse> {
    return this.getAll({ ...filters, description: query })
  },

  /**
   * Get recent transactions
   */
  async getRecent(limit = 10): Promise<Transaction[]> {
    const response = await this.getAll({
      limit,
      sortBy: 'transactionDate',
      sortOrder: 'desc'
    })
    return response.data
  },

  /**
   * Duplicate transaction
   */
  async duplicate(id: string): Promise<Transaction> {
    const original = await this.getById(id)
    
    // Remove fields that shouldn't be duplicated
    const { id: _, createdAt, updatedAt, deletedAt, ...duplicateData } = original
    
    // Create new transaction with current date
    return this.create({
      ...duplicateData,
      transactionDate: new Date().toISOString(),
      familyMemberIds: original.members?.map(m => m.id) || [],
      tagIds: original.tags?.map(t => t.id) || []
    })
  }
}

export default transactionsApi
