import { api, handleApiResponse } from './base.api'
import type {
  ProgressHistoryEntry,
  ProgressHistoryFilters,
  ProgressHistoryListResponse,
  ProgressHistoryResponse,
  UpdateProgressHistoryData
} from '../../types/progress-history.types'

export const progressHistoryApi = {
  /**
   * Get all progress history with filters and pagination
   */
  async getAll(filters: ProgressHistoryFilters = {}): Promise<ProgressHistoryListResponse> {
    const params = new URLSearchParams()

    // Add filters to params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value))
      }
    })

    // Use the correct route - this endpoint might not exist, use getByGoal instead
    return handleApiResponse(api.get(`/progress-history?${params.toString()}`))
  },

  /**
   * Get progress history for a specific goal
   */
  async getByGoal(goalId: string, filters: Omit<ProgressHistoryFilters, 'goalId'> = {}): Promise<ProgressHistoryListResponse> {
    const params = new URLSearchParams()
    
    // Add filters to params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value))
      }
    })
    
    return handleApiResponse(api.get(`/goals/${goalId}/progress-history?${params.toString()}`))
  },

  /**
   * Get recent progress updates
   */
  async getRecent(limit = 10): Promise<{ success: boolean; data: ProgressHistoryEntry[]; message: string }> {
    return handleApiResponse(api.get(`/progress-history/recent?limit=${limit}`))
  },

  /**
   * Update progress history entry description
   */
  async updateDescription(id: string, data: UpdateProgressHistoryData): Promise<ProgressHistoryResponse> {
    return handleApiResponse(api.put(`/progress-history/${id}`, data))
  },

  /**
   * Delete progress history entry
   */
  async delete(id: string): Promise<{ success: boolean; message: string }> {
    return handleApiResponse(api.delete(`/progress-history/${id}`))
  },

  /**
   * Get progress history by operation type
   */
  async getByOperation(operation: 'add' | 'subtract' | 'set', filters: Omit<ProgressHistoryFilters, 'operation'> = {}): Promise<ProgressHistoryListResponse> {
    return this.getAll({ ...filters, operation })
  },

  /**
   * Get progress history by date range
   */
  async getByDateRange(fromDate: string, toDate: string, filters: Omit<ProgressHistoryFilters, 'fromDate' | 'toDate'> = {}): Promise<ProgressHistoryListResponse> {
    return this.getAll({ ...filters, fromDate, toDate })
  },

  /**
   * Search progress history by goal name or description
   */
  async search(query: string, filters: ProgressHistoryFilters = {}): Promise<ProgressHistoryListResponse> {
    // Note: This would require backend implementation for text search
    // For now, we'll filter on the frontend after getting results
    const response = await this.getAll(filters)
    
    if (query.trim() === '') {
      return response
    }
    
    const filteredData = response.data.filter(entry => 
      entry.goalName.toLowerCase().includes(query.toLowerCase()) ||
      (entry.description && entry.description.toLowerCase().includes(query.toLowerCase()))
    )
    
    return {
      ...response,
      data: filteredData,
      pagination: {
        ...response.pagination,
        total: filteredData.length,
        totalPages: Math.ceil(filteredData.length / response.pagination.limit)
      }
    }
  }
}
