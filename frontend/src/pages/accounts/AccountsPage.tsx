import { useState } from 'react'
import { PiggyBank, Plus } from 'lucide-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { accountsApi } from '@/lib/api'
import { AccountsList } from '@/components/accounts/AccountsList'
import { AccountModal } from '@/components/accounts/AccountModal'
import { Button } from '@/components/ui/button'

interface Account {
  id: string
  name: string
  type: 'CHECKING' | 'SAVINGS' | 'CREDIT_CARD' | 'INVESTMENT' | 'CASH' | 'ASSETS'
  currency: string
  creditLimit?: number
  exchangeRate?: number
  includeInTotal: boolean
  logoPath?: string
  archived: boolean
  createdAt: string
  updatedAt: string
}

export function AccountsPage() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedAccount, setSelectedAccount] = useState<Account | undefined>()
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create')

  const queryClient = useQueryClient()

  const toggleVisibilityMutation = useMutation({
    mutationFn: ({ id, includeInTotal }: { id: string; includeInTotal: boolean }) =>
      accountsApi.update(id, { includeInTotal }),
    onMutate: async ({ id, includeInTotal }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['accounts'] })

      // Snapshot the previous value
      const previousAccounts = queryClient.getQueryData(['accounts'])

      // Optimistically update to the new value
      queryClient.setQueryData(['accounts'], (old: any) => {
        if (!old?.data) return old
        return {
          ...old,
          data: old.data.map((account: Account) =>
            account.id === id ? { ...account, includeInTotal } : account
          )
        }
      })

      return { previousAccounts }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accounts'] })
      toast.success('Visibilidade da conta atualizada!')
    },
    onError: (error: any, _, context) => {
      // Rollback on error
      if (context?.previousAccounts) {
        queryClient.setQueryData(['accounts'], context.previousAccounts)
      }
      console.error('Erro ao atualizar visibilidade:', error)
      const message = error.response?.data?.message ||
                     error.response?.data?.errors?.[0] ||
                     'Erro ao atualizar conta. Tente novamente.'
      toast.error(message)
    }
  })

  const deleteMutation = useMutation({
    mutationFn: accountsApi.delete,
    onMutate: async (accountId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['accounts'] })

      // Snapshot the previous value
      const previousAccounts = queryClient.getQueryData(['accounts'])

      // Optimistically remove the account
      queryClient.setQueryData(['accounts'], (old: any) => {
        if (!old?.data) return old
        return {
          ...old,
          data: old.data.filter((account: Account) => account.id !== accountId)
        }
      })

      return { previousAccounts }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accounts'] })
      toast.success('Conta excluída com sucesso!')
    },
    onError: (error: any, _, context) => {
      // Rollback on error
      if (context?.previousAccounts) {
        queryClient.setQueryData(['accounts'], context.previousAccounts)
      }
      console.error('Erro ao excluir conta:', error)
      const message = error.response?.data?.message ||
                     error.response?.data?.errors?.[0] ||
                     'Erro ao excluir conta. Tente novamente.'
      toast.error(message)
    }
  })

  const handleCreateAccount = () => {
    setSelectedAccount(undefined)
    setModalMode('create')
    setIsModalOpen(true)
  }

  const handleEditAccount = (account: Account) => {
    setSelectedAccount(account)
    setModalMode('edit')
    setIsModalOpen(true)
  }

  const handleToggleVisibility = (account: Account) => {
    toggleVisibilityMutation.mutate({
      id: account.id,
      includeInTotal: !account.includeInTotal
    })
  }

  const handleDeleteAccount = (account: Account) => {
    if (window.confirm(`Tem certeza que deseja excluir a conta "${account.name}"?`)) {
      deleteMutation.mutate(account.id)
    }
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedAccount(undefined)
  }

  return (
    <div className="space-y-8">
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <PiggyBank className="h-8 w-8" />
              Contas
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Gerencie suas contas bancárias e cartões
            </p>
          </div>

          <Button
            onClick={handleCreateAccount}
            className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200"
          >
            <Plus className="h-5 w-5 mr-2" />
            Nova Conta
          </Button>
        </div>
      </div>

      <AccountsList
        onEdit={handleEditAccount}
        onDelete={handleDeleteAccount}
        onToggleVisibility={handleToggleVisibility}
      />

      <AccountModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        account={selectedAccount}
        mode={modalMode}
      />
    </div>
  )
}
