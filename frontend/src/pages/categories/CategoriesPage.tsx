import { Tags, Plus, Folder, FolderTree, Archive } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { useCategoryStats } from '@/hooks/useCategories'
import { CreateCategoryDialog } from '@/components/categories/CreateCategoryDialog'
import { EditCategoryDialog } from '@/components/categories/EditCategoryDialog'
import { CategoriesList } from '@/components/categories/CategoriesList'
import type { Category } from '@/types/category.types'

export function CategoriesPage() {
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [createSubcategoryParentId, setCreateSubcategoryParentId] = useState<string | null>(null)

  const { data: stats, isLoading: isLoadingStats } = useCategoryStats()

  // Handlers for category actions
  const handleEdit = (category: Category) => {
    setEditingCategory(category)
  }

  const handleCreateCategory = () => {
    setIsCreateDialogOpen(true)
  }

  const handleCreateSubcategory = (parentId: string) => {
    setCreateSubcategoryParentId(parentId)
    setIsCreateDialogOpen(true)
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <Tags className="h-8 w-8" />
              Categorias
            </h1>
            <p className="text-lg text-muted-foreground">
              Organize suas transações por categorias hierárquicas
            </p>
          </div>
          <CreateCategoryDialog
            open={isCreateDialogOpen}
            onOpenChange={(open) => {
              setIsCreateDialogOpen(open)
              if (!open) {
                setCreateSubcategoryParentId(null)
              }
            }}
            defaultParentId={createSubcategoryParentId || undefined}
          >
            <Button className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200">
              <Plus className="h-5 w-5 mr-2" />
              Nova Categoria
            </Button>
          </CreateCategoryDialog>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid gap-8">
        {/* Stats Cards - Optimized Layout */}
        <div className="grid gap-6 md:grid-cols-3">
          <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-foreground">Total de Categorias</CardTitle>
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-soft">
                <Tags className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">
                {isLoadingStats ? '...' : stats?.totalCategories || 0}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {stats?.totalCategories === 0 ? 'Nenhuma categoria cadastrada' : 'categorias cadastradas'}
              </p>
            </CardContent>
          </Card>

          <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-foreground">Hierarquia</CardTitle>
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-info shadow-soft">
                <FolderTree className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div>
                  <div className="text-2xl font-bold text-foreground">
                    {isLoadingStats ? '...' : stats?.parentCategories || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">Principais</p>
                </div>
                <div className="h-8 w-px bg-secondary-700"></div>
                <div>
                  <div className="text-2xl font-bold text-foreground">
                    {isLoadingStats ? '...' : stats?.subcategories || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">Subcategorias</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-foreground">Status</CardTitle>
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-success shadow-soft">
                <Archive className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div>
                  <div className="text-2xl font-bold text-success-400">
                    {isLoadingStats ? '...' : (stats?.totalCategories || 0) - (stats?.archivedCategories || 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">Ativas</p>
                </div>
                <div className="h-8 w-px bg-secondary-700"></div>
                <div>
                  <div className="text-2xl font-bold text-warning-400">
                    {isLoadingStats ? '...' : stats?.archivedCategories || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">Arquivadas</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Categories List */}
        <Card className="glass-deep shadow-elegant">
          <CardHeader className="pb-6">
            <CardTitle className="text-2xl font-bold text-gradient">Lista de Categorias</CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              Gerencie suas categorias e subcategorias
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CategoriesList
              onEdit={handleEdit}
              onCreateSubcategory={handleCreateSubcategory}
            />
          </CardContent>
        </Card>
      </div>

      {/* Edit Category Dialog */}
      {editingCategory && (
        <EditCategoryDialog
          category={editingCategory}
          open={!!editingCategory}
          onOpenChange={(open) => {
            if (!open) setEditingCategory(null)
          }}
        />
      )}
    </div>
  )
}
