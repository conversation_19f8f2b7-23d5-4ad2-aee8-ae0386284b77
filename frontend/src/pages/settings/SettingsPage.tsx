import { Settings } from 'lucide-react'

export function SettingsPage() {
  return (
    <div className="space-y-8">
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
          <Settings className="h-8 w-8" />
          Configurações
        </h1>
        <p className="text-lg text-muted-foreground mt-2">
          Personalize sua experiência no sistema
        </p>
      </div>

      <div className="glass-deep p-8 rounded-2xl shadow-elegant">
        <div className="py-16 text-center">
          <div className="flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-deep shadow-glow mx-auto mb-6">
            <Settings className="h-10 w-10 text-white" />
          </div>
          <h3 className="mb-4 text-2xl font-bold text-gradient">
            Página em Desenvolvimento
          </h3>
          <p className="text-lg text-muted-foreground max-w-md mx-auto">
            A página de configurações será implementada em breve com toda a elegância da nova paleta.
          </p>
        </div>
      </div>
    </div>
  )
}
