import { useState } from 'react'
import { Calculator, Plus, BarChart3, FileText } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  useBudgets, 
  useBudgetStats, 
  useCreateBudget, 
  useUpdateBudget, 
  useDeleteBudget 
} from '@/hooks/useBudgets'
import { BudgetsStatsCards } from '@/components/budgets/BudgetsStatsCards'
import { BudgetsList } from '@/components/budgets/BudgetsList'
import { BudgetModal } from '@/components/budgets/BudgetModal'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { toast } from 'react-hot-toast'
import type { Budget, BudgetFilters, CreateBudgetRequest, UpdateBudgetRequest } from '@/types/budget.types'

export function BudgetsPage() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingBudget, setEditingBudget] = useState<Budget | null>(null)
  const [filters, setFilters] = useState<BudgetFilters>({
    includeProgress: true,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  // Queries
  const { data: budgetsData, isLoading: isLoadingBudgets } = useBudgets(filters)
  // TODO: Implementar endpoint de stats no backend
  // const { data: stats, isLoading: isLoadingStats } = useBudgetStats()

  // Mutations
  const createBudgetMutation = useCreateBudget()
  const updateBudgetMutation = useUpdateBudget()
  const deleteBudgetMutation = useDeleteBudget()

  const budgets = budgetsData?.data || []

  // Handlers
  const handleCreateBudget = () => {
    setIsCreateModalOpen(true)
  }

  const handleEditBudget = (budget: Budget) => {
    setEditingBudget(budget)
  }

  const handleDeleteBudget = (budgetId: string) => {
    const budget = budgets.find(b => b.id === budgetId)
    if (!budget) return

    const confirmMessage = `Tem certeza que deseja excluir o orçamento de "${budget.category.name}" para ${getMonthName(budget.month)}/${budget.year}?`
    
    if (window.confirm(confirmMessage)) {
      deleteBudgetMutation.mutate(budgetId)
    }
  }

  const handleSubmitCreate = (data: CreateBudgetRequest) => {
    createBudgetMutation.mutate(data, {
      onSuccess: () => {
        setIsCreateModalOpen(false)
      }
    })
  }

  const handleSubmitEdit = (data: UpdateBudgetRequest) => {
    if (!editingBudget) return

    updateBudgetMutation.mutate(
      { id: editingBudget.id, data },
      {
        onSuccess: () => {
          setEditingBudget(null)
        }
      }
    )
  }

  const handleFiltersChange = (newFilters: BudgetFilters) => {
    setFilters({
      ...newFilters,
      includeProgress: true, // Always include progress for better UX
    })
  }

  const getMonthName = (month: number) => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ]
    return months[month - 1] || 'Mês Inválido'
  }

  const isLoading = createBudgetMutation.isPending || updateBudgetMutation.isPending

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <Calculator className="h-8 w-8" />
              Orçamentos
            </h1>
            <p className="text-lg text-muted-foreground">
              Gerencie seus orçamentos mensais por categoria e acompanhe seus gastos
            </p>
          </div>
          <div className="flex items-center gap-3">
            {/* Quick Actions */}
            <Button
              variant="outline"
              className="hidden sm:flex items-center gap-2"
              onClick={() => {
                // TODO: Implement budget report modal
                toast.info('Relatório de orçamentos em desenvolvimento')
              }}
            >
              <FileText className="h-4 w-4" />
              Relatório
            </Button>
            
            <Button
              onClick={handleCreateBudget}
              className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200"
            >
              <Plus className="h-5 w-5 mr-2" />
              Novo Orçamento
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid gap-8">
        {/* Stats Cards */}
        {/* TODO: Implementar stats quando endpoint estiver disponível */}
        {/* <BudgetsStatsCards stats={stats} isLoading={isLoadingStats} /> */}

        {/* Quick Insights */}
        {/* TODO: Implementar insights quando stats estiverem disponíveis */}

        {/* Budgets List */}
        <Card className="glass-deep shadow-elegant">
          <CardHeader className="pb-6">
            <CardTitle className="text-2xl font-bold text-gradient">Lista de Orçamentos</CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              Acompanhe seus orçamentos e progresso de gastos por categoria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BudgetsList
              budgets={budgets}
              isLoading={isLoadingBudgets}
              onEdit={handleEditBudget}
              onDelete={handleDeleteBudget}
              filters={filters}
              onFiltersChange={handleFiltersChange}
            />
          </CardContent>
        </Card>
      </div>

      {/* Modals */}
      <BudgetModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        onSubmit={handleSubmitCreate}
        mode="create"
        isLoading={isLoading}
      />
      
      {editingBudget && (
        <BudgetModal
          open={!!editingBudget}
          onOpenChange={(open) => !open && setEditingBudget(null)}
          onSubmit={handleSubmitEdit}
          mode="edit"
          budget={editingBudget}
          isLoading={isLoading}
        />
      )}

      {/* Loading Overlay */}
      {deleteBudgetMutation.isPending && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="glass-deep p-6 rounded-xl shadow-elegant">
            <div className="flex items-center gap-3">
              <LoadingSpinner className="h-5 w-5" />
              <span className="text-foreground">Excluindo orçamento...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
