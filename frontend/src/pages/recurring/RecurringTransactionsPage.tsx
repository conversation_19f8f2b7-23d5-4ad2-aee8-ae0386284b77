import { Repeat, Plus } from 'lucide-react'

export function RecurringTransactionsPage() {
  return (
    <div className="space-y-8">
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <Repeat className="h-8 w-8" />
              Transações Recorrentes
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Configure transações que se repetem automaticamente
            </p>
          </div>
          <button className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200 flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Nova Recorrente
          </button>
        </div>
      </div>

      <div className="glass-deep p-8 rounded-2xl shadow-elegant">
        <div className="py-16 text-center">
          <div className="flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-deep shadow-glow mx-auto mb-6">
            <Repeat className="h-10 w-10 text-white" />
          </div>
          <h3 className="mb-4 text-2xl font-bold text-gradient">
            Página em Desenvolvimento
          </h3>
          <p className="text-lg text-muted-foreground max-w-md mx-auto">
            A página de transações recorrentes será implementada em breve com toda a elegância da nova paleta.
          </p>
        </div>
      </div>
    </div>
  )
}
