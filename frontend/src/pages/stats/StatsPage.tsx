import { useQuery } from '@tanstack/react-query'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  Calendar,
  Database,
  Activity,
  Clock,
  AlertTriangle,
  CheckCircle,
  Server,
  Zap,
  Target
} from 'lucide-react'
import { dashboardApi, futureTransactionsApi, cacheApi } from '@/lib/api'
import { useFamilyMemberStats } from '@/hooks/useFamilyMembers'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatCurrency } from '@/lib/utils'

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  color: string
  textColor: string
  description?: string
}

function StatCard({ title, value, icon: Icon, color, textColor, description }: StatCardProps) {
  return (
    <div className="glass-deep p-6 rounded-xl shadow-elegant hover:shadow-glow transition-all duration-300">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`flex h-12 w-12 items-center justify-center rounded-xl ${color} shadow-soft`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-semibold text-muted-foreground">{title}</p>
          <p className={`text-3xl font-bold ${textColor}`}>
            {typeof value === 'number' && (title.includes('Saldo') || title.includes('Receitas') || title.includes('Despesas'))
              ? formatCurrency(value)
              : value}
          </p>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
      </div>
    </div>
  )
}

interface SectionHeaderProps {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
}

function SectionHeader({ title, description, icon: Icon }: SectionHeaderProps) {
  return (
    <div className="flex items-center gap-4 mb-6">
      <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-deep shadow-glow">
        <Icon className="h-6 w-6 text-white" />
      </div>
      <div>
        <h2 className="text-xl font-bold text-gradient">{title}</h2>
        <p className="text-base text-muted-foreground">{description}</p>
      </div>
    </div>
  )
}

export function StatsPage() {
  // Fetch dashboard overview
  const { data: overview, isLoading: isLoadingOverview } = useQuery({
    queryKey: ['dashboard', 'overview'],
    queryFn: dashboardApi.getOverview,
  })

  // Fetch family members stats
  const { data: familyStats, isLoading: isLoadingFamily } = useFamilyMemberStats()

  // Fetch future transactions stats
  const { data: futureStats, isLoading: isLoadingFuture } = useQuery({
    queryKey: ['future-transactions', 'stats'],
    queryFn: futureTransactionsApi.getStats,
  })

  // Fetch cache stats
  const { data: cacheStats, isLoading: isLoadingCache } = useQuery({
    queryKey: ['cache', 'stats'],
    queryFn: cacheApi.getStats,
  })

  // Fetch performance metrics
  const { data: performanceStats, isLoading: isLoadingPerformance } = useQuery({
    queryKey: ['dashboard', 'performance-metrics'],
    queryFn: dashboardApi.getPerformanceMetrics,
  })

  const isLoading = isLoadingOverview || isLoadingFamily || isLoadingFuture || isLoadingCache || isLoadingPerformance

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  const dashboardData = overview?.data || {}
  const familyData = familyStats || {} // Family stats hook already returns clean data
  const futureData = futureStats?.data?.data || {}
  const cacheData = cacheStats?.data?.data || {}
  const performanceData = performanceStats?.data?.data || {}

  return (
    <div className="space-y-10">
      {/* Page Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
          <BarChart3 className="h-8 w-8" />
          Estatísticas
        </h1>
        <p className="text-lg text-muted-foreground mt-2">
          Visão completa das métricas e estatísticas do sistema
        </p>
      </div>

      {/* Dashboard Overview Stats */}
      <div>
        <SectionHeader 
          title="Visão Geral Financeira"
          description="Principais métricas financeiras do sistema"
          icon={DollarSign}
        />
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Saldo Total"
            value={dashboardData.totalBalance || 0}
            icon={DollarSign}
            color="bg-success"
            textColor="text-success"
          />
          <StatCard
            title="Receitas (Mês)"
            value={dashboardData.monthlyIncome || 0}
            icon={TrendingUp}
            color="bg-gradient-deep"
            textColor="text-primary"
          />
          <StatCard
            title="Despesas (Mês)"
            value={dashboardData.monthlyExpenses || 0}
            icon={TrendingDown}
            color="bg-destructive"
            textColor="text-destructive"
          />
          <StatCard
            title="Contas Ativas"
            value={dashboardData.accountsCount || 0}
            icon={Database}
            color="bg-warning"
            textColor="text-warning"
          />
        </div>
      </div>

      {/* Family Members Stats */}
      <div>
        <SectionHeader 
          title="Membros da Família"
          description="Estatísticas dos membros cadastrados"
          icon={Users}
        />
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total de Membros"
            value={(familyData as any)?.totalMembers || 0}
            icon={Users}
            color="bg-gradient-deep"
            textColor="text-primary"
          />
          <StatCard
            title="Membros Ativos"
            value={(familyData as any)?.activeMembers || 0}
            icon={CheckCircle}
            color="bg-success"
            textColor="text-success"
          />
          <StatCard
            title="Membros Arquivados"
            value={(familyData as any)?.archivedMembers || 0}
            icon={Clock}
            color="bg-warning"
            textColor="text-warning"
          />
          <StatCard
            title="Membros com Transações"
            value={(familyData as any)?.membersWithTransactions || 0}
            icon={Activity}
            color="bg-info"
            textColor="text-info"
          />
        </div>
      </div>

      {/* Future Transactions Stats */}
      <div>
        <SectionHeader 
          title="Transações Futuras"
          description="Estatísticas de transações agendadas"
          icon={Calendar}
        />
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Pendente"
            value={futureData.totalPending || 0}
            icon={Calendar}
            color="bg-primary-600"
            textColor="text-primary-400"
          />
          <StatCard
            title="Vence Hoje"
            value={futureData.dueToday || 0}
            icon={Clock}
            color="bg-warning-600"
            textColor="text-warning-400"
          />
          <StatCard
            title="Esta Semana"
            value={futureData.dueThisWeek || 0}
            icon={CheckCircle}
            color="bg-success-600"
            textColor="text-success-400"
          />
          <StatCard
            title="Em Atraso"
            value={futureData.overdue || 0}
            icon={AlertTriangle}
            color="bg-error-600"
            textColor="text-error-400"
          />
        </div>
      </div>

      {/* System Performance Stats */}
      <div>
        <SectionHeader 
          title="Performance do Sistema"
          description="Métricas de performance e cache"
          icon={Server}
        />
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Cache Ativo"
            value={cacheData.isAvailable ? 'Sim' : 'Não'}
            icon={Database}
            color={cacheData.isAvailable ? 'bg-success-600' : 'bg-error-600'}
            textColor={cacheData.isAvailable ? 'text-success-400' : 'text-error-400'}
          />
          <StatCard
            title="Chaves em Cache"
            value={cacheData.keyCount || 0}
            icon={Zap}
            color="bg-primary-600"
            textColor="text-primary-400"
          />
          <StatCard
            title="Uso de Memória"
            value={cacheData.memoryUsage || 'N/A'}
            icon={Activity}
            color="bg-warning-600"
            textColor="text-warning-400"
          />
          <StatCard
            title="Queries Lentas"
            value={performanceData.slowQueries?.length || 0}
            icon={AlertTriangle}
            color="bg-error-600"
            textColor="text-error-400"
            description="Queries > 2s"
          />
        </div>
      </div>

      {/* Additional System Stats */}
      <div>
        <SectionHeader 
          title="Métricas Adicionais"
          description="Outras estatísticas importantes do sistema"
          icon={Target}
        />
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total de Transações"
            value={dashboardData.transactionsCount || 0}
            icon={Activity}
            color="bg-primary-600"
            textColor="text-primary-400"
          />
          <StatCard
            title="Categorias Ativas"
            value={dashboardData.categoriesCount || 0}
            icon={Target}
            color="bg-success-600"
            textColor="text-success-400"
          />
          <StatCard
            title="Tempo Médio de Query"
            value={`${performanceData.averageQueryTime || 0}ms`}
            icon={Zap}
            color="bg-warning-600"
            textColor="text-warning-400"
          />
          <StatCard
            title="Total de Queries"
            value={performanceData.totalQueries || 0}
            icon={Database}
            color="bg-primary-600"
            textColor="text-primary-400"
          />
        </div>
      </div>
    </div>
  )
}
