import { useState } from 'react'
import { Target, Plus } from 'lucide-react'
import {
  useGoals,
  useGoalsSummary,
  useCreateGoal,
  useUpdateGoal,
  useDeleteGoal,
  useUpdateGoalProgress,
  useCreateMilestone,
  useUpdateMilestone,
  useDeleteMilestone
} from '@/hooks/useGoals'
import { Button } from '@/components/ui/button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { GoalModal } from '@/components/goals/GoalModal'
import { GoalsList } from '@/components/goals/GoalsList'
import { GoalsStatsCards } from '@/components/goals/GoalsStatsCards'
import { MilestoneModal } from '@/components/goals/MilestoneModal'

import type {
  FinancialGoal,
  CreateGoalData,
  UpdateGoalData,
  GoalMilestone,
  CreateMilestoneData,
  UpdateMilestoneData,
  ProgressOperation
} from '@/types/goal.types'

export function GoalsPage() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingGoal, setEditingGoal] = useState<FinancialGoal | null>(null)
  const [isMilestoneModalOpen, setIsMilestoneModalOpen] = useState(false)
  const [editingMilestone, setEditingMilestone] = useState<GoalMilestone | null>(null)
  const [milestoneGoalId, setMilestoneGoalId] = useState<string | null>(null)

  // Queries
  const { data: goalsData, isLoading: isLoadingGoals } = useGoals()
  const { data: summaryData, isLoading: isLoadingSummary } = useGoalsSummary()

  // Mutations
  const createGoalMutation = useCreateGoal()
  const updateGoalMutation = useUpdateGoal()
  const deleteGoalMutation = useDeleteGoal()
  const updateProgressMutation = useUpdateGoalProgress()
  const createMilestoneMutation = useCreateMilestone()
  const updateMilestoneMutation = useUpdateMilestone()
  const deleteMilestoneMutation = useDeleteMilestone()

  // Handlers
  const handleCreateGoal = () => {
    setIsCreateModalOpen(true)
  }

  const handleEditGoal = (goal: FinancialGoal) => {
    setEditingGoal(goal)
  }

  const handleDeleteGoal = async (goalId: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta meta financeira?')) {
      try {
        await deleteGoalMutation.mutateAsync(goalId)
      } catch (error) {
        console.error('Erro ao excluir meta:', error)
      }
    }
  }

  const handleCreateSubmit = async (data: CreateGoalData | UpdateGoalData) => {
    try {
      await createGoalMutation.mutateAsync(data as CreateGoalData)
      setIsCreateModalOpen(false)
    } catch (error) {
      console.error('Erro ao criar meta:', error)
    }
  }

  const handleEditSubmit = async (data: UpdateGoalData) => {
    if (!editingGoal) return

    try {
      await updateGoalMutation.mutateAsync({
        id: editingGoal.id,
        data
      })
      setEditingGoal(null)
    } catch (error) {
      console.error('Erro ao atualizar meta:', error)
    }
  }

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false)
  }

  const handleCloseEditModal = () => {
    setEditingGoal(null)
  }

  const handleUpdateProgress = async (goalId: string, amount: number, operation: ProgressOperation = 'set', description?: string) => {
    try {
      await updateProgressMutation.mutateAsync({
        id: goalId,
        data: { amount, operation, description }
      })
    } catch (error) {
      console.error('Erro ao atualizar progresso:', error)
    }
  }

  const handleAddMilestone = (goalId: string) => {
    setMilestoneGoalId(goalId)
    setEditingMilestone(null)
    setIsMilestoneModalOpen(true)
  }

  const handleEditMilestone = (milestone: GoalMilestone) => {
    setMilestoneGoalId(milestone.goalId)
    setEditingMilestone(milestone)
    setIsMilestoneModalOpen(true)
  }

  const handleDeleteMilestone = async (milestoneId: string) => {
    if (window.confirm('Tem certeza que deseja excluir este marco?')) {
      try {
        await deleteMilestoneMutation.mutateAsync(milestoneId)
      } catch (error) {
        console.error('Erro ao excluir marco:', error)
      }
    }
  }

  // ✅ Função removida - status dos marcos agora é automático

  const handleMilestoneSubmit = async (data: CreateMilestoneData | UpdateMilestoneData) => {
    if (!milestoneGoalId) return

    try {
      if (editingMilestone) {
        await updateMilestoneMutation.mutateAsync({
          id: editingMilestone.id,
          data: data as UpdateMilestoneData
        })
      } else {
        await createMilestoneMutation.mutateAsync({
          goalId: milestoneGoalId,
          data: data as CreateMilestoneData
        })
      }
      setIsMilestoneModalOpen(false)
      setEditingMilestone(null)
      setMilestoneGoalId(null)
    } catch (error) {
      console.error('Erro ao salvar marco:', error)
    }
  }

  const handleCloseMilestoneModal = () => {
    setIsMilestoneModalOpen(false)
    setEditingMilestone(null)
    setMilestoneGoalId(null)
  }

  if (isLoadingGoals && isLoadingSummary) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <Target className="h-8 w-8" />
              Metas Financeiras
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Defina e acompanhe suas metas financeiras pessoais e familiares
            </p>
          </div>

          <Button
            onClick={handleCreateGoal}
            className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200"
            disabled={createGoalMutation.isPending}
          >
            <Plus className="h-5 w-5 mr-2" />
            Nova Meta
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <GoalsStatsCards 
        summary={summaryData?.data}
        isLoading={isLoadingSummary}
      />

      {/* Goals List */}
      <GoalsList
        goals={goalsData?.data || []}
        isLoading={isLoadingGoals}
        onEdit={handleEditGoal}
        onDelete={handleDeleteGoal}
        onUpdateProgress={handleUpdateProgress}
        onAddMilestone={handleAddMilestone}
        onEditMilestone={handleEditMilestone}
        onDeleteMilestone={handleDeleteMilestone}
      />

      {/* Create Goal Modal */}
      <GoalModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        onSubmit={handleCreateSubmit}
        mode="create"
        isLoading={createGoalMutation.isPending}
      />

      {/* Edit Goal Modal */}
      <GoalModal
        isOpen={!!editingGoal}
        onClose={handleCloseEditModal}
        onSubmit={handleEditSubmit}
        mode="edit"
        goal={editingGoal}
        isLoading={updateGoalMutation.isPending}
      />

      {/* Milestone Modal */}
      <MilestoneModal
        isOpen={isMilestoneModalOpen}
        onClose={handleCloseMilestoneModal}
        onSubmit={handleMilestoneSubmit}
        mode={editingMilestone ? 'edit' : 'create'}
        milestone={editingMilestone}
        goalTargetAmount={milestoneGoalId ? goalsData?.data.find(g => g.id === milestoneGoalId)?.targetAmount : 0}
        isLoading={createMilestoneMutation.isPending || updateMilestoneMutation.isPending}
      />
    </div>
  )
}
