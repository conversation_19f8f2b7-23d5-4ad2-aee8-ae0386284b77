import { useState } from 'react'
import { TrendingUp, Calendar, BarChart3 } from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Skeleton } from '../../components/ui/skeleton'
import { ProgressHistoryList } from '../../components/progress-history/ProgressHistoryList'
import { useRecentProgressHistory } from '../../hooks/useProgressHistory'
import { formatCurrency, formatDate } from '../../lib/utils'

export function ProgressHistoryPage() {
  const { data: recentHistory, isLoading: isLoadingRecent } = useRecentProgressHistory(5)

  const getOperationStats = () => {
    if (!recentHistory?.data) return { additions: 0, subtractions: 0, definitions: 0 }
    
    return recentHistory.data.reduce((acc, entry) => {
      switch (entry.operation) {
        case 'add':
          acc.additions++
          break
        case 'subtract':
          acc.subtractions++
          break
        case 'set':
          acc.definitions++
          break
      }
      return acc
    }, { additions: 0, subtractions: 0, definitions: 0 })
  }

  const stats = getOperationStats()

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gradient-deep">Histórico de Progresso</h1>
          <p className="text-muted-foreground mt-1">
            Acompanhe todas as atualizações de progresso das suas metas financeiras
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Recent Activity */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Atividade Recente</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoadingRecent ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{recentHistory?.data?.length || 0}</div>
            )}
            <p className="text-xs text-muted-foreground">últimas 5 atualizações</p>
          </CardContent>
        </Card>

        {/* Additions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Adições</CardTitle>
            <TrendingUp className="h-4 w-4 text-success" />
          </CardHeader>
          <CardContent>
            {isLoadingRecent ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-success">{stats.additions}</div>
            )}
            <p className="text-xs text-muted-foreground">nas últimas atualizações</p>
          </CardContent>
        </Card>

        {/* Subtractions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Subtrações</CardTitle>
            <TrendingUp className="h-4 w-4 text-destructive rotate-180" />
          </CardHeader>
          <CardContent>
            {isLoadingRecent ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-destructive">{stats.subtractions}</div>
            )}
            <p className="text-xs text-muted-foreground">nas últimas atualizações</p>
          </CardContent>
        </Card>

        {/* Definitions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Definições</CardTitle>
            <BarChart3 className="h-4 w-4 text-warning" />
          </CardHeader>
          <CardContent>
            {isLoadingRecent ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-warning">{stats.definitions}</div>
            )}
            <p className="text-xs text-muted-foreground">nas últimas atualizações</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Updates Preview */}
      {recentHistory?.data && recentHistory.data.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Atualizações Recentes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentHistory.data.map((entry) => (
                <div
                  key={entry.id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/30 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      {entry.operation === 'add' && <TrendingUp className="h-4 w-4 text-success" />}
                      {entry.operation === 'subtract' && <TrendingUp className="h-4 w-4 text-destructive rotate-180" />}
                      {entry.operation === 'set' && <BarChart3 className="h-4 w-4 text-warning" />}
                    </div>
                    
                    <div>
                      <div className="font-medium text-sm">{entry.goalName}</div>
                      <div className="text-xs text-muted-foreground">
                        {formatDate(entry.createdAt)} • {formatCurrency(entry.previousAmount)} → {formatCurrency(entry.newAmount)}
                      </div>
                      {entry.description && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {entry.description}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Badge variant={
                      entry.operation === 'add' ? 'default' :
                      entry.operation === 'subtract' ? 'destructive' : 'secondary'
                    }>
                      {entry.operation === 'add' ? 'Adição' :
                       entry.operation === 'subtract' ? 'Subtração' : 'Definição'}
                    </Badge>
                    
                    <div className="text-sm font-medium">
                      {entry.operation === 'set' ? '' : (entry.amountChanged > 0 ? '+' : '')}
                      {formatCurrency(entry.amountChanged)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Full History List */}
      <ProgressHistoryList />
    </div>
  )
}
