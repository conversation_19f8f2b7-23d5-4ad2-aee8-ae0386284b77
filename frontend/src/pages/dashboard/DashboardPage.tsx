import { useQuery } from '@tanstack/react-query'
import { dashboardApi } from '@/lib/api'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatCurrency } from '@/lib/utils'
import { TrendingUp, TrendingDown, DollarSign, Calendar } from 'lucide-react'

export function DashboardPage() {
  const { data: overview, isLoading } = useQuery({
    queryKey: ['dashboard', 'overview'],
    queryFn: dashboardApi.getOverview,
  })

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  const stats = overview?.data || {}

  return (
    <div className="space-y-8">
      <div className="glass-deep p-6 rounded-2xl">
        <h1 className="text-4xl font-bold text-gradient-deep">Dashboard</h1>
        <p className="text-lg text-muted-foreground mt-2">Visão geral das suas finanças</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="glass-deep p-6 rounded-xl shadow-elegant hover:shadow-glow transition-all duration-300">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-success shadow-glow">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">
                Saldo Total
              </p>
              <p className="text-2xl font-bold text-foreground">
                {formatCurrency(stats.totalBalance || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="glass-deep p-6 rounded-xl shadow-elegant hover:shadow-glow transition-all duration-300">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-deep shadow-glow">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">
                Receitas (Mês)
              </p>
              <p className="text-2xl font-bold text-foreground">
                {formatCurrency(stats.monthlyIncome || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="glass-deep p-6 rounded-xl shadow-elegant hover:shadow-glow transition-all duration-300">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-destructive shadow-glow">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">
                Despesas (Mês)
              </p>
              <p className="text-2xl font-bold text-foreground">
                {formatCurrency(stats.monthlyExpenses || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="glass-deep p-6 rounded-xl shadow-elegant hover:shadow-glow transition-all duration-300">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-warning shadow-glow">
                <Calendar className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">
                Transações Futuras
              </p>
              <p className="text-2xl font-bold text-foreground">
                {stats.futureTransactions || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="mb-6">
          <h3 className="text-2xl font-bold text-gradient">Transações Recentes</h3>
          <p className="text-muted-foreground mt-2">
            Suas últimas movimentações financeiras
          </p>
        </div>

        <div className="space-y-4">
          {stats.recentTransactions?.length > 0 ? (
            stats.recentTransactions.map((transaction: any) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between rounded-xl bg-accent/50 p-4 hover:bg-accent transition-colors border border-border/50"
              >
                <div>
                  <p className="font-semibold text-foreground">
                    {transaction.description}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {transaction.account?.name}
                  </p>
                </div>
                <div className="text-right">
                  <p
                    className={`font-bold ${
                      transaction.type === 'INCOME'
                        ? 'text-success'
                        : 'text-destructive'
                    }`}
                  >
                    {transaction.type === 'INCOME' ? '+' : '-'}
                    {formatCurrency(transaction.amount)}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(transaction.transactionDate).toLocaleDateString(
                      'pt-BR'
                    )}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div className="py-12 text-center">
              <p className="text-muted-foreground text-lg">Nenhuma transação encontrada</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
