import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { futureTransactionsApi } from '@/lib/api'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { FutureTransactionsList } from '@/components/transactions/FutureTransactionsList'
import { FutureTransactionForm } from '@/components/transactions/FutureTransactionForm'
import { ProjectedBalanceCard } from '@/components/transactions/ProjectedBalanceCard'
import { FutureTransactionStats } from '@/components/transactions/FutureTransactionStats'
import { Plus, Calendar, TrendingUp } from 'lucide-react'

export function FutureTransactionsPage() {
  const [showForm, setShowForm] = useState(false)
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    sortBy: 'transactionDate',
    sortOrder: 'asc' as const,
  })

  const {
    data: futureTransactions,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['future-transactions', filters],
    queryFn: () => futureTransactionsApi.getAll(filters),
  })

  const { data: stats } = useQuery({
    queryKey: ['future-transactions', 'stats'],
    queryFn: futureTransactionsApi.getStats,
  })

  const { data: projectedBalance } = useQuery({
    queryKey: ['future-transactions', 'projected-balance'],
    queryFn: () =>
      futureTransactionsApi.getProjectedBalance({
        projectionDate: new Date(
          Date.now() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(), // 30 days from now
      }),
  })

  const handleFilterChange = (newFilters: any) => {
    setFilters({ ...filters, ...newFilters, page: 1 })
  }

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const handleTransactionCreated = () => {
    setShowForm(false)
    refetch()
  }

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <Calendar className="h-8 w-8" />
              Transações Futuras
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Gerencie suas transações agendadas e veja projeções de saldo
            </p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200 flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            Nova Transação Futura
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <FutureTransactionStats stats={stats?.data} />
        <ProjectedBalanceCard projectedBalance={projectedBalance?.data} />
      </div>

      {/* Filters and Actions */}
      <div className="glass-deep p-6 rounded-xl shadow-elegant">
        <div className="flex flex-col items-start justify-between gap-6 sm:flex-row sm:items-center">
          <div className="flex flex-wrap gap-4">
            <select
              value={filters.sortBy}
              onChange={(e) => handleFilterChange({ sortBy: e.target.value })}
              className="px-4 py-2 rounded-lg border border-border bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
            >
              <option value="transactionDate">Data da Transação</option>
              <option value="amount">Valor</option>
              <option value="description">Descrição</option>
              <option value="createdAt">Data de Criação</option>
            </select>

            <select
              value={filters.sortOrder}
              onChange={(e) =>
                handleFilterChange({ sortOrder: e.target.value })
              }
              className="px-4 py-2 rounded-lg border border-border bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
            >
              <option value="asc">Crescente</option>
              <option value="desc">Decrescente</option>
            </select>

            <select
              value={filters.limit}
              onChange={(e) =>
                handleFilterChange({ limit: parseInt(e.target.value, 10) })
              }
              className="px-4 py-2 rounded-lg border border-border bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
            >
              <option value={10}>10 por página</option>
              <option value={20}>20 por página</option>
              <option value={50}>50 por página</option>
            </select>
          </div>

          <div className="flex items-center gap-3 text-base text-muted-foreground">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-info shadow-soft">
              <TrendingUp className="h-4 w-4 text-white" />
            </div>
            {futureTransactions?.data?.total || 0} transações futuras
          </div>
        </div>
      </div>

      {/* Future Transactions List */}
      <FutureTransactionsList
        transactions={futureTransactions?.data?.transactions || []}
        pagination={futureTransactions?.data?.pagination}
        onPageChange={handlePageChange}
        onRefresh={refetch}
      />

      {/* Future Transaction Form Modal */}
      {showForm && (
        <FutureTransactionForm
          onClose={() => setShowForm(false)}
          onSuccess={handleTransactionCreated}
        />
      )}
    </div>
  )
}
