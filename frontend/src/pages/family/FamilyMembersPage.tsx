import { Users, Plus } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import {
  useFamilyMemberStats,
  useArchiveFamilyMember,
  useRestoreFamilyMember,
  useDeleteFamilyMember
} from '@/hooks/useFamilyMembers'
import { FamilyMembersList } from '@/components/family/FamilyMembersList'
import { CreateFamilyMemberDialog } from '@/components/family/create-family-member-dialog'
import { EditFamilyMemberDialog } from '@/components/family/edit-family-member-dialog'
import { FamilyMember } from '@/types/family-member.types'
import { useState } from 'react'

export function FamilyMembersPage() {
  const [editingMember, setEditingMember] = useState<FamilyMember | null>(null)
  const [isCreateDialogO<PERSON>, setIsCreateDialogOpen] = useState(false)

  const queryClient = useQueryClient()
  const { data: stats, isLoading: isLoadingStats } = useFamilyMemberStats()

  // Mutations for member actions
  const archiveMutation = useArchiveFamilyMember()
  const restoreMutation = useRestoreFamilyMember()
  const deleteMutation = useDeleteFamilyMember()

  // Handlers for table actions
  const handleEdit = (member: FamilyMember) => {
    setEditingMember(member)
  }

  const handleArchive = (member: FamilyMember) => {
    if (window.confirm(`Tem certeza que deseja arquivar "${member.name}"?`)) {
      archiveMutation.mutate(member.id, {
        onSuccess: () => {
          toast.success('Membro arquivado com sucesso!')
          queryClient.invalidateQueries({ queryKey: ['family-members'] })
        },
        onError: (error: any) => {
          console.error('Erro ao arquivar membro:', error)
          const message = error.response?.data?.message || 'Erro ao arquivar membro. Tente novamente.'
          toast.error(message)
        }
      })
    }
  }

  const handleRestore = (member: FamilyMember) => {
    if (window.confirm(`Tem certeza que deseja restaurar "${member.name}"?`)) {
      restoreMutation.mutate(member.id, {
        onSuccess: () => {
          toast.success('Membro restaurado com sucesso!')
          queryClient.invalidateQueries({ queryKey: ['family-members'] })
        },
        onError: (error: any) => {
          console.error('Erro ao restaurar membro:', error)
          const message = error.response?.data?.message || 'Erro ao restaurar membro. Tente novamente.'
          toast.error(message)
        }
      })
    }
  }

  const handleDelete = (member: FamilyMember) => {
    if (window.confirm(`Tem certeza que deseja excluir permanentemente "${member.name}"? Esta ação não pode ser desfeita.`)) {
      deleteMutation.mutate(member.id, {
        onSuccess: () => {
          toast.success('Membro excluído com sucesso!')
          queryClient.invalidateQueries({ queryKey: ['family-members'] })
        },
        onError: (error: any) => {
          console.error('Erro ao excluir membro:', error)
          const message = error.response?.data?.message || 'Erro ao excluir membro. Tente novamente.'
          toast.error(message)
        }
      })
    }
  }

  const handleCreateMember = () => {
    setIsCreateDialogOpen(true)
  }



  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="flex items-center gap-3 text-4xl font-bold text-gradient-deep">
              <Users className="h-8 w-8" />
              Membros da Família
            </h1>
            <p className="text-lg text-muted-foreground">
              Gerencie os membros da sua família e suas informações
            </p>
          </div>
          <Button
            onClick={handleCreateMember}
            className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200"
          >
            <Plus className="h-5 w-5 mr-2" />
            Novo Membro
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid gap-8">
        {/* Stats Cards */}
        <div className="grid gap-6 md:grid-cols-3">
          <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-foreground">Total de Membros</CardTitle>
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-soft">
                <Users className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">
                {isLoadingStats ? '...' : stats?.totalMembers || 0}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {stats?.totalMembers === 0 ? 'Nenhum membro cadastrado' : 'membros cadastrados'}
              </p>
            </CardContent>
          </Card>

          <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-foreground">Membros Ativos</CardTitle>
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-success shadow-soft">
                <Users className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">
                {isLoadingStats ? '...' : stats?.activeMembers || 0}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Membros não arquivados
              </p>
            </CardContent>
          </Card>

          <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-foreground">Membros Arquivados</CardTitle>
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-warning shadow-soft">
                <Users className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">
                {isLoadingStats ? '...' : stats?.archivedMembers || 0}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Membros arquivados
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Family Members List */}
        <FamilyMembersList
          onEdit={handleEdit}
          onDelete={handleDelete}
          onArchive={handleArchive}
          onRestore={handleRestore}
        />
      </div>

      {/* Create Member Dialog */}
      <CreateFamilyMemberDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />

      {/* Edit Member Dialog */}
      {editingMember && (
        <EditFamilyMemberDialog
          member={editingMember}
          open={!!editingMember}
          onOpenChange={(open) => {
            if (!open) setEditingMember(null)
          }}
        />
      )}
    </div>
  )
}
