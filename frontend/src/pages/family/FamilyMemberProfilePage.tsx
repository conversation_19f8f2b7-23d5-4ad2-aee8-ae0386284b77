import { use<PERSON><PERSON><PERSON>, useNavi<PERSON>, <PERSON> } from 'react-router-dom'
import { ArrowLeft, Edit, Calendar, TrendingUp, TrendingDown, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useFamilyMember } from '@/hooks/useFamilyMembers'
import { EditFamilyMemberDialog } from '@/components/family/edit-family-member-dialog'
import { useState } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { ptBR } from 'date-fns/locale'

export function FamilyMemberProfilePage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const { data: member, isLoading, error } = useFamilyMember(id!)

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center space-x-4">
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
          <div className="h-8 w-48 bg-muted animate-pulse rounded" />
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="h-32 bg-muted animate-pulse rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  if (error || !member) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">Membro não encontrado</h1>
          <p className="text-muted-foreground">
            O membro que você está procurando não existe ou foi removido.
          </p>
          <Button onClick={() => navigate('/family-members')}>
            Voltar para lista de membros
          </Button>
        </div>
      </div>
    )
  }

  const isArchived = !!member.deletedAt

  return (
    <div className="space-y-8">
      {/* Header with Navigation */}
      <div className="glass-deep p-6 rounded-2xl shadow-elegant">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/family-members')}
              className="gap-2 hover:bg-accent"
            >
              <ArrowLeft className="h-4 w-4" />
              Voltar
            </Button>
            <Separator orientation="vertical" className="h-8" />
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16 shadow-glow">
                <AvatarImage src={member.avatar} alt={member.name} />
                <AvatarFallback style={{ backgroundColor: member.color }} className="text-white font-bold text-lg">
                  {member.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-3xl font-bold text-gradient-deep">{member.name}</h1>
                <div className="flex items-center space-x-3 mt-2">
                  <Badge variant={isArchived ? "secondary" : "default"} className="px-3 py-1">
                    {isArchived ? "Arquivado" : "Ativo"}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    Criado {formatDistanceToNow(new Date(member.createdAt), { addSuffix: true, locale: ptBR })}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <Button
            onClick={() => setIsEditDialogOpen(true)}
            className="bg-gradient-deep text-white px-6 py-3 rounded-xl font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-200 gap-2"
          >
            <Edit className="h-4 w-4" />
            Editar
          </Button>
        </div>
      </div>

      {/* Member Details */}
      <div className="grid gap-8 md:grid-cols-3">
        {/* Basic Information */}
        <Card className="glass-deep shadow-elegant">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl font-bold text-gradient">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-info shadow-soft">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              Informações Básicas
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label className="text-sm font-semibold text-muted-foreground">Nome</label>
              <p className="text-lg font-medium text-foreground mt-1">{member.name}</p>
            </div>
            <div>
              <label className="text-sm font-semibold text-muted-foreground">Cor</label>
              <div className="flex items-center space-x-3 mt-2">
                <div
                  className="h-6 w-6 rounded-full border-2 border-border shadow-soft"
                  style={{ backgroundColor: member.color }}
                />
                <span className="text-sm font-medium text-foreground">{member.color}</span>
              </div>
            </div>
            <div>
              <label className="text-sm font-semibold text-muted-foreground">Status</label>
              <p className="text-sm font-medium text-foreground mt-1">
                {isArchived ? "Arquivado" : "Ativo"}
              </p>
            </div>
            <div>
              <label className="text-sm font-semibold text-muted-foreground">Criado em</label>
              <p className="text-sm font-medium text-foreground mt-1">
                {new Date(member.createdAt).toLocaleDateString('pt-BR')}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Statistics - Placeholder */}
        <Card className="glass-deep shadow-elegant">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl font-bold text-gradient">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-success shadow-soft">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              Estatísticas
            </CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              Resumo das atividades financeiras
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between p-3 rounded-lg bg-accent/20 border border-border/50">
              <span className="text-sm font-medium text-muted-foreground">Total de Transações</span>
              <span className="font-bold text-foreground">0</span>
            </div>
            <div className="flex items-center justify-between p-3 rounded-lg bg-accent/20 border border-border/50">
              <span className="text-sm font-medium text-muted-foreground">Gastos Totais</span>
              <span className="font-bold text-destructive">R$ 0,00</span>
            </div>
            <div className="flex items-center justify-between p-3 rounded-lg bg-accent/20 border border-border/50">
              <span className="text-sm font-medium text-muted-foreground">Receitas Totais</span>
              <span className="font-bold text-success">R$ 0,00</span>
            </div>
            <div className="flex items-center justify-between p-3 rounded-lg bg-accent/20 border border-border/50">
              <span className="text-sm font-medium text-muted-foreground">Média Mensal</span>
              <span className="font-bold text-foreground">R$ 0,00</span>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="glass-deep shadow-elegant">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gradient">Ações Rápidas</CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              Operações comuns para este membro
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button variant="outline" className="w-full justify-start gap-3 h-12 border-primary/20 hover:bg-primary/10 hover:border-primary/40 transition-all">
              <DollarSign className="h-5 w-5" />
              Ver Transações
            </Button>
            <Button variant="outline" className="w-full justify-start gap-3 h-12 border-success/20 hover:bg-success/10 hover:border-success/40 transition-all">
              <TrendingUp className="h-5 w-5" />
              Relatório de Gastos
            </Button>
            <Button variant="outline" className="w-full justify-start gap-3 h-12 border-info/20 hover:bg-info/10 hover:border-info/40 transition-all">
              <TrendingDown className="h-5 w-5" />
              Histórico Financeiro
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start gap-3 h-12 border-warning/20 hover:bg-warning/10 hover:border-warning/40 transition-all"
              onClick={() => setIsEditDialogOpen(true)}
            >
              <Edit className="h-5 w-5" />
              Editar Informações
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity - Placeholder */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader className="pb-6">
          <CardTitle className="text-2xl font-bold text-gradient">Atividade Recente</CardTitle>
          <CardDescription className="text-base text-muted-foreground">
            Últimas transações e atividades deste membro
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-16">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-deep shadow-glow mx-auto mb-6">
              <Calendar className="h-8 w-8 text-white" />
            </div>
            <p className="text-lg font-medium text-muted-foreground">Nenhuma atividade recente encontrada.</p>
            <p className="text-sm mt-2 text-muted-foreground">
              As transações e atividades aparecerão aqui quando disponíveis.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      {isEditDialogOpen && (
        <EditFamilyMemberDialog
          member={member}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
        />
      )}
    </div>
  )
}
