import { useState, useEffect } from 'react'
import { Navigate, useLocation, useNavigate, Link } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { TrendingUp, Eye, EyeOff, AlertCircle, CheckCircle2 } from 'lucide-react'
import { useAuthStore } from '@/stores/auth.store'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'

const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'Email é obrigatório')
    .email('Formato de email inválido'),
  password: z
    .string()
    .min(6, 'Senha deve ter pelo menos 6 caracteres')
    .max(100, 'Senha muito longa'),
})

type LoginFormData = z.infer<typeof loginSchema>

export function LoginPage() {
  const { login, isAuthenticated, isLoading, error: authError } = useAuthStore()
  const location = useLocation()
  const navigate = useNavigate()
  const [showPassword, setShowPassword] = useState(false)
  const [rememberEmail, setRememberEmail] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isValid, touchedFields },
    watch,
    setFocus,
    setValue,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange', // Validação em tempo real
    defaultValues: {
      email: '',
      password: '',
    },
  })

  // Auto-focus no campo email quando a página carrega
  useEffect(() => {
    setFocus('email')

    // Recuperar email salvo se existir
    const savedEmail = localStorage.getItem('rememberedEmail')
    if (savedEmail) {
      setValue('email', savedEmail)
      setRememberEmail(true)
    }
  }, [setFocus, setValue])

  // Watch para validação em tempo real
  const watchedEmail = watch('email')
  const watchedPassword = watch('password')

  // Redirect if already authenticated
  if (isAuthenticated) {
    const from = location.state?.from?.pathname || '/dashboard'
    return <Navigate to={from} replace />
  }

  const onSubmit = async (data: LoginFormData) => {
    try {
      // Salvar email se "lembrar" estiver marcado
      if (rememberEmail) {
        localStorage.setItem('rememberedEmail', data.email)
      } else {
        localStorage.removeItem('rememberedEmail')
      }

      await login(data.email, data.password)

      // Navigate after successful login
      const from = location.state?.from?.pathname || '/dashboard'
      navigate(from, { replace: true })
    } catch (error) {
      // Error is handled by the store
    }
  }

  // Função para determinar o estado visual do campo
  const getFieldState = (fieldName: keyof LoginFormData) => {
    const hasError = !!errors[fieldName]
    const isTouched = touchedFields[fieldName]
    const hasValue = fieldName === 'email' ? watchedEmail : watchedPassword

    if (hasError && isTouched) return 'error'
    if (!hasError && isTouched && hasValue) return 'success'
    return 'default'
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-subtle px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-deep shadow-glow">
              <TrendingUp className="h-10 w-10 text-white" />
            </div>
          </div>
          <h2 className="mt-6 text-4xl font-bold text-gradient-deep">
            Personal Finance Manager
          </h2>
          <p className="mt-3 text-base text-muted-foreground">
            Faça login para acessar sua conta
          </p>
        </div>

        <form className="mt-8 space-y-6 glass-deep p-8 rounded-2xl" onSubmit={handleSubmit(onSubmit)}>
          {/* Mostrar erro de autenticação se existir */}
          {authError && (
            <div className="rounded-lg bg-destructive/10 border border-destructive/20 p-4 shadow-soft">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-destructive mr-3" />
                <p className="text-sm text-destructive font-medium">{authError}</p>
              </div>
            </div>
          )}

          <div className="space-y-6">
            <div className="space-y-3">
              <label
                htmlFor="email"
                className="text-sm font-semibold text-foreground"
              >
                Email
              </label>
              <div className="relative">
                <Input
                  id="email"
                  {...register('email')}
                  type="email"
                  autoComplete="email"
                  placeholder="<EMAIL>"
                  className={cn(
                    "transition-all duration-200 h-12 text-base",
                    getFieldState('email') === 'error' &&
                      "border-destructive focus-visible:ring-destructive",
                    getFieldState('email') === 'success' &&
                      "border-success focus-visible:ring-success"
                  )}
                  aria-invalid={errors.email ? 'true' : 'false'}
                  aria-describedby={errors.email ? 'email-error' : undefined}
                />
                {getFieldState('email') === 'success' && (
                  <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-success" />
                )}
              </div>
              {errors.email && (
                <p
                  id="email-error"
                  className="text-sm text-destructive flex items-center mt-2"
                  role="alert"
                >
                  <AlertCircle className="h-4 w-4 mr-2" />
                  {errors.email.message}
                </p>
              )}
            </div>

            <div className="space-y-3">
              <label
                htmlFor="password"
                className="text-sm font-semibold text-foreground"
              >
                Senha
              </label>
              <div className="relative">
                <Input
                  id="password"
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  placeholder="Sua senha"
                  className={cn(
                    "pr-12 transition-all duration-200 h-12 text-base",
                    getFieldState('password') === 'error' &&
                      "border-destructive focus-visible:ring-destructive",
                    getFieldState('password') === 'success' &&
                      "border-success focus-visible:ring-success"
                  )}
                  aria-invalid={errors.password ? 'true' : 'false'}
                  aria-describedby={errors.password ? 'password-error' : undefined}
                />
                <div className="absolute inset-y-0 right-0 flex items-center">
                  {getFieldState('password') === 'success' && (
                    <CheckCircle2 className="h-5 w-5 text-success mr-2" />
                  )}
                  <button
                    type="button"
                    className="flex items-center pr-3 text-muted-foreground hover:text-foreground transition-colors"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={showPassword ? 'Ocultar senha' : 'Mostrar senha'}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
              {errors.password && (
                <p
                  id="password-error"
                  className="text-sm text-destructive flex items-center mt-2"
                  role="alert"
                >
                  <AlertCircle className="h-4 w-4 mr-2" />
                  {errors.password.message}
                </p>
              )}
            </div>
            {/* Checkbox para lembrar email */}
            <div className="flex items-center">
              <input
                id="remember-email"
                type="checkbox"
                checked={rememberEmail}
                onChange={(e) => setRememberEmail(e.target.checked)}
                className="h-4 w-4 rounded border-input bg-background text-primary focus:ring-primary focus:ring-offset-background"
              />
              <label htmlFor="remember-email" className="ml-3 text-sm text-muted-foreground">
                Lembrar email
              </label>
            </div>
          </div>

          <div>
            <Button
              type="submit"
              disabled={isLoading || isSubmitting || !isValid}
              className="w-full h-12 text-base font-semibold transition-all duration-200 bg-gradient-deep hover:shadow-glow"
              size="lg"
            >
              {isLoading || isSubmitting ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2">Entrando...</span>
                </div>
              ) : (
                'Entrar'
              )}
            </Button>
          </div>

          <div className="text-center space-y-6">
            <p className="text-sm text-muted-foreground">
              Não tem uma conta?{' '}
              <Link
                to="/register"
                className="text-primary hover:text-primary/80 font-semibold transition-colors"
              >
                Cadastre-se aqui
              </Link>
            </p>

            <div className="border-t border-border pt-6">
              <div className="glass p-4 rounded-lg">
                <p className="text-xs text-muted-foreground">
                  <strong className="text-foreground">Credenciais de teste:</strong><br />
                  Email: <EMAIL><br />
                  Senha: 123456
                </p>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
