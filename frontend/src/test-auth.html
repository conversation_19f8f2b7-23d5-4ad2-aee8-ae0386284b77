<!DOCTYPE html>
<html>
<head>
    <title>Test Auth</title>
</head>
<body>
    <h1>Test Authentication</h1>
    <button onclick="testLogin()">Test Login</button>
    <button onclick="testGoals()">Test Goals API</button>
    <button onclick="checkLocalStorage()">Check LocalStorage</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            try {
                const response = await fetch('http://localhost:3001/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.success) {
                    localStorage.setItem('auth-token', data.data.token);
                    console.log('Token saved:', data.data.token);
                }
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }

        async function testGoals() {
            const token = localStorage.getItem('auth-token');
            if (!token) {
                document.getElementById('result').innerHTML = 'No token found. Please login first.';
                return;
            }

            try {
                const response = await fetch('http://localhost:3001/api/v1/goals', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }

        function checkLocalStorage() {
            const authStorage = localStorage.getItem('auth-storage');
            const token = localStorage.getItem('auth-token');
            
            document.getElementById('result').innerHTML = `
                <h3>LocalStorage Contents:</h3>
                <p><strong>auth-storage:</strong> ${authStorage}</p>
                <p><strong>auth-token:</strong> ${token}</p>
            `;
        }
    </script>
</body>
</html>
