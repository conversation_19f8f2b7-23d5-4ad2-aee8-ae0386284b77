import { api } from '@/lib/api'
import type {
  FamilyMember,
  CreateFamilyMemberRequest,
  UpdateFamilyMemberRequest,
  FamilyMemberFilters,
  PaginatedFamilyMembersResponse,
  FamilyMemberStats,
  FamilyMemberResponse,
} from '@/types/family-member.types'

const BASE_URL = '/family-members'

export const familyMemberService = {
  /**
   * Get all family members with optional filters
   */
  async getAll(filters: FamilyMemberFilters = {}): Promise<PaginatedFamilyMembersResponse> {
    const params = new URLSearchParams()

    if (filters.name) params.append('name', filters.name)
    if (filters.includeArchived !== undefined) {
      params.append('includeArchived', filters.includeArchived.toString())
    }
    if (filters.includeDeleted !== undefined) {
      params.append('includeDeleted', filters.includeDeleted.toString())
    }
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())

    const response = await api.get<{
      success: boolean
      data: FamilyMember[]
      pagination: {
        page: number
        limit: number
        total: number
        totalPages: number
        hasNext: boolean
        hasPrev: boolean
      }
      message?: string
    }>(`${BASE_URL}?${params.toString()}`)

    if (!response.data.success) {
      throw new Error('Erro ao buscar membros da família')
    }

    // Return the data in the expected format
    return {
      data: response.data.data,
      pagination: response.data.pagination
    }
  },

  /**
   * Get a single family member by ID
   */
  async getById(id: string): Promise<FamilyMember> {
    const response = await api.get<FamilyMemberResponse>(`${BASE_URL}/${id}`)

    if (!response.data.success) {
      throw new Error('Erro ao buscar membro da família')
    }

    return response.data.data
  },

  /**
   * Create a new family member
   */
  async create(data: CreateFamilyMemberRequest): Promise<FamilyMember> {
    const response = await api.post<FamilyMemberResponse>(BASE_URL, data)

    if (!response.data.success) {
      throw new Error('Erro ao criar membro da família')
    }

    return response.data.data
  },

  /**
   * Update an existing family member
   */
  async update(id: string, data: UpdateFamilyMemberRequest): Promise<FamilyMember> {
    const response = await api.put<FamilyMemberResponse>(`${BASE_URL}/${id}`, data)

    if (!response.data.success) {
      throw new Error('Erro ao atualizar membro da família')
    }

    return response.data.data
  },

  /**
   * Archive a family member (soft delete)
   */
  async archive(id: string): Promise<FamilyMember> {
    const response = await api.patch<FamilyMemberResponse>(`${BASE_URL}/${id}/archive`, {
      archived: true
    })

    if (!response.data.success) {
      throw new Error('Erro ao arquivar membro da família')
    }

    return response.data.data
  },

  /**
   * Restore an archived family member
   */
  async restore(id: string): Promise<FamilyMember> {
    const response = await api.patch<FamilyMemberResponse>(`${BASE_URL}/${id}/archive`, {
      archived: false
    })

    if (!response.data.success) {
      throw new Error('Erro ao restaurar membro da família')
    }

    return response.data.data
  },

  /**
   * Permanently delete a family member
   */
  async delete(id: string): Promise<void> {
    const response = await api.delete<{ success: boolean; message?: string }>(`${BASE_URL}/${id}`)

    if (!response.data.success) {
      throw new Error(response.data.message || 'Erro ao excluir membro da família permanentemente')
    }
  },

  /**
   * Get family members statistics
   */
  async getStats(): Promise<FamilyMemberStats> {
    const response = await api.get<{ success: boolean; data: FamilyMemberStats }>(`${BASE_URL}/stats`)
    
    if (!response.data.success) {
      throw new Error('Erro ao buscar estatísticas dos membros da família')
    }
    
    return response.data.data
  },

  /**
   * Upload avatar for family member
   */
  async uploadAvatar(id: string, file: File): Promise<{ avatarUrl: string }> {
    const formData = new FormData()
    formData.append('avatar', file)

    const response = await api.post<{ success: boolean; data: { avatarUrl: string } }>(
      `${BASE_URL}/${id}/avatar`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    )
    
    if (!response.data.success) {
      throw new Error('Erro ao fazer upload do avatar')
    }
    
    return response.data.data
  },

  /**
   * Remove avatar from family member
   */
  async removeAvatar(id: string): Promise<void> {
    const response = await api.delete<{ success: boolean; message?: string }>(`${BASE_URL}/${id}/avatar`)
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Erro ao remover avatar')
    }
  },
}
