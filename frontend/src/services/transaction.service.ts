import { api } from '@/lib/api'
import {
  Transaction,
  CreateTransactionData,
  UpdateTransactionData,
  TransactionFilters,
  PaginatedTransactionsResponse,
  TransactionStats,
  InstallmentListItem,
  InstallmentFilters
} from '@/types/transaction.types'

export const transactionService = {
  /**
   * Get all transactions with filters and pagination
   */
  async getAll(filters?: TransactionFilters): Promise<PaginatedTransactionsResponse> {
    const params = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value))
        }
      })
    }
    
    const response = await api.get(`/transactions?${params.toString()}`)
    return response.data.data || response.data
  },

  /**
   * Get transaction by ID with all installments
   */
  async getById(id: string): Promise<Transaction> {
    const response = await api.get(`/transactions/${id}`)
    return response.data.data
  },

  /**
   * Create a new transaction with installments
   */
  async create(data: CreateTransactionData): Promise<Transaction> {
    const response = await api.post('/transactions', data)
    return response.data.data
  },

  /**
   * Update transaction and installments (DELETE + CREATE strategy)
   */
  async update(id: string, data: UpdateTransactionData): Promise<Transaction> {
    const response = await api.put(`/transactions/${id}`, data)
    return response.data.data
  },

  /**
   * Delete transaction and all installments
   */
  async delete(id: string): Promise<void> {
    await api.delete(`/transactions/${id}`)
  },

  /**
   * Get transaction statistics
   */
  async getStats(): Promise<TransactionStats> {
    const response = await api.get('/transactions/stats')
    return response.data.data
  },

  // Note: getInstallments removed - installments now come with transactions

  /**
   * Mark installment as paid/unpaid
   */
  async updateInstallmentStatus(
    transactionId: string, 
    installmentNumber: number, 
    isPaid: boolean
  ): Promise<Transaction> {
    const response = await api.patch(
      `/transactions/${transactionId}/installments/${installmentNumber}`,
      { isPaid, paidAt: isPaid ? new Date().toISOString() : null }
    )
    return response.data.data
  },

  // Note: Bulk operations and installment-specific endpoints removed
  // These features can be implemented later when needed

  /**
   * Export transactions to CSV/Excel
   */
  async exportTransactions(
    format: 'csv' | 'excel',
    filters?: TransactionFilters
  ): Promise<Blob> {
    const params = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value))
        }
      })
    }
    
    const response = await api.get(
      `/transactions/export/${format}?${params.toString()}`,
      { responseType: 'blob' }
    )
    
    return response.data
  },

  /**
   * Import transactions from CSV/Excel
   */
  async importTransactions(file: File): Promise<{
    success: number
    errors: Array<{ row: number; message: string }>
  }> {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await api.post('/transactions/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data.data
  },

  /**
   * Duplicate transaction (useful for recurring transactions)
   */
  async duplicate(id: string, newDate?: string): Promise<Transaction> {
    const response = await api.post(`/transactions/${id}/duplicate`, {
      transactionDate: newDate
    })
    return response.data.data
  },

  /**
   * Split transaction into multiple transactions
   */
  async split(id: string, splits: Array<{
    description: string
    amount: number
    categoryId?: string
  }>): Promise<Transaction[]> {
    const response = await api.post(`/transactions/${id}/split`, { splits })
    return response.data.data
  },

  /**
   * Merge multiple transactions into one
   */
  async merge(transactionIds: string[], mergedData: {
    description: string
    categoryId?: string
  }): Promise<Transaction> {
    const response = await api.post('/transactions/merge', {
      transactionIds,
      ...mergedData
    })
    return response.data.data
  },

  /**
   * Get transaction insights/analytics
   */
  async getInsights(period: 'week' | 'month' | 'quarter' | 'year' = 'month'): Promise<{
    categoryBreakdown: Array<{
      categoryId: string
      categoryName: string
      amount: number
      percentage: number
    }>
    monthlyTrends: Array<{
      month: string
      income: number
      expenses: number
      net: number
    }>
    topExpenses: Array<{
      description: string
      amount: number
      date: string
    }>
    installmentSummary: {
      totalInstallments: number
      paidInstallments: number
      overdueInstallments: number
      upcomingInstallments: number
    }
  }> {
    const response = await api.get(`/transactions/insights?period=${period}`)
    return response.data.data
  }
}
