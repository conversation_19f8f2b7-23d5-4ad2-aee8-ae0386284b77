# 🔧 Configuração de Portas - Personal Finance Manager

## 📊 Configuração Atual

### **Frontend (Vite)**
- **Porta**: `3000`
- **URL**: `http://localhost:3000`
- **Arquivo**: `frontend/vite.config.ts`

### **Backend (Express)**
- **Porta**: `3001`
- **URL**: `http://localhost:3001`
- **Arquivo**: `backend/src/index.ts`

### **Proxy (Vite → Backend)**
- **Configuração**: `/api` → `http://localhost:3001`
- **Arquivo**: `frontend/vite.config.ts`

## 🔗 Configurações de CORS

### **Backend CORS**
- **Origem Permitida**: `http://localhost:3000`
- **Arquivo**: `backend/.env` → `FRONTEND_URL`
- **Fallback**: `http://localhost:3000` (código)

### **Variáveis de Ambiente**

#### **Frontend (.env)**
```env
VITE_API_URL=http://localhost:3001/api/v1
```

#### **Backend (.env)**
```env
PORT=3001
FRONTEND_URL=http://localhost:3000
```

## ⚠️ **IMPORTANTE - Evitar Problemas Futuros**

### **1. Sempre Verificar Consistência**
Antes de fazer mudanças, verifique se todas as configurações estão alinhadas:

```bash
# Verificar configuração do backend
curl http://localhost:3001/debug/config

# Verificar se CORS está funcionando
curl -X OPTIONS http://localhost:3001/api/v1/auth/login \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST" \
  -v
```

### **2. Ordem de Prioridade das Configurações**
1. **Variáveis de Ambiente** (`.env`) - **MAIOR PRIORIDADE**
2. **Código** (fallback values)

### **3. Checklist para Mudanças de Porta**

#### **Se mudar a porta do Frontend:**
- [ ] Atualizar `frontend/vite.config.ts` (server.port)
- [ ] Atualizar `backend/.env` (FRONTEND_URL)
- [ ] Reiniciar ambos os serviços
- [ ] Testar CORS

#### **Se mudar a porta do Backend:**
- [ ] Atualizar `backend/.env` (PORT)
- [ ] Atualizar `frontend/.env` (VITE_API_URL)
- [ ] Atualizar `frontend/vite.config.ts` (proxy target)
- [ ] Reiniciar ambos os serviços
- [ ] Testar API e proxy

### **4. Comandos de Debug**

```bash
# Verificar se serviços estão rodando
curl http://localhost:3000  # Frontend
curl http://localhost:3001/health  # Backend

# Verificar configuração do backend
curl http://localhost:3001/debug/config

# Testar proxy do frontend
curl http://localhost:3000/api/v1/health

# Testar CORS
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'
```

### **5. Logs de Debug**
O backend agora mostra a configuração de CORS no startup:
```
🔗 CORS configured for origin: http://localhost:3000
```

## 🚨 **Problemas Comuns e Soluções**

### **Erro: "CORS policy: Response to preflight request doesn't pass access control check"**
**Causa**: Discrepância entre FRONTEND_URL no backend e a porta real do frontend
**Solução**: 
1. Verificar `backend/.env` → `FRONTEND_URL`
2. Verificar `frontend/vite.config.ts` → `server.port`
3. Garantir que ambos estejam alinhados
4. Reiniciar backend

### **Erro: "Failed to load resource: net::ERR_CONNECTION_REFUSED"**
**Causa**: Discrepância entre VITE_API_URL no frontend e a porta real do backend
**Solução**:
1. Verificar `frontend/.env` → `VITE_API_URL`
2. Verificar `backend/.env` → `PORT`
3. Verificar `frontend/vite.config.ts` → `proxy.target`
4. Garantir que todos estejam alinhados
5. Reiniciar ambos os serviços

### **Cache de Configuração**
Se as mudanças não surtirem efeito:
```bash
# Limpar cache do Vite
rm -rf frontend/node_modules/.vite

# Reiniciar serviços
# Terminal 1: cd backend && npm run dev
# Terminal 2: cd frontend && npm run dev
```

## 📝 **Histórico de Mudanças**
- **2025-06-07**: Configuração inicial com Frontend:3000, Backend:3001
- **2025-06-07**: Correção de CORS - FRONTEND_URL atualizada de 5173 para 3000
- **2025-06-07**: Adicionado endpoint de debug e logs de configuração
