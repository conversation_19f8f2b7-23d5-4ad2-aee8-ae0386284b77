# Test Data Management System

This document describes the comprehensive test data management system for the Personal Finance Manager project.

## 📋 Overview

The test data management system provides:

- **Factories** for generating realistic test data
- **Seeders** for populating the database with test data
- **Fixtures** for consistent, reusable test scenarios
- **CLI Tools** for managing test data from command line
- **Database Isolation** ensuring tests don't interfere with each other

## 🏗️ Architecture

### Components

```
backend/src/tests/
├── factories/          # Data generation factories
│   ├── base.factory.ts
│   ├── user.factory.ts
│   ├── account.factory.ts
│   └── transaction.factory.ts
├── seeders/            # Database population seeders
│   ├── base.seeder.ts
│   ├── user.seeder.ts
│   ├── account.seeder.ts
│   ├── category.seeder.ts
│   └── transaction.seeder.ts
├── fixtures/           # Static test data
│   └── index.ts
└── utils/              # Management utilities
    └── test-data-manager.ts
```

## 🏭 Factories

Factories generate realistic test data using Faker.js.

### Base Factory

```typescript
import { BaseFactory } from './factories/base.factory';

class MyFactory extends BaseFactory<MyType> {
  protected definition(): Partial<MyType> {
    return {
      id: this.generateId(),
      name: this.generateName(),
      amount: this.generateAmount(10, 1000),
    };
  }
}
```

### Available Factories

#### UserFactory
```typescript
import { userFactory } from './factories/user.factory';

// Create single user
const user = userFactory.create();

// Create user with overrides
const admin = userFactory.create({ email: '<EMAIL>' });

// Create user variants
const inactiveUser = userFactory.variant('inactive');
const brazilianUser = userFactory.variant('brazilian');

// Create multiple users
const users = userFactory.createMany(5);

// Create user family
const family = userFactory.createFamily(3);
```

#### AccountFactory
```typescript
import { accountFactory } from './factories/account.factory';

// Create account for user
const account = accountFactory.forUser('user-id');

// Create specific account type
const checking = accountFactory.variant('checking');
const savings = accountFactory.variant('savings');

// Create complete account set
const accounts = accountFactory.createAccountSet('user-id');
```

#### TransactionFactory
```typescript
import { transactionFactory } from './factories/transaction.factory';

// Create transaction
const transaction = transactionFactory.create();

// Create specific types
const income = transactionFactory.variant('income');
const expense = transactionFactory.variant('expense');

// Create transaction history
const history = transactionFactory.createTransactionHistory(
  'account-id', 'user-id', 90 // 90 days
);
```

## 🌱 Seeders

Seeders populate the database with test data.

### Using Seeders

```typescript
import { UserSeeder } from './seeders/user.seeder';
import { AccountSeeder } from './seeders/account.seeder';

const userSeeder = new UserSeeder(prisma);
const accountSeeder = new AccountSeeder(prisma);

// Create users
const users = await userSeeder.run({ count: 10 });

// Create accounts for users
const accounts = await accountSeeder.run({ count: 3 });

// Cleanup
await userSeeder.cleanup();
await accountSeeder.cleanup();
```

### Seeder Registry

```typescript
import { SeederRegistry } from './seeders/base.seeder';

// Register seeders
SeederRegistry.register('users', userSeeder, 0);
SeederRegistry.register('accounts', accountSeeder, 1);

// Run all seeders
await SeederRegistry.runAll();

// Cleanup all
await SeederRegistry.cleanupAll();
```

## 📋 Fixtures

Fixtures provide consistent, reusable test data.

### Using Fixtures

```typescript
import { FixtureManager, userFixtures } from './fixtures';

// Load single fixture
const admin = FixtureManager.load('admin', userFixtures);

// Load multiple fixtures
const users = FixtureManager.loadMany(['admin', 'regularUser'], userFixtures);

// Load complete scenario
const scenario = FixtureManager.loadScenario('userWithTransactionHistory');
```

### Available Fixtures

#### User Fixtures
- `admin` - Administrator user
- `regularUser` - Regular user
- `inactiveUser` - Inactive user

#### Account Fixtures
- `checkingAccount` - Checking account
- `savingsAccount` - Savings account
- `creditCard` - Credit card account

#### Category Fixtures
- `foodExpense` - Food expense category
- `salaryIncome` - Salary income category
- `transportExpense` - Transport expense category

#### Transaction Fixtures
- `salaryTransaction` - Monthly salary
- `groceryExpense` - Grocery shopping
- `gasExpense` - Gas station expense
- `transferTransaction` - Account transfer

### Test Scenarios

#### basicUserWithAccounts
- Regular user with checking and savings accounts
- Basic expense and income categories

#### userWithTransactionHistory
- Complete user setup with all account types
- Full transaction history with various types

#### multiUserScenario
- Multiple users for testing isolation

## 🛠️ Test Data Manager

Central system for managing all test data operations.

### Basic Usage

```typescript
import { TestDataManager } from './utils/test-data-manager';

const manager = new TestDataManager();

// Create test data
const result = await manager.createTestData({
  users: 5,
  accountsPerUser: 3,
  transactionsPerAccount: 20,
  clean: true,
});

// Clean all test data
await manager.cleanAllTestData();

// Get statistics
const stats = await manager.getStats();
```

### Advanced Usage

```typescript
// Create minimal dataset
await manager.createMinimalTestData();

// Create comprehensive dataset
await manager.createComprehensiveTestData();

// Create data for specific user
await manager.createUserTestData('user-id');

// Reset database
await manager.resetDatabase();
```

## 🖥️ CLI Tools

Command-line interface for managing test data.

### Installation

```bash
npm install --save-dev commander
```

### Available Commands

```bash
# Create test data
npm run test:data create

# Create with options
npm run test:data create --users 10 --accounts 5 --transactions 50

# Use fixtures
npm run test:data create --fixtures --scenario userWithTransactionHistory

# Clean test data
npm run test:data clean

# Reset database
npm run test:data reset

# Show statistics
npm run test:data stats

# Create minimal dataset
npm run test:data minimal

# Create comprehensive dataset
npm run test:data comprehensive

# List available scenarios
npm run test:data scenarios

# List available fixtures
npm run test:data fixtures

# Create data for specific user
npm run test:data user <user-id>
```

### CLI Examples

```bash
# Quick setup for development
npm run test:data minimal

# Full dataset for comprehensive testing
npm run test:data comprehensive

# Custom dataset
npm run test:data create --users 20 --accounts 3 --transactions 100

# Use predefined scenario
npm run test:data create --fixtures --scenario userWithTransactionHistory

# Clean up after testing
npm run test:data clean
```

## 🧪 Integration with Tests

### Setup in Test Files

```typescript
import { TestDataManager } from '../utils/test-data-manager';

describe('API Tests', () => {
  let testDataManager: TestDataManager;

  beforeAll(async () => {
    testDataManager = new TestDataManager();
    await testDataManager.createMinimalTestData();
  });

  afterAll(async () => {
    await testDataManager.cleanAllTestData();
    await testDataManager.disconnect();
  });

  beforeEach(async () => {
    // Create fresh data for each test if needed
  });

  afterEach(async () => {
    // Clean up test-specific data
  });
});
```

### Using Factories in Tests

```typescript
import { userFactory, accountFactory } from '../factories';

describe('User Service', () => {
  it('should create user account', async () => {
    const userData = userFactory.create();
    const user = await userService.create(userData);
    
    const accountData = accountFactory.forUser(user.id);
    const account = await accountService.create(accountData);
    
    expect(account.userId).toBe(user.id);
  });
});
```

### Using Fixtures in Tests

```typescript
import { FixtureManager, userFixtures } from '../fixtures';

describe('Authentication', () => {
  it('should authenticate admin user', async () => {
    const admin = FixtureManager.load('admin', userFixtures);
    
    const response = await request(app)
      .post('/api/auth/login')
      .send({
        email: admin.email,
        password: admin.password,
      });
    
    expect(response.status).toBe(200);
  });
});
```

## 🔧 Configuration

### Environment Variables

```bash
# Test database
DATABASE_URL=postgresql://test:test@localhost:5432/test_finance_db

# Test environment
NODE_ENV=test

# Faker locale
FAKER_LOCALE=pt_BR
```

### Database Isolation

The system ensures test isolation by:

1. **Separate test database**
2. **Automatic cleanup** between test runs
3. **Transaction rollback** for individual tests
4. **Unique identifiers** for test data

### Performance Optimization

- **Batch operations** for large datasets
- **Connection pooling** for database operations
- **Lazy loading** of related data
- **Efficient cleanup** strategies

## 📊 Best Practices

### Factory Design

1. **Keep definitions simple** - Focus on essential fields
2. **Use variants** for different scenarios
3. **Provide sensible defaults** - Make tests predictable
4. **Use realistic data** - Leverage Faker.js effectively

### Seeder Organization

1. **Follow dependency order** - Users → Categories → Accounts → Transactions
2. **Implement cleanup** - Always provide cleanup methods
3. **Handle errors gracefully** - Log and propagate errors
4. **Use transactions** - Ensure atomicity

### Test Data Strategy

1. **Minimal by default** - Use small datasets for fast tests
2. **Comprehensive when needed** - Large datasets for integration tests
3. **Isolated tests** - Each test should be independent
4. **Predictable data** - Use fixtures for consistent scenarios

### Performance Guidelines

1. **Batch operations** - Create multiple records efficiently
2. **Reuse connections** - Don't create new connections per operation
3. **Clean selectively** - Only clean what you created
4. **Monitor memory** - Watch for memory leaks in long-running tests

## 🚨 Troubleshooting

### Common Issues

#### "Database connection failed"
```bash
# Check database is running
docker ps | grep postgres

# Verify connection string
echo $DATABASE_URL
```

#### "Seeder not found"
```typescript
// Ensure seeder is registered
SeederRegistry.register('mySeeder', mySeeder, 0);
```

#### "Fixture not found"
```typescript
// Check fixture name and set
const fixture = FixtureManager.load('correctName', correctFixtureSet);
```

#### "Memory leaks in tests"
```typescript
// Always disconnect after tests
afterAll(async () => {
  await testDataManager.disconnect();
});
```

### Debugging

```typescript
// Enable debug logging
process.env.DEBUG = 'test-data:*';

// Check database stats
const stats = await testDataManager.getStats();
console.log('Database stats:', stats);

// Verify test environment
console.log('Environment:', process.env.NODE_ENV);
```

## 📚 Additional Resources

- [Faker.js Documentation](https://fakerjs.dev/)
- [Prisma Testing Guide](https://www.prisma.io/docs/guides/testing)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)
- [Commander.js CLI](https://github.com/tj/commander.js)
