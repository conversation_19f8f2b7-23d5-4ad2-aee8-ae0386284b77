# Regulamento Adapta AI Challenge 2025

## Desafio: Trazendo o Futuro para o Presente com Inteligência Artificial

A inteligência artificial (IA) está redefinindo o que será possível, transformando a maneira como vivemos, trabalhamos, aprendemos e construímos negócios. No Adapta AI Challenge 2025, desafiamos você a explorar as infinitas possibilidades da IA generativa e de agentes inteligentes para criar soluções que terão impacto real na vida das pessoas.

Escolha um ou mais dos quatro problemas abaixo e desenvolva, com as tecnologias disponíveis hoje, soluções que aumentem a produtividade, melhorem a aprendizagem, fortaleçam negócios ou empoderem o ser humano com "superpoderes".

**Objetivo:** Criar soluções inovadoras, práticas e escaláveis que resolvam problemas reais, utilizando IA generativa, LLMs, RAG ou agentes. Se<PERSON> criativo, pense fora da caixa e mostre como a IA transformará o presente!

## Os Quatro Desafios

### 1. Além do Chat

Os chatbots, como o ChatGPT, revolucionaram a interação com a IA, mas o potencial vai muito além de conversas. Que funcionalidade tornará um chatbot ainda mais potente e interessante? O que falta numa ferramenta como o ChatGPT?

**Desafio:** Desenvolva soluções que tornem um chatbot de IA mais útil. Exemplos:
- IA em chatbot que combina respostas de múltiplos modelos em uma única super resposta e ainda é proativa.
- Geração de apresentações a partir de prompts simples, com imagens, animações e textos detalhados.
- Ferramentas de web scraping/browsing que consigam fazer buscas complexas em centenas de páginas da web em segundo plano.

**Pergunta inspiradora:** Qual ferramenta ou serviço faltaria ao ChatGPT para ser um produto mais poderoso e interessante, mas que ele ainda não possui?

### 2. O Futuro dos Agentes

Agentes de IA são o próximo passo na revolução da IA: ferramentas autônomas que tomam ações sem depender de comandos diretos, desencadeadas por eventos específicos ou a partir de rotinas pré-definidas que podem ser executadas sem a necessidade de interação ou comando do usuário (como um e-mail recebido, uma mudança de status, uma visita numa página, uma ação em um produto ou um horário programado). Como podemos usar agentes para automatizar tarefas, otimizar processos ou criar experiências inovadoras?

**Desafio:** Crie um agente de IA que execute ações de forma independente, resolvendo problemas ou antecipando necessidades. Exemplos:
- Um agente com múltiplas ferramentas criativas (geração e edição de imagens, geração e edição de vídeo, geração e edição de áudio) que permita ao usuário executar múltiplos comandos de maneira simultânea, sendo orquestrados pelo próprio agente.
- Agente que faz ligações e marca consultas ou realiza vendas.
- Sistema que monitora PRs em tempo real e analisa vulnerabilidades e possibilidades de melhorias de performance no código, seguindo as melhores práticas.

**Pergunta inspiradora:** Qual fluxo de trabalho importante poderia ser delegado para um agente de IA?

### 3. Superando Limitações Técnicas da IA Generativa

A IA generativa tem desafios técnicos, como alucinações, falta de memória de longo prazo, dificuldade em gerenciar grandes bancos de dados ou comunicação entre agentes. Como podemos abordar essas limitações para criar soluções mais confiáveis e robustas?

**Desafio:** Desenvolva soluções que mitiguem uma ou mais limitações técnicas da IA generativa. Exemplos:
- Um serviço que gerencia a memória de curto, médio e longo prazo de LLMs, fazendo o ingest, processamento e atualização de maneira automática e podendo ser conectado a bancos já existentes (PostgreSQL, Redis, etc.).
- Gestão de custos de API, a partir dos tokens consumidos, que funcione bem numa escala gigante.
- Diminuição de alucinações de IA generativa em fluxos de trabalho onde ela é mais presente.
- Um sistema de "impressão digital" para reconhecer o que é conteúdo real de uma pessoa ou não.

**Pergunta inspiradora:** Como podemos tornar a IA generativa mais confiável, efetiva, gerenciável e integrada aos sistemas do mundo real?

### 4. Web Apps com IA Generativa Nativa

Web apps impulsionadas por IA generativa podem resolver problemas de forma mais rápida, eficiente ou acessível. Como podemos usar a IA para criar aplicações web que transformem a maneira como aprendemos, trabalhamos ou gerenciamos negócios?

**Desafio:** Crie uma web app que integre IA generativa para resolver um problema específico de maneira inovadora. Exemplos:
- Flashcards com IA para aprendizado personalizado e acelerado (ex.: "Duolingo para história").
- Um CRM que organiza contatos, sugere ações e redige comunicações automaticamente.

## Da Participação

### 6.1 A participação será voluntária, gratuita, nominativa e intransferível, restrita aos 80 participantes aprovados.

### 6.2 As equipes terão de 3 a 5 integrantes, alinhadas ao tema do evento.

### 6.3 A Comissão Organizadora auxiliará na formação de equipes incompletas ou na inclusão de participantes sem grupo.

### 6.4 Composição sugerida (não obrigatória):
- 1 Especialista em Produto
- 1 Especialista em UX/UI
- Até 3 Desenvolvedores
- 1 Especialista em Inteligência Artificial e Dados

### 6.5 Os participantes serão responsáveis por suas necessidades (ex.: transporte, equipamentos), mas poderão usar a premiação inicial para custear deslocamento e hospedagem.

### 6.6 A Comissão Organizadora disponibilizará aos participantes o espaço de trabalho com internet e alimentação. Cada participante deverá levar o próprio equipamento de trabalho.

## Da Programação

### 7.1 O evento incluirá mentorias, oficinas, avaliações e apresentações de pitch. O cronograma completo estará disponível em [https://adapta.org/hackathon](https://adapta.org/hackathon).

### 7.2 O Adapta AI Challenge 2025 iniciará às 09h de 12/07/2025 (sábado). As equipes poderão trabalhar durante a madrugada no local ou retornar às suas residências. No domingo, 13/07/2025, a programação iniciará por volta das 09h, com submissão de projetos até 13h, pitches a partir das 15h, seguidos de premiação e happy hour.

### 7.3 Alterações na programação serão comunicadas por e-mail e no canal oficial do Discord.

## Da Submissão de Projetos

### 8.1 As submissões deverão ser enviadas via formulário disponibilizado até 13h de 13/07/2025, contendo:

#### a) Vídeo Demo: Até 120 segundos, no YouTube (público ou não listado), gravando a tela da solução com código funcional (sem protótipos). Vídeos excedendo o limite causarão desclassificação.

#### b) Apresentação: Slides em PDF (recomenda-se 10 slides), com problema, solução, tecnologias usadas e roadmap.

#### c) Repositórios: 1 a 3 links públicos no GitHub, com arquivo README e boas práticas de commits Conventional Commits.

### 8.2 Adendo sobre Apresentação de Pitch: Além da submissão, cada equipe realizará uma apresentação de pitch de 3 minutos, seguida de 3 minutos de interação com a banca de jurados, para avaliação dos projetos com base nos critérios estabelecidos no item 9. A apresentação ocorrerá a partir das 15h de 13/07/2025, conforme programação.

### 8.3 O total de links não poderá exceder 5:
- 1 vídeo demo;
- 1 PDF de apresentação;
- 1 a 3 repositórios.

Links fora do especificado causarão desclassificação imediata.

### 8.4 Os projetos passarão por code review qualitativo, avaliando repositórios e boas práticas. Projetos sem código próprio poderão ser desclassificados.

## Dos Critérios de Avaliação

### 9.1 Os projetos serão avaliados com base em:
- a) Impacto Real: Soluções que melhoraram a vida das pessoas, aumentaram a produtividade, facilitaram a aprendizagem ou fortaleceram negócios.
- b) Aplicabilidade no Uso de IA: Projetos que utilizaram tecnologias disponíveis hoje (modelos, APIs, frameworks) e demonstraram aplicabilidade prática.
- c) Escalabilidade: Soluções com potencial de crescimento e adaptação a diferentes contextos ou públicos.
- d) Originalidade: Ideias que ainda não foram criadas/aperfeiçoadas e não um clone de algo que já existe.

### 9.2 Cada critério receberá nota de 0 a 4 (0 = mínima aderência, 4 = máxima aderência), com pesos iguais.

### 9.3 A nota final considerará a média das avaliações dos jurados.

### 9.4 A divulgação das notas ficará a critério da organização.

## Das Etapas de Avaliação

### 10.1 Etapa Única (13/07/2025): Jurados nomeados avaliarão e classificarão os 10 melhores projetos com base nos critérios do item 9.

### 10.2 Os vencedores serão anunciados em 13/07/2025 durante o evento, por e-mail e redes sociais.

### 10.3 Adapta Summit (16/08/2025): Os três primeiros colocados da etapa presencial terão até 15/08/2025 para aprimorar suas soluções e apresentá-las no Adapta Summit (16/08/2025). As premiações serão:
- 1º Lugar: R$ 250.000,00, divididos igualmente entre os membros;
- 2º Lugar: R$ 150.000,00, divididos igualmente entre os membros;
- 3º Lugar: R$ 100.000,00, divididos igualmente entre os membros.
- Total: R$ 500.000,00.

## Da Premiação

### 11.1 O Adapta AI Challenge 2025 distribuirá R$ 1.000.000,00 em prêmios, sendo a maior premiação de hackathon já realizada no Brasil, dividida em duas etapas: a etapa presencial (12-13/07/2025) e o Adapta Summit (16/08/2025).

### 11.2 Premiação da Etapa Presencial:
- Premiação Inicial: Cada um dos 80 participantes aprovados que comparecer presencialmente ao evento, submeter o projeto conforme item 8 e apresentar o pitch receberá R$ 4.000,00 (total de R$ 320.000,00), que poderá ser usado para custear deslocamento e hospedagem (especialmente para participantes fora de São Paulo). Esta premiação será restrita a participantes presenciais que concluírem todas as etapas exigidas.
- 1º, 2º e 3º Lugares: Cada equipe receberá R$ 40.000,00, divididos igualmente entre os membros (ex.: R$ 8.000,00 por integrante em uma equipe de 5). Total: R$ 120.000,00.
- 4º, 5º e 6º Lugares: Cada equipe receberá R$ 20.000,00, divididos igualmente entre os membros (ex.: R$ 4.000,00 por integrante em uma equipe de 5). Total: R$ 60.000,00.

### 11.3 Premiação do Adapta Summit:
- Os três primeiros colocados da etapa presencial terão até 15/08/2025 para aprimorar suas soluções e apresentá-las no Adapta Summit (16/08/2025). As premiações serão:
  - 1º Lugar: R$ 250.000,00, divididos igualmente entre os membros;
  - 2º Lugar: R$ 150.000,00, divididos igualmente entre os membros;
  - 3º Lugar: R$ 100.000,00, divididos igualmente entre os membros.
  - Total: R$ 500.000,00.

### 11.4 Condições Gerais:
- Todos os prêmios serão pessoais, intransferíveis e restritos aos participantes que atenderem às condições de elegibilidade (presença, submissão e apresentação).
- A entrega dos prêmios será via cartão pré-pago ou transferência bancária, a ser definido pela Comissão Organizadora e anunciado durante o evento presencial, em até 30 dias úteis após a divulgação dos resultados de cada etapa (13/07/2025 para a etapa presencial e 16/08/2025 para o Adapta Summit). Prazos de logística poderão variar, e a organização fornecerá um código de rastreio, se aplicável.
- Menores de 18 anos terão os prêmios entregues aos responsáveis legais.

### 11.5 Nota sobre o Total: O total de R$ 1.000.000,00 inclui a premiação inicial (R$ 320.000,00 para 80 participantes), os prêmios da etapa presencial (R$ 180.000,00 para os seis primeiros colocados) e os prêmios do Adapta Summit (R$ 500.000,00 para os três primeiros colocados).

## Dos Avaliadores

### 12.1 A banca de jurados será composta por profissionais nomeados pela organização e terá sua composição divulgada no site oficial do evento [https://adapta.org/hackathon](https://adapta.org/hackathon).

### 12.2 Jurados deverão informar conflitos de interesse à Comissão Organizadora.

### 12.3 As decisões dos jurados, tanto na etapa presencial quanto no Adapta Summit, serão soberanas e irrecorríveis.

### 12.4 Feedbacks serão opcionais e não de responsabilidade da organização.

## Da Comunicação

### 13.1 A comunicação será feita via Discord, e-mail, redes sociais ou YouTube.

### 13.2 Os participantes deverão acompanhar atualizações e resultados, incluindo informações sobre o Adapta Summit.

## Das Considerações Finais

### 14.1 Datas poderão ser alteradas, com divulgação no site ou Discord.

### 14.2 A Comissão Organizadora coordenará o evento, incluindo mentores, jurados e o Adapta Summit. Os participantes autorizam o uso de dados fornecidos na inscrição pela organização e patrocinadores.

### 14.3 Decisões da organização e jurados serão irrecorríveis.

### 14.4 O evento terá finalidade cultural, sem caráter comercial ou vínculo com aquisição de produtos/serviços.

### 14.5 O regulamento poderá ser alterado, com comunicação no site.

### 14.6 O evento poderá ser suspenso por força maior (ex.: falhas de infraestrutura), sem indenizações.

### 14.7 Casos omissos serão resolvidos pela Comissão Organizadora.

### 14.8 A inscrição implicará a aceitação integral deste regulamento.