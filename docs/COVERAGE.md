# Test Coverage Documentation

This document describes the comprehensive test coverage system for the Personal Finance Manager project.

## 📊 Overview

Our test coverage system provides:

- **Unified Coverage Reporting** across frontend and backend
- **Automated Threshold Checking** with quality gates
- **CI/CD Integration** with GitHub Actions
- **Coverage Badges** for README display
- **Detailed HTML Reports** for analysis

## 🎯 Coverage Thresholds

### Global Thresholds

| Metric | Threshold | Description |
|--------|-----------|-------------|
| Lines | 75% | Minimum percentage of lines executed |
| Functions | 75% | Minimum percentage of functions called |
| Branches | 70% | Minimum percentage of branches taken |
| Statements | 75% | Minimum percentage of statements executed |

### Component-Specific Thresholds

#### Backend
- **Controllers**: 85% (all metrics)
- **Services**: 85% (all metrics)
- **Middleware**: 80% (all metrics)
- **Utils**: 90% (all metrics)

#### Frontend
- **Components**: 85% (all metrics)
- **Hooks**: 90% (all metrics)
- **Stores**: 85% (all metrics)
- **Utils**: 95% (all metrics)

## 🚀 Running Coverage Tests

### Quick Start

Run all tests with coverage:
```bash
npm run test:coverage
```

### Individual Components

```bash
# Frontend only
npm run test:coverage:frontend

# Backend only
npm run test:coverage:backend

# Combine existing reports
npm run test:coverage:combine

# Check thresholds only
npm run test:coverage:thresholds

# Generate badge only
npm run test:coverage:badge
```

### Manual Script Execution

```bash
# Full coverage suite
./scripts/test-coverage.sh

# Specific components
./scripts/test-coverage.sh frontend
./scripts/test-coverage.sh backend
./scripts/test-coverage.sh combine
./scripts/test-coverage.sh thresholds
./scripts/test-coverage.sh badge
```

## 📁 Generated Reports

After running coverage tests, the following reports are generated:

### Frontend Reports
- `frontend/coverage/index.html` - HTML report
- `frontend/coverage/lcov.info` - LCOV format
- `frontend/coverage/coverage-final.json` - Raw coverage data
- `frontend/coverage/coverage-summary.json` - Summary metrics

### Backend Reports
- `backend/coverage/index.html` - HTML report
- `backend/coverage/lcov.info` - LCOV format
- `backend/coverage/coverage-final.json` - Raw coverage data
- `backend/coverage/coverage-summary.json` - Summary metrics

### Combined Reports
- `coverage-combined/index.html` - Unified HTML report
- `coverage-combined/coverage-combined.json` - Combined raw data
- `coverage-combined/coverage-summary-combined.json` - Combined metrics
- `coverage-combined/coverage-badge.md` - Badge markdown
- `coverage-combined/coverage-badge-url.txt` - Badge URL

## 🏷️ Coverage Badge

The coverage badge is automatically generated and can be used in README files:

```markdown
![Coverage](https://img.shields.io/badge/coverage-XX%25-brightgreen)
```

Badge colors:
- **Green** (brightgreen): ≥80% coverage
- **Yellow**: 60-79% coverage
- **Red**: <60% coverage

## 🔧 Configuration

### Backend Configuration (Jest)

Located in `backend/jest.config.js`:

```javascript
module.exports = {
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/tests/**',
    '!src/scripts/**',
    '!src/index.ts',
    '!src/types/**',
    '!src/**/*.interface.ts',
    '!src/**/*.type.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 75,
      lines: 75,
      statements: 75
    },
    // Component-specific thresholds...
  },
  coverageReporters: [
    'text',
    'text-summary',
    'lcov',
    'html',
    'json',
    'json-summary',
    'cobertura'
  ]
};
```

### Frontend Configuration (Vitest)

Located in `frontend/vitest.config.ts`:

```typescript
export default defineConfig({
  test: {
    coverage: {
      provider: 'v8',
      reporter: ['text', 'text-summary', 'json', 'json-summary', 'html', 'lcov', 'cobertura'],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
        // Component-specific thresholds...
      },
    },
  },
});
```

## 🤖 CI/CD Integration

### GitHub Actions Workflow

The `.github/workflows/test-coverage.yml` workflow:

1. **Runs on**: Push to main/develop, PRs, manual trigger
2. **Services**: PostgreSQL 15, Redis 7
3. **Steps**:
   - Setup Node.js and dependencies
   - Configure test environment
   - Run backend tests with coverage
   - Run frontend tests with coverage
   - Combine coverage reports
   - Check thresholds
   - Upload to Codecov
   - Comment on PRs with coverage results
   - Fail if thresholds not met

### Coverage Comments on PRs

Automatic PR comments include:

- Coverage metrics table with pass/fail status
- Frontend vs Backend breakdown
- Links to detailed reports
- Threshold compliance indicators

## 📈 Improving Coverage

### Identifying Low Coverage Areas

1. **Open HTML Reports**: Navigate to `coverage-combined/index.html`
2. **Review Red/Yellow Areas**: Focus on uncovered lines
3. **Check Branch Coverage**: Look for missing conditional paths
4. **Analyze Function Coverage**: Ensure all functions are tested

### Best Practices

#### Backend Testing
- **Controllers**: Test all endpoints, error cases, validation
- **Services**: Test business logic, edge cases, error handling
- **Middleware**: Test authentication, validation, error scenarios
- **Utils**: Test all utility functions with various inputs

#### Frontend Testing
- **Components**: Test rendering, props, user interactions
- **Hooks**: Test all return values, state changes, effects
- **Stores**: Test state mutations, actions, selectors
- **Utils**: Test all utility functions comprehensively

### Common Coverage Gaps

1. **Error Handling**: Often missed in happy path testing
2. **Edge Cases**: Boundary conditions and unusual inputs
3. **Async Operations**: Promise rejections and timeouts
4. **Conditional Logic**: All branches of if/else statements
5. **Event Handlers**: User interactions and callbacks

## 🔍 Troubleshooting

### Common Issues

#### "Coverage below threshold" Error
```bash
❌ Lines coverage below threshold: 72% < 75%
```
**Solution**: Add tests for uncovered lines or adjust thresholds

#### "No coverage data found"
```bash
❌ No coverage data found in frontend or backend
```
**Solution**: Ensure tests run successfully before combining

#### "Failed to combine coverage reports"
```bash
❌ Failed to combine coverage reports
```
**Solution**: Check that both frontend and backend generated coverage files

### Debugging Coverage Issues

1. **Check Test Execution**:
   ```bash
   cd backend && npm run test:coverage
   cd frontend && npm run test:coverage
   ```

2. **Verify Coverage Files**:
   ```bash
   ls -la backend/coverage/
   ls -la frontend/coverage/
   ```

3. **Manual Combination**:
   ```bash
   node scripts/coverage-combine.js
   ```

4. **Check Thresholds**:
   ```bash
   ./scripts/test-coverage.sh thresholds
   ```

## 📚 Additional Resources

- [Jest Coverage Documentation](https://jestjs.io/docs/configuration#collectcoveragefrom-array)
- [Vitest Coverage Guide](https://vitest.dev/guide/coverage.html)
- [Istanbul Coverage Reports](https://istanbul.js.org/)
- [Codecov Integration](https://docs.codecov.com/docs)

## 🎯 Coverage Goals

### Short Term (Current Sprint)
- [ ] Achieve 75% overall coverage
- [ ] Implement all threshold checks
- [ ] Setup CI/CD integration
- [ ] Generate coverage badges

### Medium Term (Next 2 Sprints)
- [ ] Achieve 85% overall coverage
- [ ] Implement performance regression testing
- [ ] Add mutation testing
- [ ] Setup coverage trending

### Long Term (Future Sprints)
- [ ] Achieve 90% overall coverage
- [ ] Implement visual regression testing
- [ ] Add accessibility testing coverage
- [ ] Setup automated coverage optimization
