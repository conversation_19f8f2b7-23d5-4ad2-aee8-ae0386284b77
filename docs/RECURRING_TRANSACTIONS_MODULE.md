# Módulo de Transações Recorrentes

## Visão Geral

O módulo de Transações Recorrentes permite criar, gerenciar e processar automaticamente transações que se repetem em intervalos regulares. Este módulo é essencial para automatizar receitas e despesas fixas como salários, aluguéis, contas mensais, etc.

## Funcionalidades Principais

### 1. Criação de Transações Recorrentes
- Suporte a valores fixos ou percentuais
- Frequências: Diária, Semanal, Mensal, Anual
- Datas de início e fim configuráveis
- Associação com contas e categorias

### 2. Processamento Automático
- Job scheduler usando node-schedule
- Execução diária às 00:01
- Geração automática de transações
- Tratamento de erros e retry

### 3. Geração de Parcelas
- Criação de transações parceladas
- Relacionamento parent-child
- Cancelamento de parcelas futuras

## API Endpoints

### Transações Recorrentes

#### `POST /api/v1/recurring-transactions`
Cria uma nova transação recorrente.

**Request Body:**
```json
{
  "description": "Salário mensal",
  "fixedAmount": 5000.00,
  "frequency": "MONTHLY",
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "type": "INCOME",
  "accountId": "account-id",
  "categoryId": "category-id",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "rt-123",
    "description": "Salário mensal",
    "fixedAmount": 5000.00,
    "frequency": "MONTHLY",
    "startDate": "2024-01-01",
    "endDate": "2024-12-31",
    "type": "INCOME",
    "accountId": "account-id",
    "categoryId": "category-id",
    "isActive": true,
    "nextExecution": "2024-02-01",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "version": 1
  },
  "message": "Transação recorrente criada com sucesso"
}
```

#### `GET /api/v1/recurring-transactions`
Lista transações recorrentes com filtros.

**Query Parameters:**
- `accountId` (string): Filtrar por conta
- `categoryId` (string): Filtrar por categoria
- `type` (string): Filtrar por tipo (INCOME, EXPENSE, TRANSFER)
- `frequency` (string): Filtrar por frequência
- `isActive` (boolean): Filtrar por status ativo
- `page` (number): Página (padrão: 1)
- `limit` (number): Itens por página (padrão: 20)

#### `GET /api/v1/recurring-transactions/:id`
Obtém uma transação recorrente por ID.

#### `PUT /api/v1/recurring-transactions/:id`
Atualiza uma transação recorrente.

#### `DELETE /api/v1/recurring-transactions/:id`
Remove uma transação recorrente (soft delete).

### Funcionalidades Especiais

#### `POST /api/v1/recurring-transactions/:id/installments`
Cria parcelas a partir de uma transação recorrente.

**Request Body:**
```json
{
  "numberOfInstallments": 12,
  "startDate": "2024-01-01",
  "description": "Parcela do financiamento"
}
```

#### `GET /api/v1/recurring-transactions/:id/next-execution`
Calcula a próxima data de execução.

#### `GET /api/v1/recurring-transactions/:id/should-execute`
Verifica se deve executar em uma data específica.

## Tipos de Valores

### Valor Fixo
```json
{
  "fixedAmount": 1500.00,
  "percentageAmount": null
}
```

### Valor Percentual
```json
{
  "fixedAmount": null,
  "percentageAmount": 10.0
}
```

**Nota:** Valores fixos e percentuais são mutuamente exclusivos.

## Frequências Suportadas

### DAILY (Diária)
- Executa todos os dias
- Útil para controles diários

### WEEKLY (Semanal)
- Executa no mesmo dia da semana da data de início
- Exemplo: Se iniciou numa segunda, executa todas as segundas

### MONTHLY (Mensal)
- Executa no mesmo dia do mês da data de início
- Exemplo: Se iniciou no dia 15, executa todo dia 15

### YEARLY (Anual)
- Executa no mesmo dia e mês da data de início
- Exemplo: Se iniciou em 01/01, executa todo 01/01

## Job Scheduler

### Configuração
O sistema usa `node-schedule` para processar transações recorrentes automaticamente.

### Execução
- **Horário:** Diariamente às 00:01 (timezone: America/Sao_Paulo)
- **Retry:** 3 tentativas com delay de 10 segundos
- **Logs:** Auditoria completa de execuções

### Processo
1. Busca todas as transações recorrentes ativas
2. Verifica se devem executar hoje
3. Calcula o valor (fixo ou percentual)
4. Cria a transação correspondente
5. Registra logs de auditoria

## Validações

### Criação
- Descrição obrigatória (1-255 caracteres)
- Exatamente um tipo de valor (fixo OU percentual)
- Data de início obrigatória
- Data de fim posterior à data de início (se fornecida)
- Conta e categoria devem existir
- Valor percentual apenas para INCOME e EXPENSE

### Atualização
- Pelo menos um campo deve ser fornecido
- Mesmas validações da criação para campos alterados

## Tratamento de Erros

### Códigos de Erro
- `VALIDATION_ERROR`: Dados inválidos
- `RECURRING_TRANSACTION_NOT_FOUND`: Transação não encontrada
- `DUPLICATE_DATA`: Dados duplicados
- `APPLICATION_ERROR`: Erro de lógica de negócio
- `INTERNAL_ERROR`: Erro interno do servidor

### Rate Limiting
- Consultas: 200 requests/15min
- Modificações: 50 requests/15min

## Exemplos de Uso

### Salário Mensal
```json
{
  "description": "Salário",
  "fixedAmount": 5000.00,
  "frequency": "MONTHLY",
  "startDate": "2024-01-01",
  "type": "INCOME",
  "accountId": "conta-corrente",
  "categoryId": "salario"
}
```

### Aluguel
```json
{
  "description": "Aluguel",
  "fixedAmount": 1200.00,
  "frequency": "MONTHLY",
  "startDate": "2024-01-05",
  "type": "EXPENSE",
  "accountId": "conta-corrente",
  "categoryId": "moradia"
}
```

### Investimento Percentual
```json
{
  "description": "Investimento automático",
  "percentageAmount": 20.0,
  "frequency": "MONTHLY",
  "startDate": "2024-01-01",
  "type": "TRANSFER",
  "accountId": "conta-corrente",
  "destinationAccountId": "conta-investimento"
}
```

## Monitoramento

### Logs de Auditoria
Todas as operações são registradas com:
- Timestamp
- Usuário
- IP
- Ação realizada
- Dados modificados

### Métricas de Job
- Total processado
- Sucessos
- Falhas
- Tempo de execução

## Troubleshooting

### Job não executa
1. Verificar se o scheduler está ativo
2. Verificar logs de erro
3. Verificar se as transações estão ativas
4. Verificar datas de início/fim

### Valores incorretos
1. Para percentuais, verificar saldo da conta
2. Para valores fixos, verificar configuração
3. Verificar se a conta/categoria ainda existem

### Performance
1. Monitorar número de transações recorrentes
2. Otimizar consultas se necessário
3. Considerar processamento em lotes para grandes volumes

## Segurança

### Autenticação
- JWT obrigatório em todas as rotas
- Validação de token em cada request

### Autorização
- Usuários só acessam suas próprias transações
- Validação de propriedade dos recursos

### Auditoria
- Log completo de todas as operações
- Rastreabilidade de mudanças
- Detecção de anomalias

## Status da Implementação

### ✅ Concluído
- **Infraestrutura**: node-schedule instalado e configurado
- **Schemas de Validação**: Zod schemas completos com validações de negócio
- **RecurringTransactionService**: CRUD completo com lógica de negócio
- **Job Scheduler**: Sistema de jobs automático para processamento
- **Geração de Parcelas**: Funcionalidade para criar installments
- **API Controller**: Endpoints REST completos
- **Rotas e Middleware**: Configuração completa com autenticação e rate limiting
- **Testes Unitários**: Testes básicos implementados
- **Testes de Integração**: Estrutura de testes criada
- **Documentação**: Documentação técnica completa

### 🔧 Arquivos Implementados
```
src/
├── config/
│   └── scheduler.config.ts          # Configuração do node-schedule
├── services/
│   ├── recurring-transaction.service.ts      # Serviço principal
│   └── recurring-transaction-job.service.ts  # Serviço de jobs
├── controllers/
│   └── recurring-transaction.controller.ts   # Controller da API
├── routes/
│   └── recurring-transaction.routes.ts       # Rotas REST
├── schemas/
│   └── recurring-transaction.schemas.ts      # Validações Zod
└── tests/
    ├── recurring-transaction.service.test.ts
    ├── recurring-transaction-jobs.test.ts
    └── recurring-transaction.integration.test.ts

docs/
└── RECURRING_TRANSACTIONS_MODULE.md  # Esta documentação
```

### 🚀 Funcionalidades Implementadas
1. **CRUD de Transações Recorrentes**
   - Criação com valores fixos ou percentuais
   - Listagem com filtros avançados
   - Atualização com validações
   - Soft delete

2. **Processamento Automático**
   - Job diário às 00:01 (timezone: America/Sao_Paulo)
   - Processamento de todas as frequências (DAILY, WEEKLY, MONTHLY, YEARLY)
   - Cálculo automático de valores percentuais baseado no saldo da conta
   - Sistema de retry com 3 tentativas

3. **Geração de Parcelas**
   - Criação de installments a partir de transações recorrentes
   - Relacionamento parent-child entre transações
   - Validações de datas e valores

4. **API REST Completa**
   - 8 endpoints implementados
   - Validação de entrada com Zod
   - Tratamento de erros padronizado
   - Rate limiting configurado

### 📊 Métricas de Qualidade
- **Cobertura de Testes**: Testes unitários básicos implementados
- **Validação de Dados**: 100% dos endpoints com validação Zod
- **Tratamento de Erros**: Padronizado em todos os controllers
- **Documentação**: Completa com exemplos de uso

### 🔄 Integração com Sistema Existente
- Integra com módulo de Transactions existente
- Utiliza schemas Prisma já definidos
- Segue padrões de arquitetura do projeto
- Compatível com sistema de autenticação JWT

### 🎯 Próximos Passos Recomendados
1. **Testes de Integração**: Configurar ambiente de teste com banco de dados
2. **Monitoramento**: Implementar métricas de performance dos jobs
3. **Interface Frontend**: Criar componentes React para gerenciar transações recorrentes
4. **Notificações**: Sistema de alertas para falhas de processamento
5. **Backup**: Estratégia de backup para dados de transações recorrentes

### 📝 Notas Técnicas
- Modelo `RecurringTransaction` já existia no schema Prisma
- Enum `RecurrenceFrequency` já estava implementado
- Dados de seed já existem para testes
- Sistema é thread-safe com controle de execução simultânea
- Graceful shutdown implementado para jobs em execução
