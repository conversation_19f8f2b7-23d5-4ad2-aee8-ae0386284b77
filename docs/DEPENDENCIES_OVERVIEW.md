# 📦 Dependencies Overview - Personal Finance Manager

**Data da Última Atualização**: Junho 2025  
**Versão do Projeto**: 1.0.0-beta

## 🎯 Visão Geral

Este documento fornece uma visão completa de todas as dependências do projeto Personal Finance Manager, organizadas por workspace e categoria.

## 📊 Resumo Executivo

| Workspace | Dependências Principais | Dev Dependencies | Total |
|-----------|------------------------|------------------|-------|
| **Root** | 3 | 6 | 9 |
| **Backend** | 23 | 21 | 44 |
| **Frontend** | 33 | 24 | 57 |
| **Shared** | 0 | 0 | 0 |
| **TOTAL** | **59** | **51** | **110** |

## 🏗️ Root Workspace

### Dependências de Desenvolvimento
```json
{
  "concurrently": "^8.2.2",
  "@types/node": "^20.10.0", 
  "typescript": "^5.3.0",
  "cypress": "^13.6.0",
  "@cypress/code-coverage": "^3.12.0",
  "cypress-real-events": "^1.11.0"
}
```

**Propósito**: Orquestração de workspaces, testes E2E e ferramentas de desenvolvimento.

## 🔧 Backend Dependencies

### Dependências Principais (23)
```json
{
  "@hookform/resolvers": "^5.0.1",
  "@prisma/client": "^5.7.0",
  "@types/ioredis": "^4.28.10",
  "@types/multer": "^1.4.12",
  "@types/node-schedule": "^2.1.7",
  "bcryptjs": "^2.4.3",
  "compression": "^1.7.4",
  "cors": "^2.8.5",
  "date-fns": "^2.30.0",
  "dotenv": "^16.3.1",
  "express": "^4.18.2",
  "express-rate-limit": "^7.1.5",
  "express-validator": "^7.0.1",
  "helmet": "^7.1.0",
  "ioredis": "^5.6.1",
  "jsonwebtoken": "^9.0.2",
  "morgan": "^1.10.0",
  "multer": "^2.0.1",
  "node-schedule": "^2.1.1",
  "prisma": "^5.7.0",
  "redis": "^5.5.5",
  "uuid": "^9.0.1",
  "zod": "^3.22.4"
}
```

### Dependências de Desenvolvimento (21)
```json
{
  "@faker-js/faker": "^9.8.0",
  "@types/bcryptjs": "^2.4.6",
  "@types/compression": "^1.7.5",
  "@types/cors": "^2.8.17",
  "@types/express": "^4.17.21",
  "@types/jest": "^29.5.8",
  "@types/jsonwebtoken": "^9.0.5",
  "@types/morgan": "^1.9.9",
  "@types/redis": "^4.0.10",
  "@types/supertest": "^2.0.16",
  "@types/uuid": "^9.0.7",
  "@typescript-eslint/eslint-plugin": "^6.12.0",
  "@typescript-eslint/parser": "^6.12.0",
  "commander": "^4.1.1",
  "eslint": "^8.54.0",
  "jest": "^29.7.0",
  "nodemon": "^3.0.2",
  "redis-memory-server": "^0.12.1",
  "supertest": "^6.3.3",
  "ts-jest": "^29.1.1",
  "ts-node": "^10.9.2",
  "tsx": "^4.6.0",
  "typescript": "^5.3.0"
}
```

### Categorias Backend

#### **🔐 Autenticação e Segurança**
- `bcryptjs` - Hash de senhas
- `jsonwebtoken` - JWT tokens
- `helmet` - Headers de segurança
- `express-rate-limit` - Rate limiting

#### **🗄️ Banco de Dados e Cache**
- `@prisma/client` - ORM moderno
- `prisma` - Database toolkit
- `ioredis` - Cliente Redis
- `redis` - Cache em memória

#### **🌐 Web Framework**
- `express` - Framework web
- `cors` - Cross-origin requests
- `compression` - Compressão gzip
- `morgan` - HTTP logging

#### **⏰ Jobs e Agendamento**
- `node-schedule` - Cron jobs
- `date-fns` - Manipulação de datas

#### **📝 Validação e Tipos**
- `zod` - Schema validation
- `express-validator` - Request validation
- `@types/*` - TypeScript definitions

#### **🧪 Testes**
- `jest` - Framework de testes
- `supertest` - HTTP testing
- `@faker-js/faker` - Dados fake
- `redis-memory-server` - Redis em memória

## ⚛️ Frontend Dependencies

### Dependências Principais (33)
```json
{
  "@hookform/resolvers": "^5.1.0",
  "@radix-ui/react-alert-dialog": "^1.1.14",
  "@radix-ui/react-avatar": "^1.1.10",
  "@radix-ui/react-checkbox": "^1.3.2",
  "@radix-ui/react-dialog": "^1.1.14",
  "@radix-ui/react-dropdown-menu": "^2.1.15",
  "@radix-ui/react-label": "^2.1.7",
  "@radix-ui/react-popover": "^1.1.14",
  "@radix-ui/react-progress": "^1.1.7",
  "@radix-ui/react-select": "^2.2.5",
  "@radix-ui/react-separator": "^1.1.7",
  "@radix-ui/react-slot": "^1.2.3",
  "@radix-ui/react-switch": "^1.2.5",
  "@tanstack/react-query": "^5.8.4",
  "@tanstack/react-table": "^8.21.3",
  "axios": "^1.6.2",
  "class-variance-authority": "^0.7.1",
  "clsx": "^2.1.1",
  "date-fns": "^2.30.0",
  "lucide-react": "^0.294.0",
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "react-hook-form": "^7.57.0",
  "react-hot-toast": "^2.5.2",
  "react-query": "^3.39.3",
  "react-router-dom": "^6.20.1",
  "recharts": "^2.8.0",
  "sonner": "^2.0.5",
  "tailwind-merge": "^2.6.0",
  "tailwindcss-animate": "^1.0.7",
  "zod": "^3.25.56",
  "zustand": "^4.4.7"
}
```

### Dependências de Desenvolvimento (24)
```json
{
  "@cypress/code-coverage": "^3.14.4",
  "@types/react": "^18.2.37",
  "@types/react-dom": "^18.2.15",
  "@typescript-eslint/eslint-plugin": "^6.10.0",
  "@typescript-eslint/parser": "^6.10.0",
  "@vitejs/plugin-react": "^4.1.1",
  "@vitest/coverage-v8": "^0.34.6",
  "@vitest/ui": "^0.34.6",
  "autoprefixer": "^10.4.16",
  "cypress": "^14.4.1",
  "cypress-real-events": "^1.14.0",
  "eslint": "^8.53.0",
  "eslint-config-prettier": "^10.1.5",
  "eslint-plugin-prettier": "^5.4.1",
  "eslint-plugin-react": "^7.37.5",
  "eslint-plugin-react-hooks": "^4.6.0",
  "eslint-plugin-react-refresh": "^0.4.4",
  "postcss": "^8.4.31",
  "prettier": "^3.5.3",
  "prettier-plugin-tailwindcss": "^0.6.12",
  "tailwindcss": "^3.3.5",
  "typescript": "^5.2.2",
  "vite": "^5.0.0",
  "vitest": "^0.34.6"
}
```

### Categorias Frontend

#### **⚛️ React Ecosystem**
- `react` - Biblioteca principal
- `react-dom` - DOM rendering
- `react-router-dom` - Roteamento
- `react-hook-form` - Formulários
- `react-hot-toast` - Notificações (legacy)

#### **🎨 UI e Design System**
- `@radix-ui/*` - Componentes primitivos (shadcn/ui)
- `lucide-react` - Ícones
- `tailwindcss` - CSS utility-first
- `tailwind-merge` - Merge de classes
- `tailwindcss-animate` - Animações
- `class-variance-authority` - Variantes de componentes

#### **📊 Estado e Dados**
- `@tanstack/react-query` - Server state
- `@tanstack/react-table` - Tabelas
- `zustand` - Client state
- `axios` - HTTP client

#### **📈 Gráficos e Visualização**
- `recharts` - Biblioteca de gráficos

#### **🔔 Notificações**
- `sonner` - Sistema moderno de notificações
- `react-hot-toast` - Sistema legacy (manter compatibilidade)

#### **📝 Validação e Utilitários**
- `zod` - Schema validation
- `@hookform/resolvers` - Integração forms + zod
- `date-fns` - Manipulação de datas
- `clsx` - Conditional classes

#### **🛠️ Build e Desenvolvimento**
- `vite` - Build tool
- `@vitejs/plugin-react` - Plugin React
- `vitest` - Framework de testes
- `cypress` - E2E testing

## 🔄 Dependências Compartilhadas

### Entre Backend e Frontend
- `date-fns` - Manipulação de datas
- `zod` - Schema validation
- `typescript` - Tipagem estática

### Versões Alinhadas
- `typescript`: ^5.x em todos os workspaces
- `date-fns`: ^2.30.0 em backend e frontend
- `zod`: ^3.x em backend e frontend

## 📊 Análise de Dependências

### **🟢 Dependências Críticas (Core)**
- React 18 - Interface moderna
- Express 4 - Backend robusto
- Prisma 5 - ORM moderno
- PostgreSQL - Banco relacional
- Redis - Cache performático

### **🟡 Dependências Importantes (Features)**
- shadcn/ui (@radix-ui) - Design system
- React Query - Estado servidor
- React Hook Form - Formulários
- Recharts - Visualizações
- JWT - Autenticação

### **🔵 Dependências de Desenvolvimento**
- Vite - Build rápido
- Jest/Vitest - Testes
- Cypress - E2E
- ESLint/Prettier - Code quality
- TypeScript - Type safety

## 🔒 Segurança

### Dependências de Segurança
- `helmet` - Headers seguros
- `bcryptjs` - Hash de senhas
- `express-rate-limit` - Rate limiting
- `cors` - CORS policy
- `express-validator` - Input validation

### Auditoria Regular
```bash
npm audit --audit-level high
npm run security:scan
```

## 📈 Performance

### Otimizações
- `compression` - Gzip compression
- `ioredis` - Cache Redis
- `@tanstack/react-query` - Cache inteligente
- `vite` - Build otimizado

## 🔄 Atualizações

### Estratégia de Updates
1. **Dependabot** configurado para updates automáticos
2. **Testes automáticos** antes de merge
3. **Semantic versioning** respeitado
4. **Breaking changes** documentados

### Próximas Atualizações Planejadas
- React 19 (quando estável)
- Prisma 6 (quando disponível)
- Node.js 20 LTS

---

**📋 Documento mantido por**: Augment Agent  
**🔄 Última atualização**: Junho 2025  
**✅ Status**: Sincronizado com package.json atual
