# CI/CD Pipeline Documentation

This document describes the comprehensive CI/CD pipeline for the Personal Finance Manager project.

## 📋 Overview

The CI/CD pipeline provides:

- **Automated Testing** across multiple environments
- **Quality Gates** with strict thresholds
- **Security Scanning** and vulnerability detection
- **Performance Testing** with quality gates
- **Automated Deployment** to staging and production
- **Dependency Management** with Dependabot

## 🏗️ Pipeline Architecture

### Workflows

```
.github/workflows/
├── ci.yml              # Main CI/CD pipeline
├── deploy.yml          # Deployment workflow
├── quality-gates.yml   # Quality gates and thresholds
└── test-coverage.yml   # Coverage reporting (existing)
```

### Pipeline Flow

```mermaid
graph TD
    A[Code Push/PR] --> B[Code Quality]
    B --> C[Backend Tests]
    B --> D[Frontend Tests]
    C --> E[Performance Tests]
    D --> E
    E --> F[E2E Tests]
    F --> G[Quality Gates]
    G --> H{All Gates Pass?}
    H -->|Yes| I[Deploy Staging]
    H -->|No| J[Block Merge]
    I --> K[Security Scan]
    K --> L[Deploy Production]
```

## 🚀 Main CI Pipeline (ci.yml)

### Jobs Overview

#### 1. 🔍 Code Quality
- **Duration**: ~10 minutes
- **Runs on**: ubuntu-latest
- **Purpose**: Lint, type-check, and format validation

**Steps:**
- ESLint for backend and frontend
- TypeScript compilation check
- Prettier format validation

#### 2. 🧪 Backend Tests
- **Duration**: ~20 minutes
- **Runs on**: ubuntu-latest with PostgreSQL and Redis
- **Purpose**: Comprehensive backend testing

**Services:**
- PostgreSQL 14
- Redis 7

**Steps:**
- Unit tests with Jest
- Integration tests with database
- Coverage report generation
- Upload to Codecov

#### 3. 🎨 Frontend Tests
- **Duration**: ~15 minutes
- **Runs on**: ubuntu-latest
- **Purpose**: Frontend component and unit testing

**Steps:**
- Unit tests with Vitest
- Component tests
- Coverage report generation
- Upload to Codecov

#### 4. ⚡ Performance Tests
- **Duration**: ~15 minutes
- **Runs on**: ubuntu-latest
- **Purpose**: API performance validation

**Dependencies**: backend-tests

**Steps:**
- Database setup and seeding
- k6 installation and configuration
- Performance test execution
- Results artifact upload

#### 5. 🎭 E2E Tests
- **Duration**: ~30 minutes
- **Runs on**: ubuntu-latest
- **Purpose**: End-to-end user flow testing

**Dependencies**: backend-tests, frontend-tests

**Steps:**
- Full application build
- Server startup (backend + frontend)
- Cypress E2E test execution
- Screenshot/video artifact upload

### Environment Variables

```yaml
NODE_VERSION: '18'
POSTGRES_VERSION: '14'
DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_finance_db
REDIS_URL: redis://localhost:6379
NODE_ENV: test
JWT_SECRET: test-secret-key-for-ci
```

## 🛡️ Quality Gates (quality-gates.yml)

### Coverage Quality Gate

**Thresholds:**
- Backend: ≥80% line coverage
- Frontend: ≥75% line coverage

**Actions:**
- Automatic PR comments with coverage report
- Block merge if thresholds not met
- Generate coverage badges

### Performance Quality Gate

**Thresholds:**
- Average response time: <500ms
- 95th percentile: <500ms
- 99th percentile: <1000ms
- Error rate: <1%
- Throughput: >50 req/s

**Test Scenarios:**
- Authentication endpoints
- CRUD operations
- Dashboard data loading
- Transaction processing

### Security Quality Gate

**Checks:**
- npm audit for vulnerabilities
- CodeQL static analysis
- Dependency vulnerability scan
- No critical vulnerabilities allowed

### Code Quality Gate

**Thresholds:**
- ESLint errors: 0
- ESLint warnings: ≤10
- TypeScript compilation: Must pass
- Prettier formatting: Must pass

## 🚀 Deployment Pipeline (deploy.yml)

### Staging Deployment

**Triggers:**
- Push to main branch
- Successful CI pipeline completion

**Steps:**
1. Build application (backend + frontend)
2. Run smoke tests
3. Deploy to staging environment
4. Health checks
5. Staging validation tests

### Production Deployment

**Dependencies:**
- Successful staging deployment
- Security scan completion

**Steps:**
1. Build production artifacts
2. Pre-deployment tests
3. Deploy to production
4. Production health checks
5. Post-deployment validation
6. Slack notification

### Rollback Process

**Triggers:**
- Manual trigger on deployment failure
- Automatic on health check failure

**Steps:**
1. Rollback to previous version
2. Verify rollback success
3. Notify team via Slack

## 📊 Quality Metrics and Reporting

### Coverage Reporting

- **Combined Reports**: Backend + Frontend unified coverage
- **Threshold Enforcement**: Automatic quality gate failures
- **PR Comments**: Detailed coverage breakdown
- **Badges**: Auto-generated coverage badges

### Performance Monitoring

- **Response Time Tracking**: P95, P99 percentiles
- **Error Rate Monitoring**: <1% threshold
- **Throughput Validation**: >50 req/s minimum
- **Historical Trends**: Performance over time

### Security Monitoring

- **Vulnerability Scanning**: npm audit + CodeQL
- **Dependency Tracking**: Automated updates via Dependabot
- **SAST Analysis**: Static application security testing
- **Security Reports**: Artifact generation for review

## 🔧 Configuration and Setup

### Required Secrets

```yaml
# GitHub Secrets
CODECOV_TOKEN: # For coverage uploads
SLACK_WEBHOOK: # For deployment notifications

# Environment-specific
STAGING_DATABASE_URL: # Staging database
PRODUCTION_DATABASE_URL: # Production database
```

### Branch Protection Rules

**Main Branch:**
- Require PR reviews (1 reviewer minimum)
- Require status checks to pass
- Require branches to be up to date
- Restrict pushes to admins only

**Required Status Checks:**
- Code Quality
- Backend Tests
- Frontend Tests
- Performance Tests
- E2E Tests
- All Quality Gates

### Dependabot Configuration

**Update Schedule:**
- Weekly updates on Mondays at 9:00 AM
- Separate PRs for backend, frontend, root, and shared dependencies
- GitHub Actions updates included

**Ignored Updates:**
- Major version updates for critical packages (React, Express, Prisma)
- Manual review required for breaking changes

## 🚨 Troubleshooting

### Common Issues

#### "Database connection failed"
```bash
# Check PostgreSQL service status
docker ps | grep postgres

# Verify connection string
echo $DATABASE_URL
```

#### "Performance tests failing"
```bash
# Check server startup
curl -f http://localhost:3001/api/v1/health

# Verify test data seeding
npm run test:data:stats
```

#### "E2E tests timing out"
```bash
# Check frontend build
npm run build:frontend

# Verify server startup
curl -f http://localhost:5173
```

#### "Coverage below threshold"
```bash
# Run local coverage check
npm run test:coverage:combined

# Check specific module coverage
npm run test:coverage:backend
npm run test:coverage:frontend
```

### Debug Commands

```bash
# Local CI simulation
npm run test:all

# Performance test locally
npm run test:performance:gates

# Quality gates check
npm run test:coverage:thresholds

# Security scan
npm run security:scan
```

## 📈 Performance Optimization

### Pipeline Speed

**Optimizations:**
- Parallel job execution
- Docker layer caching
- npm cache utilization
- Selective test execution

**Typical Execution Times:**
- Code Quality: 5-10 minutes
- Backend Tests: 15-20 minutes
- Frontend Tests: 10-15 minutes
- Performance Tests: 10-15 minutes
- E2E Tests: 20-30 minutes
- **Total Pipeline**: 30-45 minutes

### Resource Usage

**Compute Resources:**
- ubuntu-latest runners
- 2-core, 7GB RAM per job
- PostgreSQL and Redis services
- Artifact storage for reports

**Cost Optimization:**
- Conditional job execution
- Timeout limits on all jobs
- Efficient test data management
- Cleanup after test completion

## 🔄 Maintenance

### Regular Tasks

**Weekly:**
- Review Dependabot PRs
- Check performance trends
- Update security policies
- Review failed builds

**Monthly:**
- Update CI/CD documentation
- Review and adjust thresholds
- Optimize pipeline performance
- Security audit review

**Quarterly:**
- Major dependency updates
- Pipeline architecture review
- Tool and service updates
- Performance baseline updates

### Monitoring and Alerts

**GitHub Actions:**
- Workflow failure notifications
- Performance degradation alerts
- Security vulnerability alerts
- Deployment status updates

**External Monitoring:**
- Codecov for coverage trends
- Performance monitoring dashboards
- Security scanning reports
- Dependency vulnerability tracking

## 📚 Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [k6 Performance Testing](https://k6.io/docs/)
- [Cypress E2E Testing](https://docs.cypress.io/)
- [Codecov Integration](https://docs.codecov.com/)
- [Dependabot Configuration](https://docs.github.com/en/code-security/dependabot)
