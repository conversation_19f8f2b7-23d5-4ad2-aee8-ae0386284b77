import { defineConfig } from 'cypress'
import viteConfig from './frontend/vite.config'

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:5173',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    pageLoadTimeout: 30000,
    
    // Configuração de pastas
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.ts',
    fixturesFolder: 'cypress/fixtures',
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos',
    downloadsFolder: 'cypress/downloads',
    
    // Configuração de ambiente
    env: {
      // URLs para diferentes ambientes
      apiUrl: 'http://localhost:3001/api',
      frontendUrl: 'http://localhost:5173',
      backendUrl: 'http://localhost:3001',
      
      // Configurações de teste
      testUser: {
        email: '<EMAIL>',
        password: 'Test123!@#',
        name: 'Test User'
      },
      
      // Configurações JWT
      jwtSecret: 'test-jwt-secret-key',
      tokenExpiry: '24h'
    },
    
    setupNodeEvents(on, config) {
      // Plugin para code coverage
      require('@cypress/code-coverage/task')(on, config)
      
      // Task para limpar banco de dados de teste
      on('task', {
        'db:seed': () => {
          // Implementar seeding do banco de dados de teste
          return null
        },
        
        'db:clean': () => {
          // Implementar limpeza do banco de dados de teste
          return null
        },
        
        'generate:jwt': (payload) => {
          // Implementar geração de JWT para testes
          const jwt = require('jsonwebtoken')
          return jwt.sign(payload, config.env.jwtSecret, { expiresIn: config.env.tokenExpiry })
        },
        
        log: (message) => {
          console.log(message)
          return null
        }
      })
      
      // Configuração de ambiente baseada em variáveis
      if (config.env.environment === 'staging') {
        config.baseUrl = 'https://staging.personal-finance.com'
        config.env.apiUrl = 'https://api-staging.personal-finance.com'
      } else if (config.env.environment === 'production') {
        config.baseUrl = 'https://personal-finance.com'
        config.env.apiUrl = 'https://api.personal-finance.com'
      }
      
      return config
    },
  },
  
  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
      viteConfig: viteConfig,
    },
    
    // Configuração de pastas para component testing
    specPattern: 'frontend/src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.ts',
    indexHtmlFile: 'cypress/support/component-index.html',
    
    setupNodeEvents(on, config) {
      // Plugin para code coverage
      require('@cypress/code-coverage/task')(on, config)
      
      return config
    },
  },
  
  // Configurações globais
  retries: {
    runMode: 2,
    openMode: 0,
  },
  
  // Configurações de browser
  chromeWebSecurity: false,
  
  // Configurações de relatórios
  reporter: 'cypress-multi-reporters',
  reporterOptions: {
    configFile: 'cypress/reporter-config.json',
  },
})
