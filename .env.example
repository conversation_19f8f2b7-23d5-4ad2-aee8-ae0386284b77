# TaskMaster AI API Keys (Required to enable respective provider)
ANTHROPIC_API_KEY="your_anthropic_api_key_here"       # Required: Format: sk-ant-api03-...
PERPLEXITY_API_KEY="your_perplexity_api_key_here"     # Optional: Format: pplx-...
OPENAI_API_KEY="your_openai_api_key_here"             # Optional, for OpenAI/OpenRouter models. Format: sk-proj-...
GOOGLE_API_KEY="your_google_api_key_here"             # Optional, for Google Gemini models.
MISTRAL_API_KEY="your_mistral_key_here"               # Optional, for Mistral AI models.
XAI_API_KEY="YOUR_XAI_KEY_HERE"                       # Optional, for xAI AI models.
AZURE_OPENAI_API_KEY="your_azure_key_here"            # Optional, for Azure OpenAI models (requires endpoint in .taskmaster/config.json).
OLLAMA_API_KEY="your_ollama_api_key_here"             # Optional: For remote Ollama servers that require authentication.

# ===== PERSONAL FINANCE MANAGER CONFIGURATION =====

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/personal_finance_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="7d"

# Server Configuration
PORT=3001
NODE_ENV="development"

# CORS Configuration
FRONTEND_URL="http://localhost:5173"

# API Configuration
API_PREFIX="/api/v1"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL="info"

# Security
BCRYPT_ROUNDS=12

# API Keys (for future integrations)
# N8N_WEBHOOK_SECRET="your-n8n-webhook-secret"
# EXTERNAL_API_KEY="your-external-api-key"

# Development Tools
# PRISMA_STUDIO_PORT=5555